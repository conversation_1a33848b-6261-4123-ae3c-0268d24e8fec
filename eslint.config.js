import typescriptPlugin from "@typescript-eslint/eslint-plugin";
import typescriptParser from "@typescript-eslint/parser";
import prettierPlugin from "eslint-plugin-prettier";

export default [
  {
    files: ["**/*.ts"],
    languageOptions: {
      ecmaVersion: 2020, // Specify ECMAScript version
      sourceType: "module",
      parser: typescriptParser,
      parserOptions: {
        project: ["./tsconfig.json", "./tsconfig.node.json"], // Include both configs
        tsconfigRootDir: process.cwd(), // Ensure correct directory resolution
      },
    },
    plugins: {
      "@typescript-eslint": typescriptPlugin,
      prettier: prettierPlugin,
    },
    rules: {
      ...typescriptPlugin.configs.recommended.rules, // Apply recommended TypeScript rules
      ...typescriptPlugin.configs["recommended-requiring-type-checking"].rules,
      "@typescript-eslint/no-unused-vars": [
        "warn",
        {
          argsIgnorePattern: "^_", // Ignore unused function arguments starting with '_'
          varsIgnorePattern: "^_", // Ignore unused variables starting with '_'
        },
      ],
      "no-mixed-spaces-and-tabs": "off", // Handled by Prettier
      "prettier/prettier": "warn", // Prettier warnings will follow your Prettier config
    },
  },
  {
    ignores: ["dist", "node_modules"], // Explicitly ignore the dist and node_modules folders
  },
];
