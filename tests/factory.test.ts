import {
  assert,
  describe,
  test,
  clearStore,
  beforeAll,
  afterAll,
} from "matchstick-as/assembly/index";
import { createDenManagerCreatedEvent, createMockedFunctionsForFactory } from "./utils/factory-utils";
import { handleDenManagerDeployed } from "../src/mappings/Factory";
import { createMockedFunctionsForToken } from "./utils/token-utils";
import {
  NECT,
  _collateral,
  _decimals,
  _denManager,
  _liquidVault,
  _name,
  _priceFeed,
  _stabilityPool,
  _symbol,
  iBGT,
} from "./utils/constants";
import { createMockedFunctionsForStabilityPool } from "./utils/stability-pool-utils";
import { log } from "@graphprotocol/graph-ts";
// Tests structure (matchstick-as >=0.5.0)
// https://thegraph.com/docs/en/developer/matchstick/#tests-structure-0-5-0

describe("Describe entity assertions", () => {
  beforeAll(() => {
    let denManagerCreatedEvent = createDenManagerCreatedEvent(
      _denManager,
      _collateral,
      _priceFeed
    );

    createMockedFunctionsForToken(
      _collateral,
      _name,
      _symbol,
      _decimals,
      _priceFeed
    );

    log.info("factory: {}, {}, {}", [denManagerCreatedEvent.address.toHex(), denManagerCreatedEvent.params.denManager.toHex(), denManagerCreatedEvent.parameters[0].value.data.toString()] )
    createMockedFunctionsForFactory(
      denManagerCreatedEvent.address,
      _stabilityPool
    )

    createMockedFunctionsForStabilityPool(
      _stabilityPool,
      [iBGT],
      _liquidVault,
      NECT
    )

    handleDenManagerDeployed(denManagerCreatedEvent);
  });

  afterAll(() => {
    clearStore();
  });

  test("DenManager created and stored", () => {
    assert.entityCount("DenManager", 1);

    assert.fieldEquals(
      "DenManager",
      _denManager.toHex(),
      "totalCollateral",
      "0"
    );
  });

  test("Token created and stored", () => {
    assert.entityCount("Token", 1);

    assert.fieldEquals(
      "Token",
      _collateral.toHex(),
      "decimals",
      _decimals.toString()
    );

    assert.fieldEquals("Token", _collateral.toHex(), "name", _name.toString());

    assert.fieldEquals(
      "Token",
      _collateral.toHex(),
      "symbol",
      _symbol.toString()
    );

    assert.fieldEquals(
      "Token",
      _collateral.toHex(),
      "oracle",
      _priceFeed.toHex()
    );

    assert.fieldEquals("Token", _collateral.toHex(), "price", "10000");
  });
});
