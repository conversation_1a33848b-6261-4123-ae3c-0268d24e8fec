import {
  assert,
  describe,
  test,
  clearStore,
  beforeAll,
  afterAll,
} from "matchstick-as/assembly/index";
import {
  NECT,
  _collateral,
  _decimals,
  _denManager,
  _liquidVault,
  _name,
  _priceFeed,
  _stabilityPool,
  _symbol,
  alice,
  iBGT,
} from "./utils/constants";
import { createDenUpdatedEvent, createLTermsUpdatedEvent, createRedemptionEvent, createTotalStakesUpdatedEvent } from "./utils/den-manager-utils";
import { handleDenUpdated, handleLTermsUpdated, handleRedemption, handleTotalStakesUpdated } from "../src/mappings/DenManager";
import { generateDenId } from "../src/utils/helper";
import { Address, BigInt } from "@graphprotocol/graph-ts";
import { createDenManagerCreatedEvent, createMockedFunctionsForFactory } from "./utils/factory-utils";
import { createMockedFunctionsForToken } from "./utils/token-utils";
import { handleDenManagerDeployed } from "../src/mappings/Factory";
import { getDenManager } from "../src/entities/DenManager";
import { createMockedFunctionsForStabilityPool } from "./utils/stability-pool-utils";
// Tests structure (matchstick-as >=0.5.0)
// https://thegraph.com/docs/en/developer/matchstick/#tests-structure-0-5-0

describe("Describe entity assertions", () => {
  beforeAll(() => {
    let denManagerCreatedEvent = createDenManagerCreatedEvent(_denManager, _collateral, _priceFeed);

    createMockedFunctionsForToken(_collateral, _name, _symbol, _decimals, _priceFeed);
    createMockedFunctionsForFactory(denManagerCreatedEvent.address, _stabilityPool);
    createMockedFunctionsForStabilityPool(_stabilityPool, [iBGT], _liquidVault, NECT);
    
    handleDenManagerDeployed(denManagerCreatedEvent);

    let denUpdatedEvent = createDenUpdatedEvent(_denManager, alice);
    handleDenUpdated(denUpdatedEvent);
  });

  afterAll(() => {
    clearStore();
  });

  test("Den created and updated", () => {
    assert.entityCount("Den", 1);

    let denId = generateDenId(_denManager, alice);
    assert.fieldEquals("Den", denId, "denManager", _denManager.toHex());

    assert.fieldEquals("Den", denId, "owner", alice.toHex());

    assert.fieldEquals("Den", denId, "collateral", "1000");
    assert.fieldEquals("Den", denId, "debt", "1000");
    assert.fieldEquals("Den", denId, "rawCollateral", BigInt.fromString("10").pow(18).times(BigInt.fromI32(1000)).toString());
    assert.fieldEquals("Den", denId, "rawDebt", BigInt.fromString("10").pow(18).times(BigInt.fromI32(1000)).toString());
    assert.fieldEquals("Den", denId, "rawStake", BigInt.fromString("10").pow(18).times(BigInt.fromI32(1000)).toString());

    assert.fieldEquals("Den", denId, "status", "open");
  });

  test("DenChange created and stored", () => {
    assert.entityCount("DenChange", 1);

    let denId = generateDenId(_denManager, alice);
    let denChangeId = "0";

    assert.fieldEquals("DenChange", denChangeId, "den", denId);
    assert.fieldEquals("DenChange", denChangeId, "denOperation", "accrueRewards");

    assert.fieldEquals("DenChange", denChangeId, "collateralBefore", "0");
    assert.fieldEquals("DenChange", denChangeId, "debtBefore", "0");
    assert.fieldEquals("DenChange", denChangeId, "collateralRatioBefore", "null");

    assert.fieldEquals("DenChange", denChangeId, "collateralAfter", "1000");
    assert.fieldEquals("DenChange", denChangeId, "debtAfter", "1000");
    assert.fieldEquals("DenChange", denChangeId, "collateralChange", "1000");
    assert.fieldEquals("DenChange", denChangeId, "debtChange", "1000");
  });

  test("Redemption", () => {
    let redemptionEvent = createRedemptionEvent(_denManager, alice);
    handleRedemption(redemptionEvent);

    assert.entityCount("Redemption", 1);
    let redemptionId = "0";

    assert.fieldEquals("Redemption", redemptionId, "tokensAttemptedToRedeem", "1000");
    assert.fieldEquals("Redemption", redemptionId, "tokensActuallyRedeemed", "1000");
    assert.fieldEquals("Redemption", redemptionId, "collateralRedeemed", "10");
    assert.fieldEquals("Redemption", redemptionId, "fee", "1");

    assert.fieldEquals("Redemption", redemptionId, "denManager", _denManager.toHex());
  })

  test("LTermsUpdated", () => {
    let ltermsUpdatedEvent = createLTermsUpdatedEvent(_denManager);
    handleLTermsUpdated(ltermsUpdatedEvent);

    assert.entityCount("DenManager", 1);

    assert.fieldEquals("DenManager", _denManager.toHex(), "rawTotalRedistributedCollateral", BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)).toString());
    assert.fieldEquals("DenManager", _denManager.toHex(), "rawTotalRedistributedDebt", BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)).toString());
  })

  test("Total Stake Updates", () => {
    let totalStakesUpdatedEvent = createTotalStakesUpdatedEvent(_denManager);
    handleTotalStakesUpdated(totalStakesUpdatedEvent);

    assert.entityCount("DenManager", 1);

    assert.fieldEquals("DenManager", _denManager.toHex(), "totalStakedAmount", BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)).toString());
  })
});
