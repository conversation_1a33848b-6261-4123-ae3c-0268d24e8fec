import { afterAll, assert, beforeAll, clearStore, describe, test } from "matchstick-as";
import { createMockedFunctionsForStabilityPool } from "./utils/stability-pool-utils";
import { NECT, _collateral, _decimals, _denManager, _liquidVault, _name, _priceFeed, _stabilityPool, _symbol, alice, bob, iBGT } from "./utils/constants";
import { Address, BigInt, log } from "@graphprotocol/graph-ts";
import { createDepositEvent, createMockedFunctionsForLiquidVault } from "./utils/liquid-vault-utils";
import { handleUserDeposit } from "../src/mappings/LiquidVault";
import { getUser } from "../src/entities/User";
import { generateSharePositionId, getSharePosition } from "../src/entities/SharePosition";
import { createMockedFunctionForTokenBalance, createMockedFunctionsForToken } from "./utils/token-utils";
import { createDenManagerCreatedEvent, createMockedFunctionsForFactory } from "./utils/factory-utils";
import { handleDenManagerDeployed } from "../src/mappings/Factory";
import { generatePortfoliotId } from "../src/entities/Portfolio";

/**
 * 
 * if Alice has 50 LiquidVault shares and Bob has 25 Liquid Vault shares and in total there are 75 Liquid Vault shares.
 * And LiquidVault has 1000 NECT, 20 iBGT
 * Alice will own:
 * 50 * 1000 NECT / 75 = 666.67 NECT
 * 50 * 20 iBGT / 75 = 13.34 iBGT
 * 50 * 50 POLLEN / 75 = 33.34 POLLEN
 * Bob will do the same calculation but with 25 shares instead of Alice's 50 shares.
 */
describe("Describe LiquidVault tests", () => {
    beforeAll(() => {
      // deploy denManager
      let denManagerCreatedEvent = createDenManagerCreatedEvent(
        _denManager,
        _collateral,
        _priceFeed
      );
  
      createMockedFunctionsForToken(
        _collateral,
        _name,
        _symbol,
        _decimals,
        _priceFeed
      );

      createMockedFunctionsForToken(
        NECT,
        "NECT token",
        "NECT",
        _decimals,
        _priceFeed
      );
  
      createMockedFunctionsForFactory(
        denManagerCreatedEvent.address,
        _stabilityPool
      )
  
      createMockedFunctionsForStabilityPool(
        _stabilityPool,
        [iBGT],
        _liquidVault,
        NECT
      )
  
      handleDenManagerDeployed(denManagerCreatedEvent);

      // deposit
      
      createMockedFunctionsForStabilityPool(_stabilityPool, [iBGT], _liquidVault, NECT);
      createMockedFunctionsForLiquidVault(_liquidVault, BigInt.fromI32(1000));
      createMockedFunctionForTokenBalance(iBGT, _stabilityPool, BigInt.fromI32(0));
      createMockedFunctionForTokenBalance(NECT, _stabilityPool, BigInt.fromI32(0));
    });
  
    afterAll(() => {
      clearStore();
    });

    test("Alice deposit 1000 NECT", () => {
      let shareAmount = BigInt.fromI32(1000);
      let depositEvent = createDepositEvent(alice, shareAmount, BigInt.fromI32(100));
      handleUserDeposit(depositEvent);
      
      let user = getUser(alice);
      let sharePositionId = generateSharePositionId(alice);
      // Check user data
      assert.fieldEquals("User", user.id, "sharePosition", sharePositionId);

      // Check share position data
      let sharePosition = getSharePosition(alice);
      assert.fieldEquals("SharePosition", sharePositionId, "owner", alice.toHex());
      assert.fieldEquals("SharePosition", sharePositionId, "shareAmount", "1000");

      // Check pool portfolio
      let poolId = "SharePool";
      let portfolioId = generatePortfoliotId(depositEvent);
      assert.fieldEquals("SharePool", poolId, "portfolio", portfolioId);
      assert.fieldEquals("Portfolio", portfolioId, "totalShares", shareAmount.toString());
    })
});