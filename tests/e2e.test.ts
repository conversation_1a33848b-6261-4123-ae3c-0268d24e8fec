import { assert, describe, newMockEvent, test } from "matchstick-as";
import { createDenManagerCreatedEvent, createMockedFunctionsForFactory } from "./utils/factory-utils";
import { _borrowerOperation, _collateral, _decimals, _denManager, _liquidVault, _name, _priceFeed, _stabilityPool, _symbol, alice, bob, BorrowerOperation, iBGT, NECT } from "./utils/constants";
import { createMockedFunctionForTokenBalance, createMockedFunctionsForToken } from "./utils/token-utils";
import { createMockedFunctionsForStabilityPool } from "./utils/stability-pool-utils";
import { handleDenManagerDeployed } from "../src/mappings/Factory";
import { createDenUpdatedEvent, createNewDenCreatedEvent } from "./utils/den-utils";
import { BigInt, log } from "@graphprotocol/graph-ts";
import { handleDenCreated, handleDenUpdated } from "../src/mappings/BorrowerOperations";
import { getDen } from "../src/entities/Den";
import { createDepositEvent, createMockedFunctionsForLiquidVault } from "./utils/liquid-vault-utils";
import { handleUserDeposit } from "../src/mappings/LiquidVault";
import { getSharePosition } from "../src/entities/SharePosition";
import { createSharePool } from "../src/entities/SharePool";
import { generatePortfoliotId } from "../src/entities/Portfolio";

let aliceInitialDebtAmount = BigInt.fromI32(1000);
let bobInitialInitialDebtAmount = BigInt.fromI32(500);
let aliceRedeemAmount = BigInt.fromI32(300);
let aliceInitialCollAmount = BigInt.fromI32(100); // 100 eth
let bobInitialCollAmount = BigInt.fromI32(50); // 50 eth


/** Integration testing e2e
 * 1. Admin deployes new DenManager with iBGT as collateral
 * 2. Alice creates new den - borrows 1000 NECT
 * 3. Bob creates new den - borrows 500 NECT
 * 4. Alice borrow 200 NECT more.
 * 5. Alice redeems 300 NECT
 * 6. Alice deposit 500 NECT in stability pool and get 50 shares
 * 7. Bob deposit 200 NECT in stability pool and get 20 shares
 * 8. Check pool portfolio and snapshot
 */
describe("E2E test", () => {

    test("Deploy new den manager with iBGT collateral and register mock contract functions", () => {
        // Deploy new den manager
        let denManagerCreatedEvent = createDenManagerCreatedEvent(
            _denManager,
            _collateral,
            _priceFeed
        );

        // Register mock contract functions
        createMockedFunctionsForToken(
            _collateral,
            _name,
            _symbol,
            _decimals,
            _priceFeed
        );
      
        createMockedFunctionsForFactory(
            denManagerCreatedEvent.address,
            _stabilityPool
        )
      
        createMockedFunctionsForStabilityPool(
            _stabilityPool,
            [iBGT],
            _liquidVault,
            NECT
        )

        handleDenManagerDeployed(denManagerCreatedEvent);

        // Check entities status
        assert.entityCount("DenManager", 1);

        assert.fieldEquals(
            "DenManager",
            _denManager.toHex(),
            "totalCollateral",
            "0"
        );

        assert.fieldEquals(
            "DenManager",
            _denManager.toHex(),
            "collateral",
            _collateral.toHex()
        );

        assert.fieldEquals("SharePool", "SharePool", "pool_address", _stabilityPool.toHex());
        assert.fieldEquals("SharePool", "SharePool", "vault_address", _liquidVault.toHex());
        assert.fieldEquals("SharePool", "SharePool", "debt_token", NECT.toHex());
    });

    test("Alice borrows 1000 NECT", () => {
        let newDenCreatedEvent = createNewDenCreatedEvent(_borrowerOperation, _denManager, alice);
        let denUpdatedEvent = createDenUpdatedEvent(
            _borrowerOperation, 
            _denManager, 
            alice, 
            aliceInitialDebtAmount, 
            aliceInitialCollAmount, 
            aliceInitialCollAmount, 
            BorrowerOperation.openDen
        );

        handleDenCreated(newDenCreatedEvent);
        handleDenUpdated(denUpdatedEvent);

        // Check states
        let den = getDen(_denManager, alice);
        assert.entityCount("Den", 1);
        assert.fieldEquals("Den", den.id, "denManager", _denManager.toHex());

        assert.fieldEquals("Den", den.id, "rawCollateral", aliceInitialCollAmount.toString());
        assert.fieldEquals("Den", den.id, "rawDebt", aliceInitialDebtAmount.toString());
        assert.fieldEquals("Den", den.id, "rawStake", aliceInitialCollAmount.toString());
    });

    test("Bob borrows 500 NECT", () => {
        let newDenCreatedEvent = createNewDenCreatedEvent(_borrowerOperation, _denManager, bob);
        let denUpdatedEvent = createDenUpdatedEvent(
            _borrowerOperation, 
            _denManager, 
            bob, 
            bobInitialInitialDebtAmount, 
            bobInitialCollAmount, 
            bobInitialCollAmount, 
            BorrowerOperation.openDen
        );

        handleDenCreated(newDenCreatedEvent);
        handleDenUpdated(denUpdatedEvent);

        // Check states
        let den = getDen(_denManager, bob);
        assert.entityCount("Den", 2);
        assert.fieldEquals("Den", den.id, "denManager", _denManager.toHex());

        assert.fieldEquals("Den", den.id, "rawCollateral", bobInitialCollAmount.toString());
        assert.fieldEquals("Den", den.id, "rawDebt", bobInitialInitialDebtAmount.toString());
        assert.fieldEquals("Den", den.id, "rawStake", bobInitialCollAmount.toString());
    })

    test("Alice borrows 200 NECT more", () => {
        let denUpdatedEvent = createDenUpdatedEvent(
            _borrowerOperation,
            _denManager,
            alice,
            BigInt.fromI32(1200),
            BigInt.fromI32(120),
            BigInt.fromI32(120),
            BorrowerOperation.adjustDen
        );

        handleDenUpdated(denUpdatedEvent);

        // check status
        let den = getDen(_denManager, alice);
        assert.entityCount("Den", 2);
        assert.fieldEquals("Den", den.id, "rawDebt", "1200");
        assert.fieldEquals("Den", den.id, "rawCollateral", "120");
        assert.fieldEquals("Den", den.id, "rawStake", "120");
    })

    test("Alice redeems 300 NECT", () => {
        // Redeem 30 collat, so debt should be decreased by 300 NECT
        let denUpdatedEvent = createDenUpdatedEvent(
            _borrowerOperation,
            _denManager,
            alice,
            BigInt.fromI32(900),
            BigInt.fromI32(90),
            BigInt.fromI32(90),
            BorrowerOperation.adjustDen
        );

        handleDenUpdated(denUpdatedEvent);

        // check status
        let den = getDen(_denManager, alice);
        assert.entityCount("Den", 2);
        assert.fieldEquals("Den", den.id, "rawDebt", "900");
        assert.fieldEquals("Den", den.id, "rawCollateral", "90");
        assert.fieldEquals("Den", den.id, "rawStake", "90");
    })

    test("Register mock contract functions for stability pool", () => {
        createMockedFunctionsForToken(
            NECT,
            "NECT token",
            "NECT",
            _decimals,
            _priceFeed
        );

        createMockedFunctionsForStabilityPool(_stabilityPool, [iBGT], _liquidVault, NECT);
        createMockedFunctionsForLiquidVault(_liquidVault, BigInt.fromI32(50));
        createMockedFunctionForTokenBalance(iBGT, _stabilityPool, BigInt.fromI32(0));
        createMockedFunctionForTokenBalance(NECT, _stabilityPool, BigInt.fromI32(0));
    })

    test("Alice deposits 500 NECT in stability pool and get 50 shares", () => {
        let depositEvent = createDepositEvent(alice, BigInt.fromI32(50), BigInt.fromI32(500));
        handleUserDeposit(depositEvent);

        let sharePosition = getSharePosition(alice);
        assert.entityCount("SharePosition", 1);
        assert.fieldEquals("User", alice.toHex(), "sharePosition", sharePosition.id);
        assert.fieldEquals("SharePosition", sharePosition.id, "shareAmount", "50");

        let portfolioId = generatePortfoliotId(depositEvent);
        assert.entityCount("Portfolio", 1);
        assert.fieldEquals("Portfolio", portfolioId, "timestamp", depositEvent.block.timestamp.toString());
        assert.fieldEquals("Portfolio", portfolioId, "totalShares", "50");
        assert.fieldEquals("SharePool", "SharePool", "portfolio", portfolioId);

        assert.entityCount("Asset", 2);
    })

    test("Bob deposits 200 NECT in stability pool and get 20 shares", () => {
        let depositEvent = createDepositEvent(bob, BigInt.fromI32(20), BigInt.fromI32(200));
        handleUserDeposit(depositEvent);

        let sharePosition = getSharePosition(bob);
        assert.entityCount("SharePosition", 2);
        assert.fieldEquals("User", bob.toHex(), "sharePosition", sharePosition.id);
        assert.fieldEquals("SharePosition", sharePosition.id, "shareAmount", "20");

        let portfolioId = generatePortfoliotId(depositEvent);
        assert.entityCount("Portfolio", 1);
        assert.fieldEquals("Portfolio", portfolioId, "timestamp", depositEvent.block.timestamp.toString());
        assert.fieldEquals("Portfolio", portfolioId, "totalShares", "50");
        assert.fieldEquals("SharePool", "SharePool", "portfolio", portfolioId);

        assert.entityCount("Asset", 2);
    })
});