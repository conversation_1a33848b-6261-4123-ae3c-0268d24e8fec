import { Address, BigInt, ethereum } from "@graphprotocol/graph-ts";
import {
  BorrowingFeePaid,
  DenCreated,
  DenUpdated,
} from "../../generated/BorrowerOperations/BorrowerOperations";
import { newMockEvent } from "matchstick-as";

export function createDenCreatedEvent(
  _denManager: Address,
  _borrower: Address
): DenCreated {
  let mockEvent = newMockEvent();
  let event = new DenCreated(
    mockEvent.address,
    mockEvent.logIndex,
    mockEvent.transactionLogIndex,
    mockEvent.logType,
    mockEvent.block,
    mockEvent.transaction,
    mockEvent.parameters,
    mockEvent.receipt
  );
  event.parameters = new Array();

  event.parameters.push(
    new ethereum.EventParam(
      "denManager",
      ethereum.Value.fromAddress(_denManager)
    )
  );
  event.parameters.push(
    new ethereum.EventParam("_borrower", ethereum.Value.fromAddress(_borrower))
  );

  return event;
}

export function createDenUpdatedEvent(
  _denManager: Address,
  _borrower: Address
): DenUpdated {
  let mockEvent = newMockEvent();
  let event = new DenUpdated(
    mockEvent.address,
    mockEvent.logIndex,
    mockEvent.transactionLogIndex,
    mockEvent.logType,
    mockEvent.block,
    mockEvent.transaction,
    mockEvent.parameters,
    mockEvent.receipt
  );
  event.parameters = new Array();

  event.parameters.push(
    new ethereum.EventParam(
      "_denManager",
      ethereum.Value.fromAddress(_denManager)
    )
  );

  event.parameters.push(
    new ethereum.EventParam("_borrower", ethereum.Value.fromAddress(_borrower))
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_debt",
      ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_coll",
      ethereum.Value.fromSignedBigInt((BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000))))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "stake",
      ethereum.Value.fromSignedBigInt((BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000))))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "operation",
      ethereum.Value.fromSignedBigInt(BigInt.fromString("0"))
    )
  );
  return event;
}

export function createBorrowingFeePaidEvent(
  _denManager: Address,
  _borrower: Address
): BorrowingFeePaid {
  let mockEvent = newMockEvent();
  let event = new BorrowingFeePaid(
    mockEvent.address,
    mockEvent.logIndex,
    mockEvent.transactionLogIndex,
    mockEvent.logType,
    mockEvent.block,
    mockEvent.transaction,
    mockEvent.parameters,
    mockEvent.receipt
  );
  event.parameters = new Array();

  event.parameters.push(
    new ethereum.EventParam(
      "denManager",
      ethereum.Value.fromAddress(_denManager)
    )
  );
  event.parameters.push(
    new ethereum.EventParam("borrower", ethereum.Value.fromAddress(_borrower))
  );
  event.parameters.push(
    new ethereum.EventParam("amount", ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)))  )
  );

  return event;
}
