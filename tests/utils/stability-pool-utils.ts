import { Address, ethereum } from "@graphprotocol/graph-ts";
import { createMockedFunction } from "matchstick-as";

export function createMockedFunctionsForStabilityPool(_address: Address, _collateralTokens: Address[], _liquidVaultAddress: Address, _debtToken: Address): void {
    createMockedFunction(_address, 'getCollateralTokens', 'getCollateralTokens():(address[])')
      .withArgs([])
      .returns([ethereum.Value.fromAddressArray(_collateralTokens)])

    createMockedFunction(_address, 'liquidVault', 'liquidVault():(address)')
      .withArgs([])
      .returns([ethereum.Value.fromAddress(_liquidVaultAddress)])

    createMockedFunction(_address, 'debtToken', 'debtToken():(address)')
      .withArgs([])
      .returns([ethereum.Value.fromAddress(_debtToken)])

    return;
}