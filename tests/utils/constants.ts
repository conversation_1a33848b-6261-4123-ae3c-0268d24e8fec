import { Address, BigInt } from "@graphprotocol/graph-ts";

export const _factory = Address.fromString("0xA89e0730429a8D50826Dbc56b762399f585Efe92");
export const _borrowerOperation = Address.fromString("0xdd0b74f192224a077444a78325Ceb952fe14E627");
export const _denManager = Address.fromString("0xec3a5dfa3800c7cfd4b86dd1120cd6c3dd52bf8c");
export const _priceFeed = Address.fromString("0x80d4134cc87628dc88333fdcdfb5a39226bbcaf7");
export const _stabilityPool = Address.fromString("0x284229e6531DFE50a2bb515Cd0A1318738B05eEF");
export const _liquidVault = Address.fromString("0xD74cFBb909537EBE11Faf71E5c30E88E977aee13")

export const alice = Address.fromString("0x31123fF9c38604cf76F45b181C3A618B3F88ccab");
export const bob = Address.fromString("0xB1e0B5d8E2C790f171bB421fAf13293DAbC9FD2C");

export const _decimals = BigInt.fromString("18");
export const _name = "MockUSDT";
export const _symbol = "MUSDT";

export const iBGT = Address.fromString("0xB1e0B5d8E2C790f171bB421fAf13293DAbC9FD2A");
export const NECT = Address.fromString("0xB1e0B5d8E2C790f171bB421fAf13293DAbC9FD2B");

export const _collateral = iBGT;


export enum BorrowerOperation {
    openDen,
    closeDen,
    adjustDen
}