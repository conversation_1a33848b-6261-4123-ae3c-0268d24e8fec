import { newMockEvent } from "matchstick-as";
import { ethereum, Address, BigInt } from "@graphprotocol/graph-ts";
import {
  DenUpdated,
  LTermsUpdated,
  Redemption,
  TotalStakesUpdated,
} from "../../generated/templates/DenManager/DenManager";
import { alice } from "./constants";

export function createDenUpdatedEvent(_denManager: Address, _borrower: Address): DenUpdated {
  let mockEvent = newMockEvent();
  let event = new DenUpdated(
    _denManager,
    mockEvent.logIndex,
    mockEvent.transactionLogIndex,
    mockEvent.logType,
    mockEvent.block,
    mockEvent.transaction,
    mockEvent.parameters,
    mockEvent.receipt
  );
  event.parameters = new Array();

  event.parameters.push(
    new ethereum.EventParam(
      "_borrower",
      ethereum.Value.fromAddress(_borrower)
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_debt",
      ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_coll",
      ethereum.Value.fromSignedBigInt((BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000))))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_stake",
      ethereum.Value.fromSignedBigInt((BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000))))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_operation",
      ethereum.Value.fromSignedBigInt(BigInt.fromString("0"))
    )
  );
  return event;
}

// TODO We will add user in params
export function createRedemptionEvent(_denManager: Address, _borrower: Address): Redemption {
  let mockEvent = newMockEvent();
  let event = new Redemption(
    _denManager,
    mockEvent.logIndex,
    mockEvent.transactionLogIndex,
    mockEvent.logType,
    mockEvent.block,
    mockEvent.transaction,
    mockEvent.parameters,
    mockEvent.receipt
  );
  event.parameters = new Array();

  event.parameters.push(
    new ethereum.EventParam(
      "_borrower",
      ethereum.Value.fromAddress(_borrower)
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_attemptedDebtAmount",
      ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_actualDebtAmount",
      ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_collateralSent",
      ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(10)))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_collateralFee",
      ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1)))
    )
  );

  return event;
}

export function createLTermsUpdatedEvent(_denManager: Address): LTermsUpdated {
  let mockEvent = newMockEvent();
  let event = new LTermsUpdated(
    _denManager,
    mockEvent.logIndex,
    mockEvent.transactionLogIndex,
    mockEvent.logType,
    mockEvent.block,
    mockEvent.transaction,
    mockEvent.parameters,
    mockEvent.receipt
  );
  event.parameters = new Array();

  event.parameters.push(
    new ethereum.EventParam(
      "_L_collateral",
      ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)))
    )
  );

  event.parameters.push(
    new ethereum.EventParam(
      "_L_debt",
      ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)))
    )
  );

  return event;
}

export function createTotalStakesUpdatedEvent(
  _denManager: Address
): TotalStakesUpdated {
  let mockEvent = newMockEvent();
  let event = new TotalStakesUpdated(
    _denManager,
    mockEvent.logIndex,
    mockEvent.transactionLogIndex,
    mockEvent.logType,
    mockEvent.block,
    mockEvent.transaction,
    mockEvent.parameters,
    mockEvent.receipt
  );
  event.parameters = new Array();

  event.parameters.push(
    new ethereum.EventParam(
      "_newTotalStakes",
      ethereum.Value.fromSignedBigInt(BigInt.fromI32(10).pow(18).times(BigInt.fromI32(1000)))
    )
  );

  return event;
}
