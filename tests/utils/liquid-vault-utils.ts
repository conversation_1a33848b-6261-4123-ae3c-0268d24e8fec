import { createMockedFunction, newMockEvent } from "matchstick-as";
import { Address, BigInt, ethereum } from "@graphprotocol/graph-ts";
import { Deposit, Withdraw } from "../../generated/LiquidVault/LiquidVault";

export function createDepositEvent(user: Address, shares: BigInt, assets: BigInt): Deposit {
    let mockEvent = newMockEvent();
    let depositEvent = new Deposit(
      mockEvent.address,
      mockEvent.logIndex,
      mockEvent.transactionLogIndex,
      mockEvent.logType,
      mockEvent.block,
      mockEvent.transaction,
      mockEvent.parameters,
      mockEvent.receipt
    );
    depositEvent.parameters = new Array();

    depositEvent.parameters.push(
      new ethereum.EventParam(
        "sender",
        ethereum.Value.fromAddress(user)
      )
    );

    depositEvent.parameters.push(
      new ethereum.EventParam(
        "owner",
        ethereum.Value.fromAddress(user)
      )
    );
  
    depositEvent.parameters.push(
      new ethereum.EventParam(
        "assets",
        ethereum.Value.fromSignedBigInt(assets)
      )
    );

    
    depositEvent.parameters.push(
      new ethereum.EventParam(
        "shares",
        ethereum.Value.fromSignedBigInt(shares)
      )
    );
        
    return depositEvent;
  }

  export function createWithdrawEvent(user: Address, shares: BigInt, assets: BigInt): Withdraw {
    let mockEvent = newMockEvent();
    let event = new Withdraw(
      mockEvent.address,
      mockEvent.logIndex,
      mockEvent.transactionLogIndex,
      mockEvent.logType,
      mockEvent.block,
      mockEvent.transaction,
      mockEvent.parameters,
      mockEvent.receipt
    );
    event.parameters = new Array();
  
    event.parameters.push(
      new ethereum.EventParam(
        "sender",
        ethereum.Value.fromAddress(user)
      )
    );

    event.parameters.push(
      new ethereum.EventParam(
        "receiver",
        ethereum.Value.fromAddress(user)
      )
    );

    event.parameters.push(
      new ethereum.EventParam(
        "owner",
        ethereum.Value.fromAddress(user)
      )
    );
  
    event.parameters.push(
      new ethereum.EventParam(
        "assets",
        ethereum.Value.fromSignedBigInt(assets)
      )
    );
    

    event.parameters.push(
      new ethereum.EventParam(
        "shares",
        ethereum.Value.fromSignedBigInt(shares)
      )
    );
    
    return event;
  }

  export function createMockedFunctionsForLiquidVault(_address: Address, _totalSupply: BigInt): void {
    createMockedFunction(_address, 'totalSupply', 'totalSupply():(uint256)')
      .withArgs([])
      .returns([ethereum.Value.fromSignedBigInt(_totalSupply)])
  
    return;
  }