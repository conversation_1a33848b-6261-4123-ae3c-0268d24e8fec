import { Address, ethereum, BigInt } from "@graphprotocol/graph-ts";
import { createMockedFunction } from "matchstick-as";

export function createMockedFunctionsForToken(_address: Address, _name: string, _symbol: string, _decimals: BigInt, _priceFeed: Address): void {
    createMockedFunction(_address, 'decimals', 'decimals():(uint8)')
      .withArgs([])
      .returns([ethereum.Value.fromSignedBigInt(_decimals)])

    createMockedFunction(_address, 'name', 'name():(string)')
      .withArgs([])
      .returns([ethereum.Value.fromString(_name)])

    createMockedFunction(_address, 'symbol', 'symbol():(string)')
      .withArgs([])
      .returns([ethereum.Value.fromString(_symbol)])

    createMockedFunction(_priceFeed, 'fetchPrice', 'fetchPrice(address):(uint256)')
        .withArgs([ethereum.Value.fromAddress(_address)])
        .returns([ethereum.Value.fromSignedBigInt(BigInt.fromI32(10000))])
    
    return;
}

export function createMockedFunctionForTokenBalance(_address: Address, _account: Address, _balance: BigInt): void {
  createMockedFunction(_address, 'balanceOf', 'balanceOf(address):(uint256)')
  .withArgs([ethereum.Value.fromAddress(_account)])
  .returns([ethereum.Value.fromSignedBigInt(_balance)])
}