import { newMockEvent } from "matchstick-as";
import { DenCreated, DenUpdated } from "../../generated/BorrowerOperations/BorrowerOperations";
import { Address, BigInt, ethereum } from "@graphprotocol/graph-ts";
import { BorrowerOperation } from "./constants";

export const createNewDenCreatedEvent = (
    _srcAddress: Address,
    _denManagerAddress: Address, 
    _borrowerAddress: Address): DenCreated => {
    let mockEvent = newMockEvent();
    let event = new DenCreated(
        _srcAddress,
        mockEvent.logIndex,
        mockEvent.transactionLogIndex,
        mockEvent.logType,
        mockEvent.block,
        mockEvent.transaction,
        mockEvent.parameters,
        mockEvent.receipt
    );
    event.parameters = new Array();

    event.parameters.push(
        new ethereum.EventParam(
            "denManager",
            ethereum.Value.fromAddress(_denManagerAddress)
        )
    );

    event.parameters.push(
        new ethereum.EventParam(
            "_borrower",
            ethereum.Value.fromAddress(_borrowerAddress)
        )
    );

    event.parameters.push(
        new ethereum.EventParam(
            "arrayIndex",
            ethereum.Value.fromSignedBigInt(BigInt.fromI32(1))
        )
    );

    return event;
}

export const createDenUpdatedEvent = (
    _srcAddress: Address, 
    _denManagerAddress: Address, 
    _borrowerAddress: Address, 
    _debtAmount: BigInt, 
    _collAmount: BigInt, 
    _stakeAmount: BigInt, 
    _operation: BorrowerOperation
): DenUpdated => {
    let mockEvent = newMockEvent();
    let event = new DenUpdated(
        _srcAddress,
        mockEvent.logIndex,
        mockEvent.transactionLogIndex,
        mockEvent.logType,
        mockEvent.block,
        mockEvent.transaction,
        mockEvent.parameters,
        mockEvent.receipt
    );

    event.parameters = new Array();

    event.parameters.push(
        new ethereum.EventParam(
            "_denManager",
            ethereum.Value.fromAddress(_denManagerAddress)
        )
    )
    event.parameters.push(
        new ethereum.EventParam(
            "_borrower",
            ethereum.Value.fromAddress(_borrowerAddress)
        )
    )

    event.parameters.push(
        new ethereum.EventParam(
            "_debt",
            ethereum.Value.fromSignedBigInt(_debtAmount)
        )
    )

    event.parameters.push(
        new ethereum.EventParam(
            "_coll",
            ethereum.Value.fromSignedBigInt(_collAmount)
        )
    )

    event.parameters.push(
        new ethereum.EventParam(
            "stake",
            ethereum.Value.fromSignedBigInt(_stakeAmount)
        )
    )

    event.parameters.push(
        new ethereum.EventParam(
            "operation",
            ethereum.Value.fromI32(_operation)
        )
    )

    return event;
}