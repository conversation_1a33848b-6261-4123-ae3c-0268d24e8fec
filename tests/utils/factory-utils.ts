import { createMockedFunction, newMockEvent } from "matchstick-as";
import { ethereum, Address } from "@graphprotocol/graph-ts";
import { NewDeployment } from "../../generated/Factory/Factory";

export function createDenManagerCreatedEvent(
  _denManager: Address,
  _collateral: Address,
  _priceFeed: Address
): NewDeployment {
  let mockEvent = newMockEvent();
  let newDeploymentEvent = new NewDeployment(
    mockEvent.address,
    mockEvent.logIndex,
    mockEvent.transactionLogIndex,
    mockEvent.logType,
    mockEvent.block,
    mockEvent.transaction,
    mockEvent.parameters,
    mockEvent.receipt
  );
  newDeploymentEvent.parameters = new Array();

  newDeploymentEvent.parameters.push(
    new ethereum.EventParam(
      "collateral",
      ethereum.Value.fromAddress(_collateral)
    )
  );
  newDeploymentEvent.parameters.push(
    new ethereum.EventParam(
      "priceFeed",
      ethereum.Value.fromAddress(_priceFeed)
    )
  );
  newDeploymentEvent.parameters.push(
    new ethereum.EventParam(
      "denManager",
      ethereum.Value.fromAddress(_denManager)
    )
  );

  newDeploymentEvent.parameters.push(
    new ethereum.EventParam(
      "sortedDens",
      ethereum.Value.fromAddress(Address.zero())
    )
  );

  return newDeploymentEvent;
}

export function createMockedFunctionsForFactory(_address: Address, _stabilityPoolAddress: Address): void {
  createMockedFunction(_address, 'stabilityPool', 'stabilityPool():(address)')
    .withArgs([])
    .returns([ethereum.Value.fromAddress(_stabilityPoolAddress)])

  return;
}