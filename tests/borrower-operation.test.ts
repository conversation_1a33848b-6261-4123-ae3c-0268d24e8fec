import { assert, beforeAll, describe, test } from "matchstick-as";
import {
  NECT,
  _collateral,
  _decimals,
  _denManager,
  _liquidVault,
  _name,
  _priceFeed,
  _stabilityPool,
  _symbol,
  alice,
  iBGT,
} from "./utils/constants";
import { createMockedFunctionsForToken } from "./utils/token-utils";
import { createDenManagerCreatedEvent, createMockedFunctionsForFactory } from "./utils/factory-utils";
import { handleDenManagerDeployed } from "../src/mappings/Factory";
import { createBorrowingFeePaidEvent, createDenCreatedEvent, createDenUpdatedEvent } from "./utils/borrower-operations-utils";
import { handleBorrowingFeePaid, handleDenCreated, handleDenUpdated } from "../src/mappings/BorrowerOperations";
import { generateDenId } from "../src/utils/helper";
import { BigInt } from "@graphprotocol/graph-ts";
import { DenChange } from "../generated/schema";
import { createMockedFunctionsForStabilityPool } from "./utils/stability-pool-utils";

describe("Describe entity assertions", () => {
  beforeAll(() => {
    let denManagerCreatedEvent = createDenManagerCreatedEvent(
      _denManager,
      _collateral,
      _priceFeed
    );

    createMockedFunctionsForToken(
      _collateral,
      _name,
      _symbol,
      _decimals,
      _priceFeed
    );
    createMockedFunctionsForFactory(denManagerCreatedEvent.address, _stabilityPool);
    createMockedFunctionsForStabilityPool(_stabilityPool, [iBGT], _liquidVault, NECT);
    

    handleDenManagerDeployed(denManagerCreatedEvent);

    let denCreatedEvent = createDenCreatedEvent(_denManager, alice);
    handleDenCreated(denCreatedEvent);
  });

  test("Den created", () => {
    assert.entityCount("Den", 1);

    let denId = generateDenId(_denManager, alice);
    assert.fieldEquals("Den", denId, "denManager", _denManager.toHex());

    assert.fieldEquals("Den", denId, "owner", alice.toHex());

    assert.fieldEquals("Den", denId, "collateral", "0");
    assert.fieldEquals("Den", denId, "debt", "0");
    
    assert.fieldEquals("Den", denId, "status", "open");
  });

  test("Den updated", () => {
    let denUpdatedEvent = createDenUpdatedEvent(_denManager, alice);
    handleDenUpdated(denUpdatedEvent);

    let denId = generateDenId(_denManager, alice);
    assert.fieldEquals("Den", denId, "denManager", _denManager.toHex());

    assert.fieldEquals("Den", denId, "owner", alice.toHex());

    assert.fieldEquals("Den", denId, "collateral", "1000");
    assert.fieldEquals("Den", denId, "debt", "1000");
    assert.fieldEquals("Den", denId, "rawCollateral", BigInt.fromString("10").pow(18).times(BigInt.fromI32(1000)).toString());
    assert.fieldEquals("Den", denId, "rawDebt", BigInt.fromString("10").pow(18).times(BigInt.fromI32(1000)).toString());
    assert.fieldEquals("Den", denId, "rawStake", BigInt.fromString("10").pow(18).times(BigInt.fromI32(1000)).toString());

    assert.fieldEquals("Den", denId, "status", "open");
  })

  test("Borrowing Fee Paid", () => {
    let borrowingFeePaidEvent = createBorrowingFeePaidEvent(_denManager, alice);
    handleBorrowingFeePaid(borrowingFeePaidEvent);

    let denChagneId = "0";
    assert.fieldEquals("DenChange", denChagneId, "borrowingFee", "1000");
    assert.fieldEquals("DenManager", _denManager.toHex(), "totalBorrowingFeesPaid", "1000");
  })
});
