module.exports = {
  root: true,
  env: {
    browser: true,
    es2020: true
  },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    project: "./tsconfig.json", // Enable rules requiring type checking
    sourceType: "module",
  },
  plugins: ["react-refresh"],
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:@typescript-eslint/recommended-requiring-type-checking",
    "plugin:react-hooks/recommended",
    "prettier", // Ensure Prettier works with ESLint
    "plugin:prettier/recommended"
  ],
  ignorePatterns: ["dist", ".eslintrc.cjs"],
  rules: {
    "react-refresh/only-export-components": ["warn", { allowConstantExport: true }],
    "@typescript-eslint/no-unused-vars": ["warn"],
    "no-mixed-spaces-and-tabs": "off", // Avoids conflicts with Prettier
    "prettier/prettier": "warn", // Prettier warnings will follow your Prettier config
  },
  settings: {
    react: {
      version: "detect", // Automatically detect the React version
    },
  },
};