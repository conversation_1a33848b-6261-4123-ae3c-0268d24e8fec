# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@Beraborrowofficial/sdk@npm:2.3.5":
  version: 2.3.5
  resolution: "@Beraborrowofficial/sdk@npm:2.3.5::__archiveUrl=https%3A%2F%2Fnpm.pkg.github.com%2Fdownload%2F%40Beraborrowofficial%2Fsdk%2F2.3.5%2F6b4a1f9dc0593f47ba390af6731d198eaf7135e4"
  dependencies:
    "@apollo/client": "npm:^3.10.8"
    axios: "npm:1.8.1"
    graphql: "npm:^16.9.0"
    viem: "npm:2.23.3"
  checksum: 10c0/8516d0c8d2cf7122b6386a5a74cb437cba3860f84227cec206863e97671dec048e6a3e6254dc96195879580bddca5076999903297b5573d0b9a2b8dc60618111
  languageName: node
  linkType: hard

"@adraffy/ens-normalize@npm:^1.10.1":
  version: 1.11.0
  resolution: "@adraffy/ens-normalize@npm:1.11.0"
  checksum: 10c0/5111d0f1a273468cb5661ed3cf46ee58de8f32f84e2ebc2365652e66c1ead82649df94c736804e2b9cfa831d30ef24e1cc3575d970dbda583416d3a98d8870a6
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@apideck/better-ajv-errors@npm:^0.3.1":
  version: 0.3.6
  resolution: "@apideck/better-ajv-errors@npm:0.3.6"
  dependencies:
    json-schema: "npm:^0.4.0"
    jsonpointer: "npm:^5.0.0"
    leven: "npm:^3.1.0"
  peerDependencies:
    ajv: ">=8"
  checksum: 10c0/f89a1e16ecbc2ada91c56d4391c8345471e385f0b9c38d62c3bccac40ec94482cdfa496d4c2fe0af411e9851a9931c0d5042a8040f52213f603ba6b6fd7f949b
  languageName: node
  linkType: hard

"@apollo/client@npm:^3.10.8":
  version: 3.13.8
  resolution: "@apollo/client@npm:3.13.8"
  dependencies:
    "@graphql-typed-document-node/core": "npm:^3.1.1"
    "@wry/caches": "npm:^1.0.0"
    "@wry/equality": "npm:^0.5.6"
    "@wry/trie": "npm:^0.5.0"
    graphql-tag: "npm:^2.12.6"
    hoist-non-react-statics: "npm:^3.3.2"
    optimism: "npm:^0.18.0"
    prop-types: "npm:^15.7.2"
    rehackt: "npm:^0.1.0"
    symbol-observable: "npm:^4.0.0"
    ts-invariant: "npm:^0.10.3"
    tslib: "npm:^2.3.0"
    zen-observable-ts: "npm:^1.2.5"
  peerDependencies:
    graphql: ^15.0.0 || ^16.0.0
    graphql-ws: ^5.5.5 || ^6.0.3
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc
    subscriptions-transport-ws: ^0.9.0 || ^0.11.0
  peerDependenciesMeta:
    graphql-ws:
      optional: true
    react:
      optional: true
    react-dom:
      optional: true
    subscriptions-transport-ws:
      optional: true
  checksum: 10c0/0e5032c1ae1dbef72a01f87af06b84bf505d60e71eba7cb9f20f8284778d8ead65fc1b7eacc570eccb8d045577d7194e38401fbfbdf56c197e159ca91ef11755
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10c0/5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.27.2":
  version: 7.27.5
  resolution: "@babel/compat-data@npm:7.27.5"
  checksum: 10c0/da2751fcd0b58eea958f2b2f7ff7d6de1280712b709fa1ad054b73dc7d31f589e353bb50479b9dc96007935f3ed3cada68ac5b45ce93086b7122ddc32e60dc00
  languageName: node
  linkType: hard

"@babel/core@npm:^7.18.5, @babel/core@npm:^7.24.4, @babel/core@npm:^7.25.2":
  version: 7.27.4
  resolution: "@babel/core@npm:7.27.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.4"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.4"
    "@babel/types": "npm:^7.27.3"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/d2d17b106a8d91d3eda754bb3f26b53a12eb7646df73c2b2d2e9b08d90529186bc69e3823f70a96ec6e5719dc2372fb54e14ad499da47ceeb172d2f7008787b5
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.3":
  version: 7.27.5
  resolution: "@babel/generator@npm:7.27.5"
  dependencies:
    "@babel/parser": "npm:^7.27.5"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/8f649ef4cd81765c832bb11de4d6064b035ffebdecde668ba7abee68a7b0bce5c9feabb5dc5bb8aeba5bd9e5c2afa3899d852d2bd9ca77a711ba8c8379f416f0
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1":
  version: 7.27.3
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.3"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  checksum: 10c0/94996ce0a05b7229f956033e6dcd69393db2b0886d0db6aff41e704390402b8cdcca11f61449cb4f86cfd9e61b5ad3a73e4fa661eeed7846b125bd1c33dbc633
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.27.1, @babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4ee199671d6b9bdd4988aa2eea4bdced9a73abfc831d81b00c7634f49a8fc271b3ceda01c067af58018eb720c6151322015d463abea7072a368ee13f35adbb4c
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    regexpu-core: "npm:^6.2.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/591fe8bd3bb39679cc49588889b83bd628d8c4b99c55bafa81e80b1e605a348b64da955e3fd891c4ba3f36fd015367ba2eadea22af6a7de1610fbb5bcc2d3df0
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.3, @babel/helper-define-polyfill-provider@npm:^0.6.4":
  version: 0.6.4
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.4"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.6"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    debug: "npm:^4.1.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.14.2"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b74f2b46e233a178618d19432bdae16e0137d0a603497ee901155e083c4a61f26fe01d79fb95d5f4c22131ade9d958d8f587088d412cca1302633587f070919d
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/5762ad009b6a3d8b0e6e79ff6011b3b8fdda0fefad56cfa8bfbe6aa02d5a8a8a9680a45748fe3ac47e735a03d2d88c0a676e3f9f59f20ae9fadcc8d51ccd5a53
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.10.4, @babel/helper-module-imports@npm:^7.16.7, @babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1, @babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/fccb4f512a13b4c069af51e1b56b20f54024bcf1591e31e978a30f3502567f34f90a80da6a19a6148c249216292a8074a0121f9e52602510ef0f32dbce95ca01
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/6b861e7fcf6031b9c9fc2de3cd6c005e94a459d6caf3621d93346b52774925800ca29d4f64595a5ceacf4d161eb0d27649ae385110ed69491d9776686fa488e6
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10c0/94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-wrap-function": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/5ba6258f4bb57c7c9fa76b55f416b2d18c867b48c1af4f9f2f7cd7cc933fe6da7514811d08ceb4972f1493be46f4b69c40282b811d1397403febae13c2ec57b5
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4f2eaaf5fcc196580221a7ccd0f8873447b5d52745ad4096418f6101a1d2e712e9f93722c9a32bc9769a1dc197e001f60d6f5438d4dfde4b9c6a9e4df719354c
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/f625013bcdea422c470223a2614e90d2c1cc9d832e97f32ca1b4f82b34bb4aa67c3904cb4b116375d3b5b753acfb3951ed50835a1e832e7225295c7b0c24dff7
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10c0/8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10c0/c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10c0/6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-wrap-function@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/c472f75c0951bc657ab0a117538c7c116566ae7579ed47ac3f572c42dc78bd6f1e18f52ebe80d38300c991c3fcaa06979e2f8864ee919369dabd59072288de30
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/helpers@npm:7.27.4"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
  checksum: 10c0/3463551420926b3f403c1a30d66ac67bba5c4f73539a8ccb71544da129c4709ac37c57fac740ed8a261b3e6bbbf353b05e03b36ea1a6bf1081604b2a94ca43c1
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.4, @babel/parser@npm:^7.27.5":
  version: 7.27.5
  resolution: "@babel/parser@npm:7.27.5"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/f7faaebf21cc1f25d9ca8ac02c447ed38ef3460ea95be7ea760916dcf529476340d72a5a6010c6641d9ed9d12ad827c8424840277ec2295c5b082ba0f291220a
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/7dfffa978ae1cd179641a7c4b4ad688c6828c2c58ec96b118c2fb10bc3715223de6b88bff1ebff67056bb5fccc568ae773e3b83c592a1b843423319f80c99ebd
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/2cd7a55a856e5e59bbd9484247c092a41e0d9f966778e7019da324d9e0928892d26afc4fbb2ac3d76a3c5a631cd3cf0d72dd2653b44f634f6c663b9e6f80aacd
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/cf29835498c4a25bd470908528919729a0799b2ec94e89004929a5532c94a5e4b1a49bc5d6673a22e5afe05d08465873e14ee3b28c42eb3db489cdf5ca47c680
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-transform-optional-chaining": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 10c0/eddcd056f76e198868cbff883eb148acfade8f0890973ab545295df0c08e39573a72e65372bcc0b0bfadba1b043fe1aea6b0907d0b4889453ac154c404194ebc
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/b94e6c3fc019e988b1499490829c327a1067b4ddea8ad402f6d0554793c9124148c2125338c723661b6dff040951abc1f092afbf3f2d234319cd580b68e52445
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e605e0070da087f6c35579499e65801179a521b6842c15181a1e305c04fded2393f11c1efd09b087be7f8b083d1b75e8f3efcbc1292b4f60d3369e14812cff63
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/06a954ee672f7a7c44d52b6e55598da43a7064e80df219765c51c37a0692641277e90411028f7cae4f4d1dedeed084f0c453576fa421c35a81f1603c5e3e0146
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e66f7a761b8360419bbb93ab67d87c8a97465ef4637a985ff682ce7ba6918b34b29d81190204cf908d0933058ee7b42737423cd8a999546c21b3aabad4affa9a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/9144e5b02a211a4fb9a0ce91063f94fbe1004e80bde3485a0910c9f14897cf83fabd8c21267907cff25db8e224858178df0517f14333cfcf3380ad9a4139cb50
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/19abd7a7d11eef58c9340408a4c2594503f6c4eaea1baa7b0e5fbdda89df097e50663edb3448ad2300170b39efca98a75e5767af05cad3b0facb4944326896a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/772e449c69ee42a466443acefb07083bd89efb1a1d95679a4dc99ea3be9d8a3c43a2b74d2da95d7c818e9dd9e0b72bfa7c03217a1feaf108f21b7e542f0943c0
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e76b1f6f9c3bbf72e17d7639406d47f09481806de4db99a8de375a0bb40957ea309b20aa705f0c25ab1d7c845e3f365af67eafa368034521151a0e352a03ef2f
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/3313130ba3bf0699baad0e60da1c8c3c2f0c2c0a7039cd0063e54e72e739c33f1baadfc9d8c73b3fea8c85dd7250c3964fb09c8e1fa62ba0b24a9fefe0a8dbde
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.27.1":
  version: 7.27.5
  resolution: "@babel/plugin-transform-block-scoping@npm:7.27.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5c1a61f312f18d3807c4df25868161301a7bd0807092b86951fa6b9918e07ee382d58d61a204c3f9ad0b72b8f6f1d18586f8e485c355a3e959c26a070397e95e
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cc0662633c0fe6df95819fef223506ddf26c369c8d64ab21a728d9007ec866bf9436a253909819216c24a82186b6ccbc1ec94d7aaf3f82df227c7c02fa6a704b
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-static-block@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 10c0/396997dd81fc1cf242b921e337d25089d6b9dc3596e81322ff11a6359326dc44f2f8b82dcc279c2e514cafaf8964dc7ed39e9fab4b8af1308b57387d111f6a20
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-classes@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    globals: "npm:^11.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/1071f4cb1ed5deb5e6f8d0442f2293a540cac5caa5ab3c25ad0571aadcbf961f61e26d367a67894976165a543e02f3a19e40b63b909afbed6e710801a590635c
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e09a12f8c8ae0e6a6144c102956947b4ec05f6c844169121d0ec4529c2d30ad1dc59fee67736193b87a402f44552c888a519a680a31853bdb4d34788c28af3b0
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.27.1, @babel/plugin-transform-destructuring@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/plugin-transform-destructuring@npm:7.27.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f8ac96deef6f9a4cb1dff148dfa2a43116ca1c48434bba433964498c4ef5cef5557693b47463e64a71ffaaf10191c7fab0270844e8dbdc47dc4d120435025df5
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f9caddfad9a551b4dabe0dcb7c040f458fbaaa7bbb44200c20198b32c8259be8e050e58d2c853fdac901a4cfe490b86aa857036d8d461b192dd010d0e242dedb
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/22a822e5342b7066f83eaedc4fd9bb044ac6bc68725484690b33ba04a7104980e43ea3229de439286cb8db8e7db4a865733a3f05123ab58a10f189f03553746f
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/121502a252b3206913e1e990a47fea34397b4cbf7804d4cd872d45961bc45b603423f60ca87f3a3023a62528f5feb475ac1c9ec76096899ec182fcb135eba375
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/8dcd3087aca134b064fc361d2cc34eec1f900f6be039b6368104afcef10bb75dea726bb18cabd046716b89b0edaa771f50189fa16bc5c5914a38cbcf166350f7
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/953d21e01fed76da8e08fb5094cade7bf8927c1bb79301916bec2db0593b41dbcfbca1024ad5db886b72208a93ada8f57a219525aad048cf15814eeb65cf760d
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d7165cad11f571a54c8d9263d6c6bf2b817aff4874f747cb51e6e49efb32f2c9b37a6850cdb5e3b81e0b638141bb77dc782a6ec1a94128859fbdf7767581e07c
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-for-of@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4635763173a23aae24480681f2b0996b4f54a0cb2368880301a1801638242e263132d1e8adbe112ab272913d1d900ee0d6f7dea79443aef9d3325168cd88b3fb
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5abdc7b5945fbd807269dcc6e76e52b69235056023b0b35d311e8f5dfd6c09d9f225839798998fc3b663f50cf701457ddb76517025a0d7a5474f3fe56e567a4c
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-json-strings@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2379714aca025516452a7c1afa1ca42a22b9b51a5050a653cc6198a51665ab82bdecf36106d32d731512706a1e373c5637f5ff635737319aa42f3827da2326d6
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c40dc3eb2f45a92ee476412314a40e471af51a0f51a24e91b85cef5fc59f4fe06758088f541643f07f949d2c67ee7bdce10e11c5ec56791ae09b15c3b451eeca
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5b0abc7c0d09d562bf555c646dce63a30288e5db46fd2ce809a61d064415da6efc3b2b3c59b8e4fe98accd072c89a2f7c3765b400e4bf488651735d314d9feeb
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0874ccebbd1c6a155e5f6b3b29729fade1221b73152567c1af1e1a7c12848004dffecbd7eded6dc463955120040ae57c17cb586b53fb5a7a27fcd88177034c30
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-amd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/76e86cd278b6a3c5b8cca8dfb3428e9cd0c81a5df7096e04c783c506696b916a9561386d610a9d846ef64804640e0bd818ea47455fed0ee89b7f66c555b29537
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4def972dcd23375a266ea1189115a4ff61744b2c9366fc1de648b3fab2c650faf1a94092de93a33ff18858d2e6c4dddeeee5384cb42ba0129baeab01a5cdf1e2
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f16fca62d144d9cbf558e7b5f83e13bb6d0f21fdeff3024b0cecd42ffdec0b4151461da42bd0963512783ece31aafa5ffe03446b4869220ddd095b24d414e2b5
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-umd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e5962a8874889da2ab1aa32eb93ec21d419c7423c766e4befb39b4bb512b9ad44b47837b6cd1c8f1065445cbbcc6dc2be10298ac6e734e5ca1059fc23698daed
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/8eaa8c9aee00a00f3bd8bd8b561d3f569644d98cb2cfe3026d7398aabf9b29afd62f24f142b4112fa1f572d9b0e1928291b099cde59f56d6b59f4d565e58abf2
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-new-target@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9b0581412fcc5ab1b9a2d86a0c5407bd959391f0a1e77a46953fef9f7a57f3f4020d75f71098c5f9e5dcc680a87f9fd99b3205ab12e25ef8c19eed038c1e4b28
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a435fc03aaa65c6ef8e99b2d61af0994eb5cdd4a28562d78c3b0b0228ca7e501aa255e1dff091a6996d7d3ea808eb5a65fd50ecd28dfb10687a8a1095dcadc7a
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b72cbebbfe46fcf319504edc1cf59f3f41c992dd6840db766367f6a1d232cd2c52143c5eaf57e0316710bee251cae94be97c6d646b5022fcd9274ccb131b470c
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.27.2":
  version: 7.27.3
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.27.3"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.27.3"
    "@babel/plugin-transform-parameters": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f2d04f59f773a9480bbaabd082fecdb5fb2b6ae5e77147ae8df34a8b773b6148d0c4260d2beaa4755eb5f548a105f2069124b9cea96f9387128656cbb0730ee4
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-object-super@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/efa2d092ef55105deb06d30aff4e460c57779b94861188128489b72378bf1f0ab0f06a4a4d68b9ae2a59a79719fbb2d148b9a3dca19ceff9c73b1f1a95e0527c
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/807a4330f1fac08e2682d57bc82e714868fc651c8876f9a8b3a3fd8f53c129e87371f8243e712ac7dae11e090b737a2219a02fe1b6459a29e664fa073c3277bb
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5b18ff5124e503f0a25d6b195be7351a028b3992d6f2a91fb4037e2a2c386400d66bc1df8f6df0a94c708524f318729e81a95c41906e5a7919a06a43e573a525
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-parameters@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/453a9618735eeff5551d4c7f02c250606586fe1dd210ec9f69a4f15629ace180cd944339ebff2b0f11e1a40567d83a229ba1c567620e70b2ebedea576e12196a
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-methods@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/232bedfe9d28df215fb03cc7623bdde468b1246bdd6dc24465ff4bf9cc5f5a256ae33daea1fafa6cc59705e4d29da9024bb79baccaa5cd92811ac5db9b9244f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a8c4536273ca716dcc98e74ea25ca76431528554922f184392be3ddaf1761d4aa0e06f1311577755bd1613f7054fb51d29de2ada1130f743d329170a1aa1fe56
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-property-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/15713a87edd6db620d6e66eb551b4fbfff5b8232c460c7c76cedf98efdc5cd21080c97040231e19e06594c6d7dfa66e1ab3d0951e29d5814fb25e813f6d6209c
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00a4f917b70a608f9aca2fb39aabe04a60aa33165a7e0105fd44b3a8531630eb85bf5572e9f242f51e6ad2fa38c2e7e780902176c863556c58b5ba6f6e164031
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5e67b56c39c4d03e59e03ba80692b24c5a921472079b63af711b1d250fc37c1733a17069b63537f750f3e937ec44a42b1ee6a46cd23b1a0df5163b17f741f7f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.27.1":
  version: 7.27.5
  resolution: "@babel/plugin-transform-regenerator@npm:7.27.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4ace8ced76b421cd44dd9fa08bebc2f3fd76ec84e532cd1027738f411afdbc239789edd6c96dd1db412fc4a42cead5c1ac98a8aef94f35102f5de1049e64c07a
  languageName: node
  linkType: hard

"@babel/plugin-transform-regexp-modifiers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-regexp-modifiers@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/31ae596ab56751cf43468a6c0a9d6bc3521d306d2bee9c6957cdb64bea53812ce24bd13a32f766150d62b737bca5b0650b2c62db379382fff0dccbf076055c33
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-reserved-words@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e1a87691cce21a644a474d7c9a8107d4486c062957be32042d40f0a3d0cc66e00a3150989655019c255ff020d2640ac16aaf544792717d586f219f3bad295567
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bd5544b89520a22c41a6df5ddac9039821d3334c0ef364d18b0ba9674c5071c223bcc98be5867dc3865cb10796882b7594e2c40dedaff38e1b1273913fe353e1
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b34fc58b33bd35b47d67416655c2cbc8578fbb3948b4592bc15eb6d8b4046986e25c06e3b9929460fa4ab08e9653582415e7ef8b87d265e1239251bdf5a4c162
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5698df2d924f0b1b7bdb7ef370e83f99ed3f0964eb3b9c27d774d021bee7f6d45f9a73e2be369d90b4aff1603ce29827f8743f091789960e7669daf9c3cda850
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-template-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c90f403e42ef062b60654d1c122c70f3ec6f00c2f304b0931ebe6d0b432498ef8a5ef9266ddf00debc535f8390842207e44d3900eff1d2bab0cc1a700f03e083
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a13c68015311fefa06a51830bc69d5badd06c881b13d5cf9ba04bf7c73e3fc6311cc889e18d9645ce2a64a79456dc9c7be88476c0b6802f62a686cb6f662ecd6
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a6809e0ca69d77ee9804e0c1164e8a2dea5e40718f6dcf234aeddf7292e7414f7ee331d87f17eb6f160823a329d1d6751bd49b35b392ac4a6efc032e4d3038d8
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a332bc3cb3eeea67c47502bc52d13a0f8abae5a7bfcb08b93a8300ddaff8d9e1238f912969494c1b494c1898c6f19687054440706700b6d12cb0b90d88beb4d0
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6abda1bcffb79feba6f5c691859cdbe984cc96481ea65d5af5ba97c2e843154005f0886e25006a37a2d213c0243506a06eaeafd93a040dbe1f79539016a0d17a
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/236645f4d0a1fba7c18dc8ffe3975933af93e478f2665650c2d91cf528cfa1587cde5cfe277e0e501fc03b5bf57638369575d6539cef478632fb93bd7d7d7178
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.11.0":
  version: 7.27.2
  resolution: "@babel/preset-env@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "npm:^7.27.1"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope": "npm:^7.27.1"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "npm:^7.27.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "npm:^7.27.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "npm:^7.27.1"
    "@babel/plugin-proposal-private-property-in-object": "npm:7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions": "npm:^7.27.1"
    "@babel/plugin-syntax-import-attributes": "npm:^7.27.1"
    "@babel/plugin-syntax-unicode-sets-regex": "npm:^7.18.6"
    "@babel/plugin-transform-arrow-functions": "npm:^7.27.1"
    "@babel/plugin-transform-async-generator-functions": "npm:^7.27.1"
    "@babel/plugin-transform-async-to-generator": "npm:^7.27.1"
    "@babel/plugin-transform-block-scoped-functions": "npm:^7.27.1"
    "@babel/plugin-transform-block-scoping": "npm:^7.27.1"
    "@babel/plugin-transform-class-properties": "npm:^7.27.1"
    "@babel/plugin-transform-class-static-block": "npm:^7.27.1"
    "@babel/plugin-transform-classes": "npm:^7.27.1"
    "@babel/plugin-transform-computed-properties": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.27.1"
    "@babel/plugin-transform-dotall-regex": "npm:^7.27.1"
    "@babel/plugin-transform-duplicate-keys": "npm:^7.27.1"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "npm:^7.27.1"
    "@babel/plugin-transform-dynamic-import": "npm:^7.27.1"
    "@babel/plugin-transform-exponentiation-operator": "npm:^7.27.1"
    "@babel/plugin-transform-export-namespace-from": "npm:^7.27.1"
    "@babel/plugin-transform-for-of": "npm:^7.27.1"
    "@babel/plugin-transform-function-name": "npm:^7.27.1"
    "@babel/plugin-transform-json-strings": "npm:^7.27.1"
    "@babel/plugin-transform-literals": "npm:^7.27.1"
    "@babel/plugin-transform-logical-assignment-operators": "npm:^7.27.1"
    "@babel/plugin-transform-member-expression-literals": "npm:^7.27.1"
    "@babel/plugin-transform-modules-amd": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-modules-systemjs": "npm:^7.27.1"
    "@babel/plugin-transform-modules-umd": "npm:^7.27.1"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.27.1"
    "@babel/plugin-transform-new-target": "npm:^7.27.1"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.27.1"
    "@babel/plugin-transform-numeric-separator": "npm:^7.27.1"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.27.2"
    "@babel/plugin-transform-object-super": "npm:^7.27.1"
    "@babel/plugin-transform-optional-catch-binding": "npm:^7.27.1"
    "@babel/plugin-transform-optional-chaining": "npm:^7.27.1"
    "@babel/plugin-transform-parameters": "npm:^7.27.1"
    "@babel/plugin-transform-private-methods": "npm:^7.27.1"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.27.1"
    "@babel/plugin-transform-property-literals": "npm:^7.27.1"
    "@babel/plugin-transform-regenerator": "npm:^7.27.1"
    "@babel/plugin-transform-regexp-modifiers": "npm:^7.27.1"
    "@babel/plugin-transform-reserved-words": "npm:^7.27.1"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.27.1"
    "@babel/plugin-transform-spread": "npm:^7.27.1"
    "@babel/plugin-transform-sticky-regex": "npm:^7.27.1"
    "@babel/plugin-transform-template-literals": "npm:^7.27.1"
    "@babel/plugin-transform-typeof-symbol": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-escapes": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-property-regex": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-regex": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-sets-regex": "npm:^7.27.1"
    "@babel/preset-modules": "npm:0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2: "npm:^0.4.10"
    babel-plugin-polyfill-corejs3: "npm:^0.11.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.1"
    core-js-compat: "npm:^3.40.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/fd7ec310832a9ff26ed8d56bc0832cdbdb3a188e022050b74790796650649fb8373568af05b320b58b3ff922507979bad50ff95a4d504ab0081134480103504e
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@babel/types": "npm:^7.4.4"
    esutils: "npm:^2.0.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/9d02f70d7052446c5f3a4fb39e6b632695fb6801e46d31d7f7c5001f7c18d31d1ea8369212331ca7ad4e7877b73231f470b0d559162624128f1b80fe591409e6
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.11.2, @babel/runtime@npm:^7.12.5, @babel/runtime@npm:^7.18.3, @babel/runtime@npm:^7.21.0, @babel/runtime@npm:^7.23.9, @babel/runtime@npm:^7.26.0, @babel/runtime@npm:^7.27.1, @babel/runtime@npm:^7.5.5, @babel/runtime@npm:^7.8.7":
  version: 7.27.4
  resolution: "@babel/runtime@npm:7.27.4"
  checksum: 10c0/ca99e964179c31615e1352e058cc9024df7111c829631c90eec84caba6703cc32acc81503771847c306b3c70b815609fe82dde8682936debe295b0b283b2dc6e
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.1, @babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/traverse@npm:7.27.4"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/6de8aa2a0637a6ee6d205bf48b9e923928a02415771fdec60085ed754dcdf605e450bb3315c2552fa51c31a4662275b45d5ae4ad527ce55a7db9acebdbbbb8ed
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.4.4":
  version: 7.27.3
  resolution: "@babel/types@npm:7.27.3"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/bafdfc98e722a6b91a783b6f24388f478fd775f0c0652e92220e08be2cc33e02d42088542f1953ac5e5ece2ac052172b3dadedf12bec9aae57899e92fb9a9757
  languageName: node
  linkType: hard

"@beraborrowofficial/interface@workspace:.":
  version: 0.0.0-use.local
  resolution: "@beraborrowofficial/interface@workspace:."
  dependencies:
    "@Beraborrowofficial/sdk": "npm:2.3.5"
    "@emotion/react": "npm:11.13.3"
    "@emotion/styled": "npm:11.13.0"
    "@mui/icons-material": "npm:5.16.7"
    "@mui/material": "npm:5.16.7"
    "@reown/appkit": "npm:1.6.8"
    "@reown/appkit-adapter-wagmi": "npm:1.6.8"
    "@sentry/react": "npm:8.50.0"
    "@sentry/vite-plugin": "npm:3.0.0"
    "@tanstack/query-core": "npm:5.59.6"
    "@tanstack/react-query": "npm:5.59.20"
    "@types/node": "npm:22.7.6"
    "@types/react": "npm:18.3.1"
    "@types/react-dom": "npm:18.3.1"
    "@types/react-gtm-module": "npm:2.0.3"
    "@typescript-eslint/eslint-plugin": "npm:8.9.0"
    "@typescript-eslint/parser": "npm:8.9.0"
    "@typescript-eslint/utils": "npm:8.9.0"
    "@vitejs/plugin-react": "npm:4.3.2"
    "@vitejs/plugin-react-swc": "npm:3.7.1"
    "@wagmi/core": "npm:2.16.4"
    axios: "npm:1.8.1"
    dotenv: "npm:16.4.5"
    eslint: "npm:9.12.0"
    eslint-config-prettier: "npm:9.1.0"
    eslint-plugin-prettier: "npm:5.2.1"
    eslint-plugin-react-hooks: "npm:5.0.0"
    eslint-plugin-react-refresh: "npm:0.4.12"
    jotai: "npm:2.10.1"
    jotai-effect: "npm:1.0.3"
    jotai-scope: "npm:0.6.0"
    jotai-tanstack-query: "npm:0.8.8"
    posthog-js: "npm:1.170.1"
    prettier: "npm:3.3.3"
    react: "npm:18.3.1"
    react-dom: "npm:18.3.1"
    react-error-boundary: "npm:4.1.1"
    react-gtm-module: "npm:2.0.11"
    react-router-dom: "npm:6.27.0"
    react-swipeable: "npm:^7.0.2"
    sass: "npm: 1.80.1"
    typescript: "npm:5.6.3"
    viem: "npm:2.23.3"
    vite: "npm:5.4.9"
    vite-bundle-visualizer: "npm:1.2.1"
    vite-plugin-pwa: "npm:0.20.5"
    wagmi: "npm:2.14.11"
  languageName: unknown
  linkType: soft

"@coinbase/wallet-sdk@npm:4.3.0":
  version: 4.3.0
  resolution: "@coinbase/wallet-sdk@npm:4.3.0"
  dependencies:
    "@noble/hashes": "npm:^1.4.0"
    clsx: "npm:^1.2.1"
    eventemitter3: "npm:^5.0.1"
    preact: "npm:^10.24.2"
  checksum: 10c0/39e38ab6f84e34d8a61b9baf3fb69ad20b497d6844fe3f0cb1496e89bbb990066a6e8d68446f90054394eee840f3a452330ffbb015adabc34400f36a3ef03364
  languageName: node
  linkType: hard

"@ecies/ciphers@npm:^0.2.3":
  version: 0.2.3
  resolution: "@ecies/ciphers@npm:0.2.3"
  peerDependencies:
    "@noble/ciphers": ^1.0.0
  checksum: 10c0/a01bf75b1db89d34688d2784531bf4f8734e50f953ed8921383c04416a7e7eb8b5fb3ba6defddf55b46b0fb6722cec5c6462bdccbb64d31ef00468e9733a208e
  languageName: node
  linkType: hard

"@emotion/babel-plugin@npm:^11.12.0":
  version: 11.13.5
  resolution: "@emotion/babel-plugin@npm:11.13.5"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.16.7"
    "@babel/runtime": "npm:^7.18.3"
    "@emotion/hash": "npm:^0.9.2"
    "@emotion/memoize": "npm:^0.9.0"
    "@emotion/serialize": "npm:^1.3.3"
    babel-plugin-macros: "npm:^3.1.0"
    convert-source-map: "npm:^1.5.0"
    escape-string-regexp: "npm:^4.0.0"
    find-root: "npm:^1.1.0"
    source-map: "npm:^0.5.7"
    stylis: "npm:4.2.0"
  checksum: 10c0/8ccbfec7defd0e513cb8a1568fa179eac1e20c35fda18aed767f6c59ea7314363ebf2de3e9d2df66c8ad78928dc3dceeded84e6fa8059087cae5c280090aeeeb
  languageName: node
  linkType: hard

"@emotion/cache@npm:^11.13.0, @emotion/cache@npm:^11.13.5":
  version: 11.14.0
  resolution: "@emotion/cache@npm:11.14.0"
  dependencies:
    "@emotion/memoize": "npm:^0.9.0"
    "@emotion/sheet": "npm:^1.4.0"
    "@emotion/utils": "npm:^1.4.2"
    "@emotion/weak-memoize": "npm:^0.4.0"
    stylis: "npm:4.2.0"
  checksum: 10c0/3fa3e7a431ab6f8a47c67132a00ac8358f428c1b6c8421d4b20de9df7c18e95eec04a5a6ff5a68908f98d3280044f247b4965ac63df8302d2c94dba718769724
  languageName: node
  linkType: hard

"@emotion/hash@npm:^0.9.2":
  version: 0.9.2
  resolution: "@emotion/hash@npm:0.9.2"
  checksum: 10c0/0dc254561a3cc0a06a10bbce7f6a997883fd240c8c1928b93713f803a2e9153a257a488537012efe89dbe1246f2abfe2add62cdb3471a13d67137fcb808e81c2
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^1.3.0":
  version: 1.3.1
  resolution: "@emotion/is-prop-valid@npm:1.3.1"
  dependencies:
    "@emotion/memoize": "npm:^0.9.0"
  checksum: 10c0/123215540c816ff510737ec68dcc499c53ea4deb0bb6c2c27c03ed21046e2e69f6ad07a7a174d271c6cfcbcc9ea44e1763e0cf3875c92192f7689216174803cd
  languageName: node
  linkType: hard

"@emotion/memoize@npm:^0.9.0":
  version: 0.9.0
  resolution: "@emotion/memoize@npm:0.9.0"
  checksum: 10c0/13f474a9201c7f88b543e6ea42f55c04fb2fdc05e6c5a3108aced2f7e7aa7eda7794c56bba02985a46d8aaa914fcdde238727a98341a96e2aec750d372dadd15
  languageName: node
  linkType: hard

"@emotion/react@npm:11.13.3":
  version: 11.13.3
  resolution: "@emotion/react@npm:11.13.3"
  dependencies:
    "@babel/runtime": "npm:^7.18.3"
    "@emotion/babel-plugin": "npm:^11.12.0"
    "@emotion/cache": "npm:^11.13.0"
    "@emotion/serialize": "npm:^1.3.1"
    "@emotion/use-insertion-effect-with-fallbacks": "npm:^1.1.0"
    "@emotion/utils": "npm:^1.4.0"
    "@emotion/weak-memoize": "npm:^0.4.0"
    hoist-non-react-statics: "npm:^3.3.1"
  peerDependencies:
    react: ">=16.8.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/a55e770b9ea35de5d35db05a7ad40a4a3f442809fa8e4fabaf56da63ac9444f09aaf691c4e75a1455dc388991ab0c0ab4e253ce67c5836f27513e45ebd01b673
  languageName: node
  linkType: hard

"@emotion/serialize@npm:^1.3.0, @emotion/serialize@npm:^1.3.1, @emotion/serialize@npm:^1.3.3":
  version: 1.3.3
  resolution: "@emotion/serialize@npm:1.3.3"
  dependencies:
    "@emotion/hash": "npm:^0.9.2"
    "@emotion/memoize": "npm:^0.9.0"
    "@emotion/unitless": "npm:^0.10.0"
    "@emotion/utils": "npm:^1.4.2"
    csstype: "npm:^3.0.2"
  checksum: 10c0/b28cb7de59de382021de2b26c0c94ebbfb16967a1b969a56fdb6408465a8993df243bfbd66430badaa6800e1834724e84895f5a6a9d97d0d224de3d77852acb4
  languageName: node
  linkType: hard

"@emotion/sheet@npm:^1.4.0":
  version: 1.4.0
  resolution: "@emotion/sheet@npm:1.4.0"
  checksum: 10c0/3ca72d1650a07d2fbb7e382761b130b4a887dcd04e6574b2d51ce578791240150d7072a9bcb4161933abbcd1e38b243a6fb4464a7fe991d700c17aa66bb5acc7
  languageName: node
  linkType: hard

"@emotion/styled@npm:11.13.0":
  version: 11.13.0
  resolution: "@emotion/styled@npm:11.13.0"
  dependencies:
    "@babel/runtime": "npm:^7.18.3"
    "@emotion/babel-plugin": "npm:^11.12.0"
    "@emotion/is-prop-valid": "npm:^1.3.0"
    "@emotion/serialize": "npm:^1.3.0"
    "@emotion/use-insertion-effect-with-fallbacks": "npm:^1.1.0"
    "@emotion/utils": "npm:^1.4.0"
  peerDependencies:
    "@emotion/react": ^11.0.0-rc.0
    react: ">=16.8.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/5e2cc85c8a2f6e7bd012731cf0b6da3aef5906225e87e8d4a5c19da50572e24d9aaf92615aa36aa863f0fe6b62a121033356e1cad62617c48bfdaa2c3cf0d8a4
  languageName: node
  linkType: hard

"@emotion/unitless@npm:^0.10.0":
  version: 0.10.0
  resolution: "@emotion/unitless@npm:0.10.0"
  checksum: 10c0/150943192727b7650eb9a6851a98034ddb58a8b6958b37546080f794696141c3760966ac695ab9af97efe10178690987aee4791f9f0ad1ff76783cdca83c1d49
  languageName: node
  linkType: hard

"@emotion/use-insertion-effect-with-fallbacks@npm:^1.1.0":
  version: 1.2.0
  resolution: "@emotion/use-insertion-effect-with-fallbacks@npm:1.2.0"
  peerDependencies:
    react: ">=16.8.0"
  checksum: 10c0/074dbc92b96bdc09209871070076e3b0351b6b47efefa849a7d9c37ab142130767609ca1831da0055988974e3b895c1de7606e4c421fecaa27c3e56a2afd3b08
  languageName: node
  linkType: hard

"@emotion/utils@npm:^1.4.0, @emotion/utils@npm:^1.4.2":
  version: 1.4.2
  resolution: "@emotion/utils@npm:1.4.2"
  checksum: 10c0/7d0010bf60a2a8c1a033b6431469de4c80e47aeb8fd856a17c1d1f76bbc3a03161a34aeaa78803566e29681ca551e7bf9994b68e9c5f5c796159923e44f78d9a
  languageName: node
  linkType: hard

"@emotion/weak-memoize@npm:^0.4.0":
  version: 0.4.0
  resolution: "@emotion/weak-memoize@npm:0.4.0"
  checksum: 10c0/64376af11f1266042d03b3305c30b7502e6084868e33327e944b539091a472f089db307af69240f7188f8bc6b319276fd7b141a36613f1160d73d12a60f6ca1a
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/aix-ppc64@npm:0.21.5"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/android-arm64@npm:0.21.5"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/android-arm@npm:0.21.5"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/android-x64@npm:0.21.5"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/darwin-arm64@npm:0.21.5"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/darwin-x64@npm:0.21.5"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/freebsd-arm64@npm:0.21.5"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/freebsd-x64@npm:0.21.5"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-arm64@npm:0.21.5"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-arm@npm:0.21.5"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-ia32@npm:0.21.5"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-loong64@npm:0.21.5"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-mips64el@npm:0.21.5"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-ppc64@npm:0.21.5"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-riscv64@npm:0.21.5"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-s390x@npm:0.21.5"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-x64@npm:0.21.5"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/netbsd-x64@npm:0.21.5"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/openbsd-x64@npm:0.21.5"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/sunos-x64@npm:0.21.5"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/win32-arm64@npm:0.21.5"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/win32-ia32@npm:0.21.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/win32-x64@npm:0.21.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/c0f4f2bd73b7b7a9de74b716a664873d08ab71ab439e51befe77d61915af41a81ecec93b408778b3a7856185244c34c2c8ee28912072ec14def84ba2dec70adf
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.11.0":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10c0/a03d98c246bcb9109aec2c08e4d10c8d010256538dcb3f56610191607214523d4fb1b00aa81df830b6dffb74c5fa0be03642513a289c567949d3e550ca11cdf6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.18.0":
  version: 0.18.0
  resolution: "@eslint/config-array@npm:0.18.0"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.4"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10c0/0234aeb3e6b052ad2402a647d0b4f8a6aa71524bafe1adad0b8db1dfe94d7f5f26d67c80f79bb37ac61361a1d4b14bb8fb475efe501de37263cf55eabb79868f
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.13.0":
  version: 0.13.0
  resolution: "@eslint/core@npm:0.13.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10c0/ba724a7df7ed9dab387481f11d0d0f708180f40be93acce2c21dacca625c5867de3528760c42f1c457ccefe6a669d525ff87b779017eabc0d33479a36300797b
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.6.0":
  version: 0.6.0
  resolution: "@eslint/core@npm:0.6.0"
  checksum: 10c0/fffdb3046ad6420f8cb9204b6466fdd8632a9baeebdaf2a97d458a4eac0e16653ba50d82d61835d7d771f6ced0ec942ec482b2fbccc300e45f2cbf784537f240
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.1.0":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10c0/b0e63f3bc5cce4555f791a4e487bf999173fcf27c65e1ab6e7d63634d8a43b33c3693e79f192cbff486d7df1be8ebb2bd2edc6e70ddd486cbfa84a359a3e3b41
  languageName: node
  linkType: hard

"@eslint/js@npm:9.12.0":
  version: 9.12.0
  resolution: "@eslint/js@npm:9.12.0"
  checksum: 10c0/325650a59a1ce3d97c69441501ebaf415607248bacbe8c8ca35adc7cb73b524f592f266a75772f496b06f3239e3ee1996722a242148085f0ee5fb3dd7065897c
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.4":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: 10c0/b8cdb7edea5bc5f6a96173f8d768d3554a628327af536da2fc6967a93b040f2557114d98dbcdbf389d5a7b290985ad6a9ce5babc547f36fc1fde42e674d11a56
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.2.0":
  version: 0.2.8
  resolution: "@eslint/plugin-kit@npm:0.2.8"
  dependencies:
    "@eslint/core": "npm:^0.13.0"
    levn: "npm:^0.4.1"
  checksum: 10c0/554847c8f2b6bfe0e634f317fc43d0b54771eea0015c4f844f75915fdb9e6170c830c004291bad57db949d61771732e459f36ed059f45cf750af223f77357c5c
  languageName: node
  linkType: hard

"@ethereumjs/common@npm:^3.2.0":
  version: 3.2.0
  resolution: "@ethereumjs/common@npm:3.2.0"
  dependencies:
    "@ethereumjs/util": "npm:^8.1.0"
    crc-32: "npm:^1.2.0"
  checksum: 10c0/4e2256eb54cc544299f4d7ebc9daab7a3613c174de3981ea5ed84bd10c41a03d013d15b1abad292da62fd0c4b8ce5b220a258a25861ccffa32f2cc9a8a4b25d8
  languageName: node
  linkType: hard

"@ethereumjs/rlp@npm:^4.0.1":
  version: 4.0.1
  resolution: "@ethereumjs/rlp@npm:4.0.1"
  bin:
    rlp: bin/rlp
  checksum: 10c0/78379f288e9d88c584c2159c725c4a667a9742981d638bad760ed908263e0e36bdbd822c0a902003e0701195fd1cbde7adad621cd97fdfbf552c45e835ce022c
  languageName: node
  linkType: hard

"@ethereumjs/tx@npm:^4.1.2, @ethereumjs/tx@npm:^4.2.0":
  version: 4.2.0
  resolution: "@ethereumjs/tx@npm:4.2.0"
  dependencies:
    "@ethereumjs/common": "npm:^3.2.0"
    "@ethereumjs/rlp": "npm:^4.0.1"
    "@ethereumjs/util": "npm:^8.1.0"
    ethereum-cryptography: "npm:^2.0.0"
  checksum: 10c0/f168303edf5970673db06d2469a899632c64ba0cd5d24480e97683bd0e19cc22a7b0a7bc7db3a49760f09826d4c77bed89b65d65252daf54857dd3d97324fb9a
  languageName: node
  linkType: hard

"@ethereumjs/util@npm:^8.1.0":
  version: 8.1.0
  resolution: "@ethereumjs/util@npm:8.1.0"
  dependencies:
    "@ethereumjs/rlp": "npm:^4.0.1"
    ethereum-cryptography: "npm:^2.0.0"
    micro-ftch: "npm:^0.3.1"
  checksum: 10c0/4e6e0449236f66b53782bab3b387108f0ddc050835bfe1381c67a7c038fea27cb85ab38851d98b700957022f0acb6e455ca0c634249cfcce1a116bad76500160
  languageName: node
  linkType: hard

"@ethersproject/address@npm:^5.7.0":
  version: 5.8.0
  resolution: "@ethersproject/address@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/keccak256": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/rlp": "npm:^5.8.0"
  checksum: 10c0/8bac8a4b567c75c1abc00eeca08c200de1a2d5cf76d595dc04fa4d7bff9ffa5530b2cdfc5e8656cfa8f6fa046de54be47620a092fb429830a8ddde410b9d50bc
  languageName: node
  linkType: hard

"@ethersproject/bignumber@npm:^5.7.0, @ethersproject/bignumber@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/bignumber@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    bn.js: "npm:^5.2.1"
  checksum: 10c0/8e87fa96999d59d0ab4c814c79e3a8354d2ba914dfa78cf9ee688f53110473cec0df0db2aaf9d447e84ab2dbbfca39979abac4f2dac69fef4d080f4cc3e29613
  languageName: node
  linkType: hard

"@ethersproject/bytes@npm:^5.7.0, @ethersproject/bytes@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/bytes@npm:5.8.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10c0/47ef798f3ab43b95dc74097b2c92365c919308ecabc3e34d9f8bf7f886fa4b99837ba5cf4dc8921baaaafe6899982f96b0e723b3fc49132c061f83d1ca3fed8b
  languageName: node
  linkType: hard

"@ethersproject/constants@npm:^5.7.0":
  version: 5.8.0
  resolution: "@ethersproject/constants@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.8.0"
  checksum: 10c0/374b3c2c6da24f8fef62e2316eae96faa462826c0774ef588cd7313ae7ddac8eb1bb85a28dad80123148be2ba0821c217c14ecfc18e2e683c72adc734b6248c9
  languageName: node
  linkType: hard

"@ethersproject/keccak256@npm:^5.7.0, @ethersproject/keccak256@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/keccak256@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    js-sha3: "npm:0.8.0"
  checksum: 10c0/cd93ac6a5baf842313cde7de5e6e2c41feeea800db9e82955f96e7f3462d2ac6a6a29282b1c9e93b84ce7c91eec02347043c249fd037d6051214275bfc7fe99f
  languageName: node
  linkType: hard

"@ethersproject/logger@npm:^5.7.0, @ethersproject/logger@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/logger@npm:5.8.0"
  checksum: 10c0/7f39f33e8f254ee681d4778bb71ce3c5de248e1547666f85c43bfbc1c18996c49a31f969f056b66d23012f2420f2d39173107284bc41eb98d0482ace1d06403e
  languageName: node
  linkType: hard

"@ethersproject/properties@npm:^5.7.0, @ethersproject/properties@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/properties@npm:5.8.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10c0/20256d7eed65478a38dabdea4c3980c6591b7b75f8c45089722b032ceb0e1cd3dd6dd60c436cfe259337e6909c28d99528c172d06fc74bbd61be8eb9e68be2e6
  languageName: node
  linkType: hard

"@ethersproject/rlp@npm:^5.7.0, @ethersproject/rlp@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/rlp@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10c0/db742ec9c1566d6441242cc2c2ae34c1e5304d48e1fe62bc4e53b1791f219df211e330d2de331e0e4f74482664e205c2e4220e76138bd71f1ec07884e7f5221b
  languageName: node
  linkType: hard

"@ethersproject/signing-key@npm:^5.7.0":
  version: 5.8.0
  resolution: "@ethersproject/signing-key@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    bn.js: "npm:^5.2.1"
    elliptic: "npm:6.6.1"
    hash.js: "npm:1.1.7"
  checksum: 10c0/a7ff6cd344b0609737a496b6d5b902cf5528ed5a7ce2c0db5e7b69dc491d1810d1d0cd51dddf9dc74dd562ab4961d76e982f1750359b834c53c202e85e4c8502
  languageName: node
  linkType: hard

"@ethersproject/transactions@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/transactions@npm:5.7.0"
  dependencies:
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
    "@ethersproject/signing-key": "npm:^5.7.0"
  checksum: 10c0/aa4d51379caab35b9c468ed1692a23ae47ce0de121890b4f7093c982ee57e30bd2df0c743faed0f44936d7e59c55fffd80479f2c28ec6777b8de06bfb638c239
  languageName: node
  linkType: hard

"@graphql-typed-document-node/core@npm:^3.1.1":
  version: 3.2.0
  resolution: "@graphql-typed-document-node/core@npm:3.2.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10c0/94e9d75c1f178bbae8d874f5a9361708a3350c8def7eaeb6920f2c820e82403b7d4f55b3735856d68e145e86c85cbfe2adc444fdc25519cd51f108697e99346c
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10c0/aa4e0152171c07879b458d0e8a704b8c3a89a8c0541726c6b65b81e84fd8b7564b5d6c633feadc6598307d34564bd53294b533491424e8e313d7ab6c7bc5dc67
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.5":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10c0/8356359c9f60108ec204cbd249ecd0356667359b2524886b357617c4a7c3b6aace0fd5a369f63747b926a762a88f8a25bc066fa1778508d110195ce7686243e1
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10c0/909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0, @humanwhocodes/retry@npm:^0.3.1":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10c0/f0da1282dfb45e8120480b9e2e275e2ac9bbe1cf016d046fdad8e27cc1285c45bb9e711681237944445157b430093412b4446c1ab3fc4bb037861b5904101d3b
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10c0/6a4ecc713ed246ff8e5bdcc1ef7c49aaa93f7463d948ba5054dda18b02dcc6a055e2828c577bcceee058f302ce1fc95595713d44f5c45e43d459f88d267f2f04
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.4.15":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10c0/2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@lit-labs/ssr-dom-shim@npm:^1.0.0, @lit-labs/ssr-dom-shim@npm:^1.1.0, @lit-labs/ssr-dom-shim@npm:^1.2.0":
  version: 1.3.0
  resolution: "@lit-labs/ssr-dom-shim@npm:1.3.0"
  checksum: 10c0/743a9b295ef2f186712f08883da553c9990be291409615309c99aa4946cfe440a184e4213c790c24505c80beb86b9cfecf10b5fb30ce17c83698f8424f48678d
  languageName: node
  linkType: hard

"@lit/reactive-element@npm:^1.3.0, @lit/reactive-element@npm:^1.6.0":
  version: 1.6.3
  resolution: "@lit/reactive-element@npm:1.6.3"
  dependencies:
    "@lit-labs/ssr-dom-shim": "npm:^1.0.0"
  checksum: 10c0/10f1d25e24e32feb21c4c6f9e11d062901241602e12c4ecf746b3138f87fed4d8394194645514d5c1bfd5f33f3fd56ee8ef41344e2cb4413c40fe4961ec9d419
  languageName: node
  linkType: hard

"@lit/reactive-element@npm:^2.0.0, @lit/reactive-element@npm:^2.1.0":
  version: 2.1.0
  resolution: "@lit/reactive-element@npm:2.1.0"
  dependencies:
    "@lit-labs/ssr-dom-shim": "npm:^1.2.0"
  checksum: 10c0/3cd61c4e7cc8effeb2c246d5dada8fbe0a730e9e0dd488eb38c91a4f63b773e3b7f86f8384051677298e73de470c7ca6b5634df3ca190b307f8bb8e0d51bb91c
  languageName: node
  linkType: hard

"@metamask/eth-json-rpc-provider@npm:^1.0.0":
  version: 1.0.1
  resolution: "@metamask/eth-json-rpc-provider@npm:1.0.1"
  dependencies:
    "@metamask/json-rpc-engine": "npm:^7.0.0"
    "@metamask/safe-event-emitter": "npm:^3.0.0"
    "@metamask/utils": "npm:^5.0.1"
  checksum: 10c0/842f999d7a1c49b625fd863b453d076f393ac9090a1b9c7531aa24ec033e7e844c98a1c433ac02f4e66a62262d68c0d37c218dc724123da4eea1abcc12a63492
  languageName: node
  linkType: hard

"@metamask/json-rpc-engine@npm:^7.0.0":
  version: 7.3.3
  resolution: "@metamask/json-rpc-engine@npm:7.3.3"
  dependencies:
    "@metamask/rpc-errors": "npm:^6.2.1"
    "@metamask/safe-event-emitter": "npm:^3.0.0"
    "@metamask/utils": "npm:^8.3.0"
  checksum: 10c0/6c3b55de01593bc841de1bf4daac46cc307ed7c3b759fec12cbda582527962bb0d909b024e6c56251c0644379634cec24f3d37cbf3443430e148078db9baece1
  languageName: node
  linkType: hard

"@metamask/json-rpc-engine@npm:^8.0.1, @metamask/json-rpc-engine@npm:^8.0.2":
  version: 8.0.2
  resolution: "@metamask/json-rpc-engine@npm:8.0.2"
  dependencies:
    "@metamask/rpc-errors": "npm:^6.2.1"
    "@metamask/safe-event-emitter": "npm:^3.0.0"
    "@metamask/utils": "npm:^8.3.0"
  checksum: 10c0/57a584e713be98837b56b1985fc14020b74939af200c304e9dcde0a59b622f0d4b1fd07a9032dd3652b72ce330e47db8b9aa13402a443ad8c09667a4204c4c17
  languageName: node
  linkType: hard

"@metamask/json-rpc-middleware-stream@npm:^7.0.1":
  version: 7.0.2
  resolution: "@metamask/json-rpc-middleware-stream@npm:7.0.2"
  dependencies:
    "@metamask/json-rpc-engine": "npm:^8.0.2"
    "@metamask/safe-event-emitter": "npm:^3.0.0"
    "@metamask/utils": "npm:^8.3.0"
    readable-stream: "npm:^3.6.2"
  checksum: 10c0/5819e5cd1460046d309218110a76727d5b5b7b0fb379efd2e938e145905a359c2b6d4278d390760227ad5823e3f4bcaa001cbb5abeeeb014b08badbb1fa29f1f
  languageName: node
  linkType: hard

"@metamask/object-multiplex@npm:^2.0.0":
  version: 2.1.0
  resolution: "@metamask/object-multiplex@npm:2.1.0"
  dependencies:
    once: "npm:^1.4.0"
    readable-stream: "npm:^3.6.2"
  checksum: 10c0/5ccb9a627f6f4fac6c7123f3262fd68dd3ad2da16fccfdcd08954b7a930d0733fcbcaa58db289e5f9765f96efe0680cfe69de99495c109cf1d37f29ee870e703
  languageName: node
  linkType: hard

"@metamask/onboarding@npm:^1.0.1":
  version: 1.0.1
  resolution: "@metamask/onboarding@npm:1.0.1"
  dependencies:
    bowser: "npm:^2.9.0"
  checksum: 10c0/7a95eb47749217878a9e964c169a479a7532892d723eaade86c2e638e5ea5a54c697e0bbf68ab4f06dff5770639b9937da3375a3e8f958eae3f8da69f24031ed
  languageName: node
  linkType: hard

"@metamask/providers@npm:16.1.0":
  version: 16.1.0
  resolution: "@metamask/providers@npm:16.1.0"
  dependencies:
    "@metamask/json-rpc-engine": "npm:^8.0.1"
    "@metamask/json-rpc-middleware-stream": "npm:^7.0.1"
    "@metamask/object-multiplex": "npm:^2.0.0"
    "@metamask/rpc-errors": "npm:^6.2.1"
    "@metamask/safe-event-emitter": "npm:^3.1.1"
    "@metamask/utils": "npm:^8.3.0"
    detect-browser: "npm:^5.2.0"
    extension-port-stream: "npm:^3.0.0"
    fast-deep-equal: "npm:^3.1.3"
    is-stream: "npm:^2.0.0"
    readable-stream: "npm:^3.6.2"
    webextension-polyfill: "npm:^0.10.0"
  checksum: 10c0/ef0fe2cad0db6e2fd1c0b73894419e4dc153e1742e8b16e233164eaec941ef3d4859728e4a2e733e818b56093abd889fc96c7a75dccf9878cbdab45fd3b36e2c
  languageName: node
  linkType: hard

"@metamask/rpc-errors@npm:^6.2.1":
  version: 6.4.0
  resolution: "@metamask/rpc-errors@npm:6.4.0"
  dependencies:
    "@metamask/utils": "npm:^9.0.0"
    fast-safe-stringify: "npm:^2.0.6"
  checksum: 10c0/eeca3a2316c97f2f0e8922fc3a0625a704f76a1dd3b0cc78ed54dcc3c4ca7f5c3f5c90880e74c748f09f075cc21f176f3498421ad75a5c323535e454a7896c21
  languageName: node
  linkType: hard

"@metamask/safe-event-emitter@npm:^2.0.0":
  version: 2.0.0
  resolution: "@metamask/safe-event-emitter@npm:2.0.0"
  checksum: 10c0/a86b91f909834dc14de7eadd38b22d4975f6529001d265cd0f5c894351f69f39447f1ef41b690b9849c86dd2a25a39515ef5f316545d36aea7b3fc50ee930933
  languageName: node
  linkType: hard

"@metamask/safe-event-emitter@npm:^3.0.0, @metamask/safe-event-emitter@npm:^3.1.1":
  version: 3.1.2
  resolution: "@metamask/safe-event-emitter@npm:3.1.2"
  checksum: 10c0/ca59aada3e79bae9609d3be2569c25c22f9b1df05821a2fbebfbcc835a811347e814eabf9dbbddf342fef9dcadac903492a49fdc0c9bcac0aff980c0d38daab2
  languageName: node
  linkType: hard

"@metamask/sdk-communication-layer@npm:0.32.0":
  version: 0.32.0
  resolution: "@metamask/sdk-communication-layer@npm:0.32.0"
  dependencies:
    bufferutil: "npm:^4.0.8"
    date-fns: "npm:^2.29.3"
    debug: "npm:^4.3.4"
    utf-8-validate: "npm:^5.0.2"
    uuid: "npm:^8.3.2"
  peerDependencies:
    cross-fetch: ^4.0.0
    eciesjs: "*"
    eventemitter2: ^6.4.9
    readable-stream: ^3.6.2
    socket.io-client: ^4.5.1
  checksum: 10c0/f13defc09ff46839e4d5429deb327306b5c0c49378fdb2ccb3acaa89a61cc44e7f7e49bbeb56be88dd25c529c902dac0860091829893e730335094194c906ce4
  languageName: node
  linkType: hard

"@metamask/sdk-install-modal-web@npm:0.32.0":
  version: 0.32.0
  resolution: "@metamask/sdk-install-modal-web@npm:0.32.0"
  dependencies:
    "@paulmillr/qr": "npm:^0.2.1"
  checksum: 10c0/e28b12924bc26f15c62d7489e07a0201a02105b6d52babbca30d86c8488ec8e0e13fceb088aa76713eea4022957cf507053d06dea6cb35c091dcb3345e2fa435
  languageName: node
  linkType: hard

"@metamask/sdk@npm:0.32.0":
  version: 0.32.0
  resolution: "@metamask/sdk@npm:0.32.0"
  dependencies:
    "@babel/runtime": "npm:^7.26.0"
    "@metamask/onboarding": "npm:^1.0.1"
    "@metamask/providers": "npm:16.1.0"
    "@metamask/sdk-communication-layer": "npm:0.32.0"
    "@metamask/sdk-install-modal-web": "npm:0.32.0"
    "@paulmillr/qr": "npm:^0.2.1"
    bowser: "npm:^2.9.0"
    cross-fetch: "npm:^4.0.0"
    debug: "npm:^4.3.4"
    eciesjs: "npm:^0.4.11"
    eth-rpc-errors: "npm:^4.0.3"
    eventemitter2: "npm:^6.4.9"
    obj-multiplex: "npm:^1.0.0"
    pump: "npm:^3.0.0"
    readable-stream: "npm:^3.6.2"
    socket.io-client: "npm:^4.5.1"
    tslib: "npm:^2.6.0"
    util: "npm:^0.12.4"
    uuid: "npm:^8.3.2"
  checksum: 10c0/7038015fd6b516d17325b383650ec97ffe2ade3d9959c8af8d568a8742ea55bbb8a54c0ae4cbe256074d4e791c60888100ce8173624b805febdf4a707db29204
  languageName: node
  linkType: hard

"@metamask/superstruct@npm:^3.0.0, @metamask/superstruct@npm:^3.1.0":
  version: 3.2.1
  resolution: "@metamask/superstruct@npm:3.2.1"
  checksum: 10c0/117322ce1a6cd54345a06b5cf1b1e4725f5ae034eaf24127abab6af2b6c24c0ce6cc9ddca164756a5f2e9559e5aaa0ac6965c4fbf42253d0908152b4502522d9
  languageName: node
  linkType: hard

"@metamask/utils@npm:^5.0.1":
  version: 5.0.2
  resolution: "@metamask/utils@npm:5.0.2"
  dependencies:
    "@ethereumjs/tx": "npm:^4.1.2"
    "@types/debug": "npm:^4.1.7"
    debug: "npm:^4.3.4"
    semver: "npm:^7.3.8"
    superstruct: "npm:^1.0.3"
  checksum: 10c0/fa82d856362c3da9fa80262ffde776eeafb0e6f23c7e6d6401f824513a8b2641aa115c2eaae61c391950cdf4a56c57a10082c73a00a1840f8159d709380c4809
  languageName: node
  linkType: hard

"@metamask/utils@npm:^8.3.0":
  version: 8.5.0
  resolution: "@metamask/utils@npm:8.5.0"
  dependencies:
    "@ethereumjs/tx": "npm:^4.2.0"
    "@metamask/superstruct": "npm:^3.0.0"
    "@noble/hashes": "npm:^1.3.1"
    "@scure/base": "npm:^1.1.3"
    "@types/debug": "npm:^4.1.7"
    debug: "npm:^4.3.4"
    pony-cause: "npm:^2.1.10"
    semver: "npm:^7.5.4"
    uuid: "npm:^9.0.1"
  checksum: 10c0/037f463e3c6a512b21d057224b1e9645de5a86ba15c0d2140acd43fb7316bfdd9f2635ffdb98e970278eb4e0dd81080bb1855d08dff6a95280590379ad73a01b
  languageName: node
  linkType: hard

"@metamask/utils@npm:^9.0.0":
  version: 9.3.0
  resolution: "@metamask/utils@npm:9.3.0"
  dependencies:
    "@ethereumjs/tx": "npm:^4.2.0"
    "@metamask/superstruct": "npm:^3.1.0"
    "@noble/hashes": "npm:^1.3.1"
    "@scure/base": "npm:^1.1.3"
    "@types/debug": "npm:^4.1.7"
    debug: "npm:^4.3.4"
    pony-cause: "npm:^2.1.10"
    semver: "npm:^7.5.4"
    uuid: "npm:^9.0.1"
  checksum: 10c0/8298d6f58d1cf8f5b3e057a4fdf364466f6d7d860e2950713690c5b4be3edb48d952f20982af66f83753596dc2bcd5b23cb53721b389ca134117b20ef0ebf04f
  languageName: node
  linkType: hard

"@motionone/animation@npm:^10.15.1, @motionone/animation@npm:^10.18.0":
  version: 10.18.0
  resolution: "@motionone/animation@npm:10.18.0"
  dependencies:
    "@motionone/easing": "npm:^10.18.0"
    "@motionone/types": "npm:^10.17.1"
    "@motionone/utils": "npm:^10.18.0"
    tslib: "npm:^2.3.1"
  checksum: 10c0/83c01ab8ecf5fae221e5012116c4c49d4473ba88ba22197e1d8c1e39364c5c6b9c5271e57ae716fd21f92314d15c63788c48d0a30872ee8d72337e1d98b46834
  languageName: node
  linkType: hard

"@motionone/dom@npm:^10.16.2, @motionone/dom@npm:^10.16.4":
  version: 10.18.0
  resolution: "@motionone/dom@npm:10.18.0"
  dependencies:
    "@motionone/animation": "npm:^10.18.0"
    "@motionone/generators": "npm:^10.18.0"
    "@motionone/types": "npm:^10.17.1"
    "@motionone/utils": "npm:^10.18.0"
    hey-listen: "npm:^1.0.8"
    tslib: "npm:^2.3.1"
  checksum: 10c0/3bd4b1015e88464c9effc170c23bc63bbc910cbb9ca84986ec19ca82e0e13335e63a1f0d12e265fbe93616fe864fc2aec4e952d51e07932894e148de6fac2111
  languageName: node
  linkType: hard

"@motionone/easing@npm:^10.18.0":
  version: 10.18.0
  resolution: "@motionone/easing@npm:10.18.0"
  dependencies:
    "@motionone/utils": "npm:^10.18.0"
    tslib: "npm:^2.3.1"
  checksum: 10c0/0adf9b7086b0f569d28886890cc0725a489285f2debfcaf27c1c15dfef5736c9f4207cfda14c71b3275f8163777320cb7ff48ad263c7f4ccd31e12a5afc1a952
  languageName: node
  linkType: hard

"@motionone/generators@npm:^10.18.0":
  version: 10.18.0
  resolution: "@motionone/generators@npm:10.18.0"
  dependencies:
    "@motionone/types": "npm:^10.17.1"
    "@motionone/utils": "npm:^10.18.0"
    tslib: "npm:^2.3.1"
  checksum: 10c0/7ed7dda5ac58cd3e8dd347b5539d242d96e02ee16fef921c8d14295a806e6bc429a15291461ec078977bd5f6162677225addd707ca79f808e65bc3599c45c0e9
  languageName: node
  linkType: hard

"@motionone/svelte@npm:^10.16.2":
  version: 10.16.4
  resolution: "@motionone/svelte@npm:10.16.4"
  dependencies:
    "@motionone/dom": "npm:^10.16.4"
    tslib: "npm:^2.3.1"
  checksum: 10c0/a3f91d3ac5617ac8a2847abc0c8fad417cdc2cd9d814d60f7de2c909e4beeaf834b45a4288c8af6d26f62958a6c69714313b37ea6cd5aa2a9d1ad5198ec5881f
  languageName: node
  linkType: hard

"@motionone/types@npm:^10.15.1, @motionone/types@npm:^10.17.1":
  version: 10.17.1
  resolution: "@motionone/types@npm:10.17.1"
  checksum: 10c0/f7b16cd4f0feda0beac10173afa6de7384722f9f24767f78b7aa90f15b8a89d584073a64387b015a8e015a962fa4b47a8ce23621f47708a08676b12bb0d43bbb
  languageName: node
  linkType: hard

"@motionone/utils@npm:^10.15.1, @motionone/utils@npm:^10.18.0":
  version: 10.18.0
  resolution: "@motionone/utils@npm:10.18.0"
  dependencies:
    "@motionone/types": "npm:^10.17.1"
    hey-listen: "npm:^1.0.8"
    tslib: "npm:^2.3.1"
  checksum: 10c0/db57dbb6a131fab36dc1eb4e1f3a4575ca97563221663adce54c138de1e1a9eaf4a4a51ddf99fdab0341112159e0190b35cdeddfdbd08ba3ad1e35886a5324bb
  languageName: node
  linkType: hard

"@motionone/vue@npm:^10.16.2":
  version: 10.16.4
  resolution: "@motionone/vue@npm:10.16.4"
  dependencies:
    "@motionone/dom": "npm:^10.16.4"
    tslib: "npm:^2.3.1"
  checksum: 10c0/0f3096c0956848cb67c4926e65b7034d854cf704573a277679713c5a8045347c3c043f50adad0c84ee3e88c046d35ab88ec4380e5acd729f81900381e0b1fd0d
  languageName: node
  linkType: hard

"@mui/core-downloads-tracker@npm:^5.16.7":
  version: 5.17.1
  resolution: "@mui/core-downloads-tracker@npm:5.17.1"
  checksum: 10c0/c36641e274a27cdef8a75218021a5ebef0ce588cd0aec004802d56e8d59ac9d8603b8b4840df480de40ce2c3af8898d49929a8564ed5a784ff2b3100dce31366
  languageName: node
  linkType: hard

"@mui/icons-material@npm:5.16.7":
  version: 5.16.7
  resolution: "@mui/icons-material@npm:5.16.7"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
  peerDependencies:
    "@mui/material": ^5.0.0
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/49bab1754334798acaf93187d27200cf90d7c50b6a019531594aeac9e5ced9168281fec70bb040792dc86c8bc0d3bf9a876f22cfbf86ad07941ca6bc6c564921
  languageName: node
  linkType: hard

"@mui/material@npm:5.16.7":
  version: 5.16.7
  resolution: "@mui/material@npm:5.16.7"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@mui/core-downloads-tracker": "npm:^5.16.7"
    "@mui/system": "npm:^5.16.7"
    "@mui/types": "npm:^7.2.15"
    "@mui/utils": "npm:^5.16.6"
    "@popperjs/core": "npm:^2.11.8"
    "@types/react-transition-group": "npm:^4.4.10"
    clsx: "npm:^2.1.0"
    csstype: "npm:^3.1.3"
    prop-types: "npm:^15.8.1"
    react-is: "npm:^18.3.1"
    react-transition-group: "npm:^4.4.5"
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: 10c0/b11419c1a77835413471f9352586fed65fb5de19c6737e121669da0484c441c7dd9939aa73fdad779482c30efaa694fb9fdcf18dcf418af07881e60eaff92b4f
  languageName: node
  linkType: hard

"@mui/private-theming@npm:^5.17.1":
  version: 5.17.1
  resolution: "@mui/private-theming@npm:5.17.1"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@mui/utils": "npm:^5.17.1"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/53015616e3497d5fe1b068c49a5f3ebc81160fe4a08a05f1cf61acfe64522a2e6bb3d13110797a5619ceb46dce291dc13b5031cd4bcf4dbf42800b73f98640dd
  languageName: node
  linkType: hard

"@mui/styled-engine@npm:^5.16.14":
  version: 5.16.14
  resolution: "@mui/styled-engine@npm:5.16.14"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@emotion/cache": "npm:^11.13.5"
    csstype: "npm:^3.1.3"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@emotion/react": ^11.4.1
    "@emotion/styled": ^11.3.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
  checksum: 10c0/cd512faea4ad3ff5a9b315e136a518223ea3e4e34462fe70c56d1f166c46bee0a885ed982773d75c1d56ead62b95989cc5907601e8d65bfa75494b3f3288c2ad
  languageName: node
  linkType: hard

"@mui/system@npm:^5.16.7":
  version: 5.17.1
  resolution: "@mui/system@npm:5.17.1"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@mui/private-theming": "npm:^5.17.1"
    "@mui/styled-engine": "npm:^5.16.14"
    "@mui/types": "npm:~7.2.15"
    "@mui/utils": "npm:^5.17.1"
    clsx: "npm:^2.1.0"
    csstype: "npm:^3.1.3"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: 10c0/ab74424e536164b720126ddd31ff0ceea4fb51d72f8d18f9be5621b33f8bbdf7fa8c96f8d1d2c4544ddacbaa84df1a197667f10cbe8915e00df103930e40f56e
  languageName: node
  linkType: hard

"@mui/types@npm:^7.2.15":
  version: 7.4.3
  resolution: "@mui/types@npm:7.4.3"
  dependencies:
    "@babel/runtime": "npm:^7.27.1"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/8078ed0c63211377af4cf244e0b8a94d15748253139a330f6c7b983b755a57fa89bdba5d8b9ca4c30944b1567115eab3cbb9b9869c14489b0ad3249e858c9fa1
  languageName: node
  linkType: hard

"@mui/types@npm:~7.2.15":
  version: 7.2.24
  resolution: "@mui/types@npm:7.2.24"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/7756339cae70e9b684c4311924e4e3882f908552b69c434b4d13faf2f5908ce72fe889a31890257c5ad42a085207be7c1661981dfc683293e90ac6dfac3759d0
  languageName: node
  linkType: hard

"@mui/utils@npm:^5.16.6, @mui/utils@npm:^5.17.1":
  version: 5.17.1
  resolution: "@mui/utils@npm:5.17.1"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@mui/types": "npm:~7.2.15"
    "@types/prop-types": "npm:^15.7.12"
    clsx: "npm:^2.1.1"
    prop-types: "npm:^15.8.1"
    react-is: "npm:^19.0.0"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/0a2b033f85b67ad5cab86c5b9e2341cc1a1fa931eaad5489b21281e0bfe9054061817a8de50bcf3363f17f5a3f0c44400950099f36e5039e735c1b5f3b30cf2b
  languageName: node
  linkType: hard

"@noble/ciphers@npm:1.2.1":
  version: 1.2.1
  resolution: "@noble/ciphers@npm:1.2.1"
  checksum: 10c0/00e414da686ddba00f6e9bed124abb698bfe076658d40cc4e3b67b51fc7582fc3c2a7002ef33f154ea8cbf45e7783cfd48325cf3885d577ce8c0ae8bdd648069
  languageName: node
  linkType: hard

"@noble/ciphers@npm:^1.3.0":
  version: 1.3.0
  resolution: "@noble/ciphers@npm:1.3.0"
  checksum: 10c0/3ba6da645ce45e2f35e3b2e5c87ceba86b21dfa62b9466ede9edfb397f8116dae284f06652c0cd81d99445a2262b606632e868103d54ecc99fd946ae1af8cd37
  languageName: node
  linkType: hard

"@noble/curves@npm:1.4.2, @noble/curves@npm:~1.4.0":
  version: 1.4.2
  resolution: "@noble/curves@npm:1.4.2"
  dependencies:
    "@noble/hashes": "npm:1.4.0"
  checksum: 10c0/65620c895b15d46e8087939db6657b46a1a15cd4e0e4de5cd84b97a0dfe0af85f33a431bb21ac88267e3dc508618245d4cb564213959d66a84d690fe18a63419
  languageName: node
  linkType: hard

"@noble/curves@npm:1.8.0":
  version: 1.8.0
  resolution: "@noble/curves@npm:1.8.0"
  dependencies:
    "@noble/hashes": "npm:1.7.0"
  checksum: 10c0/3ebb1795f3f7d74c879bc6262a3444061585a2cab90b7b637dc57d931063dd0c95be858a4c2389e932651825dbc461c215dbcf43984a232de3bd6b2d326ba555
  languageName: node
  linkType: hard

"@noble/curves@npm:1.8.1":
  version: 1.8.1
  resolution: "@noble/curves@npm:1.8.1"
  dependencies:
    "@noble/hashes": "npm:1.7.1"
  checksum: 10c0/84902c7af93338373a95d833f77981113e81c48d4bec78f22f63f1f7fdd893bc1d3d7a3ee78f01b9a8ad3dec812a1232866bf2ccbeb2b1560492e5e7d690ab1f
  languageName: node
  linkType: hard

"@noble/curves@npm:1.9.1, @noble/curves@npm:^1.6.0, @noble/curves@npm:^1.9.1, @noble/curves@npm:~1.9.0":
  version: 1.9.1
  resolution: "@noble/curves@npm:1.9.1"
  dependencies:
    "@noble/hashes": "npm:1.8.0"
  checksum: 10c0/39c84dbfecdca80cfde2ecea4b06ef2ec1255a4df40158d22491d1400057a283f57b2b26c8b1331006e6e061db791f31d47764961c239437032e2f45e8888c1e
  languageName: node
  linkType: hard

"@noble/curves@npm:~1.8.1":
  version: 1.8.2
  resolution: "@noble/curves@npm:1.8.2"
  dependencies:
    "@noble/hashes": "npm:1.7.2"
  checksum: 10c0/e7ef119b114681d6b7530b29a21f9bbea6fa6973bc369167da2158d05054cc6e6dbfb636ba89fad7707abacc150de30188b33192f94513911b24bdb87af50bbd
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.4.0, @noble/hashes@npm:~1.4.0":
  version: 1.4.0
  resolution: "@noble/hashes@npm:1.4.0"
  checksum: 10c0/8c3f005ee72e7b8f9cff756dfae1241485187254e3f743873e22073d63906863df5d4f13d441b7530ea614b7a093f0d889309f28b59850f33b66cb26a779a4a5
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.7.0":
  version: 1.7.0
  resolution: "@noble/hashes@npm:1.7.0"
  checksum: 10c0/1ef0c985ebdb5a1bd921ea6d959c90ba826af3ae05b40b459a703e2a5e9b259f190c6e92d6220fb3800e2385521e4159e238415ad3f6b79c52f91dd615e491dc
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.7.1":
  version: 1.7.1
  resolution: "@noble/hashes@npm:1.7.1"
  checksum: 10c0/2f8ec0338ccc92b576a0f5c16ab9c017a3a494062f1fbb569ae641c5e7eab32072f9081acaa96b5048c0898f972916c818ea63cbedda707886a4b5ffcfbf94e3
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.7.2, @noble/hashes@npm:~1.7.1":
  version: 1.7.2
  resolution: "@noble/hashes@npm:1.7.2"
  checksum: 10c0/b1411eab3c0b6691d847e9394fe7f1fcd45eeb037547c8f97e7d03c5068a499b4aef188e8e717eee67389dca4fee17d69d7e0f58af6c092567b0b76359b114b2
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.8.0, @noble/hashes@npm:^1.3.1, @noble/hashes@npm:^1.4.0, @noble/hashes@npm:^1.5.0, @noble/hashes@npm:^1.8.0, @noble/hashes@npm:~1.8.0":
  version: 1.8.0
  resolution: "@noble/hashes@npm:1.8.0"
  checksum: 10c0/06a0b52c81a6fa7f04d67762e08b2c476a00285858150caeaaff4037356dd5e119f45b2a530f638b77a5eeca013168ec1b655db41bae3236cb2e9d511484fc77
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@parcel/watcher-android-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-android-arm64@npm:2.5.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-darwin-arm64@npm:2.5.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-darwin-x64@npm:2.5.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-freebsd-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-freebsd-x64@npm:2.5.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm-glibc@npm:2.5.1"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm-musl@npm:2.5.1"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm64-glibc@npm:2.5.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm64-musl@npm:2.5.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-x64-glibc@npm:2.5.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-x64-musl@npm:2.5.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-win32-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-arm64@npm:2.5.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-win32-ia32@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-ia32@npm:2.5.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@parcel/watcher-win32-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-x64@npm:2.5.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher@npm:^2.4.1":
  version: 2.5.1
  resolution: "@parcel/watcher@npm:2.5.1"
  dependencies:
    "@parcel/watcher-android-arm64": "npm:2.5.1"
    "@parcel/watcher-darwin-arm64": "npm:2.5.1"
    "@parcel/watcher-darwin-x64": "npm:2.5.1"
    "@parcel/watcher-freebsd-x64": "npm:2.5.1"
    "@parcel/watcher-linux-arm-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-arm-musl": "npm:2.5.1"
    "@parcel/watcher-linux-arm64-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-arm64-musl": "npm:2.5.1"
    "@parcel/watcher-linux-x64-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-x64-musl": "npm:2.5.1"
    "@parcel/watcher-win32-arm64": "npm:2.5.1"
    "@parcel/watcher-win32-ia32": "npm:2.5.1"
    "@parcel/watcher-win32-x64": "npm:2.5.1"
    detect-libc: "npm:^1.0.3"
    is-glob: "npm:^4.0.3"
    micromatch: "npm:^4.0.5"
    node-addon-api: "npm:^7.0.0"
    node-gyp: "npm:latest"
  dependenciesMeta:
    "@parcel/watcher-android-arm64":
      optional: true
    "@parcel/watcher-darwin-arm64":
      optional: true
    "@parcel/watcher-darwin-x64":
      optional: true
    "@parcel/watcher-freebsd-x64":
      optional: true
    "@parcel/watcher-linux-arm-glibc":
      optional: true
    "@parcel/watcher-linux-arm-musl":
      optional: true
    "@parcel/watcher-linux-arm64-glibc":
      optional: true
    "@parcel/watcher-linux-arm64-musl":
      optional: true
    "@parcel/watcher-linux-x64-glibc":
      optional: true
    "@parcel/watcher-linux-x64-musl":
      optional: true
    "@parcel/watcher-win32-arm64":
      optional: true
    "@parcel/watcher-win32-ia32":
      optional: true
    "@parcel/watcher-win32-x64":
      optional: true
  checksum: 10c0/8f35073d0c0b34a63d4c8d2213482f0ebc6a25de7b2cdd415d19cb929964a793cb285b68d1d50bfb732b070b3c82a2fdb4eb9c250eab709a1cd9d63345455a82
  languageName: node
  linkType: hard

"@paulmillr/qr@npm:^0.2.1":
  version: 0.2.1
  resolution: "@paulmillr/qr@npm:0.2.1"
  checksum: 10c0/6ca1171a7e870d948084dc0a51ca61fab4a2537c888e116cac2f3c622974a911559b212d0d0a79d57c69a8b396eb0168e3a6e46715f9881df241d78cdf081ef4
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.1.0":
  version: 0.1.2
  resolution: "@pkgr/core@npm:0.1.2"
  checksum: 10c0/fd4acc154c8f1b5c544b6dd152b7ce68f6cbb8b92e9abf2e5d756d6e95052d08d0d693a668dea67af1386d62635b50adfe463cce03c5620402b468498cc7592f
  languageName: node
  linkType: hard

"@popperjs/core@npm:^2.11.8":
  version: 2.11.8
  resolution: "@popperjs/core@npm:2.11.8"
  checksum: 10c0/4681e682abc006d25eb380d0cf3efc7557043f53b6aea7a5057d0d1e7df849a00e281cd8ea79c902a35a414d7919621fc2ba293ecec05f413598e0b23d5a1e63
  languageName: node
  linkType: hard

"@remix-run/router@npm:1.20.0":
  version: 1.20.0
  resolution: "@remix-run/router@npm:1.20.0"
  checksum: 10c0/2e017dea530717a6e93a16d478714c4c9165313a1c48e39172ec609bc20324ca6362e8ee2243602df6343644c9268d82a3f50f154d3bb8a17dddde6c37be6e83
  languageName: node
  linkType: hard

"@reown/appkit-adapter-wagmi@npm:1.6.8":
  version: 1.6.8
  resolution: "@reown/appkit-adapter-wagmi@npm:1.6.8"
  dependencies:
    "@reown/appkit": "npm:1.6.8"
    "@reown/appkit-common": "npm:1.6.8"
    "@reown/appkit-core": "npm:1.6.8"
    "@reown/appkit-polyfills": "npm:1.6.8"
    "@reown/appkit-scaffold-ui": "npm:1.6.8"
    "@reown/appkit-ui": "npm:1.6.8"
    "@reown/appkit-utils": "npm:1.6.8"
    "@reown/appkit-wallet": "npm:1.6.8"
    "@wagmi/connectors": "npm:>=5.7"
    "@walletconnect/universal-provider": "npm:2.18.0"
    "@walletconnect/utils": "npm:2.18.0"
    valtio: "npm:1.13.2"
  peerDependencies:
    "@wagmi/core": ">=2.16.4"
    viem: ">=2.23.0"
    wagmi: ">=2.14.11"
  dependenciesMeta:
    "@wagmi/connectors":
      optional: true
  checksum: 10c0/5be8ce09a67368eb715a345453d303d2957986edf4b702dad1ba78c4d50a64e97f9ad5b54468f5dcc66685c354a2c0ec36f713db6a1a8f4c0a8bf682c960b401
  languageName: node
  linkType: hard

"@reown/appkit-common@npm:1.6.8":
  version: 1.6.8
  resolution: "@reown/appkit-common@npm:1.6.8"
  dependencies:
    big.js: "npm:6.2.2"
    dayjs: "npm:1.11.10"
    viem: "npm:>=2.23.0"
  checksum: 10c0/64a4b8322d4e69fa7e71742b50f713ce290f7c8b582be5d1a2f3dea028633b1653f392b15fd1f03413eee3804632292c681a22932fda47d627eb224033cea7a6
  languageName: node
  linkType: hard

"@reown/appkit-common@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-common@npm:1.7.8"
  dependencies:
    big.js: "npm:6.2.2"
    dayjs: "npm:1.11.13"
    viem: "npm:>=2.29.0"
  checksum: 10c0/4b494f81c30596dc0de8fd7bac08111c47b8acaa0c85a5665f262f411f6055256f2ea8301c8deefa63a083288fc13e9e955f9855c5686a5d66ae536d2b5f7969
  languageName: node
  linkType: hard

"@reown/appkit-controllers@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-controllers@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": "npm:1.7.8"
    "@reown/appkit-wallet": "npm:1.7.8"
    "@walletconnect/universal-provider": "npm:2.21.0"
    valtio: "npm:1.13.2"
    viem: "npm:>=2.29.0"
  checksum: 10c0/4119c2db6d99a9e306a0155a3b80d8ae7d1515ecb7d67467beae86fca3ccaa23c78a57b3eceffd82775c265e4e635933a5bdd325276b617b8990dc7aebadcc1a
  languageName: node
  linkType: hard

"@reown/appkit-core@npm:1.6.8":
  version: 1.6.8
  resolution: "@reown/appkit-core@npm:1.6.8"
  dependencies:
    "@reown/appkit-common": "npm:1.6.8"
    "@reown/appkit-wallet": "npm:1.6.8"
    "@walletconnect/universal-provider": "npm:2.18.0"
    valtio: "npm:1.13.2"
    viem: "npm:>=2.23"
  checksum: 10c0/f2d4f206d7f702849cbed975e206d5774a45d5f0cf42f9fc4043eff31e364639657b8fd81b636291530b8d5b28246db4483a41d65e20ebb1c0651a12c94f2429
  languageName: node
  linkType: hard

"@reown/appkit-pay@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-pay@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": "npm:1.7.8"
    "@reown/appkit-controllers": "npm:1.7.8"
    "@reown/appkit-ui": "npm:1.7.8"
    "@reown/appkit-utils": "npm:1.7.8"
    lit: "npm:3.3.0"
    valtio: "npm:1.13.2"
  checksum: 10c0/bf53114d58641bead5947cb4acd39bdf202c002afe034a50b063a43ac8da2a680494d2178286942fc729392cf6e2eb94b06e113fe6dde5c5276925e807c6c7ab
  languageName: node
  linkType: hard

"@reown/appkit-polyfills@npm:1.6.8":
  version: 1.6.8
  resolution: "@reown/appkit-polyfills@npm:1.6.8"
  dependencies:
    buffer: "npm:6.0.3"
  checksum: 10c0/944b59082884d65923b4741312642e24ecf7fc66df9f5ee66b3d34504221418d642045654b3e004dea14bf47abd65806a2f6504a22f741d50ff34e381c8f4b57
  languageName: node
  linkType: hard

"@reown/appkit-polyfills@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-polyfills@npm:1.7.8"
  dependencies:
    buffer: "npm:6.0.3"
  checksum: 10c0/4f1cfe738af5faf59476d1aba3bf4f6d83116bb32c8824d00fe0378453bb52220333b66603f25c5b87ed82f43319d81dfbdabda2028f6fd6f2fd4fcfb6bee203
  languageName: node
  linkType: hard

"@reown/appkit-scaffold-ui@npm:1.6.8":
  version: 1.6.8
  resolution: "@reown/appkit-scaffold-ui@npm:1.6.8"
  dependencies:
    "@reown/appkit-common": "npm:1.6.8"
    "@reown/appkit-core": "npm:1.6.8"
    "@reown/appkit-ui": "npm:1.6.8"
    "@reown/appkit-utils": "npm:1.6.8"
    "@reown/appkit-wallet": "npm:1.6.8"
    lit: "npm:3.1.0"
  checksum: 10c0/85b64b039ef5274c90cc12325bde01aae1e70056cf9181ad776703a3a82b02e04cc35b8a77f28ab25d80f1ce4aa768729a48bc67f81fd0426c15e2a6ec244579
  languageName: node
  linkType: hard

"@reown/appkit-scaffold-ui@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-scaffold-ui@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": "npm:1.7.8"
    "@reown/appkit-controllers": "npm:1.7.8"
    "@reown/appkit-ui": "npm:1.7.8"
    "@reown/appkit-utils": "npm:1.7.8"
    "@reown/appkit-wallet": "npm:1.7.8"
    lit: "npm:3.3.0"
  checksum: 10c0/d07a27925da7c1e893f32d286c939f71149865a5d068ef1884b4c7cd3deb45327aca73fea9dabcde5f89aa355ceac0fb5b9ed952ccbb0e56a0c3464c07ed543e
  languageName: node
  linkType: hard

"@reown/appkit-ui@npm:1.6.8":
  version: 1.6.8
  resolution: "@reown/appkit-ui@npm:1.6.8"
  dependencies:
    lit: "npm:3.1.0"
    qrcode: "npm:1.5.3"
  checksum: 10c0/dfcf77981279e61b8419a3073bb87c64f5b8c711bfe99861fecbad823fc8b6f402e4730dbb0658af99fd8cd13bde15f5deff052728f366e3c4c6460f1d036fb0
  languageName: node
  linkType: hard

"@reown/appkit-ui@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-ui@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": "npm:1.7.8"
    "@reown/appkit-controllers": "npm:1.7.8"
    "@reown/appkit-wallet": "npm:1.7.8"
    lit: "npm:3.3.0"
    qrcode: "npm:1.5.3"
  checksum: 10c0/f4b0df3124d419d355358f56fd54163a12802aaebfc9d75b7396ac3a2c443747216791f590c0de27bef764140a76319774d905e89e018f9fdf26dd24ba16f232
  languageName: node
  linkType: hard

"@reown/appkit-utils@npm:1.6.8":
  version: 1.6.8
  resolution: "@reown/appkit-utils@npm:1.6.8"
  dependencies:
    "@reown/appkit-common": "npm:1.6.8"
    "@reown/appkit-core": "npm:1.6.8"
    "@reown/appkit-polyfills": "npm:1.6.8"
    "@reown/appkit-wallet": "npm:1.6.8"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/universal-provider": "npm:2.18.0"
    valtio: "npm:1.13.2"
    viem: "npm:>=2.23.0"
  peerDependencies:
    valtio: 1.13.2
  checksum: 10c0/3f7bedc4d558a6ed80d57ef75e574bdbf223b4e09d7a1dd1305b9f0241e6d9321666a5603bd1fc08287bac76009acd27cad8f0f13190f26b7c164425e34f02f8
  languageName: node
  linkType: hard

"@reown/appkit-utils@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-utils@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": "npm:1.7.8"
    "@reown/appkit-controllers": "npm:1.7.8"
    "@reown/appkit-polyfills": "npm:1.7.8"
    "@reown/appkit-wallet": "npm:1.7.8"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/universal-provider": "npm:2.21.0"
    valtio: "npm:1.13.2"
    viem: "npm:>=2.29.0"
  peerDependencies:
    valtio: 1.13.2
  checksum: 10c0/93054dddaf90823674568639c2a1119fba07a7e5461f277e8f8ae27bd7018a7aa023ddd648b0aaa80b2cdb46e8ad5bfc2f99c8fdf1996e2d7d7c5aff1c856427
  languageName: node
  linkType: hard

"@reown/appkit-wallet@npm:1.6.8":
  version: 1.6.8
  resolution: "@reown/appkit-wallet@npm:1.6.8"
  dependencies:
    "@reown/appkit-common": "npm:1.6.8"
    "@reown/appkit-polyfills": "npm:1.6.8"
    "@walletconnect/logger": "npm:2.1.2"
    zod: "npm:3.22.4"
  checksum: 10c0/de63fd403cf769207f9f8ac3f5a39042e4304ffc2b3ffc8b4b527e1a65db6d5cdd2d207169740f2f239929e3b7238ac94f9a9a70bd41c42efca3b9676dabc2c2
  languageName: node
  linkType: hard

"@reown/appkit-wallet@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-wallet@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": "npm:1.7.8"
    "@reown/appkit-polyfills": "npm:1.7.8"
    "@walletconnect/logger": "npm:2.1.2"
    zod: "npm:3.22.4"
  checksum: 10c0/8021cc184dac24ad9828340924deb8b7142025c585710a634804968b6163899a4061f96ba00f614de2287a82f53562de4f053799164413acb5694aa0bcd35783
  languageName: node
  linkType: hard

"@reown/appkit@npm:1.6.8":
  version: 1.6.8
  resolution: "@reown/appkit@npm:1.6.8"
  dependencies:
    "@reown/appkit-common": "npm:1.6.8"
    "@reown/appkit-core": "npm:1.6.8"
    "@reown/appkit-polyfills": "npm:1.6.8"
    "@reown/appkit-scaffold-ui": "npm:1.6.8"
    "@reown/appkit-ui": "npm:1.6.8"
    "@reown/appkit-utils": "npm:1.6.8"
    "@reown/appkit-wallet": "npm:1.6.8"
    "@walletconnect/types": "npm:2.18.0"
    "@walletconnect/universal-provider": "npm:2.18.0"
    "@walletconnect/utils": "npm:2.18.0"
    bs58: "npm:6.0.0"
    valtio: "npm:1.13.2"
    viem: "npm:>=2.23.0"
  checksum: 10c0/4627fbea11dbd6c60da463fd7c79d63443a9e329a81e33683f568801f0f9fb825ea703d585de860beace835cfa18d75c44a1e03f1facaccb907c63e3cd7922ee
  languageName: node
  linkType: hard

"@reown/appkit@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": "npm:1.7.8"
    "@reown/appkit-controllers": "npm:1.7.8"
    "@reown/appkit-pay": "npm:1.7.8"
    "@reown/appkit-polyfills": "npm:1.7.8"
    "@reown/appkit-scaffold-ui": "npm:1.7.8"
    "@reown/appkit-ui": "npm:1.7.8"
    "@reown/appkit-utils": "npm:1.7.8"
    "@reown/appkit-wallet": "npm:1.7.8"
    "@walletconnect/types": "npm:2.21.0"
    "@walletconnect/universal-provider": "npm:2.21.0"
    bs58: "npm:6.0.0"
    valtio: "npm:1.13.2"
    viem: "npm:>=2.29.0"
  checksum: 10c0/d5c8ba49f9eb4e2446219d9f84a603a11cb940379f5f37e46da507e94bcd2657a9fc232248b1692f3fa6c6105dd582442da0c745d13c3cbb89860db8a0bb39c6
  languageName: node
  linkType: hard

"@rollup/plugin-babel@npm:^5.2.0":
  version: 5.3.1
  resolution: "@rollup/plugin-babel@npm:5.3.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.10.4"
    "@rollup/pluginutils": "npm:^3.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0
    "@types/babel__core": ^7.1.9
    rollup: ^1.20.0||^2.0.0
  peerDependenciesMeta:
    "@types/babel__core":
      optional: true
  checksum: 10c0/2766134dd5567c0d4fd6909d1f511ce9bf3bd9d727e1bc5ffdd6097a3606faca324107ae8e0961839ee4dbb45e5e579ae601efe472fc0a271259aea79920cafa
  languageName: node
  linkType: hard

"@rollup/plugin-node-resolve@npm:^15.2.3":
  version: 15.3.1
  resolution: "@rollup/plugin-node-resolve@npm:15.3.1"
  dependencies:
    "@rollup/pluginutils": "npm:^5.0.1"
    "@types/resolve": "npm:1.20.2"
    deepmerge: "npm:^4.2.2"
    is-module: "npm:^1.0.0"
    resolve: "npm:^1.22.1"
  peerDependencies:
    rollup: ^2.78.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/ecf3abe890fc98ad665fdbfb1ea245253e0d1f2bc6d9f4e8f496f212c76a2ce7cd4b9bc0abd21e6bcaa16f72d1c67cc6b322ea12a6ec68e8a8834df8242a5ecd
  languageName: node
  linkType: hard

"@rollup/plugin-replace@npm:^2.4.1":
  version: 2.4.2
  resolution: "@rollup/plugin-replace@npm:2.4.2"
  dependencies:
    "@rollup/pluginutils": "npm:^3.1.0"
    magic-string: "npm:^0.25.7"
  peerDependencies:
    rollup: ^1.20.0 || ^2.0.0
  checksum: 10c0/ea3d27291c791661638b91809d0247dde1ee71be0b16fa7060078c2700db3669eada2c3978ea979b917b29ebe06f3fddc8797feae554da966264a22142b5771a
  languageName: node
  linkType: hard

"@rollup/plugin-terser@npm:^0.4.3":
  version: 0.4.4
  resolution: "@rollup/plugin-terser@npm:0.4.4"
  dependencies:
    serialize-javascript: "npm:^6.0.1"
    smob: "npm:^1.0.0"
    terser: "npm:^5.17.4"
  peerDependencies:
    rollup: ^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/b9cb6c8f02ac1c1344019e9fb854321b74f880efebc41b6bdd84f18331fce0f4a2aadcdb481042245cd3f409b429ac363af71f9efec4a2024731d67d32af36ee
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^3.1.0":
  version: 3.1.0
  resolution: "@rollup/pluginutils@npm:3.1.0"
  dependencies:
    "@types/estree": "npm:0.0.39"
    estree-walker: "npm:^1.0.1"
    picomatch: "npm:^2.2.2"
  peerDependencies:
    rollup: ^1.20.0||^2.0.0
  checksum: 10c0/7151753160d15ba2b259461a6c25b3932150994ea52dba8fd3144f634c7647c2e56733d986e2c15de67c4d96a9ee7d6278efa6d2e626a7169898fd64adc0f90c
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^5.0.1":
  version: 5.1.4
  resolution: "@rollup/pluginutils@npm:5.1.4"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    estree-walker: "npm:^2.0.2"
    picomatch: "npm:^4.0.2"
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/6d58fbc6f1024eb4b087bc9bf59a1d655a8056a60c0b4021d3beaeec3f0743503f52467fd89d2cf0e7eccf2831feb40a05ad541a17637ea21ba10b21c2004deb
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.41.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-android-arm64@npm:4.41.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-darwin-arm64@npm:4.41.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-darwin-x64@npm:4.41.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.41.1"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-freebsd-x64@npm:4.41.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.41.1"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.41.1"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.41.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.41.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.41.1"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.41.1"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.41.1"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-musl@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-riscv64-musl@npm:4.41.1"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.41.1"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.41.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.41.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.41.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.41.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.41.1":
  version: 4.41.1
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.41.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@safe-global/safe-apps-provider@npm:0.18.5":
  version: 0.18.5
  resolution: "@safe-global/safe-apps-provider@npm:0.18.5"
  dependencies:
    "@safe-global/safe-apps-sdk": "npm:^9.1.0"
    events: "npm:^3.3.0"
  checksum: 10c0/5699b4abd63d1042aca299cddb466ebf79b0e6709a22b277c7320343edce36e50f4d5356c4eda4497e1c2f4d6a92b14b29c7aefe0cf673f5614752f5ff6fbac5
  languageName: node
  linkType: hard

"@safe-global/safe-apps-provider@npm:0.18.6":
  version: 0.18.6
  resolution: "@safe-global/safe-apps-provider@npm:0.18.6"
  dependencies:
    "@safe-global/safe-apps-sdk": "npm:^9.1.0"
    events: "npm:^3.3.0"
  checksum: 10c0/e8567a97e43740bfe21b6f8a7759cabed2bc96eb50fd494118cab13a20f14797fbca3e02d18f0395054fcfbf2fd86315e5433d5b26f73bed6c3c86881087716c
  languageName: node
  linkType: hard

"@safe-global/safe-apps-sdk@npm:9.1.0, @safe-global/safe-apps-sdk@npm:^9.1.0":
  version: 9.1.0
  resolution: "@safe-global/safe-apps-sdk@npm:9.1.0"
  dependencies:
    "@safe-global/safe-gateway-typescript-sdk": "npm:^3.5.3"
    viem: "npm:^2.1.1"
  checksum: 10c0/13af12122a6b1388e7960a76c3c421ea5ed97197646cd1f720b9fc9364fad0cc8f21cda23773130cd6bf57935a36f9e93f5222569cc80382709430b5cad26fda
  languageName: node
  linkType: hard

"@safe-global/safe-gateway-typescript-sdk@npm:^3.5.3":
  version: 3.23.1
  resolution: "@safe-global/safe-gateway-typescript-sdk@npm:3.23.1"
  checksum: 10c0/609cfdf71e73cb55c2596e6fa6212c73ff96aa5c857d2e5a98db193853638a8b8b2165c8cb8334a9060885acb2e688b33675bab000445bd3ec99bc6245cf8d61
  languageName: node
  linkType: hard

"@scure/base@npm:^1.1.3, @scure/base@npm:~1.2.2, @scure/base@npm:~1.2.4, @scure/base@npm:~1.2.5":
  version: 1.2.6
  resolution: "@scure/base@npm:1.2.6"
  checksum: 10c0/49bd5293371c4e062cb6ba689c8fe3ea3981b7bb9c000400dc4eafa29f56814cdcdd27c04311c2fec34de26bc373c593a1d6ca6d754398a488d587943b7c128a
  languageName: node
  linkType: hard

"@scure/base@npm:~1.1.6":
  version: 1.1.9
  resolution: "@scure/base@npm:1.1.9"
  checksum: 10c0/77a06b9a2db8144d22d9bf198338893d77367c51b58c72b99df990c0a11f7cadd066d4102abb15e3ca6798d1529e3765f55c4355742465e49aed7a0c01fe76e8
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.4.0":
  version: 1.4.0
  resolution: "@scure/bip32@npm:1.4.0"
  dependencies:
    "@noble/curves": "npm:~1.4.0"
    "@noble/hashes": "npm:~1.4.0"
    "@scure/base": "npm:~1.1.6"
  checksum: 10c0/6849690d49a3bf1d0ffde9452eb16ab83478c1bc0da7b914f873e2930cd5acf972ee81320e3df1963eb247cf57e76d2d975b5f97093d37c0e3f7326581bf41bd
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.6.2":
  version: 1.6.2
  resolution: "@scure/bip32@npm:1.6.2"
  dependencies:
    "@noble/curves": "npm:~1.8.1"
    "@noble/hashes": "npm:~1.7.1"
    "@scure/base": "npm:~1.2.2"
  checksum: 10c0/a0abd62d1fe34b4d90b84feb25fa064ad452fd51be9fd7ea3dcd376059c0e8d08d4fe454099030f43fb91a1bee85cd955f093f221bbc522178919f779fbe565c
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.7.0, @scure/bip32@npm:^1.5.0":
  version: 1.7.0
  resolution: "@scure/bip32@npm:1.7.0"
  dependencies:
    "@noble/curves": "npm:~1.9.0"
    "@noble/hashes": "npm:~1.8.0"
    "@scure/base": "npm:~1.2.5"
  checksum: 10c0/e3d4c1f207df16abcd79babcdb74d36f89bdafc90bf02218a5140cc5cba25821d80d42957c6705f35210cc5769714ea9501d4ae34732cdd1c26c9ff182a219f7
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.3.0":
  version: 1.3.0
  resolution: "@scure/bip39@npm:1.3.0"
  dependencies:
    "@noble/hashes": "npm:~1.4.0"
    "@scure/base": "npm:~1.1.6"
  checksum: 10c0/1ae1545a7384a4d9e33e12d9e9f8824f29b0279eb175b0f0657c0a782c217920054f9a1d28eb316a417dfc6c4e0b700d6fbdc6da160670107426d52fcbe017a8
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.5.4":
  version: 1.5.4
  resolution: "@scure/bip39@npm:1.5.4"
  dependencies:
    "@noble/hashes": "npm:~1.7.1"
    "@scure/base": "npm:~1.2.4"
  checksum: 10c0/0b398b8335b624c16dfb0d81b0e79f80f098bb98e327f1d68ace56636e0c56cc09a240ed3ba9c1187573758242ade7000260d65c15d3a6bcd95ac9cb284b450a
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.6.0, @scure/bip39@npm:^1.4.0":
  version: 1.6.0
  resolution: "@scure/bip39@npm:1.6.0"
  dependencies:
    "@noble/hashes": "npm:~1.8.0"
    "@scure/base": "npm:~1.2.5"
  checksum: 10c0/73a54b5566a50a3f8348a5cfd74d2092efeefc485efbed83d7a7374ffd9a75defddf446e8e5ea0385e4adb49a94b8ae83c5bad3e16333af400e932f7da3aaff8
  languageName: node
  linkType: hard

"@sentry-internal/browser-utils@npm:8.50.0":
  version: 8.50.0
  resolution: "@sentry-internal/browser-utils@npm:8.50.0"
  dependencies:
    "@sentry/core": "npm:8.50.0"
  checksum: 10c0/9424ed585d30922cc4c5c9287bfd8c1796b3dfeb884013dbe95197879c9e9edbac011328b67472c20b646e874465c86e3e3428b5b8cf0538567ed78a9a8750cb
  languageName: node
  linkType: hard

"@sentry-internal/feedback@npm:8.50.0":
  version: 8.50.0
  resolution: "@sentry-internal/feedback@npm:8.50.0"
  dependencies:
    "@sentry/core": "npm:8.50.0"
  checksum: 10c0/7321d749be14a121f57c36edc2a73f5b70b67faf9050ad0ff026acba02a0df2b297c784b737a024b10f82fb30ec1c36ff2799d26d450c8f92867af69b32ec0e2
  languageName: node
  linkType: hard

"@sentry-internal/replay-canvas@npm:8.50.0":
  version: 8.50.0
  resolution: "@sentry-internal/replay-canvas@npm:8.50.0"
  dependencies:
    "@sentry-internal/replay": "npm:8.50.0"
    "@sentry/core": "npm:8.50.0"
  checksum: 10c0/35d7780d183eee4206804893025ab74cbbbeee5f92afee9e2339036217a22f1da45aafab6bf2554f2624c74783029b74fd2a0f575a429183eed382a37c1123ec
  languageName: node
  linkType: hard

"@sentry-internal/replay@npm:8.50.0":
  version: 8.50.0
  resolution: "@sentry-internal/replay@npm:8.50.0"
  dependencies:
    "@sentry-internal/browser-utils": "npm:8.50.0"
    "@sentry/core": "npm:8.50.0"
  checksum: 10c0/f9164bf48bc0d6a5a9688b2086505a740249804d8aeaa7a5c80db085414b9d0ace6d5551471bd8a3dc780461e00ff55b5cd1b7b753a22e88d7ca49277b774a02
  languageName: node
  linkType: hard

"@sentry/babel-plugin-component-annotate@npm:3.0.0":
  version: 3.0.0
  resolution: "@sentry/babel-plugin-component-annotate@npm:3.0.0"
  checksum: 10c0/7f4f9439c19e0c23c853721d8f488c2d13fa44cb2da776d77ab9dbc281e8bee7eeea5b605d1894a34da9a54a0a2c745fa24a75147a246571f89fb34276bbafd3
  languageName: node
  linkType: hard

"@sentry/browser@npm:8.50.0":
  version: 8.50.0
  resolution: "@sentry/browser@npm:8.50.0"
  dependencies:
    "@sentry-internal/browser-utils": "npm:8.50.0"
    "@sentry-internal/feedback": "npm:8.50.0"
    "@sentry-internal/replay": "npm:8.50.0"
    "@sentry-internal/replay-canvas": "npm:8.50.0"
    "@sentry/core": "npm:8.50.0"
  checksum: 10c0/d35258ce6ba7ac7fcb80c6797975daa3aade55ed08fe27929e36aba7724db45d9d4979e40f2a6238d528526d710aa95732bb0b0e55df9f5126ca23b15a8510d1
  languageName: node
  linkType: hard

"@sentry/bundler-plugin-core@npm:3.0.0":
  version: 3.0.0
  resolution: "@sentry/bundler-plugin-core@npm:3.0.0"
  dependencies:
    "@babel/core": "npm:^7.18.5"
    "@sentry/babel-plugin-component-annotate": "npm:3.0.0"
    "@sentry/cli": "npm:2.39.1"
    dotenv: "npm:^16.3.1"
    find-up: "npm:^5.0.0"
    glob: "npm:^9.3.2"
    magic-string: "npm:0.30.8"
    unplugin: "npm:1.0.1"
  checksum: 10c0/6b9648fc910dc0385a2de3835436044f22240c0dc24cc24e03af7ff021cdac3a8c7609b6f97efb23382c574e99bf486277b6760a850d148d47dc39a2b482ba68
  languageName: node
  linkType: hard

"@sentry/cli-darwin@npm:2.39.1":
  version: 2.39.1
  resolution: "@sentry/cli-darwin@npm:2.39.1"
  conditions: os=darwin
  languageName: node
  linkType: hard

"@sentry/cli-linux-arm64@npm:2.39.1":
  version: 2.39.1
  resolution: "@sentry/cli-linux-arm64@npm:2.39.1"
  conditions: (os=linux | os=freebsd) & cpu=arm64
  languageName: node
  linkType: hard

"@sentry/cli-linux-arm@npm:2.39.1":
  version: 2.39.1
  resolution: "@sentry/cli-linux-arm@npm:2.39.1"
  conditions: (os=linux | os=freebsd) & cpu=arm
  languageName: node
  linkType: hard

"@sentry/cli-linux-i686@npm:2.39.1":
  version: 2.39.1
  resolution: "@sentry/cli-linux-i686@npm:2.39.1"
  conditions: (os=linux | os=freebsd) & (cpu=x86 | cpu=ia32)
  languageName: node
  linkType: hard

"@sentry/cli-linux-x64@npm:2.39.1":
  version: 2.39.1
  resolution: "@sentry/cli-linux-x64@npm:2.39.1"
  conditions: (os=linux | os=freebsd) & cpu=x64
  languageName: node
  linkType: hard

"@sentry/cli-win32-i686@npm:2.39.1":
  version: 2.39.1
  resolution: "@sentry/cli-win32-i686@npm:2.39.1"
  conditions: os=win32 & (cpu=x86 | cpu=ia32)
  languageName: node
  linkType: hard

"@sentry/cli-win32-x64@npm:2.39.1":
  version: 2.39.1
  resolution: "@sentry/cli-win32-x64@npm:2.39.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@sentry/cli@npm:2.39.1":
  version: 2.39.1
  resolution: "@sentry/cli@npm:2.39.1"
  dependencies:
    "@sentry/cli-darwin": "npm:2.39.1"
    "@sentry/cli-linux-arm": "npm:2.39.1"
    "@sentry/cli-linux-arm64": "npm:2.39.1"
    "@sentry/cli-linux-i686": "npm:2.39.1"
    "@sentry/cli-linux-x64": "npm:2.39.1"
    "@sentry/cli-win32-i686": "npm:2.39.1"
    "@sentry/cli-win32-x64": "npm:2.39.1"
    https-proxy-agent: "npm:^5.0.0"
    node-fetch: "npm:^2.6.7"
    progress: "npm:^2.0.3"
    proxy-from-env: "npm:^1.1.0"
    which: "npm:^2.0.2"
  dependenciesMeta:
    "@sentry/cli-darwin":
      optional: true
    "@sentry/cli-linux-arm":
      optional: true
    "@sentry/cli-linux-arm64":
      optional: true
    "@sentry/cli-linux-i686":
      optional: true
    "@sentry/cli-linux-x64":
      optional: true
    "@sentry/cli-win32-i686":
      optional: true
    "@sentry/cli-win32-x64":
      optional: true
  bin:
    sentry-cli: bin/sentry-cli
  checksum: 10c0/fcef4ac58a268f1c4242360ac74fef319a7e629d4168b1cf22c67c7d4e5d1fdf3613c29a949c570cb0375a42a289a13ba495545b60cda037f9783254abd7b4c8
  languageName: node
  linkType: hard

"@sentry/core@npm:8.50.0":
  version: 8.50.0
  resolution: "@sentry/core@npm:8.50.0"
  checksum: 10c0/cdd32cc75bfc73d660d3bc3919cbc9d03220a3414951a47a39af96450248107eb2037efdffe15275a6e6e195ee5a0f40814baeaf9905b41efcec83719f459823
  languageName: node
  linkType: hard

"@sentry/react@npm:8.50.0":
  version: 8.50.0
  resolution: "@sentry/react@npm:8.50.0"
  dependencies:
    "@sentry/browser": "npm:8.50.0"
    "@sentry/core": "npm:8.50.0"
    hoist-non-react-statics: "npm:^3.3.2"
  peerDependencies:
    react: ^16.14.0 || 17.x || 18.x || 19.x
  checksum: 10c0/7d052537b37c6ec4d200abe9940a8ad2294697b1bfa49a6d6a1d9c28bfb0ed4da961a585950bb683a85b0789ae57c249b5b8a7d6c81f105ac81fd79bba2c08ce
  languageName: node
  linkType: hard

"@sentry/vite-plugin@npm:3.0.0":
  version: 3.0.0
  resolution: "@sentry/vite-plugin@npm:3.0.0"
  dependencies:
    "@sentry/bundler-plugin-core": "npm:3.0.0"
    unplugin: "npm:1.0.1"
  checksum: 10c0/c7841ba49ac67aeaa9b8112bd29c64f30b1ff4a2d098724a08a78999b494e7316a6388856a53b026addda0add1d197ac4b354a8081eb4b0e21865d60c58bad96
  languageName: node
  linkType: hard

"@socket.io/component-emitter@npm:~3.1.0":
  version: 3.1.2
  resolution: "@socket.io/component-emitter@npm:3.1.2"
  checksum: 10c0/c4242bad66f67e6f7b712733d25b43cbb9e19a595c8701c3ad99cbeb5901555f78b095e24852f862fffb43e96f1d8552e62def885ca82ae1bb05da3668fd87d7
  languageName: node
  linkType: hard

"@stablelib/aead@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/aead@npm:1.0.1"
  checksum: 10c0/8ec16795a6f94264f93514661e024c5b0434d75000ea133923c57f0db30eab8ddc74fa35f5ff1ae4886803a8b92e169b828512c9e6bc02c818688d0f5b9f5aef
  languageName: node
  linkType: hard

"@stablelib/binary@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/binary@npm:1.0.1"
  dependencies:
    "@stablelib/int": "npm:^1.0.1"
  checksum: 10c0/154cb558d8b7c20ca5dc2e38abca2a3716ce36429bf1b9c298939cea0929766ed954feb8a9c59245ac64c923d5d3466bb7d99f281debd3a9d561e1279b11cd35
  languageName: node
  linkType: hard

"@stablelib/bytes@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/bytes@npm:1.0.1"
  checksum: 10c0/ee99bb15dac2f4ae1aa4e7a571e76483617a441feff422442f293993bc8b2c7ef021285c98f91a043bc05fb70502457799e28ffd43a8564a17913ee5ce889237
  languageName: node
  linkType: hard

"@stablelib/chacha20poly1305@npm:1.0.1":
  version: 1.0.1
  resolution: "@stablelib/chacha20poly1305@npm:1.0.1"
  dependencies:
    "@stablelib/aead": "npm:^1.0.1"
    "@stablelib/binary": "npm:^1.0.1"
    "@stablelib/chacha": "npm:^1.0.1"
    "@stablelib/constant-time": "npm:^1.0.1"
    "@stablelib/poly1305": "npm:^1.0.1"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/fe202aa8aface111c72bc9ec099f9c36a7b1470eda9834e436bb228618a704929f095b937f04e867fe4d5c40216ff089cbfeb2eeb092ab33af39ff333eb2c1e6
  languageName: node
  linkType: hard

"@stablelib/chacha@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/chacha@npm:1.0.1"
  dependencies:
    "@stablelib/binary": "npm:^1.0.1"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/4d70b484ae89416d21504024f977f5517bf16b344b10fb98382c9e3e52fe8ca77ac65f5d6a358d8b152f2c9ffed101a1eb15ed1707cdf906e1b6624db78d2d16
  languageName: node
  linkType: hard

"@stablelib/constant-time@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/constant-time@npm:1.0.1"
  checksum: 10c0/694a282441215735a1fdfa3d06db5a28ba92423890967a154514ef28e0d0298ce7b6a2bc65ebc4273573d6669a6b601d330614747aa2e69078c1d523d7069e12
  languageName: node
  linkType: hard

"@stablelib/ed25519@npm:^1.0.2":
  version: 1.0.3
  resolution: "@stablelib/ed25519@npm:1.0.3"
  dependencies:
    "@stablelib/random": "npm:^1.0.2"
    "@stablelib/sha512": "npm:^1.0.1"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/b4a05e3c24dabd8a9e0b5bd72dea761bfb4b5c66404308e9f0529ef898e75d6f588234920762d5372cb920d9d47811250160109f02d04b6eed53835fb6916eb9
  languageName: node
  linkType: hard

"@stablelib/hash@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/hash@npm:1.0.1"
  checksum: 10c0/58b5572a4067820b77a1606ed2d4a6dc4068c5475f68ba0918860a5f45adf60b33024a0cea9532dcd8b7345c53b3c9636a23723f5f8ae83e0c3648f91fb5b5cc
  languageName: node
  linkType: hard

"@stablelib/hkdf@npm:1.0.1":
  version: 1.0.1
  resolution: "@stablelib/hkdf@npm:1.0.1"
  dependencies:
    "@stablelib/hash": "npm:^1.0.1"
    "@stablelib/hmac": "npm:^1.0.1"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/722d30e36afa8029fda2a9e8c65ad753deff92a234e708820f9fd39309d2494e1c035a4185f29ae8d7fbf8a74862b27128c66a1fb4bd7a792bd300190080dbe9
  languageName: node
  linkType: hard

"@stablelib/hmac@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/hmac@npm:1.0.1"
  dependencies:
    "@stablelib/constant-time": "npm:^1.0.1"
    "@stablelib/hash": "npm:^1.0.1"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/a111d5e687966b62c81f7dbd390f13582b027edee9bd39df6474a6472e5ad89d705e735af32bae2c9280a205806649f54b5ff8c4e8c8a7b484083a35b257e9e6
  languageName: node
  linkType: hard

"@stablelib/int@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/int@npm:1.0.1"
  checksum: 10c0/e1a6a7792fc2146d65de56e4ef42e8bc385dd5157eff27019b84476f564a1a6c43413235ed0e9f7c9bb8907dbdab24679467aeb10f44c92e6b944bcd864a7ee0
  languageName: node
  linkType: hard

"@stablelib/keyagreement@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/keyagreement@npm:1.0.1"
  dependencies:
    "@stablelib/bytes": "npm:^1.0.1"
  checksum: 10c0/18c9e09772a058edee265c65992ec37abe4ab5118171958972e28f3bbac7f2a0afa6aaf152ec1d785452477bdab5366b3f5b750e8982ae9ad090f5fa2e5269ba
  languageName: node
  linkType: hard

"@stablelib/poly1305@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/poly1305@npm:1.0.1"
  dependencies:
    "@stablelib/constant-time": "npm:^1.0.1"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/080185ffa92f5111e6ecfeab7919368b9984c26d048b9c09a111fbc657ea62bb5dfe6b56245e1804ce692a445cc93ab6625936515fa0e7518b8f2d86feda9630
  languageName: node
  linkType: hard

"@stablelib/random@npm:1.0.2, @stablelib/random@npm:^1.0.1, @stablelib/random@npm:^1.0.2":
  version: 1.0.2
  resolution: "@stablelib/random@npm:1.0.2"
  dependencies:
    "@stablelib/binary": "npm:^1.0.1"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/ebb217cfb76db97d98ec07bd7ce03a650fa194b91f0cb12382738161adff1830f405de0e9bad22bbc352422339ff85f531873b6a874c26ea9b59cfcc7ea787e0
  languageName: node
  linkType: hard

"@stablelib/sha256@npm:1.0.1":
  version: 1.0.1
  resolution: "@stablelib/sha256@npm:1.0.1"
  dependencies:
    "@stablelib/binary": "npm:^1.0.1"
    "@stablelib/hash": "npm:^1.0.1"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/e29ee9bc76eece4345e9155ce4bdeeb1df8652296be72bd2760523ad565e3b99dca85b81db3b75ee20b34837077eb8542ca88f153f162154c62ba1f75aecc24a
  languageName: node
  linkType: hard

"@stablelib/sha512@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/sha512@npm:1.0.1"
  dependencies:
    "@stablelib/binary": "npm:^1.0.1"
    "@stablelib/hash": "npm:^1.0.1"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/84549070a383f4daf23d9065230eb81bc8f590c68bf5f7968f1b78901236b3bb387c14f63773dc6c3dc78e823b1c15470d2a04d398a2506391f466c16ba29b58
  languageName: node
  linkType: hard

"@stablelib/wipe@npm:^1.0.1":
  version: 1.0.1
  resolution: "@stablelib/wipe@npm:1.0.1"
  checksum: 10c0/c5a54f769c286a5b3ecff979471dfccd4311f2e84a959908e8c0e3aa4eed1364bd9707f7b69d1384b757e62cc295c221fa27286c7f782410eb8a690f30cfd796
  languageName: node
  linkType: hard

"@stablelib/x25519@npm:1.0.3":
  version: 1.0.3
  resolution: "@stablelib/x25519@npm:1.0.3"
  dependencies:
    "@stablelib/keyagreement": "npm:^1.0.1"
    "@stablelib/random": "npm:^1.0.2"
    "@stablelib/wipe": "npm:^1.0.1"
  checksum: 10c0/d8afe8a120923a434359d7d1c6759780426fed117a84a6c0f84d1a4878834cb4c2d7da78a1fa7cf227ce3924fdc300cd6ed6e46cf2508bf17b1545c319ab8418
  languageName: node
  linkType: hard

"@surma/rollup-plugin-off-main-thread@npm:^2.2.3":
  version: 2.2.3
  resolution: "@surma/rollup-plugin-off-main-thread@npm:2.2.3"
  dependencies:
    ejs: "npm:^3.1.6"
    json5: "npm:^2.2.0"
    magic-string: "npm:^0.25.0"
    string.prototype.matchall: "npm:^4.0.6"
  checksum: 10c0/4f36a7488cdae2907053a48231430e8e9aa8f1903a96131bf8325786afba3224011f9120164cae75043558bd051881050b071958388fe477927d340b1cc1a066
  languageName: node
  linkType: hard

"@swc/core-darwin-arm64@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-darwin-arm64@npm:1.11.29"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@swc/core-darwin-x64@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-darwin-x64@npm:1.11.29"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@swc/core-linux-arm-gnueabihf@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-linux-arm-gnueabihf@npm:1.11.29"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@swc/core-linux-arm64-gnu@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-linux-arm64-gnu@npm:1.11.29"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@swc/core-linux-arm64-musl@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-linux-arm64-musl@npm:1.11.29"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@swc/core-linux-x64-gnu@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-linux-x64-gnu@npm:1.11.29"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@swc/core-linux-x64-musl@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-linux-x64-musl@npm:1.11.29"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@swc/core-win32-arm64-msvc@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-win32-arm64-msvc@npm:1.11.29"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@swc/core-win32-ia32-msvc@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-win32-ia32-msvc@npm:1.11.29"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@swc/core-win32-x64-msvc@npm:1.11.29":
  version: 1.11.29
  resolution: "@swc/core-win32-x64-msvc@npm:1.11.29"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@swc/core@npm:^1.7.26":
  version: 1.11.29
  resolution: "@swc/core@npm:1.11.29"
  dependencies:
    "@swc/core-darwin-arm64": "npm:1.11.29"
    "@swc/core-darwin-x64": "npm:1.11.29"
    "@swc/core-linux-arm-gnueabihf": "npm:1.11.29"
    "@swc/core-linux-arm64-gnu": "npm:1.11.29"
    "@swc/core-linux-arm64-musl": "npm:1.11.29"
    "@swc/core-linux-x64-gnu": "npm:1.11.29"
    "@swc/core-linux-x64-musl": "npm:1.11.29"
    "@swc/core-win32-arm64-msvc": "npm:1.11.29"
    "@swc/core-win32-ia32-msvc": "npm:1.11.29"
    "@swc/core-win32-x64-msvc": "npm:1.11.29"
    "@swc/counter": "npm:^0.1.3"
    "@swc/types": "npm:^0.1.21"
  peerDependencies:
    "@swc/helpers": ">=0.5.17"
  dependenciesMeta:
    "@swc/core-darwin-arm64":
      optional: true
    "@swc/core-darwin-x64":
      optional: true
    "@swc/core-linux-arm-gnueabihf":
      optional: true
    "@swc/core-linux-arm64-gnu":
      optional: true
    "@swc/core-linux-arm64-musl":
      optional: true
    "@swc/core-linux-x64-gnu":
      optional: true
    "@swc/core-linux-x64-musl":
      optional: true
    "@swc/core-win32-arm64-msvc":
      optional: true
    "@swc/core-win32-ia32-msvc":
      optional: true
    "@swc/core-win32-x64-msvc":
      optional: true
  peerDependenciesMeta:
    "@swc/helpers":
      optional: true
  checksum: 10c0/d2df8f09fb0246d1794d09d5192d43efcfd061f3a59956ee1b26c4a031852bb0afaa1b12f915773a807272a3ff6f88870d90970dfd75bca379e0d206a2663643
  languageName: node
  linkType: hard

"@swc/counter@npm:^0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: 10c0/8424f60f6bf8694cfd2a9bca45845bce29f26105cda8cf19cdb9fd3e78dc6338699e4db77a89ae449260bafa1cc6bec307e81e7fb96dbf7dcfce0eea55151356
  languageName: node
  linkType: hard

"@swc/types@npm:^0.1.21":
  version: 0.1.21
  resolution: "@swc/types@npm:0.1.21"
  dependencies:
    "@swc/counter": "npm:^0.1.3"
  checksum: 10c0/2baa89c824426e0de0c84e212278010e2df8dc2d6ffaa6f1e306e1b2930c6404b3d3f8989307e8c42ceb95ac143ab7a80be138af6a014d5c782dce5be94dcd5e
  languageName: node
  linkType: hard

"@tanstack/query-core@npm:5.59.6":
  version: 5.59.6
  resolution: "@tanstack/query-core@npm:5.59.6"
  checksum: 10c0/b868ea403aa5be2ed020147a2a67cccbf5ba4963c599b95997f50c698bc40ad483e5b0e00a01156476b0f8637fc4999071b55abfc94e5309f74c99ab10099b1e
  languageName: node
  linkType: hard

"@tanstack/react-query@npm:5.59.6":
  version: 5.59.6
  resolution: "@tanstack/react-query@npm:5.59.6"
  dependencies:
    "@tanstack/query-core": "npm:5.59.6"
  peerDependencies:
    react: ^18 || ^19
  checksum: 10c0/ac2d5821ab2c9484a3236a59087a63b5a4124e1c29a92cdb7097210f77bc7fa0dd1513a643c5e39f024857189fe2c802f9c93c43e9853ce07ee402ada3f7544a
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.20.5":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": "npm:^7.20.7"
    "@babel/types": "npm:^7.20.7"
    "@types/babel__generator": "npm:*"
    "@types/babel__template": "npm:*"
    "@types/babel__traverse": "npm:*"
  checksum: 10c0/bdee3bb69951e833a4b811b8ee9356b69a61ed5b7a23e1a081ec9249769117fa83aaaf023bb06562a038eb5845155ff663e2d5c75dd95c1d5ccc91db012868ff
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.27.0
  resolution: "@types/babel__generator@npm:7.27.0"
  dependencies:
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/9f9e959a8792df208a9d048092fda7e1858bddc95c6314857a8211a99e20e6830bdeb572e3587ae8be5429e37f2a96fcf222a9f53ad232f5537764c9e13a2bbd
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": "npm:^7.1.0"
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/cc84f6c6ab1eab1427e90dd2b76ccee65ce940b778a9a67be2c8c39e1994e6f5bbc8efa309f6cea8dc6754994524cd4d2896558df76d92e7a1f46ecffee7112b
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*":
  version: 7.20.7
  resolution: "@types/babel__traverse@npm:7.20.7"
  dependencies:
    "@babel/types": "npm:^7.20.7"
  checksum: 10c0/5386f0af44f8746b063b87418f06129a814e16bb2686965a575e9d7376b360b088b89177778d8c426012abc43dd1a2d8ec3218bfc382280c898682746ce2ffbd
  languageName: node
  linkType: hard

"@types/debug@npm:^4.1.7":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "npm:*"
  checksum: 10c0/5dcd465edbb5a7f226e9a5efd1f399c6172407ef5840686b73e3608ce135eeca54ae8037dcd9f16bdb2768ac74925b820a8b9ecc588a58ca09eca6acabe33e2f
  languageName: node
  linkType: hard

"@types/estree@npm:0.0.39":
  version: 0.0.39
  resolution: "@types/estree@npm:0.0.39"
  checksum: 10c0/f0af6c95ac1988c4827964bd9d3b51d24da442e2188943f6dfcb1e1559103d5d024d564b2e9d3f84c53714a02a0a7435c7441138eb63d9af5de4dfc66cdc0d92
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.7, @types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10c0/be815254316882f7c40847336cd484c3bc1c3e34f710d197160d455dc9d6d050ffbf4c3bc76585dba86f737f020ab20bdb137ebe0e9116b0c86c7c0342221b8c
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 10c0/5ce692ffe1549e1b827d99ef8ff71187457e0eb44adbae38fdf7b9a74bae8d20642ee963c14516db1d35fa2652e65f47680fdf679dcbde52bbfadd021f497225
  languageName: node
  linkType: hard

"@types/node@npm:22.7.6":
  version: 22.7.6
  resolution: "@types/node@npm:22.7.6"
  dependencies:
    undici-types: "npm:~6.19.2"
  checksum: 10c0/d4406a63afce981c363fb1d1954aaf1759ad2d487c0833ebf667565ea4e45ff217d6fab4b5343badbdeccdf9d2e4a0841d633e0c929ceabcb33c288663dd0c73
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.2
  resolution: "@types/parse-json@npm:4.0.2"
  checksum: 10c0/b1b863ac34a2c2172fbe0807a1ec4d5cb684e48d422d15ec95980b81475fac4fdb3768a8b13eef39130203a7c04340fc167bae057c7ebcafd7dec9fe6c36aeb1
  languageName: node
  linkType: hard

"@types/prop-types@npm:*, @types/prop-types@npm:^15.7.12":
  version: 15.7.14
  resolution: "@types/prop-types@npm:15.7.14"
  checksum: 10c0/1ec775160bfab90b67a782d735952158c7e702ca4502968aa82565bd8e452c2de8601c8dfe349733073c31179116cf7340710160d3836aa8a1ef76d1532893b1
  languageName: node
  linkType: hard

"@types/react-dom@npm:18.3.1":
  version: 18.3.1
  resolution: "@types/react-dom@npm:18.3.1"
  dependencies:
    "@types/react": "npm:*"
  checksum: 10c0/8b416551c60bb6bd8ec10e198c957910cfb271bc3922463040b0d57cf4739cdcd24b13224f8d68f10318926e1ec3cd69af0af79f0291b599a992f8c80d47f1eb
  languageName: node
  linkType: hard

"@types/react-gtm-module@npm:2.0.3":
  version: 2.0.3
  resolution: "@types/react-gtm-module@npm:2.0.3"
  checksum: 10c0/73895910e375fd45315a285b8b5019f7d923f3aefd0bd26b421166253d9b8bdc3273e63764910b3e0ac6192e3eeedb5bee6db0aeb64137776968312fe675302a
  languageName: node
  linkType: hard

"@types/react-transition-group@npm:^4.4.10":
  version: 4.4.12
  resolution: "@types/react-transition-group@npm:4.4.12"
  peerDependencies:
    "@types/react": "*"
  checksum: 10c0/0441b8b47c69312c89ec0760ba477ba1a0808a10ceef8dc1c64b1013ed78517332c30f18681b0ec0b53542731f1ed015169fed1d127cc91222638ed955478ec7
  languageName: node
  linkType: hard

"@types/react@npm:*":
  version: 19.1.6
  resolution: "@types/react@npm:19.1.6"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/8b10b198e28997b3c57559750f8bcf5ae7b33c554b16b6f4fe2ece1d4de6a2fc8cb53e7effe08ec9cb939d2f479eb97c5e08aac2cf83b10a90164fe451cc8ea2
  languageName: node
  linkType: hard

"@types/react@npm:18.3.1":
  version: 18.3.1
  resolution: "@types/react@npm:18.3.1"
  dependencies:
    "@types/prop-types": "npm:*"
    csstype: "npm:^3.0.2"
  checksum: 10c0/18d856c12a4ec93f3cda2d58ef3d77a9480818afd3af895f812896fb82cfca1f35a692ab1add4ce826a4eb58a071624c7d1c8c6c4ccfb81c100d2916dc607614
  languageName: node
  linkType: hard

"@types/resolve@npm:1.20.2":
  version: 1.20.2
  resolution: "@types/resolve@npm:1.20.2"
  checksum: 10c0/c5b7e1770feb5ccfb6802f6ad82a7b0d50874c99331e0c9b259e415e55a38d7a86ad0901c57665d93f75938be2a6a0bc9aa06c9749192cadb2e4512800bbc6e6
  languageName: node
  linkType: hard

"@types/trusted-types@npm:^2.0.2":
  version: 2.0.7
  resolution: "@types/trusted-types@npm:2.0.7"
  checksum: 10c0/4c4855f10de7c6c135e0d32ce462419d8abbbc33713b31d294596c0cc34ae1fa6112a2f9da729c8f7a20707782b0d69da3b1f8df6645b0366d08825ca1522e0c
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.9.0":
  version: 8.9.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.9.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.9.0"
    "@typescript-eslint/type-utils": "npm:8.9.0"
    "@typescript-eslint/utils": "npm:8.9.0"
    "@typescript-eslint/visitor-keys": "npm:8.9.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.3.1"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^1.3.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.0.0 || ^8.0.0-alpha.0
    eslint: ^8.57.0 || ^9.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/07f273dc270268980bbf65ea5e0c69d05377e42dbdb2dd3f4a1293a3536c049ddfb548eb9ec6e60394c2361c4a15b62b8246951f83e16a9d16799578a74dc691
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.9.0":
  version: 8.9.0
  resolution: "@typescript-eslint/parser@npm:8.9.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.9.0"
    "@typescript-eslint/types": "npm:8.9.0"
    "@typescript-eslint/typescript-estree": "npm:8.9.0"
    "@typescript-eslint/visitor-keys": "npm:8.9.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/aca7c838de85fb700ecf5682dc6f8f90a0fbfe09a3044a176c0dc3ffd9c5e7105beb0919a30824f46b02223a74119b4f5a9834a0663328987f066cb359b5dbed
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.9.0":
  version: 8.9.0
  resolution: "@typescript-eslint/scope-manager@npm:8.9.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.9.0"
    "@typescript-eslint/visitor-keys": "npm:8.9.0"
  checksum: 10c0/1fb77a982e3384d8cabd64678ea8f9de328708080ff9324bf24a44da4e8d7b7692ae4820efc3ef36027bf0fd6a061680d3c30ce63d661fb31e18970fca5e86c5
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.9.0":
  version: 8.9.0
  resolution: "@typescript-eslint/type-utils@npm:8.9.0"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:8.9.0"
    "@typescript-eslint/utils": "npm:8.9.0"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^1.3.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/aff06afda9ac7d12f750e76c8f91ed8b56eefd3f3f4fbaa93a64411ec9e0bd2c2972f3407e439320d98062b16f508dce7604b8bb2b803fded9d3148e5ee721b1
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.9.0":
  version: 8.9.0
  resolution: "@typescript-eslint/types@npm:8.9.0"
  checksum: 10c0/8d901b7ed2f943624c24f7fa67f7be9d49a92554d54c4f27397c05b329ceff59a9ea246810b53ff36fca08760c14305dd4ce78fbac7ca0474311b0575bf49010
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.9.0":
  version: 8.9.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.9.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.9.0"
    "@typescript-eslint/visitor-keys": "npm:8.9.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^1.3.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/bb5ec70727f07d1575e95f9d117762636209e1ab073a26c4e873e1e5b4617b000d300a23d294ad81693f7e99abe3e519725452c30b235a253edcd85b6ae052b0
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.9.0":
  version: 8.9.0
  resolution: "@typescript-eslint/utils@npm:8.9.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    "@typescript-eslint/scope-manager": "npm:8.9.0"
    "@typescript-eslint/types": "npm:8.9.0"
    "@typescript-eslint/typescript-estree": "npm:8.9.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
  checksum: 10c0/af13e3d501060bdc5fa04b131b3f9a90604e5c1d4845d1f8bd94b703a3c146a76debfc21fe65a7f3a0459ed6c57cf2aa3f0a052469bb23b6f35ff853fe9495b1
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.9.0":
  version: 8.9.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.9.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.9.0"
    eslint-visitor-keys: "npm:^3.4.3"
  checksum: 10c0/e33208b946841f1838d87d64f4ee230f798e68bdce8c181d3ac0abb567f758cb9c4bdccc919d493167869f413ca4c400e7db0f7dd7e8fc84ab6a8344076a7458
  languageName: node
  linkType: hard

"@vitejs/plugin-react-swc@npm:3.7.1":
  version: 3.7.1
  resolution: "@vitejs/plugin-react-swc@npm:3.7.1"
  dependencies:
    "@swc/core": "npm:^1.7.26"
  peerDependencies:
    vite: ^4 || ^5
  checksum: 10c0/2d613e69c0d0b809c94df80ca2b0caf39c50f0b98aa1f8599fd086bc37dac1449898eb6572000e1c133313137cac93440c4cb0861e05820c78bd2c07a52e64a8
  languageName: node
  linkType: hard

"@vitejs/plugin-react@npm:4.3.2":
  version: 4.3.2
  resolution: "@vitejs/plugin-react@npm:4.3.2"
  dependencies:
    "@babel/core": "npm:^7.25.2"
    "@babel/plugin-transform-react-jsx-self": "npm:^7.24.7"
    "@babel/plugin-transform-react-jsx-source": "npm:^7.24.7"
    "@types/babel__core": "npm:^7.20.5"
    react-refresh: "npm:^0.14.2"
  peerDependencies:
    vite: ^4.2.0 || ^5.0.0
  checksum: 10c0/945f357175bea45031dc98d379e63cd34cd60a51b3dd394b66138696625ac8b55bc913a23481f78bbe15ca558c21ea4699b936abbd8242003d7c0ad51d298727
  languageName: node
  linkType: hard

"@wagmi/connectors@npm:5.7.7":
  version: 5.7.7
  resolution: "@wagmi/connectors@npm:5.7.7"
  dependencies:
    "@coinbase/wallet-sdk": "npm:4.3.0"
    "@metamask/sdk": "npm:0.32.0"
    "@safe-global/safe-apps-provider": "npm:0.18.5"
    "@safe-global/safe-apps-sdk": "npm:9.1.0"
    "@walletconnect/ethereum-provider": "npm:2.17.0"
    cbw-sdk: "npm:@coinbase/wallet-sdk@3.9.3"
  peerDependencies:
    "@wagmi/core": 2.16.4
    typescript: ">=5.0.4"
    viem: 2.x
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/aab2381ea9bb17278e5c76d1ef5039a3f32eac5dfea46b71e217f8e4714624907bcc05c37c1e9327bc4602d4175bdf325e4246dc98bd0ea45ee68c2b67032c6e
  languageName: node
  linkType: hard

"@wagmi/connectors@npm:>=5.7":
  version: 5.8.4
  resolution: "@wagmi/connectors@npm:5.8.4"
  dependencies:
    "@coinbase/wallet-sdk": "npm:4.3.0"
    "@metamask/sdk": "npm:0.32.0"
    "@safe-global/safe-apps-provider": "npm:0.18.6"
    "@safe-global/safe-apps-sdk": "npm:9.1.0"
    "@walletconnect/ethereum-provider": "npm:2.21.1"
    cbw-sdk: "npm:@coinbase/wallet-sdk@3.9.3"
  peerDependencies:
    "@wagmi/core": 2.17.2
    typescript: ">=5.0.4"
    viem: 2.x
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/4d7acaca05b460964ddd6f9c4db93a4f994758373fca1ada4f3b904a93d4f22c693e848eb0a26331905677c1a9e486f2f1eab68ecc0d05bff3ea6d278e8e09e3
  languageName: node
  linkType: hard

"@wagmi/core@npm:2.16.4":
  version: 2.16.4
  resolution: "@wagmi/core@npm:2.16.4"
  dependencies:
    eventemitter3: "npm:5.0.1"
    mipd: "npm:0.0.7"
    zustand: "npm:5.0.0"
  peerDependencies:
    "@tanstack/query-core": ">=5.0.0"
    typescript: ">=5.0.4"
    viem: 2.x
  peerDependenciesMeta:
    "@tanstack/query-core":
      optional: true
    typescript:
      optional: true
  checksum: 10c0/eac99dba4eae35c90b52a0757fb0dfc044f10441c18f880033d3bbb9a2ca25caeb90f8b633ea46983b1af7edbabeadf0110d7b33d03e94d23d7c4616c9c3cb32
  languageName: node
  linkType: hard

"@walletconnect/core@npm:2.17.0":
  version: 2.17.0
  resolution: "@walletconnect/core@npm:2.17.0"
  dependencies:
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/jsonrpc-ws-connection": "npm:1.0.14"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/relay-api": "npm:1.0.11"
    "@walletconnect/relay-auth": "npm:1.0.4"
    "@walletconnect/safe-json": "npm:1.0.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.17.0"
    "@walletconnect/utils": "npm:2.17.0"
    events: "npm:3.3.0"
    lodash.isequal: "npm:4.5.0"
    uint8arrays: "npm:3.1.0"
  checksum: 10c0/34ae5b9b68c08c1dd3ebb2a6ebff8697307e76fbfe4d6b51d5d090da5cd1613e1c66fa5ac3a87c914333458d7b5bf075bb664292f6b2c7d438c72f706d87416d
  languageName: node
  linkType: hard

"@walletconnect/core@npm:2.18.0":
  version: 2.18.0
  resolution: "@walletconnect/core@npm:2.18.0"
  dependencies:
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/jsonrpc-ws-connection": "npm:1.0.16"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/relay-api": "npm:1.0.11"
    "@walletconnect/relay-auth": "npm:1.1.0"
    "@walletconnect/safe-json": "npm:1.0.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.18.0"
    "@walletconnect/utils": "npm:2.18.0"
    "@walletconnect/window-getters": "npm:1.0.1"
    events: "npm:3.3.0"
    lodash.isequal: "npm:4.5.0"
    uint8arrays: "npm:3.1.0"
  checksum: 10c0/6e77b129c1399d0a55964ba85694209eda95e9a24de3cc2297b138ae87ac6f307a500adc21297343dcd7b0cdda7d074b2fbb1350db3fc8251e34bf6531c0875b
  languageName: node
  linkType: hard

"@walletconnect/core@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/core@npm:2.21.0"
  dependencies:
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/jsonrpc-ws-connection": "npm:1.0.16"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/relay-api": "npm:1.0.11"
    "@walletconnect/relay-auth": "npm:1.1.0"
    "@walletconnect/safe-json": "npm:1.0.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.21.0"
    "@walletconnect/utils": "npm:2.21.0"
    "@walletconnect/window-getters": "npm:1.0.1"
    es-toolkit: "npm:1.33.0"
    events: "npm:3.3.0"
    uint8arrays: "npm:3.1.0"
  checksum: 10c0/4b4915221baa2f2f4157594dccb8184e98a503a852c675d49ed59b698d19315f3a976ef01f4021ac97623f2406c55a96a3a991296fcf9cf6b3745991ac68fb41
  languageName: node
  linkType: hard

"@walletconnect/core@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/core@npm:2.21.1"
  dependencies:
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/jsonrpc-ws-connection": "npm:1.0.16"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/relay-api": "npm:1.0.11"
    "@walletconnect/relay-auth": "npm:1.1.0"
    "@walletconnect/safe-json": "npm:1.0.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.21.1"
    "@walletconnect/utils": "npm:2.21.1"
    "@walletconnect/window-getters": "npm:1.0.1"
    es-toolkit: "npm:1.33.0"
    events: "npm:3.3.0"
    uint8arrays: "npm:3.1.0"
  checksum: 10c0/78664ab17591cd023dfe497e89db2e1d330354ce1b88fe4a75a700ee5a581eaa1ad0a61549b0c269587cc5d8d932155ff01ce98d74b506c41b9c172ca2ec252e
  languageName: node
  linkType: hard

"@walletconnect/environment@npm:^1.0.1":
  version: 1.0.1
  resolution: "@walletconnect/environment@npm:1.0.1"
  dependencies:
    tslib: "npm:1.14.1"
  checksum: 10c0/08eacce6452950a17f4209c443bd4db6bf7bddfc860593bdbd49edda9d08821696dee79e5617a954fbe90ff32c1d1f1691ef0c77455ed3e4201b328856a5e2f7
  languageName: node
  linkType: hard

"@walletconnect/ethereum-provider@npm:2.17.0":
  version: 2.17.0
  resolution: "@walletconnect/ethereum-provider@npm:2.17.0"
  dependencies:
    "@walletconnect/jsonrpc-http-connection": "npm:1.0.8"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/modal": "npm:2.7.0"
    "@walletconnect/sign-client": "npm:2.17.0"
    "@walletconnect/types": "npm:2.17.0"
    "@walletconnect/universal-provider": "npm:2.17.0"
    "@walletconnect/utils": "npm:2.17.0"
    events: "npm:3.3.0"
  checksum: 10c0/b046a9c296e95b22841f0b2efd28a4ce1a38529a9ba412d3c8ffc482879d79c3d2a24b8c0ec712baecf781938b4321ab5c1ecad5573d078add7c47b0cfd08a25
  languageName: node
  linkType: hard

"@walletconnect/ethereum-provider@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/ethereum-provider@npm:2.21.1"
  dependencies:
    "@reown/appkit": "npm:1.7.8"
    "@walletconnect/jsonrpc-http-connection": "npm:1.0.8"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/sign-client": "npm:2.21.1"
    "@walletconnect/types": "npm:2.21.1"
    "@walletconnect/universal-provider": "npm:2.21.1"
    "@walletconnect/utils": "npm:2.21.1"
    events: "npm:3.3.0"
  checksum: 10c0/91247045202a7f040338f7588d7c323cc845ac47c6ca8749f38ab07ac30a219a1ef6698ee03b97f5d48ca57e3fa1e1863c9fbc1371a1471501b5843014cacd18
  languageName: node
  linkType: hard

"@walletconnect/events@npm:1.0.1, @walletconnect/events@npm:^1.0.1":
  version: 1.0.1
  resolution: "@walletconnect/events@npm:1.0.1"
  dependencies:
    keyvaluestorage-interface: "npm:^1.0.0"
    tslib: "npm:1.14.1"
  checksum: 10c0/919a97e1dacf7096aefe07af810362cfc190533a576dcfa21387295d825a3c3d5f90bedee73235b1b343f5c696f242d7bffc5ea3359d3833541349ca23f50df8
  languageName: node
  linkType: hard

"@walletconnect/heartbeat@npm:1.2.2":
  version: 1.2.2
  resolution: "@walletconnect/heartbeat@npm:1.2.2"
  dependencies:
    "@walletconnect/events": "npm:^1.0.1"
    "@walletconnect/time": "npm:^1.0.2"
    events: "npm:^3.3.0"
  checksum: 10c0/a97b07764c397fe3cd26e8ea4233ecc8a26049624df7edc05290d286266bc5ba1de740d12c50dc1b7e8605198c5974e34e2d5318087bd4e9db246e7b273f4592
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-http-connection@npm:1.0.8":
  version: 1.0.8
  resolution: "@walletconnect/jsonrpc-http-connection@npm:1.0.8"
  dependencies:
    "@walletconnect/jsonrpc-utils": "npm:^1.0.6"
    "@walletconnect/safe-json": "npm:^1.0.1"
    cross-fetch: "npm:^3.1.4"
    events: "npm:^3.3.0"
  checksum: 10c0/cfac9ae74085d383ebc6edf075aeff01312818ac95e706cb8538ef4d4e6d82e75fb51529b3a9b65fa56a3f0f32a1738defad61713ed8a5f67cee25a79b6b4614
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-provider@npm:1.0.14":
  version: 1.0.14
  resolution: "@walletconnect/jsonrpc-provider@npm:1.0.14"
  dependencies:
    "@walletconnect/jsonrpc-utils": "npm:^1.0.8"
    "@walletconnect/safe-json": "npm:^1.0.2"
    events: "npm:^3.3.0"
  checksum: 10c0/9801bd516d81e92977b6add213da91e0e4a7a5915ad22685a4d2a733bab6199e9053485b76340cd724c7faa17a1b0eb842696247944fd57fb581488a2e1bed75
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-types@npm:1.0.4, @walletconnect/jsonrpc-types@npm:^1.0.2, @walletconnect/jsonrpc-types@npm:^1.0.3":
  version: 1.0.4
  resolution: "@walletconnect/jsonrpc-types@npm:1.0.4"
  dependencies:
    events: "npm:^3.3.0"
    keyvaluestorage-interface: "npm:^1.0.0"
  checksum: 10c0/752978685b0596a4ba02e1b689d23873e464460e4f376c97ef63e6b3ab273658ca062de2bfcaa8a498d31db0c98be98c8bbfbe5142b256a4b3ef425e1707f353
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-utils@npm:1.0.8, @walletconnect/jsonrpc-utils@npm:^1.0.6, @walletconnect/jsonrpc-utils@npm:^1.0.8":
  version: 1.0.8
  resolution: "@walletconnect/jsonrpc-utils@npm:1.0.8"
  dependencies:
    "@walletconnect/environment": "npm:^1.0.1"
    "@walletconnect/jsonrpc-types": "npm:^1.0.3"
    tslib: "npm:1.14.1"
  checksum: 10c0/e4a6bd801cf555bca775e03d961d1fe5ad0a22838e3496adda43ab4020a73d1b38de7096c06940e51f00fccccc734cd422fe4f1f7a8682302467b9c4d2a93d5d
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-ws-connection@npm:1.0.14":
  version: 1.0.14
  resolution: "@walletconnect/jsonrpc-ws-connection@npm:1.0.14"
  dependencies:
    "@walletconnect/jsonrpc-utils": "npm:^1.0.6"
    "@walletconnect/safe-json": "npm:^1.0.2"
    events: "npm:^3.3.0"
    ws: "npm:^7.5.1"
  checksum: 10c0/a710ecc51f8d3ed819ba6d6e53151ef274473aa8746ffdeaffaa3d4c020405bc694b0d179649fc2510a556eb4daf02f4a9e3dacef69ff95f673939bd67be649e
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-ws-connection@npm:1.0.16":
  version: 1.0.16
  resolution: "@walletconnect/jsonrpc-ws-connection@npm:1.0.16"
  dependencies:
    "@walletconnect/jsonrpc-utils": "npm:^1.0.6"
    "@walletconnect/safe-json": "npm:^1.0.2"
    events: "npm:^3.3.0"
    ws: "npm:^7.5.1"
  checksum: 10c0/30a09d24ffb6b4b291e2d1263504c4ea6c6797c992f5e6eb8033e58bd24749c80fd4e5ba6ffaadb28f8ced0c6b131213195b616f8983bb9f56aa7c91e83e6218
  languageName: node
  linkType: hard

"@walletconnect/keyvaluestorage@npm:1.1.1":
  version: 1.1.1
  resolution: "@walletconnect/keyvaluestorage@npm:1.1.1"
  dependencies:
    "@walletconnect/safe-json": "npm:^1.0.1"
    idb-keyval: "npm:^6.2.1"
    unstorage: "npm:^1.9.0"
  peerDependencies:
    "@react-native-async-storage/async-storage": 1.x
  peerDependenciesMeta:
    "@react-native-async-storage/async-storage":
      optional: true
  checksum: 10c0/de2ec39d09ce99370865f7d7235b93c42b3e4fd3406bdbc644329eff7faea2722618aa88ffc4ee7d20b1d6806a8331261b65568187494cbbcceeedbe79dc30e8
  languageName: node
  linkType: hard

"@walletconnect/logger@npm:2.1.2":
  version: 2.1.2
  resolution: "@walletconnect/logger@npm:2.1.2"
  dependencies:
    "@walletconnect/safe-json": "npm:^1.0.2"
    pino: "npm:7.11.0"
  checksum: 10c0/c66e835d33f737f48d6269f151650f6d7bb85bd8b59580fb8116f94d460773820968026e666ddf4a1753f28fceb3c54aae8230a445108a116077cb13a293842f
  languageName: node
  linkType: hard

"@walletconnect/modal-core@npm:2.7.0":
  version: 2.7.0
  resolution: "@walletconnect/modal-core@npm:2.7.0"
  dependencies:
    valtio: "npm:1.11.2"
  checksum: 10c0/84b11735c005e37e661aa0f08b2e8c8098db3b2cacd957c4a73f4d3de11b2d5e04dd97ab970f8d22fc3e8269fea3297b9487e177343bbab8dd69b3b917fb7f60
  languageName: node
  linkType: hard

"@walletconnect/modal-ui@npm:2.7.0":
  version: 2.7.0
  resolution: "@walletconnect/modal-ui@npm:2.7.0"
  dependencies:
    "@walletconnect/modal-core": "npm:2.7.0"
    lit: "npm:2.8.0"
    motion: "npm:10.16.2"
    qrcode: "npm:1.5.3"
  checksum: 10c0/b717f1fc9854b7d14a4364720fce2d44167f547533340704644ed2fdf9d861b3798ffd19a3b51062a366a8bc39f84b9a8bb3dd04e9e33da742192359be00b051
  languageName: node
  linkType: hard

"@walletconnect/modal@npm:2.7.0":
  version: 2.7.0
  resolution: "@walletconnect/modal@npm:2.7.0"
  dependencies:
    "@walletconnect/modal-core": "npm:2.7.0"
    "@walletconnect/modal-ui": "npm:2.7.0"
  checksum: 10c0/2f3074eebbca41a46e29680dc2565bc762133508774f05db0075a82b0b66ecc8defca40a94ad63669676090a7e3ef671804592b10e91636ab1cdeac014a1eb11
  languageName: node
  linkType: hard

"@walletconnect/relay-api@npm:1.0.11":
  version: 1.0.11
  resolution: "@walletconnect/relay-api@npm:1.0.11"
  dependencies:
    "@walletconnect/jsonrpc-types": "npm:^1.0.2"
  checksum: 10c0/2595d7e68d3a93e7735e0b6204811762898b0ce1466e811d78be5bcec7ac1cde5381637615a99104099165bf63695da5ef9381d6ded29924a57a71b10712a91d
  languageName: node
  linkType: hard

"@walletconnect/relay-auth@npm:1.0.4":
  version: 1.0.4
  resolution: "@walletconnect/relay-auth@npm:1.0.4"
  dependencies:
    "@stablelib/ed25519": "npm:^1.0.2"
    "@stablelib/random": "npm:^1.0.1"
    "@walletconnect/safe-json": "npm:^1.0.1"
    "@walletconnect/time": "npm:^1.0.2"
    tslib: "npm:1.14.1"
    uint8arrays: "npm:^3.0.0"
  checksum: 10c0/e90294ff718c5c1e49751a28916aaac45dd07d694f117052506309eb05b68cc2c72d9b302366e40d79ef952c22bd0bbea731d09633a6663b0ab8e18b4804a832
  languageName: node
  linkType: hard

"@walletconnect/relay-auth@npm:1.1.0":
  version: 1.1.0
  resolution: "@walletconnect/relay-auth@npm:1.1.0"
  dependencies:
    "@noble/curves": "npm:1.8.0"
    "@noble/hashes": "npm:1.7.0"
    "@walletconnect/safe-json": "npm:^1.0.1"
    "@walletconnect/time": "npm:^1.0.2"
    uint8arrays: "npm:^3.0.0"
  checksum: 10c0/29eb41ce8e70d581a3a8c8f771a70d2775d6feca548ac7ea85a792471d865a6d63be02f7deb1591056299abc2f77e1a7b5e7a0c7f95f0e48cd62e783047cee46
  languageName: node
  linkType: hard

"@walletconnect/safe-json@npm:1.0.2, @walletconnect/safe-json@npm:^1.0.1, @walletconnect/safe-json@npm:^1.0.2":
  version: 1.0.2
  resolution: "@walletconnect/safe-json@npm:1.0.2"
  dependencies:
    tslib: "npm:1.14.1"
  checksum: 10c0/8689072018c1ff7ab58eca67bd6f06b53702738d8183d67bfe6ed220aeac804e41901b8ee0fb14299e83c70093fafb90a90992202d128d53b2832bb01b591752
  languageName: node
  linkType: hard

"@walletconnect/sign-client@npm:2.17.0":
  version: 2.17.0
  resolution: "@walletconnect/sign-client@npm:2.17.0"
  dependencies:
    "@walletconnect/core": "npm:2.17.0"
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.17.0"
    "@walletconnect/utils": "npm:2.17.0"
    events: "npm:3.3.0"
  checksum: 10c0/48f7d13b3db49584a40dc2653f49fabadd100a324e2213476b8d9e4d6fe0808a08ae14103d2e5b609abff3115197003d8570d606275dbd0f6774d0d49da10c61
  languageName: node
  linkType: hard

"@walletconnect/sign-client@npm:2.18.0":
  version: 2.18.0
  resolution: "@walletconnect/sign-client@npm:2.18.0"
  dependencies:
    "@walletconnect/core": "npm:2.18.0"
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.18.0"
    "@walletconnect/utils": "npm:2.18.0"
    events: "npm:3.3.0"
  checksum: 10c0/35adab15684abb2b1c4e33e2f1abc850e74b47c978ac7a75df684aac1595742b148995adf43c692e178cc25dee1a430ed6603907283d9303d2afad1ce39dad57
  languageName: node
  linkType: hard

"@walletconnect/sign-client@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/sign-client@npm:2.21.0"
  dependencies:
    "@walletconnect/core": "npm:2.21.0"
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.21.0"
    "@walletconnect/utils": "npm:2.21.0"
    events: "npm:3.3.0"
  checksum: 10c0/72cca06c99a2cf49aeaefaa13783fa01505d358a578f4b18c1742b790505fb95bf4d9d80a89092531a16e257f16b2d73c3bc6846c3ff0ecafbaf5394dbe0519f
  languageName: node
  linkType: hard

"@walletconnect/sign-client@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/sign-client@npm:2.21.1"
  dependencies:
    "@walletconnect/core": "npm:2.21.1"
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.21.1"
    "@walletconnect/utils": "npm:2.21.1"
    events: "npm:3.3.0"
  checksum: 10c0/ed33f8150a4d9966ca80c6455557fb2aa8f396c48ca4e4f56ff0bd0f97d53dafcc3609073d7c31f54d3ea87392045ddfbca2d7a0b8544eaa5c618a3a92f90b66
  languageName: node
  linkType: hard

"@walletconnect/time@npm:1.0.2, @walletconnect/time@npm:^1.0.2":
  version: 1.0.2
  resolution: "@walletconnect/time@npm:1.0.2"
  dependencies:
    tslib: "npm:1.14.1"
  checksum: 10c0/6317f93086e36daa3383cab4a8579c7d0bed665fb0f8e9016575200314e9ba5e61468f66142a7bb5b8489bb4c9250196576d90a60b6b00e0e856b5d0ab6ba474
  languageName: node
  linkType: hard

"@walletconnect/types@npm:2.17.0":
  version: 2.17.0
  resolution: "@walletconnect/types@npm:2.17.0"
  dependencies:
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    events: "npm:3.3.0"
  checksum: 10c0/bdc0c062da1edb4410882d9cfca1bb30eb0afd7caea90d5e7a66eaf15e28380e9ef97635cd5e5a017947f4c814c1f780622b4d8946b11a335d415ae066ec7ade
  languageName: node
  linkType: hard

"@walletconnect/types@npm:2.18.0":
  version: 2.18.0
  resolution: "@walletconnect/types@npm:2.18.0"
  dependencies:
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    events: "npm:3.3.0"
  checksum: 10c0/5f657706629efdd316586be9b4c6f654deede6210f70e4afdd292c00899ce0f8ac6d31bc2894645ddeaba73f48748d5a0c5e5adfbcff6ef5379b699ab2fd7447
  languageName: node
  linkType: hard

"@walletconnect/types@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/types@npm:2.21.0"
  dependencies:
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    events: "npm:3.3.0"
  checksum: 10c0/1b969b045b77833315c56ae6948e551c175b6496e894be7b19db88a376d16a662a8b728ec753e01336053262ca16567ae36eed48f6dfe32cdf8d01cf66211588
  languageName: node
  linkType: hard

"@walletconnect/types@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/types@npm:2.21.1"
  dependencies:
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/heartbeat": "npm:1.2.2"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    events: "npm:3.3.0"
  checksum: 10c0/60468f50ea7c95ac5269a9e53a0417d50302978a927c042a0376d4dcb0d336f2187a129e8c602a173ccf020a193a4dde50f3f9f74d5b8da0a9801aa9d672458e
  languageName: node
  linkType: hard

"@walletconnect/universal-provider@npm:2.17.0":
  version: 2.17.0
  resolution: "@walletconnect/universal-provider@npm:2.17.0"
  dependencies:
    "@walletconnect/jsonrpc-http-connection": "npm:1.0.8"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/sign-client": "npm:2.17.0"
    "@walletconnect/types": "npm:2.17.0"
    "@walletconnect/utils": "npm:2.17.0"
    events: "npm:3.3.0"
  checksum: 10c0/7c1afc79054db5add4e937d7adaadb4fc26aecffb5d749d388418fa5d4eb153807ab4de301b642cd80669b4e5c6bcae917f18cf5ce8696d87da8b3705b60d1ec
  languageName: node
  linkType: hard

"@walletconnect/universal-provider@npm:2.18.0":
  version: 2.18.0
  resolution: "@walletconnect/universal-provider@npm:2.18.0"
  dependencies:
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/jsonrpc-http-connection": "npm:1.0.8"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/sign-client": "npm:2.18.0"
    "@walletconnect/types": "npm:2.18.0"
    "@walletconnect/utils": "npm:2.18.0"
    events: "npm:3.3.0"
    lodash: "npm:4.17.21"
  checksum: 10c0/50556253130480e05ab00ab16a4c73656e8dbe7a7d728eb2759d28f9aef0f3cd0caf9b510ce3f1ab5538481f40956324404e6391ce20ecc1103c28db1c05316d
  languageName: node
  linkType: hard

"@walletconnect/universal-provider@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/universal-provider@npm:2.21.0"
  dependencies:
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/jsonrpc-http-connection": "npm:1.0.8"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/sign-client": "npm:2.21.0"
    "@walletconnect/types": "npm:2.21.0"
    "@walletconnect/utils": "npm:2.21.0"
    es-toolkit: "npm:1.33.0"
    events: "npm:3.3.0"
  checksum: 10c0/856fa961926b15bd91e6a35a2f7f3db832d7a81fdb04ee0553ac882ac8c307a42bdeb439b2b6bb4ca0b834953e933f4d380883d1ad73cbbc7e88568091fa8aab
  languageName: node
  linkType: hard

"@walletconnect/universal-provider@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/universal-provider@npm:2.21.1"
  dependencies:
    "@walletconnect/events": "npm:1.0.1"
    "@walletconnect/jsonrpc-http-connection": "npm:1.0.8"
    "@walletconnect/jsonrpc-provider": "npm:1.0.14"
    "@walletconnect/jsonrpc-types": "npm:1.0.4"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/logger": "npm:2.1.2"
    "@walletconnect/sign-client": "npm:2.21.1"
    "@walletconnect/types": "npm:2.21.1"
    "@walletconnect/utils": "npm:2.21.1"
    es-toolkit: "npm:1.33.0"
    events: "npm:3.3.0"
  checksum: 10c0/75e97c9a52025b18c05d2e029384492c8a9f82044971be6fef1856962984ff6dc48805fc732d1cd748979ab19a6eb688c9e8ed7a0944f57efd384d1ab6375252
  languageName: node
  linkType: hard

"@walletconnect/utils@npm:2.17.0":
  version: 2.17.0
  resolution: "@walletconnect/utils@npm:2.17.0"
  dependencies:
    "@stablelib/chacha20poly1305": "npm:1.0.1"
    "@stablelib/hkdf": "npm:1.0.1"
    "@stablelib/random": "npm:1.0.2"
    "@stablelib/sha256": "npm:1.0.1"
    "@stablelib/x25519": "npm:1.0.3"
    "@walletconnect/relay-api": "npm:1.0.11"
    "@walletconnect/relay-auth": "npm:1.0.4"
    "@walletconnect/safe-json": "npm:1.0.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.17.0"
    "@walletconnect/window-getters": "npm:1.0.1"
    "@walletconnect/window-metadata": "npm:1.0.1"
    detect-browser: "npm:5.3.0"
    elliptic: "npm:^6.5.7"
    query-string: "npm:7.1.3"
    uint8arrays: "npm:3.1.0"
  checksum: 10c0/d1da74b2cd7af35f16d735fe408cfc820c611b2709bd00899e4e91b0b0a6dcd8f344f97df34d0ef8cabc121619a40b62118ffa2aa233ddba9863d1ba23480a0c
  languageName: node
  linkType: hard

"@walletconnect/utils@npm:2.18.0":
  version: 2.18.0
  resolution: "@walletconnect/utils@npm:2.18.0"
  dependencies:
    "@ethersproject/transactions": "npm:5.7.0"
    "@noble/ciphers": "npm:1.2.1"
    "@noble/curves": "npm:1.8.1"
    "@noble/hashes": "npm:1.7.1"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/relay-api": "npm:1.0.11"
    "@walletconnect/relay-auth": "npm:1.1.0"
    "@walletconnect/safe-json": "npm:1.0.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.18.0"
    "@walletconnect/window-getters": "npm:1.0.1"
    "@walletconnect/window-metadata": "npm:1.0.1"
    detect-browser: "npm:5.3.0"
    elliptic: "npm:6.6.1"
    query-string: "npm:7.1.3"
    uint8arrays: "npm:3.1.0"
  checksum: 10c0/1da75307f6949f8d6ee7f6967a0eed7eef6651dd2863291d5ea9ae8800592680d676249f4a89fcfbd3acf15fec2e9c14b19e477cf07cb3b009cbf8bce8bd4d26
  languageName: node
  linkType: hard

"@walletconnect/utils@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/utils@npm:2.21.0"
  dependencies:
    "@noble/ciphers": "npm:1.2.1"
    "@noble/curves": "npm:1.8.1"
    "@noble/hashes": "npm:1.7.1"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/relay-api": "npm:1.0.11"
    "@walletconnect/relay-auth": "npm:1.1.0"
    "@walletconnect/safe-json": "npm:1.0.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.21.0"
    "@walletconnect/window-getters": "npm:1.0.1"
    "@walletconnect/window-metadata": "npm:1.0.1"
    bs58: "npm:6.0.0"
    detect-browser: "npm:5.3.0"
    query-string: "npm:7.1.3"
    uint8arrays: "npm:3.1.0"
    viem: "npm:2.23.2"
  checksum: 10c0/2a091072aba6351f1576e459056e54b6af14a900fe0bc0dcff06df7abb58fb7f4ed2637905d62ae2e85188dfecc65867ced3b28b3475bd7c1327a276745cb25e
  languageName: node
  linkType: hard

"@walletconnect/utils@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/utils@npm:2.21.1"
  dependencies:
    "@noble/ciphers": "npm:1.2.1"
    "@noble/curves": "npm:1.8.1"
    "@noble/hashes": "npm:1.7.1"
    "@walletconnect/jsonrpc-utils": "npm:1.0.8"
    "@walletconnect/keyvaluestorage": "npm:1.1.1"
    "@walletconnect/relay-api": "npm:1.0.11"
    "@walletconnect/relay-auth": "npm:1.1.0"
    "@walletconnect/safe-json": "npm:1.0.2"
    "@walletconnect/time": "npm:1.0.2"
    "@walletconnect/types": "npm:2.21.1"
    "@walletconnect/window-getters": "npm:1.0.1"
    "@walletconnect/window-metadata": "npm:1.0.1"
    bs58: "npm:6.0.0"
    detect-browser: "npm:5.3.0"
    query-string: "npm:7.1.3"
    uint8arrays: "npm:3.1.0"
    viem: "npm:2.23.2"
  checksum: 10c0/367cf46f2534805fd4555564f2b1056fcc927464b9f1b9be495e1f1c599ec43cf5cc75ea1f01bec92a0e85fba029b6298a77820b1e9e61a7bf7e1bbde3525811
  languageName: node
  linkType: hard

"@walletconnect/window-getters@npm:1.0.1, @walletconnect/window-getters@npm:^1.0.1":
  version: 1.0.1
  resolution: "@walletconnect/window-getters@npm:1.0.1"
  dependencies:
    tslib: "npm:1.14.1"
  checksum: 10c0/c3aedba77aa9274b8277c4189ec992a0a6000377e95656443b3872ca5b5fe77dd91170b1695027fc524dc20362ce89605d277569a0d9a5bedc841cdaf14c95df
  languageName: node
  linkType: hard

"@walletconnect/window-metadata@npm:1.0.1":
  version: 1.0.1
  resolution: "@walletconnect/window-metadata@npm:1.0.1"
  dependencies:
    "@walletconnect/window-getters": "npm:^1.0.1"
    tslib: "npm:1.14.1"
  checksum: 10c0/f190e9bed77282d8ba868a4895f4d813e135f9bbecb8dd4aed988ab1b06992f78128ac19d7d073cf41d8a6a74d0c055cd725908ce0a894649fd25443ad934cf4
  languageName: node
  linkType: hard

"@wry/caches@npm:^1.0.0":
  version: 1.0.1
  resolution: "@wry/caches@npm:1.0.1"
  dependencies:
    tslib: "npm:^2.3.0"
  checksum: 10c0/a7bca3377f1131d3f1080f2e39d0692c9d1ca86bfd55734786f167f46aad28a4c8e772107324e8319843fb8068fdf98abcdea376d8a589316b1f0cdadf81f8b1
  languageName: node
  linkType: hard

"@wry/context@npm:^0.7.0":
  version: 0.7.4
  resolution: "@wry/context@npm:0.7.4"
  dependencies:
    tslib: "npm:^2.3.0"
  checksum: 10c0/6cc8249b8ba195cda7643bffb30969e33d54a99f118a29dd12f1c34064ee0adf04253cfa0ba5b9893afde0a9588745828962877b9585106f7488e8299757638b
  languageName: node
  linkType: hard

"@wry/equality@npm:^0.5.6":
  version: 0.5.7
  resolution: "@wry/equality@npm:0.5.7"
  dependencies:
    tslib: "npm:^2.3.0"
  checksum: 10c0/8503ff6d4eb80f303d1387e71e51da59ccfc2160fa6d464618be80946fe43a654ea73f0c5b90d659fc4dfc3e38cbbdd6650d595fe5865be476636e444470853e
  languageName: node
  linkType: hard

"@wry/trie@npm:^0.5.0":
  version: 0.5.0
  resolution: "@wry/trie@npm:0.5.0"
  dependencies:
    tslib: "npm:^2.3.0"
  checksum: 10c0/8c8cfcac96ba4bc69dabf02740e19e613f501b398e80bacc32cd95e87228f75ecb41cd1a76a65abae9756c0f61ab3536e0da52de28857456f9381ffdf5995d3e
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"abitype@npm:1.0.8, abitype@npm:^1.0.6":
  version: 1.0.8
  resolution: "abitype@npm:1.0.8"
  peerDependencies:
    typescript: ">=5.0.4"
    zod: ^3 >=3.22.0
  peerDependenciesMeta:
    typescript:
      optional: true
    zod:
      optional: true
  checksum: 10c0/d3393f32898c1f0f6da4eed2561da6830dcd0d5129a160fae9517214236ee6a6c8e5a0380b8b960c5bc1b949320bcbd015ec7f38b5d7444f8f2b854a1b5dd754
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.8.1":
  version: 8.14.1
  resolution: "acorn@npm:8.14.1"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dbd36c1ed1d2fa3550140000371fcf721578095b18777b85a79df231ca093b08edc6858d75d6e48c73e431c174dcf9214edbd7e6fa5911b93bd8abfa54e47123
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 10c0/dc4f757e40b5f3e3d674bc9beb4f1048f4ee83af189bae39be99f57bf1f48dde166a8b0a5342a84b5944ee8e6ed1e5a9d801858f4ad44764e84957122fe46261
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ajv@npm:^8.6.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    fast-uri: "npm:^3.0.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
  checksum: 10c0/ec3ba10a573c6b60f94639ffc53526275917a2df6810e4ab5a6b959d87459f9ef3f00d5e7865b82677cb7d21590355b34da14d1d0b9c32d75f95a187e76fff35
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"anymatch@npm:^3.1.3, anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10c0/74e1d2d996941c7a1badda9cabb7caab8c449db9086407cad8a1b71d2604cc8abf105db8ca4e02c04579ec58b7be40279ddb09aea4784832984485499f48432d
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10c0/2f2459caa06ae0f7f615003f9104b01f6435cc803e11bd2a655107d52a1781dc040532dc44d93026b694cc18793993246237423e13a5337e86b43ed604932c06
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10c0/669a32c2cb7e45091330c680e92eaeb791bc1d4132d827591e499cd1f776ff5a873e77e5f92d0ce795a8d60f10761dec9ddfe7225a5de680f5d357f67b1aac73
  languageName: node
  linkType: hard

"async-mutex@npm:^0.2.6":
  version: 0.2.6
  resolution: "async-mutex@npm:0.2.6"
  dependencies:
    tslib: "npm:^2.0.0"
  checksum: 10c0/440f1388fdbf2021261ba05952765182124a333681692fdef6af13935c20bfc2017e24e902362f12b29094a77b359ce3131e8dd45b1db42f1d570927ace9e7d9
  languageName: node
  linkType: hard

"async@npm:^3.2.3":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: 10c0/36484bb15ceddf07078688d95e27076379cc2f87b10c03b6dd8a83e89475a3c8df5848859dd06a4c95af1e4c16fc973de0171a77f18ea00be899aca2a4f85e70
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 10c0/4c058baf6df1bc5a1697cf182e2029c58cd99975288a13f9e70068ef5d6f4e1f1fd7c4d2c3c4912eae44797d1725be9700995736deca441b39f3e66d8dee97ef
  languageName: node
  linkType: hard

"atomic-sleep@npm:^1.0.0":
  version: 1.0.0
  resolution: "atomic-sleep@npm:1.0.0"
  checksum: 10c0/e329a6665512736a9bbb073e1761b4ec102f7926cce35037753146a9db9c8104f5044c1662e4a863576ce544fb8be27cd2be6bc8c1a40147d03f31eb1cfb6e8a
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10c0/d07226ef4f87daa01bd0fe80f8f310982e345f372926da2e5296aecc25c41cab440916bbaa4c5e1034b453af3392f67df5961124e4b586df1e99793a1374bdb2
  languageName: node
  linkType: hard

"axios@npm:1.8.1":
  version: 1.8.1
  resolution: "axios@npm:1.8.1"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/b2e1d5a61264502deee4b50f0a6df0aa3b174c546ccf68c0dff714a2b8863232e0bd8cb5b84f853303e97f242a98260f9bb9beabeafe451ad5af538e9eb7ac22
  languageName: node
  linkType: hard

"babel-plugin-macros@npm:^3.1.0":
  version: 3.1.0
  resolution: "babel-plugin-macros@npm:3.1.0"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
    cosmiconfig: "npm:^7.0.0"
    resolve: "npm:^1.19.0"
  checksum: 10c0/c6dfb15de96f67871d95bd2e8c58b0c81edc08b9b087dc16755e7157f357dc1090a8dc60ebab955e92587a9101f02eba07e730adc253a1e4cf593ca3ebd3839c
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.10":
  version: 0.4.13
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.13"
  dependencies:
    "@babel/compat-data": "npm:^7.22.6"
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b4a54561606d388e6f9499f39f03171af4be7f9ce2355e737135e40afa7086cf6790fdd706c2e59f488c8fa1f76123d28783708e07ddc84647dca8ed8fb98e06
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.11.0":
  version: 0.11.1
  resolution: "babel-plugin-polyfill-corejs3@npm:0.11.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.3"
    core-js-compat: "npm:^3.40.0"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/025f754b6296d84b20200aff63a3c1acdd85e8c621781f2bd27fe2512d0060526192d02329326947c6b29c27cf475fbcfaaff8c51eab1d2bfc7b79086bb64229
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.1":
  version: 0.6.4
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/ebaaf9e4e53201c02f496d3f686d815e94177b3e55b35f11223b99c60d197a29f907a2e87bbcccced8b7aff22a807fccc1adaf04722864a8e1862c8845ab830a
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base-x@npm:^5.0.0":
  version: 5.0.1
  resolution: "base-x@npm:5.0.1"
  checksum: 10c0/4ab6b02262b4fd499b147656f63ce7328bd5f895450401ce58a2f9e87828aea507cf0c320a6d8725389f86e8a48397562661c0bca28ef3276a22821b30f7a713
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"big.js@npm:6.2.2":
  version: 6.2.2
  resolution: "big.js@npm:6.2.2"
  checksum: 10c0/58d204f6a1a92508dc2eb98d964e2cc6dabb37a3d9fc8a1f0b77a34dead7c11e17b173d9a6df2d5a7a0f78d5c80853a9ce6df29852da59ab10b088e981195165
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"bn.js@npm:^4.11.9":
  version: 4.12.2
  resolution: "bn.js@npm:4.12.2"
  checksum: 10c0/09a249faa416a9a1ce68b5f5ec8bbca87fe54e5dd4ef8b1cc8a4969147b80035592bddcb1e9cc814c3ba79e573503d5c5178664b722b509fb36d93620dba9b57
  languageName: node
  linkType: hard

"bn.js@npm:^5.2.1":
  version: 5.2.2
  resolution: "bn.js@npm:5.2.2"
  checksum: 10c0/cb97827d476aab1a0194df33cd84624952480d92da46e6b4a19c32964aa01553a4a613502396712704da2ec8f831cf98d02e74ca03398404bd78a037ba93f2ab
  languageName: node
  linkType: hard

"bowser@npm:^2.9.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 10c0/04efeecc7927a9ec33c667fa0965dea19f4ac60b3fea60793c2e6cf06c1dcd2f7ae1dbc656f450c5f50783b1c75cf9dc173ba6f3b7db2feee01f8c4b793e1bd3
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 10c0/6f366d7c4990f82c366e3878492ba9a372a73163c09871e80d82fb4ae0d23f9f8924cb8a662330308206e6b3b76ba1d528b4601c9ef73c2166b440b2ea3b7571
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0, browserslist@npm:^4.24.4":
  version: 4.25.0
  resolution: "browserslist@npm:4.25.0"
  dependencies:
    caniuse-lite: "npm:^1.0.30001718"
    electron-to-chromium: "npm:^1.5.160"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/cc16c55b4468b18684a0e1ca303592b38635b1155d6724f172407192737a2f405b8030d87a05813729592793445b3d15e737b0055f901cdecccb29b1e580a1c5
  languageName: node
  linkType: hard

"bs58@npm:6.0.0":
  version: 6.0.0
  resolution: "bs58@npm:6.0.0"
  dependencies:
    base-x: "npm:^5.0.0"
  checksum: 10c0/61910839746625ee4f69369f80e2634e2123726caaa1da6b3bcefcf7efcd9bdca86603360fed9664ffdabe0038c51e542c02581c72ca8d44f60329fe1a6bc8f4
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer@npm:6.0.3, buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.2.1"
  checksum: 10c0/2a905fbbcde73cc5d8bd18d1caa23715d5f83a5935867c2329f0ac06104204ba7947be098fe1317fbd8830e26090ff8e764f08cd14fefc977bb248c3487bcbd0
  languageName: node
  linkType: hard

"bufferutil@npm:^4.0.8":
  version: 4.0.9
  resolution: "bufferutil@npm:4.0.9"
  dependencies:
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.3.0"
  checksum: 10c0/f8a93279fc9bdcf32b42eba97edc672b39ca0fe5c55a8596099886cffc76ea9dd78e0f6f51ecee3b5ee06d2d564aa587036b5d4ea39b8b5ac797262a363cdf7d
  languageName: node
  linkType: hard

"cac@npm:^6.7.14":
  version: 6.7.14
  resolution: "cac@npm:6.7.14"
  checksum: 10c0/4ee06aaa7bab8981f0d54e5f5f9d4adcd64058e9697563ce336d8a3878ed018ee18ebe5359b2430eceae87e0758e62ea2019c3f52ae6e211b1bd2e133856cd10
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10c0/a13819be0681d915144467741b69875ae5f4eba8961eb0bf322aab63ec87f8250eb6d6b0dcbb2e1349876412a56129ca338592b3829ef4343527f5f18a0752d4
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10c0/92ff9b443bfe8abb15f2b1513ca182d16126359ad4f955ebc83dc4ddcc4ef3fdd2c078bc223f2673dc223488e75c99b16cc4d056624374b799e6a1555cf61b23
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001718":
  version: 1.0.30001721
  resolution: "caniuse-lite@npm:1.0.30001721"
  checksum: 10c0/fa3a8926899824b385279f1f886fe34c5efb1321c9ece1b9df25c8d567a2706db8450cc5b4d969e769e641593e08ea644909324aba93636a43e4949a75f81c4c
  languageName: node
  linkType: hard

"cbw-sdk@npm:@coinbase/wallet-sdk@3.9.3":
  version: 3.9.3
  resolution: "@coinbase/wallet-sdk@npm:3.9.3"
  dependencies:
    bn.js: "npm:^5.2.1"
    buffer: "npm:^6.0.3"
    clsx: "npm:^1.2.1"
    eth-block-tracker: "npm:^7.1.0"
    eth-json-rpc-filters: "npm:^6.0.0"
    eventemitter3: "npm:^5.0.1"
    keccak: "npm:^3.0.3"
    preact: "npm:^10.16.0"
    sha.js: "npm:^2.4.11"
  checksum: 10c0/a34b7f3e84f1d12f8235d57b3fd2e06d04e9ad9d999944b43bf0a3b0e79bc1cff336e9097f4555f85e7085ac7a1be2907732cda6a79cad1b60521d996f390b99
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.0.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.3":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.0, chokidar@npm:^4.0.3":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: "npm:^4.0.1"
  checksum: 10c0/a58b9df05bb452f7d105d9e7229ac82fa873741c0c40ddcc7bb82f8a909fbe3f7814c9ebe9bc9a2bef9b737c0ec6e2d699d179048ef06ad3ec46315df0ebe6ad
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"cliui@npm:^6.0.0":
  version: 6.0.0
  resolution: "cliui@npm:6.0.0"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^6.2.0"
  checksum: 10c0/35229b1bb48647e882104cac374c9a18e34bbf0bace0e2cf03000326b6ca3050d6b59545d91e17bfe3705f4a0e2988787aa5cde6331bf5cbbf0164732cef6492
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clsx@npm:^1.2.1":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 10c0/34dead8bee24f5e96f6e7937d711978380647e936a22e76380290e35486afd8634966ce300fc4b74a32f3762c7d4c0303f442c3e259f4ce02374eb0c82834f27
  languageName: node
  linkType: hard

"clsx@npm:^2.1.0, clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10c0/c4c8eb865f8c82baab07e71bfa8897c73454881c4f99d6bc81585aecd7c441746c1399d08363dc096c550cceaf97bd4ce1e8854e1771e9998d9f94c4fe075839
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"common-tags@npm:^1.8.0":
  version: 1.8.2
  resolution: "common-tags@npm:1.8.2"
  checksum: 10c0/23efe47ff0a1a7c91489271b3a1e1d2a171c12ec7f9b35b29b2fce51270124aff0ec890087e2bc2182c1cb746e232ab7561aaafe05f1e7452aea733d2bfe3f63
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.5.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: 10c0/281da55454bf8126cbc6625385928c43479f2060984180c42f3a86c8b8c12720a24eac260624a7d1e090004028d2dee78602330578ceec1a08e27cb8bb0a8a5b
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"cookie-es@npm:^1.2.2":
  version: 1.2.2
  resolution: "cookie-es@npm:1.2.2"
  checksum: 10c0/210eb67cd40a53986fda99d6f47118cfc45a69c4abc03490d15ab1b83ac978d5518356aecdd7a7a4969292445e3063c2302deda4c73706a67edc008127608638
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.40.0":
  version: 3.42.0
  resolution: "core-js-compat@npm:3.42.0"
  dependencies:
    browserslist: "npm:^4.24.4"
  checksum: 10c0/0138ce005c13ce642fc38e18e54a52a1c78ca8315ee6e4faad748d2a1b0ad2462ea615285ad4e6cf77afe48e47a868d898e64c70606c1eb1c9e6a9f19ee2b186
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7.0.0":
  version: 7.1.0
  resolution: "cosmiconfig@npm:7.1.0"
  dependencies:
    "@types/parse-json": "npm:^4.0.0"
    import-fresh: "npm:^3.2.1"
    parse-json: "npm:^5.0.0"
    path-type: "npm:^4.0.0"
    yaml: "npm:^1.10.0"
  checksum: 10c0/b923ff6af581638128e5f074a5450ba12c0300b71302398ea38dbeabd33bbcaa0245ca9adbedfcf284a07da50f99ede5658c80bb3e39e2ce770a99d28a21ef03
  languageName: node
  linkType: hard

"crc-32@npm:^1.2.0":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: 10c0/11dcf4a2e77ee793835d49f2c028838eae58b44f50d1ff08394a610bfd817523f105d6ae4d9b5bef0aad45510f633eb23c903e9902e4409bed1ce70cb82b9bf0
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.4":
  version: 3.2.0
  resolution: "cross-fetch@npm:3.2.0"
  dependencies:
    node-fetch: "npm:^2.7.0"
  checksum: 10c0/d8596adf0269130098a676f6739a0922f3cc7b71cc89729925411ebe851a87026171c82ea89154c4811c9867c01c44793205a52e618ce2684650218c7fbeeb9f
  languageName: node
  linkType: hard

"cross-fetch@npm:^4.0.0":
  version: 4.1.0
  resolution: "cross-fetch@npm:4.1.0"
  dependencies:
    node-fetch: "npm:^2.7.0"
  checksum: 10c0/628b134ea27cfcada67025afe6ef1419813fffc5d63d175553efa75a2334522d450300a0f3f0719029700da80e96327930709d5551cf6deb39bb62f1d536642e
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"crossws@npm:^0.3.4":
  version: 0.3.5
  resolution: "crossws@npm:0.3.5"
  dependencies:
    uncrypto: "npm:^0.1.3"
  checksum: 10c0/9e873546f0806606c4f775219f6811768fc3b3b0765ca8230722e849058ad098318af006e1faa39a8008c03009c37c519f6bccad41b0d78586237585c75fb38b
  languageName: node
  linkType: hard

"crypto-random-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "crypto-random-string@npm:2.0.0"
  checksum: 10c0/288589b2484fe787f9e146f56c4be90b940018f17af1b152e4dde12309042ff5a2bf69e949aab8b8ac253948381529cc6f3e5a2427b73643a71ff177fa122b37
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2, csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/7986d40fc7979e9e6241f85db8d17060dd9a71bd53c894fa29d126061715e322a4cd47a00b0b8c710394854183d4120462b980b8554012acc1c0fa49df7ad38c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/f8a4534b5c69384d95ac18137d381f18a5cfae1f0fc1df0ef6feef51ef0d568606d970b69e02ea186c6c0f0eac77fe4e6ad96fec2569cc86c3afcc7475068c55
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10c0/fa7aa40078025b7810dcffc16df02c480573b7b53ef1205aa6a61533011005c1890e5ba17018c692ce7c900212b547262d33279fde801ad9843edc0863bf78c4
  languageName: node
  linkType: hard

"date-fns@npm:^2.29.3":
  version: 2.30.0
  resolution: "date-fns@npm:2.30.0"
  dependencies:
    "@babel/runtime": "npm:^7.21.0"
  checksum: 10c0/e4b521fbf22bc8c3db332bbfb7b094fd3e7627de0259a9d17c7551e2d2702608a7307a449206065916538e384f37b181565447ce2637ae09828427aed9cb5581
  languageName: node
  linkType: hard

"dayjs@npm:1.11.10":
  version: 1.11.10
  resolution: "dayjs@npm:1.11.10"
  checksum: 10c0/4de9af50639d47df87f2e15fa36bb07e0f9ed1e9c52c6caa1482788ee9a384d668f1dbd00c54f82aaab163db07d61d2899384b8254da3a9184fc6deca080e2fe
  languageName: node
  linkType: hard

"dayjs@npm:1.11.13":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: 10c0/a3caf6ac8363c7dade9d1ee797848ddcf25c1ace68d9fe8678ecf8ba0675825430de5d793672ec87b24a69bf04a1544b176547b2539982275d5542a7955f35b7
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.3.6":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"debug@npm:~4.3.1, debug@npm:~4.3.2":
  version: 4.3.7
  resolution: "debug@npm:4.3.7"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/1471db19c3b06d485a622d62f65947a19a23fbd0dd73f7fd3eafb697eec5360cde447fb075919987899b1a2096e85d35d4eb5a4de09a57600ac9cf7e6c8e768b
  languageName: node
  linkType: hard

"decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 10c0/85c39fe8fbf0482d4a1e224ef0119db5c1897f8503bcef8b826adff7a1b11414972f6fef2d7dec2ee0b4be3863cf64ac1439137ae9e6af23a3d8dcbe26a5b4b2
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.2":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 10c0/1f4fa54eb740414a816b3f6c24818fbfcabd74ac478391e9f4e2282c994127db02010ce804f3d08e38255493cfe68608b3f5c8e09fd6efc4ae46c807691f7a31
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10c0/e53481aaf1aa2c4082b5342be6b6d8ad9dfe387bc92ce197a66dea08bd4265904a087e75e464f14d1347cf2ac8afe1e4c16b266e0561cc5df29382d3c5f80044
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 10c0/db6c63864a9d3b7dc9def55d52764968a5af296de87c1b2cc71d8be8142e445208071953649e0386a8cc37cfcf9a2067a47207f1eb9ff250c2a269658fdae422
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/88a152319ffe1396ccc6ded510a3896e77efac7a1bfbaa174a7b00414a1747377e0bb525d303794a47cf30e805c2ec84e575758512c6e44a993076d29fd4e6c3
  languageName: node
  linkType: hard

"defu@npm:^6.1.4":
  version: 6.1.4
  resolution: "defu@npm:6.1.4"
  checksum: 10c0/2d6cc366262dc0cb8096e429368e44052fdf43ed48e53ad84cc7c9407f890301aa5fcb80d0995abaaf842b3949f154d060be4160f7a46cb2bc2f7726c81526f5
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"derive-valtio@npm:0.1.0":
  version: 0.1.0
  resolution: "derive-valtio@npm:0.1.0"
  peerDependencies:
    valtio: "*"
  checksum: 10c0/c64ed74e2bc140dafe080a58fd499f803cebaa89774b5d2bd0fea8054728912f1c715c5c370b4ff01ab9908b64828a7f8f0c968dc9efd0aee037e5679dd804d8
  languageName: node
  linkType: hard

"destr@npm:^2.0.3, destr@npm:^2.0.5":
  version: 2.0.5
  resolution: "destr@npm:2.0.5"
  checksum: 10c0/efabffe7312a45ad90d79975376be958c50069f1156b94c181199763a7f971e113bd92227c26b94a169c71ca7dbc13583b7e96e5164743969fc79e1ff153e646
  languageName: node
  linkType: hard

"detect-browser@npm:5.3.0, detect-browser@npm:^5.2.0":
  version: 5.3.0
  resolution: "detect-browser@npm:5.3.0"
  checksum: 10c0/88d49b70ce3836e7971345b2ebdd486ad0d457d1e4f066540d0c12f9210c8f731ccbed955fcc9af2f048f5d4629702a8e46bedf5bcad42ad49a3a0927bfd5a76
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: 10c0/4da0deae9f69e13bc37a0902d78bf7169480004b1fed3c19722d56cff578d16f0e11633b7fbf5fb6249181236c72e90024cbd68f0b9558ae06e281f47326d50d
  languageName: node
  linkType: hard

"dijkstrajs@npm:^1.0.1":
  version: 1.0.3
  resolution: "dijkstrajs@npm:1.0.3"
  checksum: 10c0/2183d61ac1f25062f3c3773f3ea8d9f45ba164a00e77e07faf8cc5750da966222d1e2ce6299c875a80f969190c71a0973042192c5624d5223e4ed196ff584c99
  languageName: node
  linkType: hard

"dom-helpers@npm:^5.0.1":
  version: 5.2.1
  resolution: "dom-helpers@npm:5.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.8.7"
    csstype: "npm:^3.0.2"
  checksum: 10c0/f735074d66dd759b36b158fa26e9d00c9388ee0e8c9b16af941c38f014a37fc80782de83afefd621681b19ac0501034b4f1c4a3bff5caa1b8667f0212b5e124c
  languageName: node
  linkType: hard

"dotenv@npm:16.4.5":
  version: 16.4.5
  resolution: "dotenv@npm:16.4.5"
  checksum: 10c0/48d92870076832af0418b13acd6e5a5a3e83bb00df690d9812e94b24aff62b88ade955ac99a05501305b8dc8f1b0ee7638b18493deb6fe93d680e5220936292f
  languageName: node
  linkType: hard

"dotenv@npm:^16.3.1":
  version: 16.5.0
  resolution: "dotenv@npm:16.5.0"
  checksum: 10c0/5bc94c919fbd955bf0ba44d33922a1e93d1078e64a1db5c30faeded1d996e7a83c55332cb8ea4fae5a9ca4d0be44cbceb95c5811e70f9f095298df09d1997dd9
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"duplexify@npm:^4.1.2":
  version: 4.1.3
  resolution: "duplexify@npm:4.1.3"
  dependencies:
    end-of-stream: "npm:^1.4.1"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^3.1.1"
    stream-shift: "npm:^1.0.2"
  checksum: 10c0/8a7621ae95c89f3937f982fe36d72ea997836a708471a75bb2a0eecde3330311b1e128a6dad510e0fd64ace0c56bff3484ed2e82af0e465600c82117eadfbda5
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"eciesjs@npm:^0.4.11":
  version: 0.4.15
  resolution: "eciesjs@npm:0.4.15"
  dependencies:
    "@ecies/ciphers": "npm:^0.2.3"
    "@noble/ciphers": "npm:^1.3.0"
    "@noble/curves": "npm:^1.9.1"
    "@noble/hashes": "npm:^1.8.0"
  checksum: 10c0/b5fc236810ff50e02f4d3155ab07f3a5d817ed7611fc63acef348e796a198a10bedf4adb152f512bcdc6ec90eb04a6f66606776bd56078d6d20bf3815bdc3f1c
  languageName: node
  linkType: hard

"ejs@npm:^3.1.6":
  version: 3.1.10
  resolution: "ejs@npm:3.1.10"
  dependencies:
    jake: "npm:^10.8.5"
  bin:
    ejs: bin/cli.js
  checksum: 10c0/52eade9e68416ed04f7f92c492183340582a36482836b11eab97b159fcdcfdedc62233a1bf0bf5e5e1851c501f2dca0e2e9afd111db2599e4e7f53ee29429ae1
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.160":
  version: 1.5.165
  resolution: "electron-to-chromium@npm:1.5.165"
  checksum: 10c0/20b91e67e7a8829a358c4a488e9b59b0e5f8d4cb075a70b9757bb21acf0fc751ca58ca7d9c6018bec74ac4bd42f7859e4ef37421c252a2275f642e12a32271d6
  languageName: node
  linkType: hard

"elliptic@npm:6.6.1, elliptic@npm:^6.5.7":
  version: 6.6.1
  resolution: "elliptic@npm:6.6.1"
  dependencies:
    bn.js: "npm:^4.11.9"
    brorand: "npm:^1.1.0"
    hash.js: "npm:^1.0.0"
    hmac-drbg: "npm:^1.0.1"
    inherits: "npm:^2.0.4"
    minimalistic-assert: "npm:^1.0.1"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10c0/8b24ef782eec8b472053793ea1e91ae6bee41afffdfcb78a81c0a53b191e715cbe1292aa07165958a9bbe675bd0955142560b1a007ffce7d6c765bcaf951a867
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encode-utf8@npm:^1.0.3":
  version: 1.0.3
  resolution: "encode-utf8@npm:1.0.3"
  checksum: 10c0/6b3458b73e868113d31099d7508514a5c627d8e16d1e0542d1b4e3652299b8f1f590c468e2b9dcdf1b4021ee961f31839d0be9d70a7f2a8a043c63b63c9b3a88
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0, end-of-stream@npm:^1.4.0, end-of-stream@npm:^1.4.1":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/870b423afb2d54bb8d243c63e07c170409d41e20b47eeef0727547aea5740bd6717aca45597a9f2745525667a6b804c1e7bede41f856818faee5806dd9ff3975
  languageName: node
  linkType: hard

"engine.io-client@npm:~6.6.1":
  version: 6.6.3
  resolution: "engine.io-client@npm:6.6.3"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.1"
    engine.io-parser: "npm:~5.2.1"
    ws: "npm:~8.17.1"
    xmlhttprequest-ssl: "npm:~2.1.1"
  checksum: 10c0/ebe0b1da6831d5a68564f9ffb80efe682da4f0538488eaffadf0bcf5177a8b4472cdb01d18a9f20dece2f8de30e2df951eb4635bef2f1b492e9f08a523db91a0
  languageName: node
  linkType: hard

"engine.io-parser@npm:~5.2.1":
  version: 5.2.3
  resolution: "engine.io-parser@npm:5.2.3"
  checksum: 10c0/ed4900d8dbef470ab3839ccf3bfa79ee518ea8277c7f1f2759e8c22a48f64e687ea5e474291394d0c94f84054749fd93f3ef0acb51fa2f5f234cc9d9d8e7c536
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10c0/ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9":
  version: 1.24.0
  resolution: "es-abstract@npm:1.24.0"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-negative-zero: "npm:^2.0.3"
    is-regex: "npm:^1.2.1"
    is-set: "npm:^2.0.3"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.4"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.4"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    stop-iteration-iterator: "npm:^1.1.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.19"
  checksum: 10c0/b256e897be32df5d382786ce8cce29a1dd8c97efbab77a26609bd70f2ed29fbcfc7a31758cb07488d532e7ccccdfca76c1118f2afe5a424cdc05ca007867c318
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10c0/c7e87467abb0b438639baa8139f701a06537d2b9bc758f23e8622c3b42fd0fdb5bde0f535686119e446dd9d5e4c0f238af4e14960f4771877cf818d023f6730b
  languageName: node
  linkType: hard

"es-toolkit@npm:1.33.0":
  version: 1.33.0
  resolution: "es-toolkit@npm:1.33.0"
  dependenciesMeta:
    "@trivago/prettier-plugin-sort-imports@4.3.0":
      unplugged: true
    prettier-plugin-sort-re-exports@0.0.1:
      unplugged: true
  checksum: 10c0/4c8dea3167a813070812e5c3f827fb677b4729b622c209cfad68dd5b449a008df6f3b515e675a4a8519618f52b87fe1d157c320668be871165f934a15c1d2f37
  languageName: node
  linkType: hard

"esbuild@npm:^0.21.3":
  version: 0.21.5
  resolution: "esbuild@npm:0.21.5"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.21.5"
    "@esbuild/android-arm": "npm:0.21.5"
    "@esbuild/android-arm64": "npm:0.21.5"
    "@esbuild/android-x64": "npm:0.21.5"
    "@esbuild/darwin-arm64": "npm:0.21.5"
    "@esbuild/darwin-x64": "npm:0.21.5"
    "@esbuild/freebsd-arm64": "npm:0.21.5"
    "@esbuild/freebsd-x64": "npm:0.21.5"
    "@esbuild/linux-arm": "npm:0.21.5"
    "@esbuild/linux-arm64": "npm:0.21.5"
    "@esbuild/linux-ia32": "npm:0.21.5"
    "@esbuild/linux-loong64": "npm:0.21.5"
    "@esbuild/linux-mips64el": "npm:0.21.5"
    "@esbuild/linux-ppc64": "npm:0.21.5"
    "@esbuild/linux-riscv64": "npm:0.21.5"
    "@esbuild/linux-s390x": "npm:0.21.5"
    "@esbuild/linux-x64": "npm:0.21.5"
    "@esbuild/netbsd-x64": "npm:0.21.5"
    "@esbuild/openbsd-x64": "npm:0.21.5"
    "@esbuild/sunos-x64": "npm:0.21.5"
    "@esbuild/win32-arm64": "npm:0.21.5"
    "@esbuild/win32-ia32": "npm:0.21.5"
    "@esbuild/win32-x64": "npm:0.21.5"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/fa08508adf683c3f399e8a014a6382a6b65542213431e26206c0720e536b31c09b50798747c2a105a4bbba1d9767b8d3615a74c2f7bf1ddf6d836cd11eb672de
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:9.1.0":
  version: 9.1.0
  resolution: "eslint-config-prettier@npm:9.1.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10c0/6d332694b36bc9ac6fdb18d3ca2f6ac42afa2ad61f0493e89226950a7091e38981b66bac2b47ba39d15b73fff2cd32c78b850a9cf9eed9ca9a96bfb2f3a2f10d
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:5.2.1":
  version: 5.2.1
  resolution: "eslint-plugin-prettier@npm:5.2.1"
  dependencies:
    prettier-linter-helpers: "npm:^1.0.0"
    synckit: "npm:^0.9.1"
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    eslint-config-prettier: "*"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 10c0/4bc8bbaf5bb556c9c501dcdff369137763c49ccaf544f9fa91400360ed5e3a3f1234ab59690e06beca5b1b7e6f6356978cdd3b02af6aba3edea2ffe69ca6e8b2
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:5.0.0":
  version: 5.0.0
  resolution: "eslint-plugin-react-hooks@npm:5.0.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 10c0/bcb74b421f32e4203a7100405b57aab85526be4461e5a1da01bc537969a30012d2ee209a2c2a6cac543833a27188ce1e6ad71e4628d0bb4a2e5365cad86c5002
  languageName: node
  linkType: hard

"eslint-plugin-react-refresh@npm:0.4.12":
  version: 0.4.12
  resolution: "eslint-plugin-react-refresh@npm:0.4.12"
  peerDependencies:
    eslint: ">=7"
  checksum: 10c0/33dd82450f7c5fa884c5c84ffaf9d9a8b363bc155432807dc09904c7db6ba724888fac4562b058268259aa7c9270b622ef411488011b3469a2add275ed5c2273
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.1.0":
  version: 8.3.0
  resolution: "eslint-scope@npm:8.3.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10c0/23bf54345573201fdf06d29efa345ab508b355492f6c6cc9e2b9f6d02b896f369b6dd5315205be94b8853809776c4d13353b85c6b531997b164ff6c3328ecf5b
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10c0/92708e882c0a5ffd88c23c0b404ac1628cf20104a108c745f240a13c332a11aac54f49a22d5762efbffc18ecbc9a580d1b7ad034bf5f3cc3307e5cbff2ec9820
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.1.0, eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 10c0/2ed81c663b147ca6f578312919483eb040295bbab759e5a371953456c636c5b49a559883e2677112453728d66293c0a4c90ab11cab3428cf02a0236d2e738269
  languageName: node
  linkType: hard

"eslint@npm:9.12.0":
  version: 9.12.0
  resolution: "eslint@npm:9.12.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.11.0"
    "@eslint/config-array": "npm:^0.18.0"
    "@eslint/core": "npm:^0.6.0"
    "@eslint/eslintrc": "npm:^3.1.0"
    "@eslint/js": "npm:9.12.0"
    "@eslint/plugin-kit": "npm:^0.2.0"
    "@humanfs/node": "npm:^0.16.5"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.3.1"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.2"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.1.0"
    eslint-visitor-keys: "npm:^4.1.0"
    espree: "npm:^10.2.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
    text-table: "npm:^0.2.0"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10c0/67cf6ea3ea28dcda7dd54aac33e2d4028eb36991d13defb0d2339c3eaa877d5dddd12cd4416ddc701a68bcde9e0bb9e65524c2e4e9914992c724f5b51e949dda
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.2.0":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: "npm:^8.14.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10c0/272beeaca70d0a1a047d61baff64db04664a33d7cfb5d144f84bc8a5c6194c6c8ebe9cc594093ca53add88baa23e59b01e69e8a0160ab32eac570482e165c462
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10c0/cb9065ec605f9da7a76ca6dadb0619dfb611e37a81e318732977d90fab50a256b95fee2d925fba7c2f3f0523aa16f91587246693bc09bc34d5a59575fe6e93d2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10c0/81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"estree-walker@npm:^1.0.1":
  version: 1.0.1
  resolution: "estree-walker@npm:1.0.1"
  checksum: 10c0/fa9e5f8c1bbe8d01e314c0f03067b64a4f22d4c58410fc5237060d0c15b81e58c23921c41acc60abbdab490f1fdfcbd6408ede2d03ca704454272e0244d61a55
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 10c0/53a6c54e2019b8c914dc395890153ffdc2322781acf4bd7d1a32d7aedc1710807bdcd866ac133903d5629ec601fbb50abe8c2e5553c7f5a0afdd9b6af6c945af
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"eth-block-tracker@npm:^7.1.0":
  version: 7.1.0
  resolution: "eth-block-tracker@npm:7.1.0"
  dependencies:
    "@metamask/eth-json-rpc-provider": "npm:^1.0.0"
    "@metamask/safe-event-emitter": "npm:^3.0.0"
    "@metamask/utils": "npm:^5.0.1"
    json-rpc-random-id: "npm:^1.0.1"
    pify: "npm:^3.0.0"
  checksum: 10c0/86a5cabef7fa8505c27b5fad1b2f0100c21fda11ad64a701f76eb4224f8c7edab706181fd0934e106a71f5465d57278448af401eb3e584b3529d943ddd4d7dfb
  languageName: node
  linkType: hard

"eth-json-rpc-filters@npm:^6.0.0":
  version: 6.0.1
  resolution: "eth-json-rpc-filters@npm:6.0.1"
  dependencies:
    "@metamask/safe-event-emitter": "npm:^3.0.0"
    async-mutex: "npm:^0.2.6"
    eth-query: "npm:^2.1.2"
    json-rpc-engine: "npm:^6.1.0"
    pify: "npm:^5.0.0"
  checksum: 10c0/69699460fd7837e13e42c1c74fbbfc44c01139ffd694e50235c78773c06059988be5c83dbe3a14d175ecc2bf3e385c4bfd3d6ab5d2d4714788b0b461465a3f56
  languageName: node
  linkType: hard

"eth-query@npm:^2.1.2":
  version: 2.1.2
  resolution: "eth-query@npm:2.1.2"
  dependencies:
    json-rpc-random-id: "npm:^1.0.0"
    xtend: "npm:^4.0.1"
  checksum: 10c0/ef28d14bfad14b8813c9ba8f9f0baf8778946a4797a222b8a039067222ac68aa3d9d53ed22a71c75b99240a693af1ed42508a99fd484cce2a7726822723346b7
  languageName: node
  linkType: hard

"eth-rpc-errors@npm:^4.0.2, eth-rpc-errors@npm:^4.0.3":
  version: 4.0.3
  resolution: "eth-rpc-errors@npm:4.0.3"
  dependencies:
    fast-safe-stringify: "npm:^2.0.6"
  checksum: 10c0/332cbc5a957b62bb66ea01da2a467da65026df47e6516a286a969cad74d6002f2b481335510c93f12ca29c46ebc8354e39e2240769d86184f9b4c30832cf5466
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^2.0.0":
  version: 2.2.1
  resolution: "ethereum-cryptography@npm:2.2.1"
  dependencies:
    "@noble/curves": "npm:1.4.2"
    "@noble/hashes": "npm:1.4.0"
    "@scure/bip32": "npm:1.4.0"
    "@scure/bip39": "npm:1.3.0"
  checksum: 10c0/c6c7626d393980577b57f709878b2eb91f270fe56116044b1d7afb70d5c519cddc0c072e8c05e4a335e05342eb64d9c3ab39d52f78bb75f76ad70817da9645ef
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"eventemitter2@npm:^6.4.9":
  version: 6.4.9
  resolution: "eventemitter2@npm:6.4.9"
  checksum: 10c0/b2adf7d9f1544aa2d95ee271b0621acaf1e309d85ebcef1244fb0ebc7ab0afa6ffd5e371535d0981bc46195ad67fd6ff57a8d1db030584dee69aa5e371a27ea7
  languageName: node
  linkType: hard

"eventemitter3@npm:5.0.1, eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 10c0/4ba5c00c506e6c786b4d6262cfbce90ddc14c10d4667e5c83ae993c9de88aa856033994dd2b35b83e8dc1170e224e66a319fa80adc4c32adcd2379bbc75da814
  languageName: node
  linkType: hard

"events@npm:3.3.0, events@npm:^3.3.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10c0/d6b6f2adbccbcda74ddbab52ed07db727ef52e31a61ed26db9feb7dc62af7fc8e060defa65e5f8af9449b86b52cc1a1f6a79f2eafcf4e62add2b7a1fa4a432f6
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"extension-port-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "extension-port-stream@npm:3.0.0"
  dependencies:
    readable-stream: "npm:^3.6.2 || ^4.4.2"
    webextension-polyfill: "npm:>=0.10.0 <1.0"
  checksum: 10c0/5645ba63b8e77996b75a5aae5a37d169fef13b65d575fa72b0cf9199c7ecd46df7ef76fbf7d6384b375544e48eb2c8912b62200320ed2a5ef9526a00fcc148d9
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 10c0/5c19af237edb5d5effda008c891a18a585f74bf12953be57923f17a3a4d0979565fc64dbc73b9e20926b9d895f5b690c618cbb969af0cf022e3222471220ad29
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0, fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10c0/111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fast-redact@npm:^3.0.0":
  version: 3.5.0
  resolution: "fast-redact@npm:3.5.0"
  checksum: 10c0/7e2ce4aad6e7535e0775bf12bd3e4f2e53d8051d8b630e0fa9e67f68cb0b0e6070d2f7a94b1d0522ef07e32f7c7cda5755e2b677a6538f1e9070ca053c42343a
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:^2.0.6":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: 10c0/d90ec1c963394919828872f21edaa3ad6f1dddd288d2bd4e977027afff09f5db40f94e39536d4646f7e01761d704d72d51dce5af1b93717f3489ef808f5f4e4d
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 10c0/74a513c2af0584448aee71ce56005185f81239eab7a2343110e5bad50c39ad4fb19c5a6f99783ead1cac7ccaf3461a6034fda89fffa2b30b6d99b9f21c2f9d29
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/ebc6e50ac7048daaeb8e64522a1ea7a26e92b3cee5cd1c7f2316cdca81ba543aa40a136b53891446ea5c3a67ec215fbaca87ad405f102dd97012f62916905630
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.5
  resolution: "fdir@npm:6.4.5"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/5d63330a1b97165e9b0fb20369fafc7cf826bc4b3e374efcb650bc77d7145ac01193b5da1a7591eab89ae6fd6b15cdd414085910b2a2b42296b1480c9f2677af
  languageName: node
  linkType: hard

"fflate@npm:^0.4.8":
  version: 0.4.8
  resolution: "fflate@npm:0.4.8"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"filelist@npm:^1.0.4":
  version: 1.0.4
  resolution: "filelist@npm:1.0.4"
  dependencies:
    minimatch: "npm:^5.0.1"
  checksum: 10c0/426b1de3944a3d153b053f1c0ebfd02dccd0308a4f9e832ad220707a6d1f1b3c9784d6cadf6b2f68f09a57565f63ebc7bcdc913ccf8012d834f472c46e596f41
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"filter-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "filter-obj@npm:1.1.0"
  checksum: 10c0/071e0886b2b50238ca5026c5bbf58c26a7c1a1f720773b8c7813d16ba93d0200de977af14ac143c5ac18f666b2cfc83073f3a5fe6a4e996c49e0863d5500fccf
  languageName: node
  linkType: hard

"find-root@npm:^1.1.0":
  version: 1.1.0
  resolution: "find-root@npm:1.1.0"
  checksum: 10c0/1abc7f3bf2f8d78ff26d9e00ce9d0f7b32e5ff6d1da2857bcdf4746134c422282b091c672cde0572cac3840713487e0a7a636af9aa1b74cb11894b447a521efa
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10c0/2c59d93e9faa2523e4fda6b4ada749bed432cfa28c8e251f33b25795e426a1c6dbada777afb1f74fcfff33934fdbdea921ee738fcc33e71adc9d6eca984a1cfc
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/5829165bd112c3c0e82be6c15b1a58fa9dcfaede3b3c54697a82fe4a62dd5ae5e8222956b448d2f98e331525f05d00404aba7d696de9e761ef6e42fdc780244f
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10c0/0e0b50f6a843a282637d43674d1fb278dda1dd85f4f99b640024cfb10b85058aac0cc781bf689d5fe50b4b7f638e91e548560723a4e76e04fe96ae35ef039cee
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.2
  resolution: "form-data@npm:4.0.2"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/e534b0cf025c831a0929bf4b9bbe1a9a6b03e273a8161f9947286b9b13bf8fb279c6944aae0070c4c311100c6d6dbb815cd955dc217728caf73fad8dc5b8ee9c
  languageName: node
  linkType: hard

"fs-extra@npm:^9.0.1":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: "npm:^1.0.0"
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/9b808bd884beff5cb940773018179a6b94a966381d005479f00adda6b44e5e3d4abf765135773d849cc27efe68c349e4a7b86acd7d3306d5932c14f3a4b17a92
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10c0/e920a2ab52663005f3cbe7ee3373e3c71c1fb5558b0b0548648cdf3e51961085032458e26c71ff1a8c8c20e7ee7caeb03d43a5d1fa8610c459333323a2e71253
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10c0/33e77fd29bddc2d9bb78ab3eb854c165909201f88c75faa8272e35899e2d35a8a642a15e7420ef945e1f64a9670d6aa3ec744106b2aa42be68ca5114025954ca
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1, get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10c0/c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-own-enumerable-property-symbols@npm:^3.0.0":
  version: 3.0.2
  resolution: "get-own-enumerable-property-symbols@npm:3.0.2"
  checksum: 10c0/103999855f3d1718c631472437161d76962cbddcd95cc642a34c07bfb661ed41b6c09a9c669ccdff89ee965beb7126b80eec7b2101e20e31e9cc6c4725305e10
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/d6a7d6afca375779a4b307738c9e80dbf7afc0bdbe5948768d54ab9653c865523d8920e670991a925936eb524b7cb6a6361d199a760b21d0ca7620194455aa4b
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.1.6":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"glob@npm:^9.3.2":
  version: 9.3.5
  resolution: "glob@npm:9.3.5"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    minimatch: "npm:^8.0.2"
    minipass: "npm:^4.2.4"
    path-scurry: "npm:^1.6.1"
  checksum: 10c0/2f6c2b9ee019ee21dc258ae97a88719614591e4c979cb4580b1b9df6f0f778a3cb38b4bdaf18dfa584637ea10f89a3c5f2533a5e449cf8741514ad18b0951f2e
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10c0/b96ff42620c9231ad468d4c58ff42afee7777ee1c963013ff8aabe095a451d0ceeb8dcd8ef4cbd64d2538cef45f787a78ba3a9574f4a634438963e334471302d
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10c0/9d156f313af79d80b1566b93e19285f481c591ad6d0d319b4be5e03750d004dde40a39a0f26f7e635f9007a3600802f53ecd85a759b86f109e80a5f705e01846
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10c0/e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"graphql-tag@npm:^2.12.6":
  version: 2.12.6
  resolution: "graphql-tag@npm:2.12.6"
  dependencies:
    tslib: "npm:^2.1.0"
  peerDependencies:
    graphql: ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10c0/7763a72011bda454ed8ff1a0d82325f43ca6478e4ce4ab8b7910c4c651dd00db553132171c04d80af5d5aebf1ef6a8a9fd53ccfa33b90ddc00aa3d4be6114419
  languageName: node
  linkType: hard

"graphql@npm:^16.9.0":
  version: 16.11.0
  resolution: "graphql@npm:16.11.0"
  checksum: 10c0/124da7860a2292e9acf2fed0c71fc0f6a9b9ca865d390d112bdd563c1f474357141501c12891f4164fe984315764736ad67f705219c62f7580681d431a85db88
  languageName: node
  linkType: hard

"h3@npm:^1.15.2":
  version: 1.15.3
  resolution: "h3@npm:1.15.3"
  dependencies:
    cookie-es: "npm:^1.2.2"
    crossws: "npm:^0.3.4"
    defu: "npm:^6.1.4"
    destr: "npm:^2.0.5"
    iron-webcrypto: "npm:^1.2.1"
    node-mock-http: "npm:^1.0.0"
    radix3: "npm:^1.1.2"
    ufo: "npm:^1.6.1"
    uncrypto: "npm:^0.1.3"
  checksum: 10c0/4b83daceda6f39cd508d56382dc3a83ef14453d0119ada290c7fda3c69d907ccaf2547fd233f3e001a9ffae2cde4e2543e4361d714c29fb6ec664f604d5b84a3
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10c0/2de0cdc4a1ccf7a1e75ffede1876994525ac03cc6f5ae7392d3415dd475cd9eee5bceec63669ab61aa997ff6cceebb50ef75561c7002bed8988de2b9d1b40788
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10c0/46538dddab297ec2f43923c3d35237df45d8c55a6fc1067031e04c13ed8a9a8f94954460632fd4da84c31a1721eefee16d901cbb1ae9602bab93bb6e08f93b95
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hash.js@npm:1.1.7, hash.js@npm:^1.0.0, hash.js@npm:^1.0.3":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: "npm:^2.0.3"
    minimalistic-assert: "npm:^1.0.1"
  checksum: 10c0/41ada59494eac5332cfc1ce6b7ebdd7b88a3864a6d6b08a3ea8ef261332ed60f37f10877e0c825aaa4bddebf164fbffa618286aeeec5296675e2671cbfa746c4
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hey-listen@npm:^1.0.8":
  version: 1.0.8
  resolution: "hey-listen@npm:1.0.8"
  checksum: 10c0/38db3028b4756f3d536c0f6a92da53bad577ab649b06dddfd0a4d953f9a46bbc6a7f693c8c5b466a538d6d23dbc469260c848427f0de14198a2bbecbac37b39e
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: "npm:^1.0.3"
    minimalistic-assert: "npm:^1.0.0"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10c0/f3d9ba31b40257a573f162176ac5930109816036c59a09f901eb2ffd7e5e705c6832bedfff507957125f2086a0ab8f853c0df225642a88bf1fcaea945f20600d
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.3.1, hoist-non-react-statics@npm:^3.3.2":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: "npm:^16.7.0"
  checksum: 10c0/fe0889169e845d738b59b64badf5e55fa3cf20454f9203d1eb088df322d49d4318df774828e789898dcb280e8a5521bb59b3203385662ca5e9218a6ca5820e74
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10c0/45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/6dd639f03434003577c62b27cafdb864784ef19b2de430d8ae2a1d45e31c4fd60719e5637b44db1a88a046934307da7089e03d6089ec3ddacc1189d8de8897d1
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"idb-keyval@npm:^6.2.1":
  version: 6.2.2
  resolution: "idb-keyval@npm:6.2.2"
  checksum: 10c0/b52f0d2937cc2ec9f1da536b0b5c0875af3043ca210714beaffead4ec1f44f2ad322220305fd024596203855224d9e3523aed83e971dfb62ddc21b5b1721aeef
  languageName: node
  linkType: hard

"idb@npm:^7.0.1":
  version: 7.1.1
  resolution: "idb@npm:7.1.1"
  checksum: 10c0/72418e4397638797ee2089f97b45fc29f937b830bc0eb4126f4a9889ecf10320ceacf3a177fe5d7ffaf6b4fe38b20bbd210151549bfdc881db8081eed41c870d
  languageName: node
  linkType: hard

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.3.1":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10c0/f9f652c957983634ded1e7f02da3b559a0d4cc210fca3792cb67f1b153623c9c42efdc1c4121af171e295444459fc4a9201101fb041b1104a3c000bccb188337
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0":
  version: 4.3.7
  resolution: "immutable@npm:4.3.7"
  checksum: 10c0/9b099197081b22f6433003e34929da8ecddbbdc1474cdc8aa3b7669dee4adda349c06143de22def36016d1b6de5322b043eccd7a11db1dad2ca85dad4fff5435
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/bf8cc494872fef783249709385ae883b447e3eb09db0ebd15dcead7d9afe7224dad7bd7591c6b73b0b19b3c0f9640eb8ee884f01cfaf2887ab995b0b36a0cbec
  languageName: node
  linkType: hard

"import-from-esm@npm:^1.3.3":
  version: 1.3.4
  resolution: "import-from-esm@npm:1.3.4"
  dependencies:
    debug: "npm:^4.3.4"
    import-meta-resolve: "npm:^4.0.0"
  checksum: 10c0/fcd42ead421892e1d9dbc90e510f45c7d3b58887c35077cf2318e4aa39b52c07c06e2b54efd16dfe8e712421439c23794d18a5e8956cca237fc90790ed8e2241
  languageName: node
  linkType: hard

"import-meta-resolve@npm:^4.0.0":
  version: 4.1.0
  resolution: "import-meta-resolve@npm:4.1.0"
  checksum: 10c0/42f3284b0460635ddf105c4ad99c6716099c3ce76702602290ad5cbbcd295700cbc04e4bdf47bacf9e3f1a4cec2e1ff887dabc20458bef398f9de22ddff45ef5
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/03966f5e259b009a9bf1a78d60da920df198af4318ec004f57b8aef1dd3fe377fbc8cce63a96e8c810010302654de89f9e19de1cd8ad0061d15be28a695465c7
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"iron-webcrypto@npm:^1.2.1":
  version: 1.2.1
  resolution: "iron-webcrypto@npm:1.2.1"
  checksum: 10c0/5cf27c6e2bd3ef3b4970e486235fd82491ab8229e2ed0ac23307c28d6c80d721772a86ed4e9fe2a5cabadd710c2f024b706843b40561fb83f15afee58f809f66
  languageName: node
  linkType: hard

"is-arguments@npm:^1.0.4":
  version: 1.2.0
  resolution: "is-arguments@npm:1.2.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/6377344b31e9fcb707c6751ee89b11f132f32338e6a782ec2eac9393b0cbd32235dad93052998cda778ee058754860738341d8114910d50ada5615912bb929fc
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/c5c9f25606e86dbb12e756694afbbff64bc8b348d1bc989324c037e1068695131930199d6ad381952715dad3a9569333817f0b1a72ce5af7f883ce802e49c83d
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10c0/e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/d70c236a5e82de6fc4d44368ffd0c2fee2b088b893511ce21e679da275a5ecc6015ff59a7d7e1bdd7ca39f71a8dbdd253cf8cce5c6b3c91cdd5b42b5ce677298
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10c0/f4f4b905ceb195be90a6ea7f34323bf1c18e3793f18922e3e9a73c684c29eeeeff5175605c3a3a74cc38185fe27758f07efba3dbae812e5c5afbc0d2316b40e4
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/36ff6baf6bd18b3130186990026f5a95c709345c39cd368468e6c1b6ab52201e9fd26d8e1f4c066357b4938b0f0401e1a5000e08257787c1a02f3a719457001e
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10c0/ceebaeb9d92e8adee604076971dd6000d38d6afc40bb843ea8e45c5579b57671c3f3b50d7f04869618242c6cee08d1b67806a8cb8edaaaf7c0748b3720d6066f
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10c0/ef3548a99d7e7f1370ce21006baca6d40c73e9f15c941f89f0049c79714c873d03b02dae1c64b3f861f55163ecc16da06506c5b8a1d4f16650b3d9351c380153
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/1a4d199c8e9e9cac5128d32e6626fa7805175af9df015620ac0d5d45854ccf348ba494679d872d37301032e35a54fc7978fba1687e8721b2139aea7870cafa2f
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 10c0/e828365958d155f90c409cdbe958f64051d99e8aedc2c8c4cd7c89dcf35329daed42f7b99346f7828df013e27deb8f721cf9408ba878c76eb9e8290235fbcdcc
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/818dff679b64f19e228a8205a1e2d09989a98e98def3a817f889208cfcbf918d321b251aadf2c05918194803ebd2eb01b14fc9d0b2bea53d984f4137bfca5e97
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10, is-generator-function@npm:^1.0.7":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/fdfa96c8087bf36fc4cd514b474ba2ff404219a4dd4cfa6cf5426404a1eed259bdcdb98f082a71029a48d01f27733e3436ecc6690129a7ec09cb0434bee03a2a
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10c0/2c4d431b74e00fdda7162cd8e4b763d6f6f217edf97d4f8538b94b8702b150610e2c64961340015fe8df5b1fcee33ccd2e9b62619c4a8a3a155f8de6d6d355fc
  languageName: node
  linkType: hard

"is-module@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-module@npm:1.0.0"
  checksum: 10c0/795a3914bcae7c26a1c23a1e5574c42eac13429625045737bf3e324ce865c0601d61aee7a5afbca1bee8cb300c7d9647e7dc98860c9bdbc3b7fdc51d8ac0bffc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: 10c0/bcdcf6b8b9714063ffcfa9929c575ac69bfdabb8f4574ff557dfc086df2836cf07e3906f5bbc4f2a5c12f8f3ba56af640c843cdfc74da8caed86c7c7d66fd08e
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/97b451b41f25135ff021d85c436ff0100d84a039bb87ffd799cbcdbea81ef30c464ced38258cdd34f080be08fc3b076ca1f472086286d2aa43521d6ec6a79f53
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-obj@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-obj@npm:1.0.1"
  checksum: 10c0/5003acba0af7aa47dfe0760e545a89bbac89af37c12092c3efadc755372cdaec034f130e7a3653a59eb3c1843cfc72ca71eaf1a6c3bafe5a0bab3611a47f9945
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/1d3715d2b7889932349241680032e85d0b492cfcb045acb75ffc2c3085e8d561184f1f7e84b6f8321935b4aea39bc9c6ba74ed595b57ce4881a51dfdbc214e04
  languageName: node
  linkType: hard

"is-regexp@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-regexp@npm:1.0.0"
  checksum: 10c0/34cacda1901e00f6e44879378f1d2fa96320ea956c1bec27713130aaf1d44f6e7bd963eed28945bfe37e600cb27df1cf5207302680dad8bdd27b9baff8ecf611
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10c0/f73732e13f099b2dc879c2a12341cfc22ccaca8dd504e6edae26484bd5707a35d503fba5b4daad530a9b088ced1ae6c9d8200fd92e09b428fe14ea79ce8080b7
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/65158c2feb41ff1edd6bbd6fd8403a69861cf273ff36077982b5d4d68e1d59278c71691216a4a64632bd76d4792d4d1d2553901b6666d84ade13bba5ea7bc7db
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/2f518b4e47886bb81567faba6ffd0d8a8333cf84336e2e78bf160693972e32ad00fe84b0926491cc598dee576fdc55642c92e62d0cbe96bf36f643b6f956f94d
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/f08f3e255c12442e833f75a9e2b84b2d4882fdfd920513cf2a4a2324f0a5b076c8fd913778e3ea5d258d5183e9d92c0cd20e04b03ab3df05316b049b2670af1e
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15, is-typed-array@npm:^1.1.3":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/415511da3669e36e002820584e264997ffe277ff136643a3126cc949197e6ca3334d0f12d084e83b1994af2e9c8141275c741cf2b7da5a2ff62dd0cac26f76c4
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10c0/443c35bb86d5e6cc5929cd9c75a4024bb0fff9586ed50b092f94e700b89c43a33b186b76dbc6d54f3d3d09ece689ab38dcdc1af6a482cbe79c0f2da0a17f1299
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/8e0a9c07b0c780949a100e2cab2b5560a48ecd4c61726923c1a9b77b6ab0aa0046c9e7fb2206042296817045376dee2c8ab1dabe08c7c3dfbf195b01275a085b
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/6491eba08acb8dc9532da23cb226b7d0192ede0b88f16199e592e4769db0a077119c1f5d2283d1e0d16d739115f70046e887e477eb0e66cd90e1bb29f28ba647
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 10c0/a6fa2d370d21be487c0165c7a440d567274fbba1a817f2f0bfa41cc5e3af25041d84267baa22df66696956038a43973e72fca117918c91431920bdef490fa25e
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10c0/4199f14a7a13da2177c66c31080008b7124331956f47bca57dd0b6ea9f11687aa25e565a2c7a2b519bc86988d10398e3049a1f5df13c9f6b7664154690ae79fd
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isows@npm:1.0.6":
  version: 1.0.6
  resolution: "isows@npm:1.0.6"
  peerDependencies:
    ws: "*"
  checksum: 10c0/f89338f63ce2f497d6cd0f86e42c634209328ebb43b3bdfdc85d8f1589ee75f02b7e6d9e1ba274101d0f6f513b1b8cbe6985e6542b4aaa1f0c5fd50d9c1be95c
  languageName: node
  linkType: hard

"isows@npm:1.0.7":
  version: 1.0.7
  resolution: "isows@npm:1.0.7"
  peerDependencies:
    ws: "*"
  checksum: 10c0/43c41fe89c7c07258d0be3825f87e12da8ac9023c5b5ae6741ec00b2b8169675c04331ea73ef8c172d37a6747066f4dc93947b17cd369f92828a3b3e741afbda
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jake@npm:^10.8.5":
  version: 10.9.2
  resolution: "jake@npm:10.9.2"
  dependencies:
    async: "npm:^3.2.3"
    chalk: "npm:^4.0.2"
    filelist: "npm:^1.0.4"
    minimatch: "npm:^3.1.2"
  bin:
    jake: bin/cli.js
  checksum: 10c0/c4597b5ed9b6a908252feab296485a4f87cba9e26d6c20e0ca144fb69e0c40203d34a2efddb33b3d297b8bd59605e6c1f44f6221ca1e10e69175ecbf3ff5fe31
  languageName: node
  linkType: hard

"jotai-effect@npm:1.0.3":
  version: 1.0.3
  resolution: "jotai-effect@npm:1.0.3"
  peerDependencies:
    jotai: ">=2.5.0"
  checksum: 10c0/f8877f909446042c42ceffe5fbf1c42f5517525655548dfd9ecc60ebe4140581ae0bdef2f3ae84da7c6c8aabd2852699b62c1eba071fe46d178298dd829509cc
  languageName: node
  linkType: hard

"jotai-scope@npm:0.6.0":
  version: 0.6.0
  resolution: "jotai-scope@npm:0.6.0"
  peerDependencies:
    jotai: ">=2.5.0"
    react: ">=17.0.0"
  checksum: 10c0/aa51b7f231aff8307705a26eae2a71c51df1c6c1974100e6248e639e3cefac9f65688cf64a69a21a22391c6d8b78a3552ded213e85a21e6209c93dd6479b6226
  languageName: node
  linkType: hard

"jotai-tanstack-query@npm:0.8.8":
  version: 0.8.8
  resolution: "jotai-tanstack-query@npm:0.8.8"
  peerDependencies:
    "@tanstack/query-core": "*"
    jotai: ">=2.0.0"
  checksum: 10c0/a4f60b433abd7f63a80576c90f3a66a956ee1dd33033c0e3703d77be9f62ff64218825e2f051407775b58495b21cd1e6d80a1422bbc4024139a8888f8ad643ea
  languageName: node
  linkType: hard

"jotai@npm:2.10.1":
  version: 2.10.1
  resolution: "jotai@npm:2.10.1"
  peerDependencies:
    "@types/react": ">=17.0.0"
    react: ">=17.0.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    react:
      optional: true
  checksum: 10c0/9ec75716e1bae68b41abb2ca8451de0f7b35dd9107064c92d6cca284ebc7cbf884e37656d5bca7db3223b4a21b885b29b490e5922dde7d28d47ff22a058cbae3
  languageName: node
  linkType: hard

"js-sha3@npm:0.8.0":
  version: 0.8.0
  resolution: "js-sha3@npm:0.8.0"
  checksum: 10c0/43a21dc7967c871bd2c46cb1c2ae97441a97169f324e509f382d43330d8f75cf2c96dba7c806ab08a425765a9c847efdd4bffbac2d99c3a4f3de6c0218f40533
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/ef22148f9e793180b14d8a145ee6f9f60f301abf443288117b4b6c53d0ecd58354898dc506ccbb553a5f7827965cd38bc5fb726575aae93c5e8915e2de8290e1
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10c0/0d1c91569d9588e7eef2b49b59851f297f3ab93c7b35c7c221e288099322be6b562767d11e4821da500f3219542b9afd2e54c5dc573107c1126ed1080f8e96d7
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10c0/140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-rpc-engine@npm:^6.1.0":
  version: 6.1.0
  resolution: "json-rpc-engine@npm:6.1.0"
  dependencies:
    "@metamask/safe-event-emitter": "npm:^2.0.0"
    eth-rpc-errors: "npm:^4.0.2"
  checksum: 10c0/29c480f88152b1987ab0f58f9242ee163d5a7e95cd0d8ae876c08b21657022b82f6008f5eecd048842fb7f6fc3b4e364fde99ca620458772b6abd1d2c1e020d5
  languageName: node
  linkType: hard

"json-rpc-random-id@npm:^1.0.0, json-rpc-random-id@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-rpc-random-id@npm:1.0.1"
  checksum: 10c0/8d4594a3d4ef5f4754336e350291a6677fc6e0d8801ecbb2a1e92e50ca04a4b57e5eb97168a4b2a8e6888462133cbfee13ea90abc008fb2f7279392d83d3ee7a
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10c0/71e30015d7f3d6dc1c316d6298047c8ef98a06d31ad064919976583eb61e1018a60a0067338f0f79cabc00d84af3fcc489bd48ce8a46ea165d9541ba17fb30c6
  languageName: node
  linkType: hard

"json-schema@npm:^0.4.0":
  version: 0.4.0
  resolution: "json-schema@npm:0.4.0"
  checksum: 10c0/d4a637ec1d83544857c1c163232f3da46912e971d5bf054ba44fdb88f07d8d359a462b4aec46f2745efbc57053365608d88bc1d7b1729f7b4fc3369765639ed3
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10c0/cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json5@npm:^2.2.0, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jsonpointer@npm:^5.0.0":
  version: 5.0.1
  resolution: "jsonpointer@npm:5.0.1"
  checksum: 10c0/89929e58b400fcb96928c0504fcf4fc3f919d81e9543ceb055df125538470ee25290bb4984251e172e6ef8fcc55761eb998c118da763a82051ad89d4cb073fe7
  languageName: node
  linkType: hard

"keccak@npm:^3.0.3":
  version: 3.0.4
  resolution: "keccak@npm:3.0.4"
  dependencies:
    node-addon-api: "npm:^2.0.0"
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.2.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/153525c1c1f770beadb8f8897dec2f1d2dcbee11d063fe5f61957a5b236bfd3d2a111ae2727e443aa6a848df5edb98b9ef237c78d56df49087b0ca8a232ca9cd
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10c0/aa52f3c5e18e16bb6324876bb8b59dd02acf782a4b789c7b2ae21107fab95fab3890ed448d4f8dba80ce05391eeac4bfabb4f02a20221342982f806fa2cf271e
  languageName: node
  linkType: hard

"keyvaluestorage-interface@npm:^1.0.0":
  version: 1.0.0
  resolution: "keyvaluestorage-interface@npm:1.0.0"
  checksum: 10c0/0e028ebeda79a4e48c7e36708dbe7ced233c7a1f1bc925e506f150dd2ce43178bee8d20361c445bd915569709d9dc9ea80063b4d3c3cf5d615ab43aa31d3ec3d
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 10c0/cd778ba3fbab0f4d0500b7e87d1f6e1f041507c56fdcd47e8256a3012c98aaee371d4c15e0a76e0386107af2d42e2b7466160a2d80688aaa03e66e49949f42df
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10c0/effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10c0/3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"lit-element@npm:^3.3.0":
  version: 3.3.3
  resolution: "lit-element@npm:3.3.3"
  dependencies:
    "@lit-labs/ssr-dom-shim": "npm:^1.1.0"
    "@lit/reactive-element": "npm:^1.3.0"
    lit-html: "npm:^2.8.0"
  checksum: 10c0/f44c12fa3423a4e9ca5b84651410687e14646bb270ac258325e6905affac64a575f041f8440377e7ebaefa3910b6f0d6b8b1e902cb1aa5d0849b3fdfbf4fb3b6
  languageName: node
  linkType: hard

"lit-element@npm:^4.0.0, lit-element@npm:^4.2.0":
  version: 4.2.0
  resolution: "lit-element@npm:4.2.0"
  dependencies:
    "@lit-labs/ssr-dom-shim": "npm:^1.2.0"
    "@lit/reactive-element": "npm:^2.1.0"
    lit-html: "npm:^3.3.0"
  checksum: 10c0/20577f2092ac1e1bd82fba2bbc9ce0122b35dc2495906d3fbcb437c3727b9c8ed1c0691b8b859f65a51e910db1341d95233c117e1e1c88c450b30e2d3b62fdb8
  languageName: node
  linkType: hard

"lit-html@npm:^2.8.0":
  version: 2.8.0
  resolution: "lit-html@npm:2.8.0"
  dependencies:
    "@types/trusted-types": "npm:^2.0.2"
  checksum: 10c0/90057dee050803823ac884c1355b0213ab8c05fbe2ec63943c694b61aade5d36272068f3925f45a312835e504f9c9784738ef797009f0a756a750351eafb52d5
  languageName: node
  linkType: hard

"lit-html@npm:^3.1.0, lit-html@npm:^3.3.0":
  version: 3.3.0
  resolution: "lit-html@npm:3.3.0"
  dependencies:
    "@types/trusted-types": "npm:^2.0.2"
  checksum: 10c0/c1065048d89d93df6a46cdeed9abd637ae9bcc0847ee108dccbb2e1627a4074074e1d3ac9360e08a736d76f8c76b2c88166dbe465406da123b9137e29c2e0034
  languageName: node
  linkType: hard

"lit@npm:2.8.0":
  version: 2.8.0
  resolution: "lit@npm:2.8.0"
  dependencies:
    "@lit/reactive-element": "npm:^1.6.0"
    lit-element: "npm:^3.3.0"
    lit-html: "npm:^2.8.0"
  checksum: 10c0/bf33c26b1937ee204aed1adbfa4b3d43a284e85aad8ea9763c7865365917426eded4e5888158b4136095ea42054812561fe272862b61775f1198fad3588b071f
  languageName: node
  linkType: hard

"lit@npm:3.1.0":
  version: 3.1.0
  resolution: "lit@npm:3.1.0"
  dependencies:
    "@lit/reactive-element": "npm:^2.0.0"
    lit-element: "npm:^4.0.0"
    lit-html: "npm:^3.1.0"
  checksum: 10c0/7ca12c1b1593373d16b51b2220677d8936b4061de4f278ef2a85f15726bb4365a8eed89a0294816a10d6124dca81f02e83b5dfed9a6031e135a7bc68924eea6b
  languageName: node
  linkType: hard

"lit@npm:3.3.0":
  version: 3.3.0
  resolution: "lit@npm:3.3.0"
  dependencies:
    "@lit/reactive-element": "npm:^2.1.0"
    lit-element: "npm:^4.2.0"
    lit-html: "npm:^3.3.0"
  checksum: 10c0/27e6d109c04c8995f47c82a546407c5ed8d399705f9511d1f3ee562eb1ab4bc00fae5ec897da55fb50f202b2a659466e23cccd809d039e7d4f935fcecb2bc6a7
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10c0/33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 10c0/762998a63e095412b6099b8290903e0a8ddcb353ac6e2e0f2d7e7d03abd4275fe3c689d88960eb90b0dde4f177554d51a690f22a343932ecbc50a5d111849987
  languageName: node
  linkType: hard

"lodash.isequal@npm:4.5.0":
  version: 4.5.0
  resolution: "lodash.isequal@npm:4.5.0"
  checksum: 10c0/dfdb2356db19631a4b445d5f37868a095e2402292d59539a987f134a8778c62a2810c2452d11ae9e6dcac71fc9de40a6fedcb20e2952a15b431ad8b29e50e28f
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash.sortby@npm:^4.7.0":
  version: 4.7.0
  resolution: "lodash.sortby@npm:4.7.0"
  checksum: 10c0/fc48fb54ff7669f33bb32997cab9460757ee99fafaf72400b261c3e10fde21538e47d8cfcbe6a25a31bcb5b7b727c27d52626386fc2de24eb059a6d64a89cdf5
  languageName: node
  linkType: hard

"lodash@npm:4.17.21, lodash@npm:^4.17.20":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10c0/655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0, lru-cache@npm:^10.4.3":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"magic-string@npm:0.30.8":
  version: 0.30.8
  resolution: "magic-string@npm:0.30.8"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.4.15"
  checksum: 10c0/51a1f06f678c082aceddfb5943de9b6bdb88f2ea1385a1c2adf116deb73dfcfa50df6c222901d691b529455222d4d68d0b28be5689ac6f69b3baa3462861f922
  languageName: node
  linkType: hard

"magic-string@npm:^0.25.0, magic-string@npm:^0.25.7":
  version: 0.25.9
  resolution: "magic-string@npm:0.25.9"
  dependencies:
    sourcemap-codec: "npm:^1.4.8"
  checksum: 10c0/37f5e01a7e8b19a072091f0b45ff127cda676232d373ce2c551a162dd4053c575ec048b9cbb4587a1f03adb6c5d0fd0dd49e8ab070cd2c83a4992b2182d9cb56
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"micro-ftch@npm:^0.3.1":
  version: 0.3.1
  resolution: "micro-ftch@npm:0.3.1"
  checksum: 10c0/b87d35a52aded13cf2daca8d4eaa84e218722b6f83c75ddd77d74f32cc62e699a672e338e1ee19ceae0de91d19cc24dcc1a7c7d78c81f51042fe55f01b196ed3
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.5, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: 10c0/96730e5601cd31457f81a296f521eb56036e6f69133c0b18c13fe941109d53ad23a4204d946a0d638d7f3099482a0cec8c9bb6d642604612ce43ee536be3dddd
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 10c0/790ecec8c5c73973a4fbf2c663d911033e8494d5fb0960a4500634766ab05d6107d20af896ca2132e7031741f19888154d44b2408ada0852446705441383e9f8
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/3defdfd230914f22a8da203747c42ee3c405c39d4d37ffda284dac5e45b7e1f6c49aa8be606509002898e73091ff2a3bbfc59c2c6c71d4660609f63aa92f98e3
  languageName: node
  linkType: hard

"minimatch@npm:^8.0.2":
  version: 8.0.4
  resolution: "minimatch@npm:8.0.4"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/a0a394c356dd5b4cb7f821720841a82fa6f07c9c562c5b716909d1b6ec5e56a7e4c4b5029da26dd256b7d2b3a3f38cbf9ddd8680e887b9b5282b09c05501c1ca
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^4.2.4":
  version: 4.2.8
  resolution: "minipass@npm:4.2.8"
  checksum: 10c0/4ea76b030d97079f4429d6e8a8affd90baf1b6a1898977c8ccce4701c5a2ba2792e033abc6709373f25c2c4d4d95440d9d5e9464b46b7b76ca44d2ce26d939ce
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mipd@npm:0.0.7":
  version: 0.0.7
  resolution: "mipd@npm:0.0.7"
  peerDependencies:
    typescript: ">=5.0.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/c536e4fcdc15793b4538f72da389f8901a7eccb2e1eb55d8878f234a45f1c271064650e76fa2967b94743e19cc32ceab3c7b1e0dc614e28a45b0bbd6c987795d
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"motion@npm:10.16.2":
  version: 10.16.2
  resolution: "motion@npm:10.16.2"
  dependencies:
    "@motionone/animation": "npm:^10.15.1"
    "@motionone/dom": "npm:^10.16.2"
    "@motionone/svelte": "npm:^10.16.2"
    "@motionone/types": "npm:^10.15.1"
    "@motionone/utils": "npm:^10.15.1"
    "@motionone/vue": "npm:^10.16.2"
  checksum: 10c0/ea3fa2c7ce881824bcefa39b96b5e2b802d4b664b8a64644cded11197c9262e2a5b14b2e9516940e06cec37d3c39e4c79b26825c447f71ba1cfd7e3370efbe61
  languageName: node
  linkType: hard

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"multiformats@npm:^9.4.2":
  version: 9.9.0
  resolution: "multiformats@npm:9.9.0"
  checksum: 10c0/1fdb34fd2fb085142665e8bd402570659b50a5fae5994027e1df3add9e1ce1283ed1e0c2584a5c63ac0a58e871b8ee9665c4a99ca36ce71032617449d48aa975
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"node-addon-api@npm:^2.0.0":
  version: 2.0.2
  resolution: "node-addon-api@npm:2.0.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/ade6c097ba829fa4aee1ca340117bb7f8f29fdae7b777e343a9d5cbd548481d1f0894b7b907d23ce615c70d932e8f96154caed95c3fa935cfe8cf87546510f64
  languageName: node
  linkType: hard

"node-addon-api@npm:^7.0.0":
  version: 7.1.1
  resolution: "node-addon-api@npm:7.1.1"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/fb32a206276d608037fa1bcd7e9921e177fe992fc610d098aa3128baca3c0050fc1e014fa007e9b3874cf865ddb4f5bd9f43ccb7cbbbe4efaff6a83e920b17e9
  languageName: node
  linkType: hard

"node-fetch-native@npm:^1.6.4, node-fetch-native@npm:^1.6.6":
  version: 1.6.6
  resolution: "node-fetch-native@npm:1.6.6"
  checksum: 10c0/8c12dab0e640d8bc126a03d604af9cf3fc1b87f2cda5af0c71601079d5ed835c1dc149c7042b61c83f252a382e1cf1e541788f4c9e8e6c089af77497190f5dc3
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.7, node-fetch@npm:^2.7.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.2.0, node-gyp-build@npm:^4.3.0":
  version: 4.8.4
  resolution: "node-gyp-build@npm:4.8.4"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 10c0/444e189907ece2081fe60e75368784f7782cfddb554b60123743dfb89509df89f1f29c03bbfa16b3a3e0be3f48799a4783f487da6203245fa5bed239ba7407e1
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/bd8d8c76b06be761239b0c8680f655f6a6e90b48e44d43415b11c16f7e8c15be346fba0cbf71588c7cdfb52c419d928a7d3db353afc1d952d19756237d8f10b9
  languageName: node
  linkType: hard

"node-mock-http@npm:^1.0.0":
  version: 1.0.0
  resolution: "node-mock-http@npm:1.0.0"
  checksum: 10c0/cb3fd7c17e7043b87a8d7a9ef1dcd4e2cde312cd224716c5fb3a4b56b48607c257a2e7356e73262db60ebf9e17e23b7a9c5230785f630c6a437090bfd26dd242
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"obj-multiplex@npm:^1.0.0":
  version: 1.0.0
  resolution: "obj-multiplex@npm:1.0.0"
  dependencies:
    end-of-stream: "npm:^1.4.0"
    once: "npm:^1.4.0"
    readable-stream: "npm:^2.3.3"
  checksum: 10c0/914e979ab40fb26cbe4309a5fc1cc6b6a428aeff17a015b9abb1197894ee67f6f02542ffd76d8e275cc40b18adc125bff6e2d6b5090932798c135100c5942007
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10c0/b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/3b2732bd860567ea2579d1567525168de925a8d852638612846bd8082b3a1602b7b89b67b09913cbb5b9bd6e95923b2ae73580baa9d99cb4e990564e8cbf5ddc
  languageName: node
  linkType: hard

"ofetch@npm:^1.4.1":
  version: 1.4.1
  resolution: "ofetch@npm:1.4.1"
  dependencies:
    destr: "npm:^2.0.3"
    node-fetch-native: "npm:^1.6.4"
    ufo: "npm:^1.5.4"
  checksum: 10c0/fd712e84058ad5058a5880fe805e9bb1c2084fb7f9c54afa99a2c7e84065589b4312fa6e2dcca4432865e44ad1ec13fcd055c1bf7977ced838577a45689a04fa
  languageName: node
  linkType: hard

"on-exit-leak-free@npm:^0.2.0":
  version: 0.2.0
  resolution: "on-exit-leak-free@npm:0.2.0"
  checksum: 10c0/d4e1f0bea59f39aa435baaee7d76955527e245538cffc1d7bb0c165ae85e37f67690aa9272247ced17bad76052afdb45faf5ea304a2248e070202d4554c4e30c
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"open@npm:^8.4.0":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: "npm:^2.0.0"
    is-docker: "npm:^2.1.1"
    is-wsl: "npm:^2.2.0"
  checksum: 10c0/bb6b3a58401dacdb0aad14360626faf3fb7fba4b77816b373495988b724fb48941cad80c1b65d62bb31a17609b2cd91c41a181602caea597ca80dfbcc27e84c9
  languageName: node
  linkType: hard

"optimism@npm:^0.18.0":
  version: 0.18.1
  resolution: "optimism@npm:0.18.1"
  dependencies:
    "@wry/caches": "npm:^1.0.0"
    "@wry/context": "npm:^0.7.0"
    "@wry/trie": "npm:^0.5.0"
    tslib: "npm:^2.3.0"
  checksum: 10c0/1c1cd065d306de2220c6a2bdd8701cb7f9aadace36a9f16d6e02db2bee23b0291f15a1219b92cde5c66d816bd33dca876dfdcdbad04b4cf9b2a7fc5a1a221e77
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10c0/4afb687a059ee65b61df74dfe87d8d6815cd6883cb8b3d5883a910df72d0f5d029821f37025e4bccf4048873dbdb09acc6d303d27b8f76b1a80dd5a7d5334675
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10c0/6dfeb3455bff92ec3f16a982d4e3e65676345f6902d9f5ded1d8265a6318d0200ce461956d6d1c70053c7fe9f9fe65e552faac03f8140d37ef0fdd108e67013a
  languageName: node
  linkType: hard

"ox@npm:0.6.7":
  version: 0.6.7
  resolution: "ox@npm:0.6.7"
  dependencies:
    "@adraffy/ens-normalize": "npm:^1.10.1"
    "@noble/curves": "npm:^1.6.0"
    "@noble/hashes": "npm:^1.5.0"
    "@scure/bip32": "npm:^1.5.0"
    "@scure/bip39": "npm:^1.4.0"
    abitype: "npm:^1.0.6"
    eventemitter3: "npm:5.0.1"
  peerDependencies:
    typescript: ">=5.4.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/f556804e7246cc8aa56e43c6bb91302a792649638afe086a86ed3a71a5a583c05d3ad4318b212835cb8167fe561024db1625253c118018380393e161af3c3edf
  languageName: node
  linkType: hard

"ox@npm:0.7.1":
  version: 0.7.1
  resolution: "ox@npm:0.7.1"
  dependencies:
    "@adraffy/ens-normalize": "npm:^1.10.1"
    "@noble/ciphers": "npm:^1.3.0"
    "@noble/curves": "npm:^1.6.0"
    "@noble/hashes": "npm:^1.5.0"
    "@scure/bip32": "npm:^1.5.0"
    "@scure/bip39": "npm:^1.4.0"
    abitype: "npm:^1.0.6"
    eventemitter3: "npm:5.0.1"
  peerDependencies:
    typescript: ">=5.4.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/15370d76f7e5fe1b06c5b9986bc709a8c433e4242660900b3d1adb2a56c8f762a2010a9166bdb95bdf531806cde7891911456c7ec8ba135fc232a5d5037ac673
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10c0/8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10c0/1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10c0/c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10c0/77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1, path-scurry@npm:^1.6.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10c0/666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.2, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10c0/7c51f3ad2bb42c776f49ebf964c644958158be30d0a510efd5a395e8d49cb5acfed5b82c0c5b365523ce18e6ab85013c9ebe574f60305892ec3fa8eee8304ccc
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 10c0/fead19ed9d801f1b1fcd0638a1ac53eabbb0945bf615f2f8806a8b646565a04a1b0e7ef115c951d225f042cca388fdc1cd3add46d10d1ed6951c20bd2998af10
  languageName: node
  linkType: hard

"pify@npm:^5.0.0":
  version: 5.0.0
  resolution: "pify@npm:5.0.0"
  checksum: 10c0/9f6f3cd1f159652692f514383efe401a06473af35a699962230ad1c4c9796df5999961461fc1a3b81eed8e3e74adb8bd032474fb3f93eb6bdbd9f33328da1ed2
  languageName: node
  linkType: hard

"pino-abstract-transport@npm:v0.5.0":
  version: 0.5.0
  resolution: "pino-abstract-transport@npm:0.5.0"
  dependencies:
    duplexify: "npm:^4.1.2"
    split2: "npm:^4.0.0"
  checksum: 10c0/0d0e30399028ec156642b4cdfe1a040b9022befdc38e8f85935d1837c3da6050691888038433f88190d1a1eff5d90abe17ff7e6edffc09baa2f96e51b6808183
  languageName: node
  linkType: hard

"pino-std-serializers@npm:^4.0.0":
  version: 4.0.0
  resolution: "pino-std-serializers@npm:4.0.0"
  checksum: 10c0/9e8ccac9ce04a27ccc7aa26481d431b9e037d866b101b89d895c60b925baffb82685e84d5c29b05d8e3d7c146d766a9b08949cb24ab1ec526a16134c9962d649
  languageName: node
  linkType: hard

"pino@npm:7.11.0":
  version: 7.11.0
  resolution: "pino@npm:7.11.0"
  dependencies:
    atomic-sleep: "npm:^1.0.0"
    fast-redact: "npm:^3.0.0"
    on-exit-leak-free: "npm:^0.2.0"
    pino-abstract-transport: "npm:v0.5.0"
    pino-std-serializers: "npm:^4.0.0"
    process-warning: "npm:^1.0.0"
    quick-format-unescaped: "npm:^4.0.3"
    real-require: "npm:^0.1.0"
    safe-stable-stringify: "npm:^2.1.0"
    sonic-boom: "npm:^2.2.1"
    thread-stream: "npm:^0.15.1"
  bin:
    pino: bin.js
  checksum: 10c0/4cc1ed9d25a4bc5d61c836a861279fa0039159b8f2f37ec337e50b0a61f3980dab5d2b1393daec26f68a19c423262649f0818654c9ad102c35310544a202c62c
  languageName: node
  linkType: hard

"pngjs@npm:^5.0.0":
  version: 5.0.0
  resolution: "pngjs@npm:5.0.0"
  checksum: 10c0/c074d8a94fb75e2defa8021e85356bf7849688af7d8ce9995b7394d57cd1a777b272cfb7c4bce08b8d10e71e708e7717c81fd553a413f21840c548ec9d4893c6
  languageName: node
  linkType: hard

"pony-cause@npm:^2.1.10":
  version: 2.1.11
  resolution: "pony-cause@npm:2.1.11"
  checksum: 10c0/d5db6489ec42f8fcce0fd9ad2052be98cd8f63814bf32819694ec1f4c6a01bc3be6181050d83bc79e95272174a5b9776d1c2af1fa79ef51e0ccc0f97c22b1420
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10c0/c810983414142071da1d644662ce4caebce890203eb2bc7bf119f37f3fe5796226e117e6cca146b521921fa6531072674174a3325066ac66fce089a53e1e5196
  languageName: node
  linkType: hard

"postcss@npm:^8.4.43":
  version: 8.5.4
  resolution: "postcss@npm:8.5.4"
  dependencies:
    nanoid: "npm:^3.3.11"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/0feff648614a834f7cd5396ea6b05b658ca0507e10a4eaad03b56c348f6aec93f42a885fc1b30522630c6a7e49ae53b38a061e3cba526f2d9857afbe095a22bb
  languageName: node
  linkType: hard

"posthog-js@npm:1.170.1":
  version: 1.170.1
  resolution: "posthog-js@npm:1.170.1"
  dependencies:
    fflate: "npm:^0.4.8"
    preact: "npm:^10.19.3"
    web-vitals: "npm:^4.0.1"
  checksum: 10c0/8ef70f79c8e101bee13a738d9813458467c538af543bbd52725cfeedb543ce716005a3c30e889277b8892427d0a44c84771d0f0c1a007aa4f58227d9a56623c5
  languageName: node
  linkType: hard

"preact@npm:^10.16.0, preact@npm:^10.19.3, preact@npm:^10.24.2":
  version: 10.26.8
  resolution: "preact@npm:10.26.8"
  checksum: 10c0/83b6a38af15386c0d93bbaf12996d624354f9c511f79d188f0d673b8f91740849b0603c207da1f10450b07a8659333864a3735d3b5fe23a790ef6cb85d1107c8
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10c0/b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: "npm:^1.1.2"
  checksum: 10c0/81e0027d731b7b3697ccd2129470ed9913ecb111e4ec175a12f0fcfab0096516373bf0af2fef132af50cafb0a905b74ff57996d615f59512bb9ac7378fcc64ab
  languageName: node
  linkType: hard

"prettier@npm:3.3.3":
  version: 3.3.3
  resolution: "prettier@npm:3.3.3"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10c0/b85828b08e7505716324e4245549b9205c0cacb25342a030ba8885aba2039a115dbcf75a0b7ca3b37bc9d101ee61fab8113fc69ca3359f2a226f1ecc07ad2e26
  languageName: node
  linkType: hard

"pretty-bytes@npm:^5.3.0":
  version: 5.6.0
  resolution: "pretty-bytes@npm:5.6.0"
  checksum: 10c0/f69f494dcc1adda98dbe0e4a36d301e8be8ff99bfde7a637b2ee2820e7cb583b0fc0f3a63b0e3752c01501185a5cf38602c7be60da41bdf84ef5b70e89c370f3
  languageName: node
  linkType: hard

"pretty-bytes@npm:^6.1.1":
  version: 6.1.1
  resolution: "pretty-bytes@npm:6.1.1"
  checksum: 10c0/c7a660b933355f3b4587ad3f001c266a8dd6afd17db9f89ebc50812354bb142df4b9600396ba5999bdb1f9717300387dc311df91895c5f0f2a1780e22495b5f8
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10c0/bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"process-warning@npm:^1.0.0":
  version: 1.0.0
  resolution: "process-warning@npm:1.0.0"
  checksum: 10c0/43ec4229d64eb5c58340c8aacade49eb5f6fd513eae54140abf365929ca20987f0a35c5868125e2b583cad4de8cd257beb5667d9cc539d9190a7a4c3014adf22
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 10c0/40c3ce4b7e6d4b8c3355479df77aeed46f81b279818ccdc500124e6a5ab882c0cc81ff7ea16384873a95a74c4570b01b120f287abbdd4c877931460eca6084b3
  languageName: node
  linkType: hard

"progress@npm:^2.0.3":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: 10c0/1697e07cb1068055dbe9fe858d242368ff5d2073639e652b75a7eb1f2a1a8d4afd404d719de23c7b48481a6aa0040686310e2dac2f53d776daa2176d3f96369c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prop-types@npm:^15.6.2, prop-types@npm:^15.7.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10c0/59ece7ca2fb9838031d73a48d4becb9a7cc1ed10e610517c7d8f19a1e02fa47f7c27d557d8a5702bec3cfeccddc853579832b43f449e54635803f277b1c78077
  languageName: node
  linkType: hard

"proxy-compare@npm:2.5.1":
  version: 2.5.1
  resolution: "proxy-compare@npm:2.5.1"
  checksum: 10c0/116fc69ae9a6bb3654e6907fb09b73e84aa47c89275ca52648fc1d2ac8b35dbf54daa8bab078d7a735337c928e87eb52059e705434adf14989bbe6c5dcdd08fa
  languageName: node
  linkType: hard

"proxy-compare@npm:2.6.0":
  version: 2.6.0
  resolution: "proxy-compare@npm:2.6.0"
  checksum: 10c0/afd82ddc83f34af6116a5e222399fb7e626a1a443feb9d70e7a1af65561c97f670c5c8c4bde53bfe12a7cda7ef00f9863d265f3a0e949ff031a9869ecc5feb0c
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.2
  resolution: "pump@npm:3.0.2"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10c0/5ad655cb2a7738b4bcf6406b24ad0970d680649d996b55ad20d1be8e0c02394034e4c45ff7cd105d87f1e9b96a0e3d06fd28e11fae8875da26e7f7a8e2c9726f
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"qrcode@npm:1.5.3":
  version: 1.5.3
  resolution: "qrcode@npm:1.5.3"
  dependencies:
    dijkstrajs: "npm:^1.0.1"
    encode-utf8: "npm:^1.0.3"
    pngjs: "npm:^5.0.0"
    yargs: "npm:^15.3.1"
  bin:
    qrcode: bin/qrcode
  checksum: 10c0/eb961cd8246e00ae338b6d4a3a28574174456db42cec7070aa2b315fb6576b7f040b0e4347be290032e447359a145c68cb60ef884d55ca3e1076294fed46f719
  languageName: node
  linkType: hard

"query-string@npm:7.1.3":
  version: 7.1.3
  resolution: "query-string@npm:7.1.3"
  dependencies:
    decode-uri-component: "npm:^0.2.2"
    filter-obj: "npm:^1.1.0"
    split-on-first: "npm:^1.0.0"
    strict-uri-encode: "npm:^2.0.0"
  checksum: 10c0/a896c08e9e0d4f8ffd89a572d11f668c8d0f7df9c27c6f49b92ab31366d3ba0e9c331b9a620ee747893436cd1f2f821a6327e2bc9776bde2402ac6c270b801b2
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"quick-format-unescaped@npm:^4.0.3":
  version: 4.0.4
  resolution: "quick-format-unescaped@npm:4.0.4"
  checksum: 10c0/fe5acc6f775b172ca5b4373df26f7e4fd347975578199e7d74b2ae4077f0af05baa27d231de1e80e8f72d88275ccc6028568a7a8c9ee5e7368ace0e18eff93a4
  languageName: node
  linkType: hard

"radix3@npm:^1.1.2":
  version: 1.1.2
  resolution: "radix3@npm:1.1.2"
  checksum: 10c0/d4a295547f71af079868d2c2ed3814a9296ee026c5488212d58c106e6b4797c6eaec1259b46c9728913622f2240c9a944bfc8e2b3b5f6e4a5045338b1609f1e4
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 10c0/50395efda7a8c94f5dffab564f9ff89736064d32addf0cc7e8bf5e4166f09f8ded7a0849ca6c2d2a59478f7d90f78f20d8048bca3cdf8be09d8e8a10790388f3
  languageName: node
  linkType: hard

"react-dom@npm:18.3.1":
  version: 18.3.1
  resolution: "react-dom@npm:18.3.1"
  dependencies:
    loose-envify: "npm:^1.1.0"
    scheduler: "npm:^0.23.2"
  peerDependencies:
    react: ^18.3.1
  checksum: 10c0/a752496c1941f958f2e8ac56239172296fcddce1365ce45222d04a1947e0cc5547df3e8447f855a81d6d39f008d7c32eab43db3712077f09e3f67c4874973e85
  languageName: node
  linkType: hard

"react-error-boundary@npm:4.1.1":
  version: 4.1.1
  resolution: "react-error-boundary@npm:4.1.1"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
  peerDependencies:
    react: ">=16.13.1"
  checksum: 10c0/0faa4236833a5d98844f84180c8f54316b555bb3e1b38b37d59e6edf8baa754c4fad4570155dc49de61558a0e912fb7143554657f9abccf8ccd08fbee1bd7ac4
  languageName: node
  linkType: hard

"react-gtm-module@npm:2.0.11":
  version: 2.0.11
  resolution: "react-gtm-module@npm:2.0.11"
  checksum: 10c0/ceedf296a1232934788583e4a20452a82e872fcd79e24827bf615b1cd3bceb59c294d169b1bf8313ba48b9e480e681ce520cd2a5cf2381c5aaa1fb74d4f88930
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10c0/33977da7a5f1a287936a0c85639fec6ca74f4f15ef1e59a6bc20338fc73dc69555381e211f7a3529b8150a1f71e4225525b41b60b52965bda53ce7d47377ada1
  languageName: node
  linkType: hard

"react-is@npm:^18.3.1":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: 10c0/f2f1e60010c683479e74c63f96b09fb41603527cd131a9959e2aee1e5a8b0caf270b365e5ca77d4a6b18aae659b60a86150bb3979073528877029b35aecd2072
  languageName: node
  linkType: hard

"react-is@npm:^19.0.0":
  version: 19.1.0
  resolution: "react-is@npm:19.1.0"
  checksum: 10c0/b6c6cadd172d5d39f66d493700d137a5545c294a62ce0f8ec793d59794c97d2bed6bad227626f16bd0e90004ed7fdc8ed662a004e6edcf5d2b7ecb6e3040ea6b
  languageName: node
  linkType: hard

"react-refresh@npm:^0.14.2":
  version: 0.14.2
  resolution: "react-refresh@npm:0.14.2"
  checksum: 10c0/875b72ef56b147a131e33f2abd6ec059d1989854b3ff438898e4f9310bfcc73acff709445b7ba843318a953cb9424bcc2c05af2b3d80011cee28f25aef3e2ebb
  languageName: node
  linkType: hard

"react-router-dom@npm:6.27.0":
  version: 6.27.0
  resolution: "react-router-dom@npm:6.27.0"
  dependencies:
    "@remix-run/router": "npm:1.20.0"
    react-router: "npm:6.27.0"
  peerDependencies:
    react: ">=16.8"
    react-dom: ">=16.8"
  checksum: 10c0/7db48ffd0b387af0eed060ceaf42075d074e63fbd30f4cf60993526b3610883a9ff82615965001165ed69d2bf2f1bce05c594a21c8d0d845e7b9bf203201116e
  languageName: node
  linkType: hard

"react-router@npm:6.27.0":
  version: 6.27.0
  resolution: "react-router@npm:6.27.0"
  dependencies:
    "@remix-run/router": "npm:1.20.0"
  peerDependencies:
    react: ">=16.8"
  checksum: 10c0/440d6ee00890cec92a0c2183164149fbb96363efccf52bb132a964f44e51aec2f4b5a0520c67f6f17faddaa4097090fd76f7efe58263947532fceeb11dd4cdf3
  languageName: node
  linkType: hard

"react-swipeable@npm:^7.0.2":
  version: 7.0.2
  resolution: "react-swipeable@npm:7.0.2"
  peerDependencies:
    react: ^16.8.3 || ^17 || ^18 || ^19.0.0 || ^19.0.0-rc
  checksum: 10c0/cca3ec9e6321f73d187a8ac4beece2ad8cce1f4efa0215de93e2635492e7c514ccbc30311bb0e88bafb79d302916550603cdcd44ac81054a8c0f6679b18eb04d
  languageName: node
  linkType: hard

"react-transition-group@npm:^4.4.5":
  version: 4.4.5
  resolution: "react-transition-group@npm:4.4.5"
  dependencies:
    "@babel/runtime": "npm:^7.5.5"
    dom-helpers: "npm:^5.0.1"
    loose-envify: "npm:^1.4.0"
    prop-types: "npm:^15.6.2"
  peerDependencies:
    react: ">=16.6.0"
    react-dom: ">=16.6.0"
  checksum: 10c0/2ba754ba748faefa15f87c96dfa700d5525054a0141de8c75763aae6734af0740e77e11261a1e8f4ffc08fd9ab78510122e05c21c2d79066c38bb6861a886c82
  languageName: node
  linkType: hard

"react@npm:18.3.1":
  version: 18.3.1
  resolution: "react@npm:18.3.1"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/283e8c5efcf37802c9d1ce767f302dd569dd97a70d9bb8c7be79a789b9902451e0d16334b05d73299b20f048cbc3c7d288bbbde10b701fa194e2089c237dbea3
  languageName: node
  linkType: hard

"readable-stream@npm:^2.3.3":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readable-stream@npm:^3.1.1, readable-stream@npm:^3.6.0, readable-stream@npm:^3.6.2":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:^3.6.2 || ^4.4.2":
  version: 4.7.0
  resolution: "readable-stream@npm:4.7.0"
  dependencies:
    abort-controller: "npm:^3.0.0"
    buffer: "npm:^6.0.3"
    events: "npm:^3.3.0"
    process: "npm:^0.11.10"
    string_decoder: "npm:^1.3.0"
  checksum: 10c0/fd86d068da21cfdb10f7a4479f2e47d9c0a9b0c862fc0c840a7e5360201580a55ac399c764b12a4f6fa291f8cee74d9c4b7562e0d53b3c4b2769f2c98155d957
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.1.2
  resolution: "readdirp@npm:4.1.2"
  checksum: 10c0/60a14f7619dec48c9c850255cd523e2717001b0e179dc7037cfa0895da7b9e9ab07532d324bfb118d73a710887d1e35f79c495fa91582784493e085d18c72c62
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"real-require@npm:^0.1.0":
  version: 0.1.0
  resolution: "real-require@npm:0.1.0"
  checksum: 10c0/c0f8ae531d1f51fe6343d47a2a1e5756e19b65a81b4a9642b9ebb4874e0d8b5f3799bc600bf4592838242477edc6f57778593f21b71d90f8ad0d8a317bbfae1c
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10c0/7facec28c8008876f8ab98e80b7b9cb4b1e9224353fd4756dda5f2a4ab0d30fa0a5074777c6df24e1e0af463a2697513b0a11e548d99cf52f21f7bc6ba48d3ac
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: 10c0/5510785eeaf56bbfdf4e663d6753f125c08d2a372d4107bc1b756b7bf142e2ed80c2733a8b54e68fb309ba37690e66a0362699b0e21d5c1f0255dea1b00e6460
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 10c0/f73c9eba5d398c818edc71d1c6979eaa05af7a808682749dd079f8df2a6d91a9b913db216c2c9b03e0a8ba2bba8701244a93f45211afbff691c32c7b275db1b8
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3, regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/83b88e6115b4af1c537f8dabf5c3744032cb875d63bc05c288b1b8c0ef37cbe55353f95d8ca817e8843806e3e150b118bc624e4279b24b4776b4198232735a77
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.2.0"
    regjsgen: "npm:^0.8.0"
    regjsparser: "npm:^0.12.0"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: 10c0/bbcb83a854bf96ce4005ee4e4618b71c889cda72674ce6092432f0039b47890c2d0dfeb9057d08d440999d9ea03879ebbb7f26ca005ccf94390e55c348859b98
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: 10c0/44f526c4fdbf0b29286101a282189e4dbb303f4013cf3fea058668d96d113b9180d3d03d1e13f6d4cbde38b7728bf951aecd9dc199938c080093a9a6f0d7a6bd
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 10c0/99d3e4e10c8c7732eb7aa843b8da2fd8b647fe144d3711b480e4647dc3bff4b1e96691ccf17f3ace24aa866a50b064236177cb25e6e4fbbb18285d99edaed83b
  languageName: node
  linkType: hard

"rehackt@npm:^0.1.0":
  version: 0.1.0
  resolution: "rehackt@npm:0.1.0"
  peerDependencies:
    "@types/react": "*"
    react: "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    react:
      optional: true
  checksum: 10c0/3d838bfee84ec06c976f21027936f3b0fdb7660ab8a2d4d3f19c65e0daa78a268aa81352311352b8576b89a074714b36ae6cd5bdadb6e975eca079f2b342de73
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10c0/aaa267e0c5b022fc5fd4eef49d8285086b15f2a1c54b28240fdf03599cbd9c26049fee3eab894f2e1f6ca65e513b030a7c264201e3f005601e80c49fb2937ce2
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: 10c0/db91467d9ead311b4111cbd73a4e67fa7820daed2989a32f7023785a2659008c6d119752d9c4ac011ae07e537eb86523adff99804c5fdb39cd3a017f9b401bb6
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"resolve@npm:^1.14.2, resolve@npm:^1.19.0, resolve@npm:^1.22.1":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.14.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.19.0#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.1#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10c0/4eff0d4a5f9383566c7d7ec437b671cc51b25963bd61bf127c3f3d3f68e44a026d99b8d2f1ad344afff8d278a8fe70a8ea092650a716d22287e8bef7126bb2fa
  languageName: node
  linkType: hard

"rollup-plugin-visualizer@npm:^5.11.0":
  version: 5.14.0
  resolution: "rollup-plugin-visualizer@npm:5.14.0"
  dependencies:
    open: "npm:^8.4.0"
    picomatch: "npm:^4.0.2"
    source-map: "npm:^0.7.4"
    yargs: "npm:^17.5.1"
  peerDependencies:
    rolldown: 1.x
    rollup: 2.x || 3.x || 4.x
  peerDependenciesMeta:
    rolldown:
      optional: true
    rollup:
      optional: true
  bin:
    rollup-plugin-visualizer: dist/bin/cli.js
  checksum: 10c0/ec6ca9ed125bce9994ba49a340bda730661d8e8dc5c5dc014dc757185182e1eda49c6708f990cb059095e71a3741a5248f1e6ba0ced7056020692888e06b1ddf
  languageName: node
  linkType: hard

"rollup@npm:^2.43.1":
  version: 2.79.2
  resolution: "rollup@npm:2.79.2"
  dependencies:
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/bc3746c988d903c2211266ddc539379d53d92689b9cc5c2b4e3ae161689de9af491957a567c629b6cc81f48d0928a7591fc4c383fba68a48d2966c9fb8a2bce9
  languageName: node
  linkType: hard

"rollup@npm:^4.20.0":
  version: 4.41.1
  resolution: "rollup@npm:4.41.1"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.41.1"
    "@rollup/rollup-android-arm64": "npm:4.41.1"
    "@rollup/rollup-darwin-arm64": "npm:4.41.1"
    "@rollup/rollup-darwin-x64": "npm:4.41.1"
    "@rollup/rollup-freebsd-arm64": "npm:4.41.1"
    "@rollup/rollup-freebsd-x64": "npm:4.41.1"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.41.1"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.41.1"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.41.1"
    "@rollup/rollup-linux-arm64-musl": "npm:4.41.1"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.41.1"
    "@rollup/rollup-linux-powerpc64le-gnu": "npm:4.41.1"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.41.1"
    "@rollup/rollup-linux-riscv64-musl": "npm:4.41.1"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.41.1"
    "@rollup/rollup-linux-x64-gnu": "npm:4.41.1"
    "@rollup/rollup-linux-x64-musl": "npm:4.41.1"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.41.1"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.41.1"
    "@rollup/rollup-win32-x64-msvc": "npm:4.41.1"
    "@types/estree": "npm:1.0.7"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-musl":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/c4d5f2257320b50dc0e035e31d8d2f78d36b7015aef2f87cc984c0a1c97ffebf14337dddeb488b4b11ae798fea6486189b77e7cf677617dcf611d97db41ebfda
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/43c86ffdddc461fb17ff8a17c5324f392f4868f3c7dd2c6a5d9f5971713bc5fd755667212c80eab9567595f9a7509cc2f83e590ddaebd1bd19b780f9c79f9a8d
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/831f1c9aae7436429e7862c7e46f847dfe490afac20d0ee61bae06108dbf5c745a0de3568ada30ccdd3eeb0864ca8331b2eef703abd69bfea0745b21fd320750
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10c0/f2c25281bbe5d39cddbbce7f86fca5ea9b3ce3354ea6cd7c81c31b006a5a9fff4286acc5450a3b9122c56c33eba69c56b9131ad751457b2b4a585825e6a10665
  languageName: node
  linkType: hard

"safe-stable-stringify@npm:^2.1.0":
  version: 2.5.0
  resolution: "safe-stable-stringify@npm:2.5.0"
  checksum: 10c0/baea14971858cadd65df23894a40588ed791769db21bafb7fd7608397dbdce9c5aac60748abae9995e0fc37e15f2061980501e012cd48859740796bea2987f49
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sass@npm: 1.80.1":
  version: 1.80.1
  resolution: "sass@npm:1.80.1"
  dependencies:
    "@parcel/watcher": "npm:^2.4.1"
    chokidar: "npm:^4.0.0"
    immutable: "npm:^4.0.0"
    source-map-js: "npm:>=0.6.2 <2.0.0"
  bin:
    sass: sass.js
  checksum: 10c0/91ec2b8bcba4c3c68c047fdcbe04cb82646b5cc6446e04d9134ff3c642d9895363de5cbf88869f0a6aa81bcd2de9b8ccba7df7ff35d33a07a18754014ee7a31a
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.2":
  version: 0.23.2
  resolution: "scheduler@npm:0.23.2"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/26383305e249651d4c58e6705d5f8425f153211aef95f15161c151f7b8de885f24751b377e4a0b3dd42cce09aad3f87a61dab7636859c0d89b7daf1a1e2a5c78
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.3.8, semver@npm:^7.5.4, semver@npm:^7.6.0":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.1":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: "npm:^2.1.0"
  checksum: 10c0/2dd09ef4b65a1289ba24a788b1423a035581bef60817bea1f01eda8e3bda623f86357665fe7ac1b50f6d4f583f97db9615b3f07b2a2e8cbcb75033965f771dd2
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 10c0/9f8c1b2d800800d0b589de1477c753492de5c1548d4ade52f57f1d1f5e04af5481554d75ce5e5c43d4004b80a3eb714398d6907027dc0534177b7539119f4454
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/fce59f90696c450a8523e754abb305e2b8c73586452619c2bad5f7bf38c7b6b4651895c9db895679c5bef9554339cf3ef1c329b66ece3eda7255785fbe299316
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/ca5c3ccbba479d07c30460e367e66337cec825560b11e8ba9c5ebe13a2a0d6021ae34eddf94ff3dfe17a3104dc1f191519cb6c48378b503e5c3f36393938776a
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.11":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  bin:
    sha.js: ./bin.js
  checksum: 10c0/b7a371bca8821c9cc98a0aeff67444a03d48d745cb103f17228b96793f455f0eb0a691941b89ea1e60f6359207e36081d9be193252b0f128e0daf9cfea2815a5
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"smob@npm:^1.0.0":
  version: 1.5.0
  resolution: "smob@npm:1.5.0"
  checksum: 10c0/a1067f23265812de8357ed27312101af49b89129eb973e3f26ab5856ea774f88cace13342e66e32470f933ccfa916e0e9d0f7ca8bbd4f92dfab2af45c15956c2
  languageName: node
  linkType: hard

"socket.io-client@npm:^4.5.1":
  version: 4.8.1
  resolution: "socket.io-client@npm:4.8.1"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.2"
    engine.io-client: "npm:~6.6.1"
    socket.io-parser: "npm:~4.2.4"
  checksum: 10c0/544c49cc8cc77118ef68b758a8a580c8e680a5909cae05c566d2cc07ec6cd6720a4f5b7e985489bf2a8391749177a5437ac30b8afbdf30b9da6402687ad51c86
  languageName: node
  linkType: hard

"socket.io-parser@npm:~4.2.4":
  version: 4.2.4
  resolution: "socket.io-parser@npm:4.2.4"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.1"
  checksum: 10c0/9383b30358fde4a801ea4ec5e6860915c0389a091321f1c1f41506618b5cf7cd685d0a31c587467a0c4ee99ef98c2b99fb87911f9dfb329716c43b587f29ca48
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/00c3271e233ccf1fb83a3dd2060b94cc37817e0f797a93c560b9a7a86c4a0ec2961fb31263bdd24a3c28945e24868b5f063cd98744171d9e942c513454b50ae5
  languageName: node
  linkType: hard

"sonic-boom@npm:^2.2.1":
  version: 2.8.0
  resolution: "sonic-boom@npm:2.8.0"
  dependencies:
    atomic-sleep: "npm:^1.0.0"
  checksum: 10c0/6b40f2e91a999819b1dc24018a5d1c8b74e66e5d019eabad17d5b43fc309b32255b7c405ed6ec885693c8f2b969099ce96aeefde027180928bc58c034234a86d
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map@npm:^0.5.7":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 10c0/904e767bb9c494929be013017380cbba013637da1b28e5943b566031e29df04fba57edf3f093e0914be094648b577372bd8ad247fa98cfba9c600794cd16b599
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"source-map@npm:^0.7.4":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 10c0/dc0cf3768fe23c345ea8760487f8c97ef6fca8a73c83cd7c9bf2fde8bc2c34adb9c0824d6feb14bc4f9e37fb522e18af621543f1289038a66ac7586da29aa7dc
  languageName: node
  linkType: hard

"source-map@npm:^0.8.0-beta.0":
  version: 0.8.0-beta.0
  resolution: "source-map@npm:0.8.0-beta.0"
  dependencies:
    whatwg-url: "npm:^7.0.0"
  checksum: 10c0/fb4d9bde9a9fdb2c29b10e5eae6c71d10e09ef467e1afb75fdec2eb7e11fa5b343a2af553f74f18b695dbc0b81f9da2e9fa3d7a317d5985e9939499ec6087835
  languageName: node
  linkType: hard

"sourcemap-codec@npm:^1.4.8":
  version: 1.4.8
  resolution: "sourcemap-codec@npm:1.4.8"
  checksum: 10c0/f099279fdaae070ff156df7414bbe39aad69cdd615454947ed3e19136bfdfcb4544952685ee73f56e17038f4578091e12b17b283ed8ac013882916594d95b9e6
  languageName: node
  linkType: hard

"split-on-first@npm:^1.0.0":
  version: 1.1.0
  resolution: "split-on-first@npm:1.1.0"
  checksum: 10c0/56df8344f5a5de8521898a5c090023df1d8b8c75be6228f56c52491e0fc1617a5236f2ac3a066adb67a73231eac216ccea7b5b4a2423a543c277cb2f48d24c29
  languageName: node
  linkType: hard

"split2@npm:^4.0.0":
  version: 4.2.0
  resolution: "split2@npm:4.2.0"
  checksum: 10c0/b292beb8ce9215f8c642bb68be6249c5a4c7f332fc8ecadae7be5cbdf1ea95addc95f0459ef2e7ad9d45fd1064698a097e4eb211c83e772b49bc0ee423e91534
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.1.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    internal-slot: "npm:^1.1.0"
  checksum: 10c0/de4e45706bb4c0354a4b1122a2b8cc45a639e86206807ce0baf390ee9218d3ef181923fa4d2b67443367c491aa255c5fbaa64bb74648e3c5b48299928af86c09
  languageName: node
  linkType: hard

"stream-shift@npm:^1.0.2":
  version: 1.0.3
  resolution: "stream-shift@npm:1.0.3"
  checksum: 10c0/939cd1051ca750d240a0625b106a2b988c45fb5a3be0cebe9a9858cb01bc1955e8c7b9fac17a9462976bea4a7b704e317c5c2200c70f0ca715a3363b9aa4fd3b
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^2.0.0":
  version: 2.0.0
  resolution: "strict-uri-encode@npm:2.0.0"
  checksum: 10c0/010cbc78da0e2cf833b0f5dc769e21ae74cdc5d5f5bd555f14a4a4876c8ad2c85ab8b5bdf9a722dc71a11dcd3184085e1c3c0bd50ec6bb85fffc0f28cf82597d
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.6":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    regexp.prototype.flags: "npm:^1.5.3"
    set-function-name: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/1a53328ada73f4a77f1fdf1c79414700cf718d0a8ef6672af5603e709d26a24f2181208144aed7e858b1bcc1a0d08567a570abfb45567db4ae47637ed2c2f85c
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/8a8854241c4b54a948e992eb7dd6b8b3a97185112deb0037a134f5ba57541d8248dd610c966311887b6c2fd1181a3877bffb14d873ce937a344535dabcc648f8
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/59e1a70bf9414cb4c536a6e31bef5553c8ceb0cf44d8b4d0ed65c9653358d1c64dd0ec203b100df83d0413bbcde38b8c5d49e14bc4b86737d74adc593a0d35b6
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/d53af1899959e53c83b64a5fd120be93e067da740e7e75acb433849aa640782fb6c7d4cd5b84c954c84413745a3764df135a8afeb22908b86a835290788d8366
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1, string_decoder@npm:^1.3.0":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10c0/b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"stringify-object@npm:^3.3.0":
  version: 3.3.0
  resolution: "stringify-object@npm:3.3.0"
  dependencies:
    get-own-enumerable-property-symbols: "npm:^3.0.0"
    is-obj: "npm:^1.0.1"
    is-regexp: "npm:^1.0.0"
  checksum: 10c0/ba8078f84128979ee24b3de9a083489cbd3c62cb8572a061b47d4d82601a8ae4b4d86fa8c54dd955593da56bb7c16a6de51c27221fdc6b7139bb4f29d815f35b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-comments@npm:^2.0.1":
  version: 2.0.1
  resolution: "strip-comments@npm:2.0.1"
  checksum: 10c0/984321b1ec47a531bdcfddd87f217590934e2d2f142198a080ec88588280239a5b58a81ca780730679b6195e52afef83673c6d6466c07c2277f71f44d7d9553d
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"stylis@npm:4.2.0":
  version: 4.2.0
  resolution: "stylis@npm:4.2.0"
  checksum: 10c0/a7128ad5a8ed72652c6eba46bed4f416521bc9745a460ef5741edc725252cebf36ee45e33a8615a7057403c93df0866ab9ee955960792db210bb80abd5ac6543
  languageName: node
  linkType: hard

"superstruct@npm:^1.0.3":
  version: 1.0.4
  resolution: "superstruct@npm:1.0.4"
  checksum: 10c0/d355f1a96fa314e9df217aa371e8f22854644e7b600b7b0faa36860a8e50f61a60a6f1189ecf166171bf438aa6581bbd0d3adae1a65f03a3c43c62fd843e925c
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"symbol-observable@npm:^4.0.0":
  version: 4.0.0
  resolution: "symbol-observable@npm:4.0.0"
  checksum: 10c0/5e9a3ab08263a6be8cbee76587ad5880dcc62a47002787ed5ebea56b1eb30dc87da6f0183d67e88286806799fbe21c69077fbd677be4be2188e92318d6c6f31d
  languageName: node
  linkType: hard

"synckit@npm:^0.9.1":
  version: 0.9.3
  resolution: "synckit@npm:0.9.3"
  dependencies:
    "@pkgr/core": "npm:^0.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/3f2ecd7e04d5ca846ccb005017bb4be15982602b90d0ae3facf92f73837a81657b0a666d81713b23cfe25c28f26aaaabb385c59856c39c3710dba9f389cd8321
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"temp-dir@npm:^2.0.0":
  version: 2.0.0
  resolution: "temp-dir@npm:2.0.0"
  checksum: 10c0/b1df969e3f3f7903f3426861887ed76ba3b495f63f6d0c8e1ce22588679d9384d336df6064210fda14e640ed422e2a17d5c40d901f60e161c99482d723f4d309
  languageName: node
  linkType: hard

"tempy@npm:^0.6.0":
  version: 0.6.0
  resolution: "tempy@npm:0.6.0"
  dependencies:
    is-stream: "npm:^2.0.0"
    temp-dir: "npm:^2.0.0"
    type-fest: "npm:^0.16.0"
    unique-string: "npm:^2.0.0"
  checksum: 10c0/ca0882276732d1313b85006b0427620cb4a8d7a57738a2311a72befae60ed152be7d5b41b951dcb447a01a35404bed76f33eb4e37c55263cd7f807eee1187f8f
  languageName: node
  linkType: hard

"terser@npm:^5.17.4":
  version: 5.40.0
  resolution: "terser@npm:5.40.0"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.14.0"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10c0/0a6f35085217299b2e93c4c97533267f0d3a151fa771d7903bba3466345511c1ada2c474c668fc27f67b52906abac12fcf65b537dc5c177a1be9230aaa159b7f
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 10c0/02805740c12851ea5982686810702e2f14369a5f4c5c40a836821e3eefc65ffeec3131ba324692a37608294b0fd8c1e55a2dd571ffed4909822787668ddbee5c
  languageName: node
  linkType: hard

"thread-stream@npm:^0.15.1":
  version: 0.15.2
  resolution: "thread-stream@npm:0.15.2"
  dependencies:
    real-require: "npm:^0.1.0"
  checksum: 10c0/f92f1b5a9f3f35a72c374e3fecbde6f14d69d5325ad9ce88930af6ed9c7c1ec814367716b712205fa4f06242ae5dd97321ae2c00b43586590ed4fa861f3c29ae
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.0, tinyglobby@npm:^0.2.12":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/f789ed6c924287a9b7d3612056ed0cda67306cd2c80c249fd280cf1504742b12583a2089b61f4abbd24605f390809017240e250241f09938054c9b363e51c0a6
  languageName: node
  linkType: hard

"tmp@npm:^0.2.1":
  version: 0.2.3
  resolution: "tmp@npm:0.2.3"
  checksum: 10c0/3e809d9c2f46817475b452725c2aaa5d11985cf18d32a7a970ff25b568438e2c076c2e8609224feef3b7923fa9749b74428e3e634f6b8e520c534eef2fd24125
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"tr46@npm:^1.0.1":
  version: 1.0.1
  resolution: "tr46@npm:1.0.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/41525c2ccce86e3ef30af6fa5e1464e6d8bb4286a58ea8db09228f598889581ef62347153f6636cd41553dc41685bdfad0a9d032ef58df9fbb0792b3447d0f04
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.3.0":
  version: 1.4.3
  resolution: "ts-api-utils@npm:1.4.3"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: 10c0/e65dc6e7e8141140c23e1dc94984bf995d4f6801919c71d6dc27cf0cd51b100a91ffcfe5217626193e5bea9d46831e8586febdc7e172df3f1091a7384299e23a
  languageName: node
  linkType: hard

"ts-invariant@npm:^0.10.3":
  version: 0.10.3
  resolution: "ts-invariant@npm:0.10.3"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10c0/2fbc178d5903d325ee0b87fad38827eac11888b6e86979b06754fd4bcdcf44c2a99b8bcd5d59d149c0464ede55ae810b02a2aee6835ad10efe4dd0e22efd68c0
  languageName: node
  linkType: hard

"tslib@npm:1.14.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 10c0/69ae09c49eea644bc5ebe1bca4fa4cc2c82b7b3e02f43b84bd891504edf66dbc6b2ec0eef31a957042de2269139e4acff911e6d186a258fb14069cd7f6febce2
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.1.0, tslib@npm:^2.3.0, tslib@npm:^2.3.1, tslib@npm:^2.6.0, tslib@npm:^2.6.2":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10c0/7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"type-fest@npm:^0.16.0":
  version: 0.16.0
  resolution: "type-fest@npm:0.16.0"
  checksum: 10c0/6b4d846534e7bcb49a6160b068ffaed2b62570d989d909ac3f29df5ef1e993859f890a4242eebe023c9e923f96adbcb3b3e88a198c35a1ee9a731e147a6839c3
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/1105071756eb248774bc71646bfe45b682efcad93b55532c6ffa4518969fb6241354e4aa62af679ae83899ec296d69ef88f1f3763657cdb3a4d29321f7b83079
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/6ae083c6f0354f1fce18b90b243343b9982affd8d839c57bbd2c174a5d5dc71be9eb7019ffd12628a96a4815e7afa85d718d6f1e758615151d5f35df841ffb3e
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10c0/3d805b050c0c33b51719ee52de17c1cd8e6a571abdf0fffb110e45e8dd87a657e8b56eee94b776b13006d3d347a0c18a730b903cf05293ab6d92e99ff8f77e53
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10c0/e38f2ae3779584c138a2d8adfa8ecf749f494af3cd3cdafe4e688ce51418c7d2c5c88df1bd6be2bbea099c3f7cea58c02ca02ed438119e91f162a9de23f61295
  languageName: node
  linkType: hard

"typescript@npm:5.6.3":
  version: 5.6.3
  resolution: "typescript@npm:5.6.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/44f61d3fb15c35359bc60399cb8127c30bae554cd555b8e2b46d68fa79d680354b83320ad419ff1b81a0bdf324197b29affe6cc28988cd6a74d4ac60c94f9799
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A5.6.3#optional!builtin<compat/typescript>":
  version: 5.6.3
  resolution: "typescript@patch:typescript@npm%3A5.6.3#optional!builtin<compat/typescript>::version=5.6.3&hash=8c6c40"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/7c9d2e07c81226d60435939618c91ec2ff0b75fbfa106eec3430f0fcf93a584bc6c73176676f532d78c3594fe28a54b36eb40b3d75593071a7ec91301533ace7
  languageName: node
  linkType: hard

"ufo@npm:^1.5.4, ufo@npm:^1.6.1":
  version: 1.6.1
  resolution: "ufo@npm:1.6.1"
  checksum: 10c0/5a9f041e5945fba7c189d5410508cbcbefef80b253ed29aa2e1f8a2b86f4bd51af44ee18d4485e6d3468c92be9bf4a42e3a2b72dcaf27ce39ce947ec994f1e6b
  languageName: node
  linkType: hard

"uint8arrays@npm:3.1.0":
  version: 3.1.0
  resolution: "uint8arrays@npm:3.1.0"
  dependencies:
    multiformats: "npm:^9.4.2"
  checksum: 10c0/e54e64593a76541330f0fea97b1b5dea6becbbec3572b9bb88863d064f2630bede4d42eafd457f19c6ef9125f50bfc61053d519c4d71b59c3b7566a0691e3ba2
  languageName: node
  linkType: hard

"uint8arrays@npm:^3.0.0":
  version: 3.1.1
  resolution: "uint8arrays@npm:3.1.1"
  dependencies:
    multiformats: "npm:^9.4.2"
  checksum: 10c0/9946668e04f29b46bbb73cca3d190f63a2fbfe5452f8e6551ef4257d9d597b72da48fa895c15ef2ef772808a5335b3305f69da5f13a09f8c2924896b409565ff
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10c0/7dbd35ab02b0e05fe07136c72cb9355091242455473ec15057c11430129bab38b7b3624019b8778d02a881c13de44d63cd02d122ee782fb519e1de7775b5b982
  languageName: node
  linkType: hard

"uncrypto@npm:^0.1.3":
  version: 0.1.3
  resolution: "uncrypto@npm:0.1.3"
  checksum: 10c0/74a29afefd76d5b77bedc983559ceb33f5bbc8dada84ff33755d1e3355da55a4e03a10e7ce717918c436b4dfafde1782e799ebaf2aadd775612b49f7b5b2998e
  languageName: node
  linkType: hard

"undici-types@npm:~6.19.2":
  version: 6.19.8
  resolution: "undici-types@npm:6.19.8"
  checksum: 10c0/078afa5990fba110f6824823ace86073b4638f1d5112ee26e790155f481f2a868cc3e0615505b6f4282bdf74a3d8caad715fd809e870c2bb0704e3ea6082f344
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 10c0/f83bc492fdbe662860795ef37a85910944df7310cac91bd778f1c19ebc911e8b9cde84e703de631e5a2fcca3905e39896f8fc5fc6a44ddaf7f4aff1cda24f381
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 10c0/4d05252cecaf5c8e36d78dc5332e03b334c6242faf7cf16b3658525441386c0a03b5f603d42cbec0f09bb63b9fd25c9b3b09667aee75463cac3efadae2cd17ec
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 10c0/1d0a2deefd97974ddff5b7cb84f9884177f4489928dfcebb4b2b091d6124f2739df51fc6ea15958e1b5637ac2a24cff9bf21ea81e45335086ac52c0b4c717d6d
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 10c0/50ded3f8c963c7785e48c510a3b7c6bc4e08a579551489aa0349680a35b1ceceec122e33b2b6c1b579d0be2250f34bb163ac35f5f8695fe10bbc67fb757f0af8
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"unique-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "unique-string@npm:2.0.0"
  dependencies:
    crypto-random-string: "npm:^2.0.0"
  checksum: 10c0/11820db0a4ba069d174bedfa96c588fc2c96b083066fafa186851e563951d0de78181ac79c744c1ed28b51f9d82ac5b8196ff3e4560d0178046ef455d8c2244b
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unplugin@npm:1.0.1":
  version: 1.0.1
  resolution: "unplugin@npm:1.0.1"
  dependencies:
    acorn: "npm:^8.8.1"
    chokidar: "npm:^3.5.3"
    webpack-sources: "npm:^3.2.3"
    webpack-virtual-modules: "npm:^0.5.0"
  checksum: 10c0/7d59b5a28abc1cdbd6356a10f273d1266f59c3be083ab0e659a37d02d047d5df1b435e0f40f5ec97517e8fc910d314592f0d197ccceb75ef47c71c1898ec7a05
  languageName: node
  linkType: hard

"unstorage@npm:^1.9.0":
  version: 1.16.0
  resolution: "unstorage@npm:1.16.0"
  dependencies:
    anymatch: "npm:^3.1.3"
    chokidar: "npm:^4.0.3"
    destr: "npm:^2.0.5"
    h3: "npm:^1.15.2"
    lru-cache: "npm:^10.4.3"
    node-fetch-native: "npm:^1.6.6"
    ofetch: "npm:^1.4.1"
    ufo: "npm:^1.6.1"
  peerDependencies:
    "@azure/app-configuration": ^1.8.0
    "@azure/cosmos": ^4.2.0
    "@azure/data-tables": ^13.3.0
    "@azure/identity": ^4.6.0
    "@azure/keyvault-secrets": ^4.9.0
    "@azure/storage-blob": ^12.26.0
    "@capacitor/preferences": ^6.0.3 || ^7.0.0
    "@deno/kv": ">=0.9.0"
    "@netlify/blobs": ^6.5.0 || ^7.0.0 || ^8.1.0
    "@planetscale/database": ^1.19.0
    "@upstash/redis": ^1.34.3
    "@vercel/blob": ">=0.27.1"
    "@vercel/kv": ^1.0.1
    aws4fetch: ^1.0.20
    db0: ">=0.2.1"
    idb-keyval: ^6.2.1
    ioredis: ^5.4.2
    uploadthing: ^7.4.4
  peerDependenciesMeta:
    "@azure/app-configuration":
      optional: true
    "@azure/cosmos":
      optional: true
    "@azure/data-tables":
      optional: true
    "@azure/identity":
      optional: true
    "@azure/keyvault-secrets":
      optional: true
    "@azure/storage-blob":
      optional: true
    "@capacitor/preferences":
      optional: true
    "@deno/kv":
      optional: true
    "@netlify/blobs":
      optional: true
    "@planetscale/database":
      optional: true
    "@upstash/redis":
      optional: true
    "@vercel/blob":
      optional: true
    "@vercel/kv":
      optional: true
    aws4fetch:
      optional: true
    db0:
      optional: true
    idb-keyval:
      optional: true
    ioredis:
      optional: true
    uploadthing:
      optional: true
  checksum: 10c0/f719a6483fd71d0a6d4f2e98ec29721c352618c4f3641f96d0c703866dc13cda071e8afda5a68bac4e7d3880c8eece0edb2057e96ce0ac4fb649998611430a09
  languageName: node
  linkType: hard

"upath@npm:^1.2.0":
  version: 1.2.0
  resolution: "upath@npm:1.2.0"
  checksum: 10c0/3746f24099bf69dbf8234cecb671e1016e1f6b26bd306de4ff8966fb0bc463fa1014ffc48646b375de1ab573660e3a0256f6f2a87218b2dfa1779a84ef6992fa
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"use-sync-external-store@npm:1.2.0":
  version: 1.2.0
  resolution: "use-sync-external-store@npm:1.2.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: 10c0/ac4814e5592524f242921157e791b022efe36e451fe0d4fd4d204322d5433a4fc300d63b0ade5185f8e0735ded044c70bcf6d2352db0f74d097a238cebd2da02
  languageName: node
  linkType: hard

"use-sync-external-store@npm:1.4.0":
  version: 1.4.0
  resolution: "use-sync-external-store@npm:1.4.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/ec011a5055962c0f6b509d6e78c0b143f8cd069890ae370528753053c55e3b360d3648e76cfaa854faa7a59eb08d6c5fb1015e60ffde9046d32f5b2a295acea5
  languageName: node
  linkType: hard

"utf-8-validate@npm:^5.0.2":
  version: 5.0.10
  resolution: "utf-8-validate@npm:5.0.10"
  dependencies:
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.3.0"
  checksum: 10c0/23cd6adc29e6901aa37ff97ce4b81be9238d0023c5e217515b34792f3c3edb01470c3bd6b264096dd73d0b01a1690b57468de3a24167dd83004ff71c51cc025f
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"util@npm:^0.12.4":
  version: 0.12.5
  resolution: "util@npm:0.12.5"
  dependencies:
    inherits: "npm:^2.0.3"
    is-arguments: "npm:^1.0.4"
    is-generator-function: "npm:^1.0.7"
    is-typed-array: "npm:^1.1.3"
    which-typed-array: "npm:^1.1.2"
  checksum: 10c0/c27054de2cea2229a66c09522d0fa1415fb12d861d08523a8846bf2e4cbf0079d4c3f725f09dcb87493549bcbf05f5798dce1688b53c6c17201a45759e7253f3
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/bcbb807a917d374a49f475fae2e87fdca7da5e5530820ef53f65ba1d12131bd81a92ecf259cc7ce317cbe0f289e7d79fdfebcef9bfa3087c8c8a2fa304c9be54
  languageName: node
  linkType: hard

"uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/1607dd32ac7fc22f2d8f77051e6a64845c9bce5cd3dd8aa0070c074ec73e666a1f63c7b4e0f4bf2bc8b9d59dc85a15e17807446d9d2b17c8485fbc2147b27f9b
  languageName: node
  linkType: hard

"valtio@npm:1.11.2":
  version: 1.11.2
  resolution: "valtio@npm:1.11.2"
  dependencies:
    proxy-compare: "npm:2.5.1"
    use-sync-external-store: "npm:1.2.0"
  peerDependencies:
    "@types/react": ">=16.8"
    react: ">=16.8"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    react:
      optional: true
  checksum: 10c0/9ed337d1da4a3730d429b3415c2cb63340998000e62fb3e545e2fc05d27f55fc510abc89046d6719b4cae02742cdb733fe235bade90bfae50a0e13ece2287106
  languageName: node
  linkType: hard

"valtio@npm:1.13.2":
  version: 1.13.2
  resolution: "valtio@npm:1.13.2"
  dependencies:
    derive-valtio: "npm:0.1.0"
    proxy-compare: "npm:2.6.0"
    use-sync-external-store: "npm:1.2.0"
  peerDependencies:
    "@types/react": ">=16.8"
    react: ">=16.8"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    react:
      optional: true
  checksum: 10c0/514b8509308056e474c7d3ccfbbd6ac8e589740a92c53c53c78591a217e14da0694bd67f54195d8ec46920b6aab89eebab3c78c98c33d814b3606cdaacb6489b
  languageName: node
  linkType: hard

"viem@npm:2.23.2":
  version: 2.23.2
  resolution: "viem@npm:2.23.2"
  dependencies:
    "@noble/curves": "npm:1.8.1"
    "@noble/hashes": "npm:1.7.1"
    "@scure/bip32": "npm:1.6.2"
    "@scure/bip39": "npm:1.5.4"
    abitype: "npm:1.0.8"
    isows: "npm:1.0.6"
    ox: "npm:0.6.7"
    ws: "npm:8.18.0"
  peerDependencies:
    typescript: ">=5.0.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/39332d008d2ab0700aa57f541bb199350daecdfb722ae1b262404b02944e11205368fcc696cc0ab8327b9f90bf7172014687ae3e5d9091978e9d174885ccff2d
  languageName: node
  linkType: hard

"viem@npm:2.23.3":
  version: 2.23.3
  resolution: "viem@npm:2.23.3"
  dependencies:
    "@noble/curves": "npm:1.8.1"
    "@noble/hashes": "npm:1.7.1"
    "@scure/bip32": "npm:1.6.2"
    "@scure/bip39": "npm:1.5.4"
    abitype: "npm:1.0.8"
    isows: "npm:1.0.6"
    ox: "npm:0.6.7"
    ws: "npm:8.18.0"
  peerDependencies:
    typescript: ">=5.0.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/21384e8fc1c35f11c2b0f059b0174abc6b7a20f1fe1565f5e1691135b3f6545df1c57622739a841329c128c10c4f0732f8376d9d38632a5ebeefb78d3b0dd74e
  languageName: node
  linkType: hard

"viem@npm:>=2.23, viem@npm:>=2.23.0, viem@npm:>=2.29.0, viem@npm:^2.1.1":
  version: 2.30.6
  resolution: "viem@npm:2.30.6"
  dependencies:
    "@noble/curves": "npm:1.9.1"
    "@noble/hashes": "npm:1.8.0"
    "@scure/bip32": "npm:1.7.0"
    "@scure/bip39": "npm:1.6.0"
    abitype: "npm:1.0.8"
    isows: "npm:1.0.7"
    ox: "npm:0.7.1"
    ws: "npm:8.18.2"
  peerDependencies:
    typescript: ">=5.0.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/9b4b85c953363fc97dbba7d46a5fd0e5a81df16f83e82899d6092f5406b0efa2ad51aba1c4c2f084ae6421c0f1be598491473573523ef07a7d12d5fc3a087d7a
  languageName: node
  linkType: hard

"vite-bundle-visualizer@npm:1.2.1":
  version: 1.2.1
  resolution: "vite-bundle-visualizer@npm:1.2.1"
  dependencies:
    cac: "npm:^6.7.14"
    import-from-esm: "npm:^1.3.3"
    rollup-plugin-visualizer: "npm:^5.11.0"
    tmp: "npm:^0.2.1"
  bin:
    vite-bundle-visualizer: bin.js
  checksum: 10c0/038946556ba742726927047de97b0e38fbc2191ebbeb79d2e0767d1fb6fad1500ffeaea6c69bd9c906fdd26327165c9adcdb211286da166fb08ab02100149b41
  languageName: node
  linkType: hard

"vite-plugin-pwa@npm:0.20.5":
  version: 0.20.5
  resolution: "vite-plugin-pwa@npm:0.20.5"
  dependencies:
    debug: "npm:^4.3.6"
    pretty-bytes: "npm:^6.1.1"
    tinyglobby: "npm:^0.2.0"
    workbox-build: "npm:^7.1.0"
    workbox-window: "npm:^7.1.0"
  peerDependencies:
    "@vite-pwa/assets-generator": ^0.2.6
    vite: ^3.1.0 || ^4.0.0 || ^5.0.0
    workbox-build: ^7.1.0
    workbox-window: ^7.1.0
  peerDependenciesMeta:
    "@vite-pwa/assets-generator":
      optional: true
  checksum: 10c0/b0b26fab553d758185608941652cccdf7a6365fa3c749120edc83e1173de8103bf948d2ed52a159eea6b3ffe08b983077b68e78593fb2bf0e307fc517dc09065
  languageName: node
  linkType: hard

"vite@npm:5.4.9":
  version: 5.4.9
  resolution: "vite@npm:5.4.9"
  dependencies:
    esbuild: "npm:^0.21.3"
    fsevents: "npm:~2.3.3"
    postcss: "npm:^8.4.43"
    rollup: "npm:^4.20.0"
  peerDependencies:
    "@types/node": ^18.0.0 || >=20.0.0
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    sass-embedded: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.4.0
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10c0/e9c59f2c639047e37c79bbbb151c7a55a3dc27932957cf4cf0447ee0bdcc1ddfd9b1fb3ba0465371c01ba3616d62561327855794c2d652213c3a10a32e6d369d
  languageName: node
  linkType: hard

"wagmi@npm:2.14.11":
  version: 2.14.11
  resolution: "wagmi@npm:2.14.11"
  dependencies:
    "@wagmi/connectors": "npm:5.7.7"
    "@wagmi/core": "npm:2.16.4"
    use-sync-external-store: "npm:1.4.0"
  peerDependencies:
    "@tanstack/react-query": ">=5.0.0"
    react: ">=18"
    typescript: ">=5.0.4"
    viem: 2.x
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/aa4fc4a8a366c32d2605fcdadc61e52e379a9add52e655d6da5e506d2f8a2f89e268990d183a08f5b074f34d6994239aeded39e758e58f335cd68794e2cdb7af
  languageName: node
  linkType: hard

"web-vitals@npm:^4.0.1":
  version: 4.2.4
  resolution: "web-vitals@npm:4.2.4"
  checksum: 10c0/383c9281d5b556bcd190fde3c823aeb005bb8cf82e62c75b47beb411014a4ed13fa5c5e0489ed0f1b8d501cd66b0bebcb8624c1a75750bd5df13e2a3b1b2d194
  languageName: node
  linkType: hard

"webextension-polyfill@npm:>=0.10.0 <1.0":
  version: 0.12.0
  resolution: "webextension-polyfill@npm:0.12.0"
  checksum: 10c0/5ace2aaaf6a203515bdd2fb948622f186a5fbb50099b539ce9c0ad54896f9cc1fcc3c0e2a71d1f7071dd7236d7daebba1e0cbcf43bfdfe54361addf0333ee7d1
  languageName: node
  linkType: hard

"webextension-polyfill@npm:^0.10.0":
  version: 0.10.0
  resolution: "webextension-polyfill@npm:0.10.0"
  checksum: 10c0/6a45278f1fed8fbd5355f9b19a7b0b3fadc91fa3a6eef69125a1706bb3efa2181235eefbfb3f538443bb396cfcb97512361551888ce8465c08914431cb2d5b6d
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"webidl-conversions@npm:^4.0.2":
  version: 4.0.2
  resolution: "webidl-conversions@npm:4.0.2"
  checksum: 10c0/def5c5ac3479286dffcb604547628b2e6b46c5c5b8a8cfaa8c71dc3bafc85859bde5fbe89467ff861f571ab38987cf6ab3d6e7c80b39b999e50e803c12f3164f
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.3.2
  resolution: "webpack-sources@npm:3.3.2"
  checksum: 10c0/b5308d8acba4c7c6710b6df77187b274800afe0856c1508cba8aa310304558634e745b7eac4991ea086175ea6da3c64d11d958cf508980e6cb7506aa5983913e
  languageName: node
  linkType: hard

"webpack-virtual-modules@npm:^0.5.0":
  version: 0.5.0
  resolution: "webpack-virtual-modules@npm:0.5.0"
  checksum: 10c0/0742e069cd49d91ccd0b59431b3666903d321582c1b1062fa6bdae005c3538af55ff8787ea5eafbf72662f3496d3a879e2c705d55ca0af8283548a925be18484
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"whatwg-url@npm:^7.0.0":
  version: 7.1.0
  resolution: "whatwg-url@npm:7.1.0"
  dependencies:
    lodash.sortby: "npm:^4.7.0"
    tr46: "npm:^1.0.1"
    webidl-conversions: "npm:^4.0.2"
  checksum: 10c0/2785fe4647690e5a0225a79509ba5e21fdf4a71f9de3eabdba1192483fe006fc79961198e0b99f82751557309f17fc5a07d4d83c251aa5b2f85ba71e674cbee9
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10c0/aceea8ede3b08dede7dce168f3883323f7c62272b49801716e8332ff750e7ae59a511ae088840bc6874f16c1b7fd296c05c949b0e5b357bfe3c431b98c417abe
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/8dcf323c45e5c27887800df42fbe0431d0b66b1163849bb7d46b5a730ad6a96ee8bfe827d078303f825537844ebf20c02459de41239a0a9805e2fcb3cae0d471
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10c0/3345fde20964525a04cdf7c4a96821f85f0cc198f1b2ecb4576e08096746d129eb133571998fe121c77782ac8f21cbd67745a3d35ce100d26d4e684c142ea1f2
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 10c0/087038e7992649eaffa6c7a4f3158d5b53b14cf5b6c1f0e043dccfacb1ba179d12f17545d5b85ebd94a42ce280a6fe65d0cbcab70f4fc6daad1dfae85e0e6a3e
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.19, which-typed-array@npm:^1.1.2":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/702b5dc878addafe6c6300c3d0af5983b175c75fcb4f2a72dfc3dd38d93cf9e89581e4b29c854b16ea37e50a7d7fca5ae42ece5c273d8060dcd603b2404bbb3f
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10c0/e0e4a1ca27599c92a6ca4c32260e8a92e8a44f4ef6ef93f803f8ed823f486e0889fc0b93be4db59c8d51b3064951d25e43d434e95dc8c960cc3a63d65d00ba20
  languageName: node
  linkType: hard

"workbox-background-sync@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-background-sync@npm:7.3.0"
  dependencies:
    idb: "npm:^7.0.1"
    workbox-core: "npm:7.3.0"
  checksum: 10c0/cc982d62702847fb16c4ef372a8bd243348a80c2d5da1649a860b0187b45060a799a65582c2d36f1a32e31d5d68dedcb037698c41d3b2f171ea5d54d73453cf1
  languageName: node
  linkType: hard

"workbox-broadcast-update@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-broadcast-update@npm:7.3.0"
  dependencies:
    workbox-core: "npm:7.3.0"
  checksum: 10c0/25007acd3e845b5ca1f4c9ac9888ce661431723f7419cfa56b3029b6c56cbeca24902dae015c42a2d6f554f956274743e331d03ceeb4b0e3879cb7b908d0e82f
  languageName: node
  linkType: hard

"workbox-build@npm:^7.1.0":
  version: 7.3.0
  resolution: "workbox-build@npm:7.3.0"
  dependencies:
    "@apideck/better-ajv-errors": "npm:^0.3.1"
    "@babel/core": "npm:^7.24.4"
    "@babel/preset-env": "npm:^7.11.0"
    "@babel/runtime": "npm:^7.11.2"
    "@rollup/plugin-babel": "npm:^5.2.0"
    "@rollup/plugin-node-resolve": "npm:^15.2.3"
    "@rollup/plugin-replace": "npm:^2.4.1"
    "@rollup/plugin-terser": "npm:^0.4.3"
    "@surma/rollup-plugin-off-main-thread": "npm:^2.2.3"
    ajv: "npm:^8.6.0"
    common-tags: "npm:^1.8.0"
    fast-json-stable-stringify: "npm:^2.1.0"
    fs-extra: "npm:^9.0.1"
    glob: "npm:^7.1.6"
    lodash: "npm:^4.17.20"
    pretty-bytes: "npm:^5.3.0"
    rollup: "npm:^2.43.1"
    source-map: "npm:^0.8.0-beta.0"
    stringify-object: "npm:^3.3.0"
    strip-comments: "npm:^2.0.1"
    tempy: "npm:^0.6.0"
    upath: "npm:^1.2.0"
    workbox-background-sync: "npm:7.3.0"
    workbox-broadcast-update: "npm:7.3.0"
    workbox-cacheable-response: "npm:7.3.0"
    workbox-core: "npm:7.3.0"
    workbox-expiration: "npm:7.3.0"
    workbox-google-analytics: "npm:7.3.0"
    workbox-navigation-preload: "npm:7.3.0"
    workbox-precaching: "npm:7.3.0"
    workbox-range-requests: "npm:7.3.0"
    workbox-recipes: "npm:7.3.0"
    workbox-routing: "npm:7.3.0"
    workbox-strategies: "npm:7.3.0"
    workbox-streams: "npm:7.3.0"
    workbox-sw: "npm:7.3.0"
    workbox-window: "npm:7.3.0"
  checksum: 10c0/cb396f9c2a53429d1e11b4c1da2e21c9e1c98473ce15f20ae53277e47bd7ccbcb3f1f843694e588bb70b12d9332faafd098ca05b93abb0293d373f38a8de3ca8
  languageName: node
  linkType: hard

"workbox-cacheable-response@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-cacheable-response@npm:7.3.0"
  dependencies:
    workbox-core: "npm:7.3.0"
  checksum: 10c0/192c8a8878c53a205c55398bac78f2c32c0f36e55c95cab282d8a716ddf2fa72563afaed690d34d3438cc8df5fb0df4d98dcb2d93cc6d67c69a9ae592f7bf246
  languageName: node
  linkType: hard

"workbox-core@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-core@npm:7.3.0"
  checksum: 10c0/b7dce640cd9665ed207f65f5b08a50e2e24e5599790c6ea4fec987539b9d2ef81765d8c5f94acfee3a8a45d5ade8e1a4ebd0b8847a1471302ef75a5b93c7bd04
  languageName: node
  linkType: hard

"workbox-expiration@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-expiration@npm:7.3.0"
  dependencies:
    idb: "npm:^7.0.1"
    workbox-core: "npm:7.3.0"
  checksum: 10c0/6040d72122ece901becfcc59974586e9cc9b6309840b83b652c9f9aafe32ff89783404a431cadf6f888f80e5371252820e425ced499742964d6d68687f6fad1a
  languageName: node
  linkType: hard

"workbox-google-analytics@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-google-analytics@npm:7.3.0"
  dependencies:
    workbox-background-sync: "npm:7.3.0"
    workbox-core: "npm:7.3.0"
    workbox-routing: "npm:7.3.0"
    workbox-strategies: "npm:7.3.0"
  checksum: 10c0/5317a4bcc01f1aa87480f9708d7d382c15fb37d6119e71e0a2909dfd683f6060b5cc4f7b016a81fc67098f51a5d0cfd1cda20e228f2f3778ee3caf649b59996b
  languageName: node
  linkType: hard

"workbox-navigation-preload@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-navigation-preload@npm:7.3.0"
  dependencies:
    workbox-core: "npm:7.3.0"
  checksum: 10c0/69e4d43c68c06889987e9fa437995378b0632c83bad8c7044b4ed812b05b94b3a4aa8700ea4c26b2ecf68ee6858e94ff41dfa3279815c1bc385ac19c0edfb200
  languageName: node
  linkType: hard

"workbox-precaching@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-precaching@npm:7.3.0"
  dependencies:
    workbox-core: "npm:7.3.0"
    workbox-routing: "npm:7.3.0"
    workbox-strategies: "npm:7.3.0"
  checksum: 10c0/15c4c5cf5dfec684711ce3536bbfa6873f7af16b712d02ded81d3ff490ea4097e46602705548f5872c49f06e3516fd69f17e72a7fc60631ff6d68460e48f7648
  languageName: node
  linkType: hard

"workbox-range-requests@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-range-requests@npm:7.3.0"
  dependencies:
    workbox-core: "npm:7.3.0"
  checksum: 10c0/d48e1484866442864d66b1891c4965b71e997a83a7634f11452ec1a73a30a5e642e6a95d5cff45578bef4dec7a5f57bc598aeedb6189d17ca210e2c5f2898244
  languageName: node
  linkType: hard

"workbox-recipes@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-recipes@npm:7.3.0"
  dependencies:
    workbox-cacheable-response: "npm:7.3.0"
    workbox-core: "npm:7.3.0"
    workbox-expiration: "npm:7.3.0"
    workbox-precaching: "npm:7.3.0"
    workbox-routing: "npm:7.3.0"
    workbox-strategies: "npm:7.3.0"
  checksum: 10c0/c8146ece4247cbcbefba36a14f2cb65b5f74b2412f64cfc7955ff75ff653857161a1f1d94c987fbae4812f5b770eedcf99af965e512cc375fbc7fb5421bdc99c
  languageName: node
  linkType: hard

"workbox-routing@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-routing@npm:7.3.0"
  dependencies:
    workbox-core: "npm:7.3.0"
  checksum: 10c0/8ac1824211d0fbe0e916ecb2c2427bcb0ef8783f9225d8114fe22e6c326f2d8a040a089bead58064e8b096ec95abe070c04cd7353dd8830dba3ab8d608a053aa
  languageName: node
  linkType: hard

"workbox-strategies@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-strategies@npm:7.3.0"
  dependencies:
    workbox-core: "npm:7.3.0"
  checksum: 10c0/50f3c28b46b54885a9461ad6559010d9abb2a7e35e0128d05c268f3ea0a96b1a747934758121d0e821f7af63946d9db8f4d2d7e0146f12555fb05c768e6b82bb
  languageName: node
  linkType: hard

"workbox-streams@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-streams@npm:7.3.0"
  dependencies:
    workbox-core: "npm:7.3.0"
    workbox-routing: "npm:7.3.0"
  checksum: 10c0/2ae541343d187eb7a50da2cfd74051f15771d1ddd1cad6856ffd530f7cccdb8eed9a8af94ff7540b710fef73eeec37d652123ae42b0206fbbd0679dc25e66ff4
  languageName: node
  linkType: hard

"workbox-sw@npm:7.3.0":
  version: 7.3.0
  resolution: "workbox-sw@npm:7.3.0"
  checksum: 10c0/9ae275e31dd5ec51245773b6d90fda16d0b7f70d59f3a71aec732814b5aedf08aedc7fcce57739e7e89d9e1479ef97e3a202a542a511d732cf5e8b5d1c293870
  languageName: node
  linkType: hard

"workbox-window@npm:7.3.0, workbox-window@npm:^7.1.0":
  version: 7.3.0
  resolution: "workbox-window@npm:7.3.0"
  dependencies:
    "@types/trusted-types": "npm:^2.0.2"
    workbox-core: "npm:7.3.0"
  checksum: 10c0/dbda33c4761ec40051cfe6e3f1701b2381b4f3b191f7a249c32f683503ea35cf8b42d1f99df5ba3b693fac78705d8ed0c191488bdd178c525d1291d0161ec8ff
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/baad244e6e33335ea24e86e51868fe6823626e3a3c88d9a6674642afff1d34d9a154c917e74af8d845fd25d170c4ea9cf69a47133c3f3656e1252b3d462d9f6c
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"ws@npm:8.18.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/25eb33aff17edcb90721ed6b0eb250976328533ad3cd1a28a274bd263682e7296a6591ff1436d6cbc50fa67463158b062f9d1122013b361cec99a05f84680e06
  languageName: node
  linkType: hard

"ws@npm:8.18.2":
  version: 8.18.2
  resolution: "ws@npm:8.18.2"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/4b50f67931b8c6943c893f59c524f0e4905bbd183016cfb0f2b8653aa7f28dad4e456b9d99d285bbb67cca4fedd9ce90dfdfaa82b898a11414ebd66ee99141e4
  languageName: node
  linkType: hard

"ws@npm:^7.5.1":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/bd7d5f4aaf04fae7960c23dcb6c6375d525e00f795dd20b9385902bd008c40a94d3db3ce97d878acc7573df852056ca546328b27b39f47609f80fb22a0a9b61d
  languageName: node
  linkType: hard

"ws@npm:~8.17.1":
  version: 8.17.1
  resolution: "ws@npm:8.17.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/f4a49064afae4500be772abdc2211c8518f39e1c959640457dcee15d4488628620625c783902a52af2dd02f68558da2868fd06e6fd0e67ebcd09e6881b1b5bfe
  languageName: node
  linkType: hard

"xmlhttprequest-ssl@npm:~2.1.1":
  version: 2.1.2
  resolution: "xmlhttprequest-ssl@npm:2.1.2"
  checksum: 10c0/70d60869323e823f473a238f78fd108437edbc3690ecd5859c39c83217080090a18899b272e515769c0d1f518cc64cbed6b6995b23fdd7ba13b297d530b6e631
  languageName: node
  linkType: hard

"xtend@npm:^4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 10c0/308a2efd7cc296ab2c0f3b9284fd4827be01cfeb647b3ba18230e3a416eb1bc887ac050de9f8c4fd9e7856b2e8246e05d190b53c96c5ad8d8cb56dffb6f81024
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10c0/4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: 10c0/5c28b9eb7adc46544f28d9a8d20c5b3cb1215a886609a2fd41f51628d8aaa5878ccd628b755dbcd29f6bb4921bd04ffbc6dcc370689bb96e594e2f9813d2605f
  languageName: node
  linkType: hard

"yargs-parser@npm:^18.1.2":
  version: 18.1.3
  resolution: "yargs-parser@npm:18.1.3"
  dependencies:
    camelcase: "npm:^5.0.0"
    decamelize: "npm:^1.2.0"
  checksum: 10c0/25df918833592a83f52e7e4f91ba7d7bfaa2b891ebf7fe901923c2ee797534f23a176913ff6ff7ebbc1cc1725a044cc6a6539fed8bfd4e13b5b16376875f9499
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs@npm:^15.3.1":
  version: 15.4.1
  resolution: "yargs@npm:15.4.1"
  dependencies:
    cliui: "npm:^6.0.0"
    decamelize: "npm:^1.2.0"
    find-up: "npm:^4.1.0"
    get-caller-file: "npm:^2.0.1"
    require-directory: "npm:^2.1.1"
    require-main-filename: "npm:^2.0.0"
    set-blocking: "npm:^2.0.0"
    string-width: "npm:^4.2.0"
    which-module: "npm:^2.0.0"
    y18n: "npm:^4.0.0"
    yargs-parser: "npm:^18.1.2"
  checksum: 10c0/f1ca680c974333a5822732825cca7e95306c5a1e7750eb7b973ce6dc4f97a6b0a8837203c8b194f461969bfe1fb1176d1d423036635285f6010b392fa498ab2d
  languageName: node
  linkType: hard

"yargs@npm:^17.5.1":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10c0/ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"zen-observable-ts@npm:^1.2.5":
  version: 1.2.5
  resolution: "zen-observable-ts@npm:1.2.5"
  dependencies:
    zen-observable: "npm:0.8.15"
  checksum: 10c0/21d586f3d0543e1d6f05d9333a137b407dbf337907c1ee1c2fa7a7da044f7e1262e4baf4ef8902f230c6f5acb561047659eb7df73df33307233cc451efe46db1
  languageName: node
  linkType: hard

"zen-observable@npm:0.8.15":
  version: 0.8.15
  resolution: "zen-observable@npm:0.8.15"
  checksum: 10c0/71cc2f2bbb537300c3f569e25693d37b3bc91f225cefce251a71c30bc6bb3e7f8e9420ca0eb57f2ac9e492b085b8dfa075fd1e8195c40b83c951dd59c6e4fbf8
  languageName: node
  linkType: hard

"zod@npm:3.22.4":
  version: 3.22.4
  resolution: "zod@npm:3.22.4"
  checksum: 10c0/7578ab283dac0eee66a0ad0fc4a7f28c43e6745aadb3a529f59a4b851aa10872b3890398b3160f257f4b6817b4ce643debdda4fb21a2c040adda7862cab0a587
  languageName: node
  linkType: hard

"zustand@npm:5.0.0":
  version: 5.0.0
  resolution: "zustand@npm:5.0.0"
  peerDependencies:
    "@types/react": ">=18.0.0"
    immer: ">=9.0.6"
    react: ">=18.0.0"
    use-sync-external-store: ">=1.2.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    immer:
      optional: true
    react:
      optional: true
    use-sync-external-store:
      optional: true
  checksum: 10c0/7546df78aa512f1d2271e238c44699c0ac4bc57f12ae46fcfe8ba1e8a97686fc690596e654101acfabcd706099aa5d3519fc3f22d32b3082baa60699bb333e9a
  languageName: node
  linkType: hard
