# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@babel/code-frame@npm:^7.0.0":
  version: 7.24.7
  resolution: "@babel/code-frame@npm:7.24.7"
  dependencies:
    "@babel/highlight": "npm:^7.24.7"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/ab0af539473a9f5aeaac7047e377cb4f4edd255a81d84a76058595f8540784cc3fbe8acf73f1e073981104562490aabfb23008cd66dc677a456a4ed5390fdde6
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-validator-identifier@npm:7.24.7"
  checksum: 10c0/87ad608694c9477814093ed5b5c080c2e06d44cb1924ae8320474a74415241223cc2a725eea2640dd783ff1e3390e5f95eede978bc540e870053152e58f1d651
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/highlight@npm:7.24.7"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.24.7"
    chalk: "npm:^2.4.2"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/674334c571d2bb9d1c89bdd87566383f59231e16bcdcf5bb7835babdf03c9ae585ca0887a7b25bdf78f303984af028df52831c7989fecebb5101cc132da9393a
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:^0.8.0":
  version: 0.8.1
  resolution: "@cspotcode/source-map-support@npm:0.8.1"
  dependencies:
    "@jridgewell/trace-mapping": "npm:0.3.9"
  checksum: 10c0/05c5368c13b662ee4c122c7bfbe5dc0b613416672a829f3e78bc49a357a197e0218d6e74e7c66cfcd04e15a179acab080bd3c69658c9fbefd0e1ccd950a07fc6
  languageName: node
  linkType: hard

"@ethersproject/abi@npm:5.0.7":
  version: 5.0.7
  resolution: "@ethersproject/abi@npm:5.0.7"
  dependencies:
    "@ethersproject/address": "npm:^5.0.4"
    "@ethersproject/bignumber": "npm:^5.0.7"
    "@ethersproject/bytes": "npm:^5.0.4"
    "@ethersproject/constants": "npm:^5.0.4"
    "@ethersproject/hash": "npm:^5.0.4"
    "@ethersproject/keccak256": "npm:^5.0.3"
    "@ethersproject/logger": "npm:^5.0.5"
    "@ethersproject/properties": "npm:^5.0.3"
    "@ethersproject/strings": "npm:^5.0.4"
  checksum: 10c0/cb4b23481b91c99b6abf5ad476289e15187a4f1c44c5e6e951acb8f9347b0a31ffb532b8f9e3e7fd2e2f33b917c4dfaf6a87da4a1e218afa85fa8bb0fdf8e59d
  languageName: node
  linkType: hard

"@ethersproject/abstract-provider@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abstract-provider@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/networks": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/web": "npm:^5.7.0"
  checksum: 10c0/a5708e2811b90ddc53d9318ce152511a32dd4771aa2fb59dbe9e90468bb75ca6e695d958bf44d13da684dc3b6aab03f63d425ff7591332cb5d7ddaf68dff7224
  languageName: node
  linkType: hard

"@ethersproject/abstract-signer@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abstract-signer@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
  checksum: 10c0/e174966b3be17269a5974a3ae5eef6d15ac62ee8c300ceace26767f218f6bbf3de66f29d9a9c9ca300fa8551aab4c92e28d2cc772f5475fdeaa78d9b5be0e745
  languageName: node
  linkType: hard

"@ethersproject/address@npm:^5.0.4, @ethersproject/address@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/address@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
  checksum: 10c0/db5da50abeaae8f6cf17678323e8d01cad697f9a184b0593c62b71b0faa8d7e5c2ba14da78a998d691773ed6a8eb06701f65757218e0eaaeb134e5c5f3e5a908
  languageName: node
  linkType: hard

"@ethersproject/base64@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/base64@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
  checksum: 10c0/4f748cd82af60ff1866db699fbf2bf057feff774ea0a30d1f03ea26426f53293ea10cc8265cda1695301da61093bedb8cc0d38887f43ed9dad96b78f19d7337e
  languageName: node
  linkType: hard

"@ethersproject/bignumber@npm:^5.0.7, @ethersproject/bignumber@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/bignumber@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    bn.js: "npm:^5.2.1"
  checksum: 10c0/14263cdc91a7884b141d9300f018f76f69839c47e95718ef7161b11d2c7563163096fee69724c5fa8ef6f536d3e60f1c605819edbc478383a2b98abcde3d37b2
  languageName: node
  linkType: hard

"@ethersproject/bytes@npm:^5.0.4, @ethersproject/bytes@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/bytes@npm:5.7.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10c0/07dd1f0341b3de584ef26c8696674ff2bb032f4e99073856fc9cd7b4c54d1d846cabe149e864be267934658c3ce799e5ea26babe01f83af0e1f06c51e5ac791f
  languageName: node
  linkType: hard

"@ethersproject/constants@npm:^5.0.4, @ethersproject/constants@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/constants@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
  checksum: 10c0/6df63ab753e152726b84595250ea722165a5744c046e317df40a6401f38556385a37c84dadf5b11ca651c4fb60f967046125369c57ac84829f6b30e69a096273
  languageName: node
  linkType: hard

"@ethersproject/hash@npm:^5.0.4":
  version: 5.7.0
  resolution: "@ethersproject/hash@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/base64": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 10c0/1a631dae34c4cf340dde21d6940dd1715fc7ae483d576f7b8ef9e8cb1d0e30bd7e8d30d4a7d8dc531c14164602323af2c3d51eb2204af18b2e15167e70c9a5ef
  languageName: node
  linkType: hard

"@ethersproject/keccak256@npm:^5.0.3, @ethersproject/keccak256@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/keccak256@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    js-sha3: "npm:0.8.0"
  checksum: 10c0/3b1a91706ff11f5ab5496840b9c36cedca27db443186d28b94847149fd16baecdc13f6fc5efb8359506392f2aba559d07e7f9c1e17a63f9d5de9f8053cfcb033
  languageName: node
  linkType: hard

"@ethersproject/logger@npm:^5.0.5, @ethersproject/logger@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/logger@npm:5.7.0"
  checksum: 10c0/d03d460fb2d4a5e71c627b7986fb9e50e1b59a6f55e8b42a545b8b92398b961e7fd294bd9c3d8f92b35d0f6ff9d15aa14c95eab378f8ea194e943c8ace343501
  languageName: node
  linkType: hard

"@ethersproject/networks@npm:^5.7.0":
  version: 5.7.1
  resolution: "@ethersproject/networks@npm:5.7.1"
  dependencies:
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10c0/9efcdce27f150459e85d74af3f72d5c32898823a99f5410e26bf26cca2d21fb14e403377314a93aea248e57fb2964e19cee2c3f7bfc586ceba4c803a8f1b75c0
  languageName: node
  linkType: hard

"@ethersproject/properties@npm:^5.0.3, @ethersproject/properties@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/properties@npm:5.7.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10c0/4fe5d36e5550b8e23a305aa236a93e8f04d891d8198eecdc8273914c761b0e198fd6f757877406ee3eb05033ec271132a3e5998c7bd7b9a187964fb4f67b1373
  languageName: node
  linkType: hard

"@ethersproject/rlp@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/rlp@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10c0/bc863d21dcf7adf6a99ae75c41c4a3fb99698cfdcfc6d5d82021530f3d3551c6305bc7b6f0475ad6de6f69e91802b7e872bee48c0596d98969aefcf121c2a044
  languageName: node
  linkType: hard

"@ethersproject/signing-key@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/signing-key@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    bn.js: "npm:^5.2.1"
    elliptic: "npm:6.5.4"
    hash.js: "npm:1.1.7"
  checksum: 10c0/fe2ca55bcdb6e370d81372191d4e04671234a2da872af20b03c34e6e26b97dc07c1ee67e91b673680fb13344c9d5d7eae52f1fa6117733a3d68652b778843e09
  languageName: node
  linkType: hard

"@ethersproject/strings@npm:^5.0.4, @ethersproject/strings@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/strings@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10c0/570d87040ccc7d94de9861f76fc2fba6c0b84c5d6104a99a5c60b8a2401df2e4f24bf9c30afa536163b10a564a109a96f02e6290b80e8f0c610426f56ad704d1
  languageName: node
  linkType: hard

"@ethersproject/transactions@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/transactions@npm:5.7.0"
  dependencies:
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
    "@ethersproject/signing-key": "npm:^5.7.0"
  checksum: 10c0/aa4d51379caab35b9c468ed1692a23ae47ce0de121890b4f7093c982ee57e30bd2df0c743faed0f44936d7e59c55fffd80479f2c28ec6777b8de06bfb638c239
  languageName: node
  linkType: hard

"@ethersproject/web@npm:^5.7.0":
  version: 5.7.1
  resolution: "@ethersproject/web@npm:5.7.1"
  dependencies:
    "@ethersproject/base64": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 10c0/c82d6745c7f133980e8dab203955260e07da22fa544ccafdd0f21c79fae127bd6ef30957319e37b1cc80cddeb04d6bfb60f291bb14a97c9093d81ce50672f453
  languageName: node
  linkType: hard

"@float-capital/float-subgraph-uncrashable@npm:^0.0.0-alpha.4":
  version: 0.0.0-internal-testing.5
  resolution: "@float-capital/float-subgraph-uncrashable@npm:0.0.0-internal-testing.5"
  dependencies:
    "@rescript/std": "npm:9.0.0"
    graphql: "npm:^16.6.0"
    graphql-import-node: "npm:^0.0.5"
    js-yaml: "npm:^4.1.0"
  bin:
    uncrashable: bin/uncrashable
  checksum: 10c0/4cdcb101fde8eadef358f279ccf6eae22e213a8d4c7839be33f230a8e0bee2582bdd37ca45b4f09f21eb49c4c24f55f18fcb90035756ef55fcecf59292c5190f
  languageName: node
  linkType: hard

"@graphprotocol/graph-cli@npm:^0.81.0":
  version: 0.81.0
  resolution: "@graphprotocol/graph-cli@npm:0.81.0"
  dependencies:
    "@float-capital/float-subgraph-uncrashable": "npm:^0.0.0-alpha.4"
    "@oclif/core": "npm:2.8.6"
    "@oclif/plugin-autocomplete": "npm:^2.3.6"
    "@oclif/plugin-not-found": "npm:^2.4.0"
    "@whatwg-node/fetch": "npm:^0.8.4"
    assemblyscript: "npm:0.19.23"
    binary-install-raw: "npm:0.0.13"
    chalk: "npm:3.0.0"
    chokidar: "npm:3.5.3"
    debug: "npm:4.3.4"
    docker-compose: "npm:0.23.19"
    dockerode: "npm:2.5.8"
    fs-extra: "npm:9.1.0"
    glob: "npm:9.3.5"
    gluegun: "npm:5.1.6"
    graphql: "npm:15.5.0"
    immutable: "npm:4.2.1"
    ipfs-http-client: "npm:55.0.0"
    jayson: "npm:4.0.0"
    js-yaml: "npm:3.14.1"
    open: "npm:8.4.2"
    prettier: "npm:3.0.3"
    semver: "npm:7.4.0"
    sync-request: "npm:6.1.0"
    tmp-promise: "npm:3.0.3"
    web3-eth-abi: "npm:1.7.0"
    which: "npm:2.0.2"
    yaml: "npm:1.10.2"
  bin:
    graph: bin/run
  checksum: 10c0/043391713a2f48be0bae7632e8e86407d320bb77aa89e5758e809ff5d722992914d8b306b4ef2d628f8fccd1d01984ea68595ed3ce398fa67c6717d5d93edc38
  languageName: node
  linkType: hard

"@graphprotocol/graph-ts@npm:^0.35.1":
  version: 0.35.1
  resolution: "@graphprotocol/graph-ts@npm:0.35.1"
  dependencies:
    assemblyscript: "npm:0.19.10"
  checksum: 10c0/f6c4e32d3bf06a53885ad5f4f9d894e4a49a42f70bb9b0f7507eda292302cbe8a270597fcf1bb2976f9df53e4f0ed9be966ec36122f31bebf9150319e9778aed
  languageName: node
  linkType: hard

"@ipld/dag-cbor@npm:^7.0.0":
  version: 7.0.3
  resolution: "@ipld/dag-cbor@npm:7.0.3"
  dependencies:
    cborg: "npm:^1.6.0"
    multiformats: "npm:^9.5.4"
  checksum: 10c0/1298d55f224a6d9d1b42e8f33241d5453d810937a01909fbca477dfc25bf57706efc2e3ab5a272c7c4bcfb243ff4480d70db40042bbc91b2ed3400f90e64c676
  languageName: node
  linkType: hard

"@ipld/dag-json@npm:^8.0.1":
  version: 8.0.11
  resolution: "@ipld/dag-json@npm:8.0.11"
  dependencies:
    cborg: "npm:^1.5.4"
    multiformats: "npm:^9.5.4"
  checksum: 10c0/75d8ed4e2600926c75e53186faef7e91340af6ef30bfa3d8cde942b15cceec204c46ea457e6da0dd43ebe01f96d2d5db61f8ed7028bcf72c90c34cfe0c05b5ed
  languageName: node
  linkType: hard

"@ipld/dag-pb@npm:^2.1.3":
  version: 2.1.18
  resolution: "@ipld/dag-pb@npm:2.1.18"
  dependencies:
    multiformats: "npm:^9.5.4"
  checksum: 10c0/7b53ae5d567672319971e8ae65b85f86c98ad35c6233afe1fa1d0125fc26104dc46f7cb4b9cfe0b3d7cfa331b4e488f22b729f762ee587308429c534267b3e7f
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10":
  version: 1.4.15
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.15"
  checksum: 10c0/0c6b5ae663087558039052a626d2d7ed5208da36cfd707dcc5cea4a07cfc918248403dcb5989a8f7afaf245ce0573b7cc6fd94c4a30453bd10e44d9363940ba5
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:0.3.9":
  version: 0.3.9
  resolution: "@jridgewell/trace-mapping@npm:0.3.9"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.0.3"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
  checksum: 10c0/fa425b606d7c7ee5bfa6a31a7b050dd5814b4082f318e0e4190f991902181b4330f43f4805db1dd4f2433fd0ed9cc7a7b9c2683f1deeab1df1b0a98b1e24055b
  languageName: node
  linkType: hard

"@noble/hashes@npm:^1.4.0":
  version: 1.4.0
  resolution: "@noble/hashes@npm:1.4.0"
  checksum: 10c0/8c3f005ee72e7b8f9cff756dfae1241485187254e3f743873e22073d63906863df5d4f13d441b7530ea614b7a093f0d889309f28b59850f33b66cb26a779a4a5
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^2.0.0":
  version: 2.2.2
  resolution: "@npmcli/agent@npm:2.2.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/325e0db7b287d4154ecd164c0815c08007abfb07653cc57bceded17bb7fd240998a3cbdbe87d700e30bef494885eccc725ab73b668020811d56623d145b524ae
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.1
  resolution: "@npmcli/fs@npm:3.1.1"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c37a5b4842bfdece3d14dfdb054f73fe15ed2d3da61b34ff76629fb5b1731647c49166fd2a8bf8b56fcfa51200382385ea8909a3cbecdad612310c114d3f6c99
  languageName: node
  linkType: hard

"@oclif/core@npm:2.8.6":
  version: 2.8.6
  resolution: "@oclif/core@npm:2.8.6"
  dependencies:
    "@types/cli-progress": "npm:^3.11.0"
    ansi-escapes: "npm:^4.3.2"
    ansi-styles: "npm:^4.3.0"
    cardinal: "npm:^2.1.1"
    chalk: "npm:^4.1.2"
    clean-stack: "npm:^3.0.1"
    cli-progress: "npm:^3.12.0"
    debug: "npm:^4.3.4"
    ejs: "npm:^3.1.8"
    fs-extra: "npm:^9.1.0"
    get-package-type: "npm:^0.1.0"
    globby: "npm:^11.1.0"
    hyperlinker: "npm:^1.0.0"
    indent-string: "npm:^4.0.0"
    is-wsl: "npm:^2.2.0"
    js-yaml: "npm:^3.14.1"
    natural-orderby: "npm:^2.0.3"
    object-treeify: "npm:^1.1.33"
    password-prompt: "npm:^1.1.2"
    semver: "npm:^7.3.7"
    string-width: "npm:^4.2.3"
    strip-ansi: "npm:^6.0.1"
    supports-color: "npm:^8.1.1"
    supports-hyperlinks: "npm:^2.2.0"
    ts-node: "npm:^10.9.1"
    tslib: "npm:^2.5.0"
    widest-line: "npm:^3.1.0"
    wordwrap: "npm:^1.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/3809a35076289303fd6e8d9edaff783c42f0486c072c40e38fd8ba6290dac6810066f57c3d0c54604a5752c6dfe6824a2f4751a787e80569de1c74c1f282e408
  languageName: node
  linkType: hard

"@oclif/core@npm:^2.15.0":
  version: 2.16.0
  resolution: "@oclif/core@npm:2.16.0"
  dependencies:
    "@types/cli-progress": "npm:^3.11.0"
    ansi-escapes: "npm:^4.3.2"
    ansi-styles: "npm:^4.3.0"
    cardinal: "npm:^2.1.1"
    chalk: "npm:^4.1.2"
    clean-stack: "npm:^3.0.1"
    cli-progress: "npm:^3.12.0"
    debug: "npm:^4.3.4"
    ejs: "npm:^3.1.8"
    get-package-type: "npm:^0.1.0"
    globby: "npm:^11.1.0"
    hyperlinker: "npm:^1.0.0"
    indent-string: "npm:^4.0.0"
    is-wsl: "npm:^2.2.0"
    js-yaml: "npm:^3.14.1"
    natural-orderby: "npm:^2.0.3"
    object-treeify: "npm:^1.1.33"
    password-prompt: "npm:^1.1.2"
    slice-ansi: "npm:^4.0.0"
    string-width: "npm:^4.2.3"
    strip-ansi: "npm:^6.0.1"
    supports-color: "npm:^8.1.1"
    supports-hyperlinks: "npm:^2.2.0"
    ts-node: "npm:^10.9.1"
    tslib: "npm:^2.5.0"
    widest-line: "npm:^3.1.0"
    wordwrap: "npm:^1.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/1795fe61b736f1156b8a1ede274a98cd58c1d3244c16df1e105a1dbf4a2c155a5bde0b6efb40b8bf9bd7f8c1042fb5f1693009298cc3239c8d93fe254907f395
  languageName: node
  linkType: hard

"@oclif/plugin-autocomplete@npm:^2.3.6":
  version: 2.3.10
  resolution: "@oclif/plugin-autocomplete@npm:2.3.10"
  dependencies:
    "@oclif/core": "npm:^2.15.0"
    chalk: "npm:^4.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/9b306ce2b2000234484ccadc2b9bff3a57d9ddb1e13b231a4465a8d4df4e956699c8849d4850f99873e2bc64dd5e4bc9e5e3bf7af187a59231b7e6bc4cd406a9
  languageName: node
  linkType: hard

"@oclif/plugin-not-found@npm:^2.4.0":
  version: 2.4.3
  resolution: "@oclif/plugin-not-found@npm:2.4.3"
  dependencies:
    "@oclif/core": "npm:^2.15.0"
    chalk: "npm:^4"
    fast-levenshtein: "npm:^3.0.0"
  checksum: 10c0/47171c920d6acba5e105915a10d1d68eef242cf7f8434bdc18b33610bbd4d4f0957b0cb693ab52f19d172ad140c4619879dce091b02c823a4aa5671b37a1c452
  languageName: node
  linkType: hard

"@peculiar/asn1-schema@npm:^2.3.8":
  version: 2.3.8
  resolution: "@peculiar/asn1-schema@npm:2.3.8"
  dependencies:
    asn1js: "npm:^3.0.5"
    pvtsutils: "npm:^1.3.5"
    tslib: "npm:^2.6.2"
  checksum: 10c0/65f16b2a7eb91365b6dac47730ffcad4617ef04b821e0a4286c379ac7283588b0a6744032ee686e0914a0886c2a055108ed945b9c4d22821a3b123640b61f3b2
  languageName: node
  linkType: hard

"@peculiar/json-schema@npm:^1.1.12":
  version: 1.1.12
  resolution: "@peculiar/json-schema@npm:1.1.12"
  dependencies:
    tslib: "npm:^2.0.0"
  checksum: 10c0/202132c66dcc6b6aca5d0af971c015be2e163da2f7f992910783c5d39c8a7db59b6ec4f4ce419459a1f954b7e1d17b6b253f0e60072c1b3d254079f4eaebc311
  languageName: node
  linkType: hard

"@peculiar/webcrypto@npm:^1.4.0":
  version: 1.5.0
  resolution: "@peculiar/webcrypto@npm:1.5.0"
  dependencies:
    "@peculiar/asn1-schema": "npm:^2.3.8"
    "@peculiar/json-schema": "npm:^1.1.12"
    pvtsutils: "npm:^1.3.5"
    tslib: "npm:^2.6.2"
    webcrypto-core: "npm:^1.8.0"
  checksum: 10c0/4f6f24b2c52c2155b9c569b6eb1d57954cb5f7bd2764a50cdaed7aea17a6dcf304b75b87b57ba318756ffec8179a07d9a76534aaf77855912b838543e5ff8983
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@protobufjs/aspromise@npm:^1.1.1, @protobufjs/aspromise@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/aspromise@npm:1.1.2"
  checksum: 10c0/a83343a468ff5b5ec6bff36fd788a64c839e48a07ff9f4f813564f58caf44d011cd6504ed2147bf34835bd7a7dd2107052af755961c6b098fd8902b4f6500d0f
  languageName: node
  linkType: hard

"@protobufjs/base64@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/base64@npm:1.1.2"
  checksum: 10c0/eec925e681081af190b8ee231f9bad3101e189abbc182ff279da6b531e7dbd2a56f1f306f37a80b1be9e00aa2d271690d08dcc5f326f71c9eed8546675c8caf6
  languageName: node
  linkType: hard

"@protobufjs/codegen@npm:^2.0.4":
  version: 2.0.4
  resolution: "@protobufjs/codegen@npm:2.0.4"
  checksum: 10c0/26ae337c5659e41f091606d16465bbcc1df1f37cc1ed462438b1f67be0c1e28dfb2ca9f294f39100c52161aef82edf758c95d6d75650a1ddf31f7ddee1440b43
  languageName: node
  linkType: hard

"@protobufjs/eventemitter@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/eventemitter@npm:1.1.0"
  checksum: 10c0/1eb0a75180e5206d1033e4138212a8c7089a3d418c6dfa5a6ce42e593a4ae2e5892c4ef7421f38092badba4040ea6a45f0928869989411001d8c1018ea9a6e70
  languageName: node
  linkType: hard

"@protobufjs/fetch@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/fetch@npm:1.1.0"
  dependencies:
    "@protobufjs/aspromise": "npm:^1.1.1"
    "@protobufjs/inquire": "npm:^1.1.0"
  checksum: 10c0/cda6a3dc2d50a182c5865b160f72077aac197046600091dbb005dd0a66db9cce3c5eaed6d470ac8ed49d7bcbeef6ee5f0bc288db5ff9a70cbd003e5909065233
  languageName: node
  linkType: hard

"@protobufjs/float@npm:^1.0.2":
  version: 1.0.2
  resolution: "@protobufjs/float@npm:1.0.2"
  checksum: 10c0/18f2bdede76ffcf0170708af15c9c9db6259b771e6b84c51b06df34a9c339dbbeec267d14ce0bddd20acc142b1d980d983d31434398df7f98eb0c94a0eb79069
  languageName: node
  linkType: hard

"@protobufjs/inquire@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/inquire@npm:1.1.0"
  checksum: 10c0/64372482efcba1fb4d166a2664a6395fa978b557803857c9c03500e0ac1013eb4b1aacc9ed851dd5fc22f81583670b4f4431bae186f3373fedcfde863ef5921a
  languageName: node
  linkType: hard

"@protobufjs/path@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/path@npm:1.1.2"
  checksum: 10c0/cece0a938e7f5dfd2fa03f8c14f2f1cf8b0d6e13ac7326ff4c96ea311effd5fb7ae0bba754fbf505312af2e38500250c90e68506b97c02360a43793d88a0d8b4
  languageName: node
  linkType: hard

"@protobufjs/pool@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/pool@npm:1.1.0"
  checksum: 10c0/eda2718b7f222ac6e6ad36f758a92ef90d26526026a19f4f17f668f45e0306a5bd734def3f48f51f8134ae0978b6262a5c517c08b115a551756d1a3aadfcf038
  languageName: node
  linkType: hard

"@protobufjs/utf8@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/utf8@npm:1.1.0"
  checksum: 10c0/a3fe31fe3fa29aa3349e2e04ee13dc170cc6af7c23d92ad49e3eeaf79b9766264544d3da824dba93b7855bd6a2982fb40032ef40693da98a136d835752beb487
  languageName: node
  linkType: hard

"@rescript/std@npm:9.0.0":
  version: 9.0.0
  resolution: "@rescript/std@npm:9.0.0"
  checksum: 10c0/d05b19c8f958e0ff9f56cf4affa12a467a3009a7fa837cbf8eefb8aa86137127a530d316d86ea5404e430d07554b88b65dde7139bc1664f959c7a46025091b6b
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node10@npm:1.0.11"
  checksum: 10c0/28a0710e5d039e0de484bdf85fee883bfd3f6a8980601f4d44066b0a6bcd821d31c4e231d1117731c4e24268bd4cf2a788a6787c12fc7f8d11014c07d582783c
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node12@npm:1.0.11"
  checksum: 10c0/dddca2b553e2bee1308a056705103fc8304e42bb2d2cbd797b84403a223b25c78f2c683ec3e24a095e82cd435387c877239bffcb15a590ba817cd3f6b9a99fd9
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.3
  resolution: "@tsconfig/node14@npm:1.0.3"
  checksum: 10c0/67c1316d065fdaa32525bc9449ff82c197c4c19092b9663b23213c8cbbf8d88b6ed6a17898e0cbc2711950fbfaf40388938c1c748a2ee89f7234fc9e7fe2bf44
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.4
  resolution: "@tsconfig/node16@npm:1.0.4"
  checksum: 10c0/05f8f2734e266fb1839eb1d57290df1664fe2aa3b0fdd685a9035806daa635f7519bf6d5d9b33f6e69dd545b8c46bd6e2b5c79acb2b1f146e885f7f11a42a5bb
  languageName: node
  linkType: hard

"@types/bn.js@npm:^5.1.0":
  version: 5.1.5
  resolution: "@types/bn.js@npm:5.1.5"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/e9f375b43d8119ed82aed2090f83d4cda8afbb63ba13223afb02fa7550258ff90acd76d65cd7186838644048f085241cd98a3a512d8d187aa497c6039c746ac8
  languageName: node
  linkType: hard

"@types/cli-progress@npm:^3.11.0":
  version: 3.11.5
  resolution: "@types/cli-progress@npm:3.11.5"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/bf00f543ee677f61b12e390876df59354943d6c13d96640171528e9b7827f4edb7701cdd4675d6256d13ef9ee542731bd5cae585e1b43502553f69fc210dcb92
  languageName: node
  linkType: hard

"@types/concat-stream@npm:^1.6.0":
  version: 1.6.1
  resolution: "@types/concat-stream@npm:1.6.1"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/838a0ec89d59a11c425b7728fdd05b17b652086a27fdf5b787778521ccf6d3133d9e9a6e6b803785b28c0a0f7a437582813e37b317ed8100870af836ad49a7a2
  languageName: node
  linkType: hard

"@types/connect@npm:^3.4.33":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/2e1cdba2c410f25649e77856505cd60223250fa12dff7a503e492208dbfdd25f62859918f28aba95315251fd1f5e1ffbfca1e25e73037189ab85dd3f8d0a148c
  languageName: node
  linkType: hard

"@types/form-data@npm:0.0.33":
  version: 0.0.33
  resolution: "@types/form-data@npm:0.0.33"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/20bd8f7491d759ce613e35612aef37b3084be43466883ce83e1261905032939bc9e51e470e61bccf6d2f08a39659c44795531bbf66af177176ab0ddbd968e155
  languageName: node
  linkType: hard

"@types/long@npm:^4.0.1":
  version: 4.0.2
  resolution: "@types/long@npm:4.0.2"
  checksum: 10c0/42ec66ade1f72ff9d143c5a519a65efc7c1c77be7b1ac5455c530ae9acd87baba065542f8847522af2e3ace2cc999f3ad464ef86e6b7352eece34daf88f8c924
  languageName: node
  linkType: hard

"@types/minimatch@npm:^3.0.4":
  version: 3.0.5
  resolution: "@types/minimatch@npm:3.0.5"
  checksum: 10c0/a1a19ba342d6f39b569510f621ae4bbe972dc9378d15e9a5e47904c440ee60744f5b09225bc73be1c6490e3a9c938eee69eb53debf55ce1f15761201aa965f97
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:>=13.7.0":
  version: 20.14.2
  resolution: "@types/node@npm:20.14.2"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10c0/2d86e5f2227aaa42212e82ea0affe72799111b888ff900916376450b02b09b963ca888b20d9c332d8d2b833ed4781987867a38eaa2e4863fa8439071468b0a6f
  languageName: node
  linkType: hard

"@types/node@npm:22.12.0":
  version: 22.12.0
  resolution: "@types/node@npm:22.12.0"
  dependencies:
    undici-types: "npm:~6.20.0"
  checksum: 10c0/be220706732d95db2ed1c441c1e64cab90bf9a47519ce6f4c79cc5a9ec9d5c517131a149a9ac30afac1a30103e67e3a00d453ba7c1b0141608a3a7ba6397c303
  languageName: node
  linkType: hard

"@types/node@npm:^10.0.3":
  version: 10.17.60
  resolution: "@types/node@npm:10.17.60"
  checksum: 10c0/0742294912a6e79786cdee9ed77cff6ee8ff007b55d8e21170fc3e5994ad3a8101fea741898091876f8dc32b0a5ae3d64537b7176799e92da56346028d2cbcd2
  languageName: node
  linkType: hard

"@types/node@npm:^12.12.54":
  version: 12.20.55
  resolution: "@types/node@npm:12.20.55"
  checksum: 10c0/3b190bb0410047d489c49bbaab592d2e6630de6a50f00ba3d7d513d59401d279972a8f5a598b5bb8ddc1702f8a2f4ec57a65d93852f9c329639738e7053637d1
  languageName: node
  linkType: hard

"@types/node@npm:^8.0.0":
  version: 8.10.66
  resolution: "@types/node@npm:8.10.66"
  checksum: 10c0/425e0fca5bad0d6ff14336946a1e3577750dcfbb7449614786d3241ca78ff44e3beb43eace122682de1b9d8e25cf2a0456a0b3e500d78cb55cab68f892e38141
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.2
  resolution: "@types/parse-json@npm:4.0.2"
  checksum: 10c0/b1b863ac34a2c2172fbe0807a1ec4d5cb684e48d422d15ec95980b81475fac4fdb3768a8b13eef39130203a7c04340fc167bae057c7ebcafd7dec9fe6c36aeb1
  languageName: node
  linkType: hard

"@types/pbkdf2@npm:^3.0.0":
  version: 3.1.2
  resolution: "@types/pbkdf2@npm:3.1.2"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/4f60b0e3c73297f55023b993d3d543212aa7f61c8c0d6a2720f5dbe2cf38e2fe55ff295d550ac048dddbfc3d44c285cfe16126d65c613bd67a57662357e268d9
  languageName: node
  linkType: hard

"@types/qs@npm:^6.2.31":
  version: 6.9.15
  resolution: "@types/qs@npm:6.9.15"
  checksum: 10c0/49c5ff75ca3adb18a1939310042d273c9fc55920861bd8e5100c8a923b3cda90d759e1a95e18334092da1c8f7b820084687770c83a1ccef04fb2c6908117c823
  languageName: node
  linkType: hard

"@types/secp256k1@npm:^4.0.1":
  version: 4.0.6
  resolution: "@types/secp256k1@npm:4.0.6"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/0e391316ae30c218779583b626382a56546ddbefb65f1ff9cf5e078af8a7118f67f3e66e30914399cc6f8710c424d0d8c3f34262ffb1f429c6ad911fd0d0bc26
  languageName: node
  linkType: hard

"@types/ws@npm:^7.4.4":
  version: 7.4.7
  resolution: "@types/ws@npm:7.4.7"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/f1f53febd8623a85cef2652949acd19d83967e350ea15a851593e3033501750a1e04f418552e487db90a3d48611a1cff3ffcf139b94190c10f2fd1e1dc95ff10
  languageName: node
  linkType: hard

"@whatwg-node/events@npm:^0.0.3":
  version: 0.0.3
  resolution: "@whatwg-node/events@npm:0.0.3"
  checksum: 10c0/87ac0854f84650ce016ccd82a6c087eac1c6204eeb80cf358737ce7757a345e3a4ba19e9b1815b326eb1451d49878785aa9dc426631f4ea47dedbcfc51b56977
  languageName: node
  linkType: hard

"@whatwg-node/fetch@npm:^0.8.4":
  version: 0.8.8
  resolution: "@whatwg-node/fetch@npm:0.8.8"
  dependencies:
    "@peculiar/webcrypto": "npm:^1.4.0"
    "@whatwg-node/node-fetch": "npm:^0.3.6"
    busboy: "npm:^1.6.0"
    urlpattern-polyfill: "npm:^8.0.0"
    web-streams-polyfill: "npm:^3.2.1"
  checksum: 10c0/37d882bf85764aec7541cda1008099ab4d695971608946ec9b9e40326eedfd4c49507fbcc8765ebe3e9241f4dc9d1e970e0b3501a814d721c40c721d313c5d50
  languageName: node
  linkType: hard

"@whatwg-node/node-fetch@npm:^0.3.6":
  version: 0.3.6
  resolution: "@whatwg-node/node-fetch@npm:0.3.6"
  dependencies:
    "@whatwg-node/events": "npm:^0.0.3"
    busboy: "npm:^1.6.0"
    fast-querystring: "npm:^1.1.1"
    fast-url-parser: "npm:^1.1.3"
    tslib: "npm:^2.3.1"
  checksum: 10c0/49e4fd5e682d1fa1229b2c13c06074c6a633eddbe61be162fd213ddb85d6d27d51554b3cced5f6b7f3be1722a64cca7f5ffe0722d08b3285fe2f289d8d5a045d
  languageName: node
  linkType: hard

"JSONStream@npm:1.3.2":
  version: 1.3.2
  resolution: "JSONStream@npm:1.3.2"
  dependencies:
    jsonparse: "npm:^1.2.0"
    through: "npm:>=2.2.7 <3"
  bin:
    JSONStream: ./bin.js
  checksum: 10c0/97057683a9626969dff1af2a84b715a123a190def2e9dbd3ae5bd882f17395057e9489fd77832fad8945ba76f63ef99f7d6236a4f7183f5b4415681716c51480
  languageName: node
  linkType: hard

"JSONStream@npm:^1.3.5":
  version: 1.3.5
  resolution: "JSONStream@npm:1.3.5"
  dependencies:
    jsonparse: "npm:^1.2.0"
    through: "npm:>=2.2.7 <3"
  bin:
    JSONStream: ./bin.js
  checksum: 10c0/0f54694da32224d57b715385d4a6b668d2117379d1f3223dc758459246cca58fdc4c628b83e8a8883334e454a0a30aa198ede77c788b55537c1844f686a751f2
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 10c0/f742a5a107473946f426c691c08daba61a1d15942616f300b5d32fd735be88fef5cba24201757b6c407fd564555fb48c751cfa33519b2605c8a7aadd22baf372
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.3.3
  resolution: "acorn-walk@npm:8.3.3"
  dependencies:
    acorn: "npm:^8.11.0"
  checksum: 10c0/4a9e24313e6a0a7b389e712ba69b66b455b4cb25988903506a8d247e7b126f02060b05a8a5b738a9284214e4ca95f383dd93443a4ba84f1af9b528305c7f243b
  languageName: node
  linkType: hard

"acorn@npm:^8.11.0, acorn@npm:^8.4.1":
  version: 8.12.0
  resolution: "acorn@npm:8.12.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/a19f9dead009d3b430fa3c253710b47778cdaace15b316de6de93a68c355507bc1072a9956372b6c990cbeeb167d4a929249d0faeb8ae4bb6911d68d53299549
  languageName: node
  linkType: hard

"agent-base@npm:^7.0.2, agent-base@npm:^7.1.0, agent-base@npm:^7.1.1":
  version: 7.1.1
  resolution: "agent-base@npm:7.1.1"
  dependencies:
    debug: "npm:^4.3.4"
  checksum: 10c0/e59ce7bed9c63bf071a30cc471f2933862044c97fd9958967bfe22521d7a0f601ce4ed5a8c011799d0c726ca70312142ae193bbebb60f576b52be19d4a363b50
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: "npm:^2.0.0"
    indent-string: "npm:^4.0.0"
  checksum: 10c0/a42f67faa79e3e6687a4923050e7c9807db3848a037076f791d10e092677d65c1d2d863b7848560699f40fc0502c19f40963fb1cd1fb3d338a7423df8e45e039
  languageName: node
  linkType: hard

"ansi-colors@npm:^4.1.1":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: 10c0/ec87a2f59902f74e61eada7f6e6fe20094a628dab765cfdbd03c3477599368768cffccdb5d3bb19a1b6c99126783a143b1fee31aab729b31ffe5836c7e5e28b9
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.3.2":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10c0/da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-regex@npm:^4.1.0":
  version: 4.1.1
  resolution: "ansi-regex@npm:4.1.1"
  checksum: 10c0/d36d34234d077e8770169d980fed7b2f3724bfa2a01da150ccd75ef9707c80e883d27cdf7a0eac2f145ac1d10a785a8a855cffd05b85f778629a0db62e7033da
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: 10c0/ece5a8ef069fcc5298f67e3f4771a663129abd174ea2dfa87923a2be2abf6cd367ef72ac87942da00ce85bd1d651d4cd8595aebdb1b385889b89b205860e977b
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0, ansi-styles@npm:^4.3.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"ansicolors@npm:~0.3.2":
  version: 0.3.2
  resolution: "ansicolors@npm:0.3.2"
  checksum: 10c0/e202182895e959c5357db6c60791b2abaade99fcc02221da11a581b26a7f83dc084392bc74e4d3875c22f37b3c9ef48842e896e3bfed394ec278194b8003e0ac
  languageName: node
  linkType: hard

"any-signal@npm:^2.1.2":
  version: 2.1.2
  resolution: "any-signal@npm:2.1.2"
  dependencies:
    abort-controller: "npm:^3.0.0"
    native-abort-controller: "npm:^1.0.3"
  checksum: 10c0/a3f7a8f5cd6602ae7f6dbe642a8fa0af3d387c9c7d08942dd70f7c465ecca44fcf7b7116e28bf409606f7e228e8809d3d571ea5be3ae1c09c872bd865bb01b81
  languageName: node
  linkType: hard

"any-signal@npm:^3.0.0":
  version: 3.0.1
  resolution: "any-signal@npm:3.0.1"
  checksum: 10c0/3f7495920883dbd44221e3384acb1f58243133a1a062d59f0dde0817b30774f3126f93c6e4a0e1cb60b715d5c9cfa5546fa32c3f62fedce99bf64278ba4477bf
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"apisauce@npm:^2.1.5":
  version: 2.1.6
  resolution: "apisauce@npm:2.1.6"
  dependencies:
    axios: "npm:^0.21.4"
  checksum: 10c0/b4fbc478da3eb57d611346a70a7f35c0b9aff1e24472e1c069db38a32921a4022c7257310f84a41f580a1708013e3acd8d9c9f6b5319182ef0096d9a421481e2
  languageName: node
  linkType: hard

"app-module-path@npm:^2.2.0":
  version: 2.2.0
  resolution: "app-module-path@npm:2.2.0"
  checksum: 10c0/0d6d581dcee268271af1e611934b4fed715de55c382b2610de67ba6f87d01503fc0426cff687f06210e54cd57545f7a6172e1dd192914a3709ad89c06a4c3a0b
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 10c0/070ff801a9d236a6caa647507bdcc7034530604844d64408149a26b9e87c2f97650055c0f049abd1efc024b334635c01f29e0b632b371ac3f26130f4cf65997a
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 10c0/429897e68110374f39b771ec47a7161fc6a8fc33e196857c0a396dc75df0b5f65e4d046674db764330b6bb66b39ef48dd7c53b6a2ee75cfb0681e0c1a7033962
  languageName: node
  linkType: hard

"asap@npm:~2.0.6":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: 10c0/c6d5e39fe1f15e4b87677460bd66b66050cd14c772269cee6688824c1410a08ab20254bb6784f9afb75af9144a9f9a7692d49547f4d19d715aeb7c0318f3136d
  languageName: node
  linkType: hard

"asn1js@npm:^3.0.1, asn1js@npm:^3.0.5":
  version: 3.0.5
  resolution: "asn1js@npm:3.0.5"
  dependencies:
    pvtsutils: "npm:^1.3.2"
    pvutils: "npm:^1.1.3"
    tslib: "npm:^2.4.0"
  checksum: 10c0/bb8eaf4040c8f49dd475566874986f5976b81bae65a6b5526e2208a13cdca323e69ce297bcd435fdda3eb6933defe888e71974d705b6fcb14f2734a907f8aed4
  languageName: node
  linkType: hard

"assemblyscript@npm:0.19.10":
  version: 0.19.10
  resolution: "assemblyscript@npm:0.19.10"
  dependencies:
    binaryen: "npm:101.0.0-nightly.20210723"
    long: "npm:^4.0.0"
  bin:
    asc: bin/asc
    asinit: bin/asinit
  checksum: 10c0/5bf56c356ee39dc1b508c48750d67d6e581f85638be8b280e1f3bf1904a735aafd17c2645755d306b3d9ed651f41aeb3e3fdb64da65a87061d6d3496ca007b6e
  languageName: node
  linkType: hard

"assemblyscript@npm:0.19.23":
  version: 0.19.23
  resolution: "assemblyscript@npm:0.19.23"
  dependencies:
    binaryen: "npm:102.0.0-nightly.20211028"
    long: "npm:^5.2.0"
    source-map-support: "npm:^0.5.20"
  bin:
    asc: bin/asc
    asinit: bin/asinit
  checksum: 10c0/53c4aad81dc923aa4eab8260fb23ad4217f043cca52c37197d016ff253714e5fa966d3cdce59e5c6069d9b8a7e59f8e96ed5fd75b40d32cdf8980b1a6a170f23
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 10c0/f63d439cc383db1b9c5c6080d1e240bd14dae745f15d11ec5da863e182bbeca70df6c8191cffef5deba0b566ef98834610a68be79ac6379c95eeb26e1b310e25
  languageName: node
  linkType: hard

"async@npm:^3.2.3":
  version: 3.2.5
  resolution: "async@npm:3.2.5"
  checksum: 10c0/1408287b26c6db67d45cb346e34892cee555b8b59e6c68e6f8c3e495cad5ca13b4f218180e871f3c2ca30df4ab52693b66f2f6ff43644760cab0b2198bda79c1
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 10c0/4c058baf6df1bc5a1697cf182e2029c58cd99975288a13f9e70068ef5d6f4e1f1fd7c4d2c3c4912eae44797d1725be9700995736deca441b39f3e66d8dee97ef
  languageName: node
  linkType: hard

"axios@npm:^0.21.1, axios@npm:^0.21.4":
  version: 0.21.4
  resolution: "axios@npm:0.21.4"
  dependencies:
    follow-redirects: "npm:^1.14.0"
  checksum: 10c0/fbcff55ec68f71f02d3773d467db2fcecdf04e749826c82c2427a232f9eba63242150a05f15af9ef15818352b814257541155de0281f8fb2b7e8a5b79f7f2142
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base-x@npm:^3.0.2":
  version: 3.0.9
  resolution: "base-x@npm:3.0.9"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/e6bbeae30b24f748b546005affb710c5fbc8b11a83f6cd0ca999bd1ab7ad3a22e42888addc40cd145adc4edfe62fcfab4ebc91da22e4259aae441f95a77aee1a
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"beraborrow-subgraph@workspace:.":
  version: 0.0.0-use.local
  resolution: "beraborrow-subgraph@workspace:."
  dependencies:
    "@graphprotocol/graph-cli": "npm:^0.81.0"
    "@graphprotocol/graph-ts": "npm:^0.35.1"
    "@types/node": "npm:22.12.0"
    matchstick-as: "npm:^0.6.0"
    mustache: "npm:^4.2.0"
    prettier: "npm:^3.3.3"
  languageName: unknown
  linkType: soft

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"binary-install-raw@npm:0.0.13":
  version: 0.0.13
  resolution: "binary-install-raw@npm:0.0.13"
  dependencies:
    axios: "npm:^0.21.1"
    rimraf: "npm:^3.0.2"
    tar: "npm:^6.1.0"
  checksum: 10c0/f616657b3c80f75c00b6eff0d35e34119cadd221285180d44c96458d4104eb5564877cba062f554a32ef74773ace0897a77b48408becad907f17c98cb7bc84fc
  languageName: node
  linkType: hard

"binaryen@npm:101.0.0-nightly.20210723":
  version: 101.0.0-nightly.20210723
  resolution: "binaryen@npm:101.0.0-nightly.20210723"
  bin:
    wasm-opt: bin/wasm-opt
  checksum: 10c0/fc5325079276e39ece0713a86492a834f22cdb8626e86709a4d205ea4910c0392372e1751b70ac5b29b80dc1bb9e0ebcfb358956f6534f010445c6f98d3b7dd2
  languageName: node
  linkType: hard

"binaryen@npm:102.0.0-nightly.20211028":
  version: 102.0.0-nightly.20211028
  resolution: "binaryen@npm:102.0.0-nightly.20211028"
  bin:
    wasm-opt: bin/wasm-opt
  checksum: 10c0/bb9314d8de107d42dc2a233c8b394e92008656af43f23a5b903be11068a88f7a984c64a9a728be5ee3f7223d583b5b4b8a97bab66a3a389644bdc5165845b53c
  languageName: node
  linkType: hard

"bl@npm:^1.0.0":
  version: 1.2.3
  resolution: "bl@npm:1.2.3"
  dependencies:
    readable-stream: "npm:^2.3.5"
    safe-buffer: "npm:^5.1.1"
  checksum: 10c0/ee6478864d3b1295614f269f3fbabeb2362a2f2fc7f8dc2f6c1f944a278d84e0572ecefd6d0b0736d7418763f98dc3b2738253191ea9e98e4b08de211cfac0a6
  languageName: node
  linkType: hard

"blakejs@npm:^1.1.0":
  version: 1.2.1
  resolution: "blakejs@npm:1.2.1"
  checksum: 10c0/c284557ce55b9c70203f59d381f1b85372ef08ee616a90162174d1291a45d3e5e809fdf9edab6e998740012538515152471dc4f1f9dbfa974ba2b9c1f7b9aad7
  languageName: node
  linkType: hard

"blob-to-it@npm:^1.0.1":
  version: 1.0.4
  resolution: "blob-to-it@npm:1.0.4"
  dependencies:
    browser-readablestream-to-it: "npm:^1.0.3"
  checksum: 10c0/187959860668f8f7091971fc8c7291324af64b5f8aa75e6b80989d0bf6aaefafd27116e029a1c72e7185f470414e240e235b2d66ed88dfee2f7c5c8c2914e89c
  languageName: node
  linkType: hard

"bn.js@npm:4.11.6":
  version: 4.11.6
  resolution: "bn.js@npm:4.11.6"
  checksum: 10c0/e6ee7d3f597f60722cc3361071e23ccf71d3387e166de02381f180f22d2fa79f5dbbdf9e4909e81faaf5da01c16ec6857ddff02678339ce085e2058fd0e405db
  languageName: node
  linkType: hard

"bn.js@npm:^4.11.9":
  version: 4.12.0
  resolution: "bn.js@npm:4.12.0"
  checksum: 10c0/9736aaa317421b6b3ed038ff3d4491935a01419ac2d83ddcfebc5717385295fcfcf0c57311d90fe49926d0abbd7a9dbefdd8861e6129939177f7e67ebc645b21
  languageName: node
  linkType: hard

"bn.js@npm:^5.1.2, bn.js@npm:^5.2.0, bn.js@npm:^5.2.1":
  version: 5.2.1
  resolution: "bn.js@npm:5.2.1"
  checksum: 10c0/bed3d8bd34ec89dbcf9f20f88bd7d4a49c160fda3b561c7bb227501f974d3e435a48fb9b61bc3de304acab9215a3bda0803f7017ffb4d0016a0c3a740a283caa
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 10c0/6f366d7c4990f82c366e3878492ba9a372a73163c09871e80d82fb4ae0d23f9f8924cb8a662330308206e6b3b76ba1d528b4601c9ef73c2166b440b2ea3b7571
  languageName: node
  linkType: hard

"browser-readablestream-to-it@npm:^1.0.0, browser-readablestream-to-it@npm:^1.0.1, browser-readablestream-to-it@npm:^1.0.3":
  version: 1.0.3
  resolution: "browser-readablestream-to-it@npm:1.0.3"
  checksum: 10c0/921564b2224c0504f0de362548c3f84e149588835956564661795e2b8e0659b8a17018a22681c13eeb8e1a24f3335a7a8cc9269322a38617b9bc21a52652c8f5
  languageName: node
  linkType: hard

"browserify-aes@npm:^1.2.0":
  version: 1.2.0
  resolution: "browserify-aes@npm:1.2.0"
  dependencies:
    buffer-xor: "npm:^1.0.3"
    cipher-base: "npm:^1.0.0"
    create-hash: "npm:^1.1.0"
    evp_bytestokey: "npm:^1.0.3"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/967f2ae60d610b7b252a4cbb55a7a3331c78293c94b4dd9c264d384ca93354c089b3af9c0dd023534efdc74ffbc82510f7ad4399cf82bc37bc07052eea485f18
  languageName: node
  linkType: hard

"bs58@npm:^4.0.0":
  version: 4.0.1
  resolution: "bs58@npm:4.0.1"
  dependencies:
    base-x: "npm:^3.0.2"
  checksum: 10c0/613a1b1441e754279a0e3f44d1faeb8c8e838feef81e550efe174ff021dd2e08a4c9ae5805b52dfdde79f97b5c0918c78dd24a0eb726c4a94365f0984a0ffc65
  languageName: node
  linkType: hard

"bs58check@npm:^2.1.2":
  version: 2.1.2
  resolution: "bs58check@npm:2.1.2"
  dependencies:
    bs58: "npm:^4.0.0"
    create-hash: "npm:^1.1.0"
    safe-buffer: "npm:^5.1.2"
  checksum: 10c0/5d33f319f0d7abbe1db786f13f4256c62a076bc8d184965444cb62ca4206b2c92bee58c93bce57150ffbbbe00c48838ac02e6f384e0da8215cac219c0556baa9
  languageName: node
  linkType: hard

"buffer-alloc-unsafe@npm:^1.1.0":
  version: 1.1.0
  resolution: "buffer-alloc-unsafe@npm:1.1.0"
  checksum: 10c0/06b9298c9369621a830227c3797ceb3ff5535e323946d7b39a7398fed8b3243798259b3c85e287608c5aad35ccc551cec1a0a5190cc8f39652e8eee25697fc9c
  languageName: node
  linkType: hard

"buffer-alloc@npm:^1.2.0":
  version: 1.2.0
  resolution: "buffer-alloc@npm:1.2.0"
  dependencies:
    buffer-alloc-unsafe: "npm:^1.1.0"
    buffer-fill: "npm:^1.0.0"
  checksum: 10c0/09d87dd53996342ccfbeb2871257d8cdb25ce9ee2259adc95c6490200cd6e528c5fbae8f30bcc323fe8d8efb0fe541e4ac3bbe9ee3f81c6b7c4b27434cc02ab4
  languageName: node
  linkType: hard

"buffer-fill@npm:^1.0.0":
  version: 1.0.0
  resolution: "buffer-fill@npm:1.0.0"
  checksum: 10c0/55b5654fbbf2d7ceb4991bb537f5e5b5b5b9debca583fee416a74fcec47c16d9e7a90c15acd27577da7bd750b7fa6396e77e7c221e7af138b6d26242381c6e4d
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer-xor@npm:^1.0.3":
  version: 1.0.3
  resolution: "buffer-xor@npm:1.0.3"
  checksum: 10c0/fd269d0e0bf71ecac3146187cfc79edc9dbb054e2ee69b4d97dfb857c6d997c33de391696d04bdd669272751fa48e7872a22f3a6c7b07d6c0bc31dbe02a4075c
  languageName: node
  linkType: hard

"buffer@npm:^6.0.1, buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.2.1"
  checksum: 10c0/2a905fbbcde73cc5d8bd18d1caa23715d5f83a5935867c2329f0ac06104204ba7947be098fe1317fbd8830e26090ff8e764f08cd14fefc977bb248c3487bcbd0
  languageName: node
  linkType: hard

"busboy@npm:^1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: "npm:^1.1.0"
  checksum: 10c0/fa7e836a2b82699b6e074393428b91ae579d4f9e21f5ac468e1b459a244341d722d2d22d10920cdd849743dbece6dca11d72de939fb75a7448825cf2babfba1f
  languageName: node
  linkType: hard

"cacache@npm:^18.0.0":
  version: 18.0.4
  resolution: "cacache@npm:18.0.4"
  dependencies:
    "@npmcli/fs": "npm:^3.1.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^4.0.0"
    ssri: "npm:^10.0.0"
    tar: "npm:^6.1.11"
    unique-filename: "npm:^3.0.0"
  checksum: 10c0/6c055bafed9de4f3dcc64ac3dc7dd24e863210902b7c470eb9ce55a806309b3efff78033e3d8b4f7dcc5d467f2db43c6a2857aaaf26f0094b8a351d44c42179f
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7":
  version: 1.0.7
  resolution: "call-bind@npm:1.0.7"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.1"
  checksum: 10c0/a3ded2e423b8e2a265983dba81c27e125b48eefb2655e7dfab6be597088da3d47c47976c24bc51b8fd9af1061f8f87b4ab78a314f3c77784b2ae2ba535ad8b8d
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"cardinal@npm:^2.1.1":
  version: 2.1.1
  resolution: "cardinal@npm:2.1.1"
  dependencies:
    ansicolors: "npm:~0.3.2"
    redeyed: "npm:~2.1.0"
  bin:
    cdl: ./bin/cdl.js
  checksum: 10c0/0051d0e64c0e1dff480c1aace4c018c48ecca44030533257af3f023107ccdeb061925603af6d73710f0345b0ae0eb57e5241d181d9b5fdb595d45c5418161675
  languageName: node
  linkType: hard

"caseless@npm:^0.12.0, caseless@npm:~0.12.0":
  version: 0.12.0
  resolution: "caseless@npm:0.12.0"
  checksum: 10c0/ccf64bcb6c0232cdc5b7bd91ddd06e23a4b541f138336d4725233ac538041fb2f29c2e86c3c4a7a61ef990b665348db23a047060b9414c3a6603e9fa61ad4626
  languageName: node
  linkType: hard

"cborg@npm:^1.5.4, cborg@npm:^1.6.0":
  version: 1.10.2
  resolution: "cborg@npm:1.10.2"
  bin:
    cborg: cli.js
  checksum: 10c0/e8c2ff47db7161600a2f5bf95a1871cce9f207d918d518b028fc181852169c19110e000e09d3e1cfc0515ad03246216edd00185e5204a15e191cf211cecc4b89
  languageName: node
  linkType: hard

"chalk@npm:3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/ee650b0a065b3d7a6fda258e75d3a86fc8e4effa55871da730a9e42ccb035bf5fd203525e5a1ef45ec2582ecc4f65b47eb11357c526b84dd29a14fb162c414d2
  languageName: node
  linkType: hard

"chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 10c0/e6543f02ec877732e3a2d1c3c3323ddb4d39fbab687c23f526e25bd4c6a9bf3b83a696e8c769d078e04e5754921648f7821b2a2acfd16c550435fd630026e073
  languageName: node
  linkType: hard

"chalk@npm:^4, chalk@npm:^4.0.2, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"chokidar@npm:3.5.3":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/1076953093e0707c882a92c66c0f56ba6187831aa51bb4de878c1fec59ae611a3bf02898f190efec8e77a086b8df61c2b2a3ea324642a0558bdf8ee6c5dc9ca1
  languageName: node
  linkType: hard

"chownr@npm:^1.0.1":
  version: 1.1.4
  resolution: "chownr@npm:1.1.4"
  checksum: 10c0/ed57952a84cc0c802af900cf7136de643d3aba2eecb59d29344bc2f3f9bf703a301b9d84cdc71f82c3ffc9ccde831b0d92f5b45f91727d6c9da62f23aef9d9db
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: 10c0/594754e1303672171cc04e50f6c398ae16128eb134a88f801bf5354fd96f205320f23536a045d9abd8b51024a149696e51231565891d4efdab8846021ecf88e6
  languageName: node
  linkType: hard

"cipher-base@npm:^1.0.0, cipher-base@npm:^1.0.1, cipher-base@npm:^1.0.3":
  version: 1.0.4
  resolution: "cipher-base@npm:1.0.4"
  dependencies:
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/d8d005f8b64d8a77b3d3ce531301ae7b45902c9cab4ec8b66bdbd2bf2a1d9fceb9a2133c293eb3c060b2d964da0f14c47fb740366081338aa3795dd1faa8984b
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 10c0/1f90262d5f6230a17e27d0c190b09d47ebe7efdd76a03b5a1127863f7b3c9aec4c3e6c8bb3a7bbf81d553d56a1fd35728f5a8ef4c63f867ac8d690109742a8c1
  languageName: node
  linkType: hard

"clean-stack@npm:^3.0.1":
  version: 3.0.1
  resolution: "clean-stack@npm:3.0.1"
  dependencies:
    escape-string-regexp: "npm:4.0.0"
  checksum: 10c0/4ea5c03bdf78e8afb2592f34c1b5832d0c7858d37d8b0d40fba9d61a103508fa3bb527d39a99469019083e58e05d1ad54447e04217d5d36987e97182adab0e03
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: "npm:^3.1.0"
  checksum: 10c0/92a2f98ff9037d09be3dfe1f0d749664797fb674bf388375a2207a1203b69d41847abf16434203e0089212479e47a358b13a0222ab9fccfe8e2644a7ccebd111
  languageName: node
  linkType: hard

"cli-progress@npm:^3.12.0":
  version: 3.12.0
  resolution: "cli-progress@npm:3.12.0"
  dependencies:
    string-width: "npm:^4.2.3"
  checksum: 10c0/f464cb19ebde2f3880620a2adfaeeefaec6cb15c8e610c8a659ca1047ee90d69f3bf2fdabbb1fe33ac408678e882e3e0eecdb84ab5df0edf930b269b8a72682d
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.2.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 10c0/907a1c227ddf0d7a101e7ab8b300affc742ead4b4ebe920a5bf1bc6d45dce2958fcd195eb28fa25275062fe6fa9b109b93b63bc8033396ed3bcb50297008b3a3
  languageName: node
  linkType: hard

"cli-table3@npm:0.6.0":
  version: 0.6.0
  resolution: "cli-table3@npm:0.6.0"
  dependencies:
    colors: "npm:^1.1.2"
    object-assign: "npm:^4.1.0"
    string-width: "npm:^4.2.0"
  dependenciesMeta:
    colors:
      optional: true
  checksum: 10c0/3805702bb9a0d54ed8a5385237088b489109744b37654fd2fe9ca9df0369dc1603feef28f610c5f5fee8ed4350c38ddcfb1dfc7f700616e668f5487529551249
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 10c0/2176952b3649293473999a95d7bebfc9dc96410f6cbd3d2595cf12fd401f63a4bf41a7adbfd3ab2ff09ed60cb9870c58c6acdd18b87767366fabfc163700f13b
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 10c0/5ad3c534949a8c68fca8fbc6f09068f435f0ad290ab8b2f76841b9e6af7e0bb57b98cb05b0e19fe33f5d91e5a8611ad457e5f69e0a484caad1f7487fd0e8253c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 10c0/566a3d42cca25b9b3cd5528cd7754b8e89c0eb646b7f214e8e2eaddb69994ac5f0557d9c175eb5d8f0ad73531140d9c47525085ee752a91a2ab15ab459caf6d6
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"colors@npm:1.4.0, colors@npm:^1.1.2":
  version: 1.4.0
  resolution: "colors@npm:1.4.0"
  checksum: 10c0/9af357c019da3c5a098a301cf64e3799d27549d8f185d86f79af23069e4f4303110d115da98483519331f6fb71c8568d5688fa1c6523600044fd4a54e97c4efb
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.6":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:^2.20.3":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"concat-stream@npm:^1.6.0, concat-stream@npm:^1.6.2, concat-stream@npm:~1.6.2":
  version: 1.6.2
  resolution: "concat-stream@npm:1.6.2"
  dependencies:
    buffer-from: "npm:^1.0.0"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^2.2.2"
    typedarray: "npm:^0.0.6"
  checksum: 10c0/2e9864e18282946dabbccb212c5c7cec0702745e3671679eb8291812ca7fd12023f7d8cb36493942a62f770ac96a7f90009dc5c82ad69893438371720fa92617
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cosmiconfig@npm:7.0.1":
  version: 7.0.1
  resolution: "cosmiconfig@npm:7.0.1"
  dependencies:
    "@types/parse-json": "npm:^4.0.0"
    import-fresh: "npm:^3.2.1"
    parse-json: "npm:^5.0.0"
    path-type: "npm:^4.0.0"
    yaml: "npm:^1.10.0"
  checksum: 10c0/3cd38525ba22e13da0ef9f4be131df226c94f5b96fb50f6297eb17baeedefe15cf5819f8c73cde69f71cc5034e712c86bd20c7756883dd8094087680ecc25932
  languageName: node
  linkType: hard

"create-hash@npm:^1.1.0, create-hash@npm:^1.1.2, create-hash@npm:^1.2.0":
  version: 1.2.0
  resolution: "create-hash@npm:1.2.0"
  dependencies:
    cipher-base: "npm:^1.0.1"
    inherits: "npm:^2.0.1"
    md5.js: "npm:^1.3.4"
    ripemd160: "npm:^2.0.1"
    sha.js: "npm:^2.4.0"
  checksum: 10c0/d402e60e65e70e5083cb57af96d89567954d0669e90550d7cec58b56d49c4b193d35c43cec8338bc72358198b8cbf2f0cac14775b651e99238e1cf411490f915
  languageName: node
  linkType: hard

"create-hmac@npm:^1.1.4, create-hmac@npm:^1.1.7":
  version: 1.1.7
  resolution: "create-hmac@npm:1.1.7"
  dependencies:
    cipher-base: "npm:^1.0.3"
    create-hash: "npm:^1.1.0"
    inherits: "npm:^2.0.1"
    ripemd160: "npm:^2.0.0"
    safe-buffer: "npm:^5.0.1"
    sha.js: "npm:^2.4.8"
  checksum: 10c0/24332bab51011652a9a0a6d160eed1e8caa091b802335324ae056b0dcb5acbc9fcf173cf10d128eba8548c3ce98dfa4eadaa01bd02f44a34414baee26b651835
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: 10c0/157cbc59b2430ae9a90034a5f3a1b398b6738bf510f713edc4d4e45e169bc514d3d99dd34d8d01ca7ae7830b5b8b537e46ae8f3c8f932371b0875c0151d7ec91
  languageName: node
  linkType: hard

"cross-spawn@npm:7.0.3, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/5738c312387081c98d69c98e105b6327b069197f864a60593245d64c8089c8a0a744e16349281210d56835bb9274130d825a78b2ad6853ca13cfbeffc0c31750
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0":
  version: 7.0.5
  resolution: "cross-spawn@npm:7.0.5"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/aa82ce7ac0814a27e6f2b738c5a7cf1fa21a3558a1e42df449fc96541ba3ba731e4d3ecffa4435348808a86212f287c6f20a1ee551ef1ff95d01cfec5f434944
  languageName: node
  linkType: hard

"debug@npm:4":
  version: 4.3.7
  resolution: "debug@npm:4.3.7"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/1471db19c3b06d485a622d62f65947a19a23fbd0dd73f7fd3eafb697eec5360cde447fb075919987899b1a2096e85d35d4eb5a4de09a57600ac9cf7e6c8e768b
  languageName: node
  linkType: hard

"debug@npm:4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/cedbec45298dd5c501d01b92b119cd3faebe5438c3917ff11ae1bff86a6c722930ac9c8659792824013168ba6db7c4668225d845c633fbdafbbf902a6389f736
  languageName: node
  linkType: hard

"debug@npm:^3.2.6":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10c0/37d96ae42cbc71c14844d2ae3ba55adf462ec89fd3a999459dec3833944cd999af6007ff29c780f1c61153bcaaf2c842d1e4ce1ec621e4fc4923244942e4a02a
  languageName: node
  linkType: hard

"debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.4":
  version: 4.3.5
  resolution: "debug@npm:4.3.5"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/082c375a2bdc4f4469c99f325ff458adad62a3fc2c482d59923c260cb08152f34e2659f72b3767db8bb2f21ca81a60a42d1019605a412132d7b9f59363a005cc
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: "npm:^1.0.2"
  checksum: 10c0/9cfbe498f5c8ed733775db62dfd585780387d93c17477949e1670bfcfb9346e0281ce8c4bf9f4ac1fc0f9b851113bd6dc9e41182ea1644ccd97de639fa13c35a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 10c0/db6c63864a9d3b7dc9def55d52764968a5af296de87c1b2cc71d8be8142e445208071953649e0386a8cc37cfcf9a2067a47207f1eb9ff250c2a269658fdae422
  languageName: node
  linkType: hard

"delay@npm:^5.0.0":
  version: 5.0.0
  resolution: "delay@npm:5.0.0"
  checksum: 10c0/01cdc4cd0cd35fb622518a3df848e67e09763a38e7cdada2232b6fda9ddda72eddcf74f0e24211200fbe718434f2335f2a2633875a6c96037fefa6de42896ad7
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: 10c0/81b91f9d39c4eaca068eb0c1eb0e4afbdc5bb2941d197f513dd596b820b956fef43485876226d65d497bebc15666aa2aa82c679e84f65d5f2bfbf14ee46e32c1
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: 10c0/dcac00920a4d503e38bb64001acb19df4efc14536ada475725e12f52c16777afdee4db827f55f13a908ee7efc0cb282e2e3dbaeeb98c0993dd93d1802d3bf00c
  languageName: node
  linkType: hard

"dns-over-http-resolver@npm:^1.2.3":
  version: 1.2.3
  resolution: "dns-over-http-resolver@npm:1.2.3"
  dependencies:
    debug: "npm:^4.3.1"
    native-fetch: "npm:^3.0.0"
    receptacle: "npm:^1.3.2"
  checksum: 10c0/231435742246115aeb4f153721effc4d995ab8f22572240b27d85e1be4123345cbe503e9922bc46b36caaa86307fbcf65ba252302dc7a4794f330aa6d6f920b8
  languageName: node
  linkType: hard

"docker-compose@npm:0.23.19":
  version: 0.23.19
  resolution: "docker-compose@npm:0.23.19"
  dependencies:
    yaml: "npm:^1.10.2"
  checksum: 10c0/00f14c85e39d9a19726c6a3a4a44c7103688d2acd1fa6b1b0bed952c143d51e576ba33f5e51431c9befe3fd3c36db740415ca1fb6bb338a96839d2f92e9e7fc4
  languageName: node
  linkType: hard

"docker-modem@npm:^1.0.8":
  version: 1.0.9
  resolution: "docker-modem@npm:1.0.9"
  dependencies:
    JSONStream: "npm:1.3.2"
    debug: "npm:^3.2.6"
    readable-stream: "npm:~1.0.26-4"
    split-ca: "npm:^1.0.0"
  checksum: 10c0/3d9e474756434f5fe11ad5859bd4dd5830f8d41da1db6cb34ccab550d80d0eda85c12fda6f67d3c1f10a635c13b0cbeb22d7217e418d61f44629a029ebb7352d
  languageName: node
  linkType: hard

"dockerode@npm:2.5.8":
  version: 2.5.8
  resolution: "dockerode@npm:2.5.8"
  dependencies:
    concat-stream: "npm:~1.6.2"
    docker-modem: "npm:^1.0.8"
    tar-fs: "npm:~1.16.3"
  checksum: 10c0/ff4af1baeb5a571fb86d64d5816b7ba395fad9a169cc136cb3b39f51b076a12874ea0e2983a879a5a2c6c37d39df024b9071565766c1738f5024967acef8610c
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ejs@npm:3.1.8":
  version: 3.1.8
  resolution: "ejs@npm:3.1.8"
  dependencies:
    jake: "npm:^10.8.5"
  bin:
    ejs: bin/cli.js
  checksum: 10c0/a6bd58633c5b3ae19a2bfea1b94033585ad85c87ec15961f8c89c93ffdafb8b2358af827f37f7552b35d9f5393fdbd98d35a8cbcd0ee2540b7f9f7a194e86a1a
  languageName: node
  linkType: hard

"ejs@npm:^3.1.8":
  version: 3.1.10
  resolution: "ejs@npm:3.1.10"
  dependencies:
    jake: "npm:^10.8.5"
  bin:
    ejs: bin/cli.js
  checksum: 10c0/52eade9e68416ed04f7f92c492183340582a36482836b11eab97b159fcdcfdedc62233a1bf0bf5e5e1851c501f2dca0e2e9afd111db2599e4e7f53ee29429ae1
  languageName: node
  linkType: hard

"electron-fetch@npm:^1.7.2":
  version: 1.9.1
  resolution: "electron-fetch@npm:1.9.1"
  dependencies:
    encoding: "npm:^0.1.13"
  checksum: 10c0/650d91e7957ed9d7e054a5cf2d3f57ed2fae859cbe51e6910b75699f33fcf08a9cd812d418d14280da6fc58e6914dfc3177ca7b081308c4074326ced47409cd8
  languageName: node
  linkType: hard

"elliptic@npm:6.5.4":
  version: 6.5.4
  resolution: "elliptic@npm:6.5.4"
  dependencies:
    bn.js: "npm:^4.11.9"
    brorand: "npm:^1.1.0"
    hash.js: "npm:^1.0.0"
    hmac-drbg: "npm:^1.0.1"
    inherits: "npm:^2.0.4"
    minimalistic-assert: "npm:^1.0.1"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10c0/5f361270292c3b27cf0843e84526d11dec31652f03c2763c6c2b8178548175ff5eba95341dd62baff92b2265d1af076526915d8af6cc9cb7559c44a62f8ca6e2
  languageName: node
  linkType: hard

"elliptic@npm:^6.5.4":
  version: 6.5.5
  resolution: "elliptic@npm:6.5.5"
  dependencies:
    bn.js: "npm:^4.11.9"
    brorand: "npm:^1.1.0"
    hash.js: "npm:^1.0.0"
    hmac-drbg: "npm:^1.0.1"
    inherits: "npm:^2.0.4"
    minimalistic-assert: "npm:^1.0.1"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10c0/3e591e93783a1b66f234ebf5bd3a8a9a8e063a75073a35a671e03e3b25253b6e33ac121f7efe9b8808890fffb17b40596cc19d01e6e8d1fa13b9a56ff65597c8
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.0.0, end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/870b423afb2d54bb8d243c63e07c170409d41e20b47eeef0727547aea5740bd6717aca45597a9f2745525667a6b804c1e7bede41f856818faee5806dd9ff3975
  languageName: node
  linkType: hard

"enquirer@npm:2.3.6":
  version: 2.3.6
  resolution: "enquirer@npm:2.3.6"
  dependencies:
    ansi-colors: "npm:^4.1.1"
  checksum: 10c0/8e070e052c2c64326a2803db9084d21c8aaa8c688327f133bf65c4a712586beb126fd98c8a01cfb0433e82a4bd3b6262705c55a63e0f7fb91d06b9cedbde9a11
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"err-code@npm:^3.0.1":
  version: 3.0.1
  resolution: "err-code@npm:3.0.1"
  checksum: 10c0/78b1c50500adebde6699b8d27b8ce4728c132dcaad75b5d18ba44f6ccb28769d1fff8368ae1164be4559dac8b95d4e26bb15b480ba9999e0cd0f0c64beaf1b24
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10c0/ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-define-property@npm:1.0.0"
  dependencies:
    get-intrinsic: "npm:^1.2.4"
  checksum: 10c0/6bf3191feb7ea2ebda48b577f69bdfac7a2b3c9bcf97307f55fd6ef1bbca0b49f0c219a935aca506c993d8c5d8bddd937766cb760cd5e5a1071351f2df9f9aa4
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es6-promise@npm:^4.0.3":
  version: 4.2.8
  resolution: "es6-promise@npm:4.2.8"
  checksum: 10c0/2373d9c5e9a93bdd9f9ed32ff5cb6dd3dd785368d1c21e9bbbfd07d16345b3774ae260f2bd24c8f836a6903f432b4151e7816a7fa8891ccb4e1a55a028ec42c3
  languageName: node
  linkType: hard

"es6-promisify@npm:^5.0.0":
  version: 5.0.0
  resolution: "es6-promisify@npm:5.0.0"
  dependencies:
    es6-promise: "npm:^4.0.3"
  checksum: 10c0/23284c6a733cbf7842ec98f41eac742c9f288a78753c4fe46652bae826446ced7615b9e8a5c5f121a08812b1cd478ea58630f3e1c3d70835bd5dcd69c7cd75c9
  languageName: node
  linkType: hard

"escape-string-regexp@npm:4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:~4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"ethereum-bloom-filters@npm:^1.0.6":
  version: 1.1.0
  resolution: "ethereum-bloom-filters@npm:1.1.0"
  dependencies:
    "@noble/hashes": "npm:^1.4.0"
  checksum: 10c0/54b0b7a1fdf12fe02fc8f605f213d11ea026111b9d2af79ff58e8319c904d9d6cee77c62fe70bee62c4d0c7952caf58ebaf47a889d9e4199cf4da1a361a87b53
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^0.1.3":
  version: 0.1.3
  resolution: "ethereum-cryptography@npm:0.1.3"
  dependencies:
    "@types/pbkdf2": "npm:^3.0.0"
    "@types/secp256k1": "npm:^4.0.1"
    blakejs: "npm:^1.1.0"
    browserify-aes: "npm:^1.2.0"
    bs58check: "npm:^2.1.2"
    create-hash: "npm:^1.2.0"
    create-hmac: "npm:^1.1.7"
    hash.js: "npm:^1.1.7"
    keccak: "npm:^3.0.0"
    pbkdf2: "npm:^3.0.17"
    randombytes: "npm:^2.1.0"
    safe-buffer: "npm:^5.1.2"
    scrypt-js: "npm:^3.0.0"
    secp256k1: "npm:^4.0.1"
    setimmediate: "npm:^1.0.5"
  checksum: 10c0/aa36e11fca9d67d67c96e02a98b33bae2e1add20bd11af43feb7f28cdafe0cd3bdbae3bfecc7f2d9ec8f504b10a1c8f7590f5f7fe236560fd8083dd321ad7144
  languageName: node
  linkType: hard

"ethereumjs-util@npm:^7.1.0":
  version: 7.1.5
  resolution: "ethereumjs-util@npm:7.1.5"
  dependencies:
    "@types/bn.js": "npm:^5.1.0"
    bn.js: "npm:^5.1.2"
    create-hash: "npm:^1.1.2"
    ethereum-cryptography: "npm:^0.1.3"
    rlp: "npm:^2.2.4"
  checksum: 10c0/8b9487f35ecaa078bf9af6858eba6855fc61c73cc2b90c8c37486fcf94faf4fc1c5cda9758e6769f9ef2658daedaf2c18b366312ac461f8c8a122b392e3041eb
  languageName: node
  linkType: hard

"ethjs-unit@npm:0.1.6":
  version: 0.1.6
  resolution: "ethjs-unit@npm:0.1.6"
  dependencies:
    bn.js: "npm:4.11.6"
    number-to-bn: "npm:1.7.0"
  checksum: 10c0/0115ddeb4bc932026b9cd259f6eb020a45b38be62e3786526b70e4c5fb0254184bf6e8b7b3f0c8bb80d4d596a73893e386c02221faf203895db7cb9c29b37188
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"evp_bytestokey@npm:^1.0.3":
  version: 1.0.3
  resolution: "evp_bytestokey@npm:1.0.3"
  dependencies:
    md5.js: "npm:^1.3.4"
    node-gyp: "npm:latest"
    safe-buffer: "npm:^5.1.1"
  checksum: 10c0/77fbe2d94a902a80e9b8f5a73dcd695d9c14899c5e82967a61b1fc6cbbb28c46552d9b127cff47c45fcf684748bdbcfa0a50410349109de87ceb4b199ef6ee99
  languageName: node
  linkType: hard

"execa@npm:5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 10c0/160456d2d647e6019640bd07111634d8c353038d9fa40176afb7cd49b0548bdae83b56d05e907c2cce2300b81cae35d800ef92fefb9d0208e190fa3b7d6bb579
  languageName: node
  linkType: hard

"eyes@npm:^0.1.8":
  version: 0.1.8
  resolution: "eyes@npm:0.1.8"
  checksum: 10c0/4c79a9cbf45746d8c9f48cc957e35ad8ea336add1c7b8d5a0e002efc791a7a62b27b2188184ef1a1eea7bc3cd06b161791421e0e6c5fe78309705a162c53eea8
  languageName: node
  linkType: hard

"fast-decode-uri-component@npm:^1.0.1":
  version: 1.0.1
  resolution: "fast-decode-uri-component@npm:1.0.1"
  checksum: 10c0/039d50c2e99d64f999c3f2126c23fbf75a04a4117e218a149ca0b1d2aeb8c834b7b19d643b9d35d4eabce357189a6a94085f78cf48869e6e26cc59b036284bc3
  languageName: node
  linkType: hard

"fast-fifo@npm:^1.0.0":
  version: 1.3.2
  resolution: "fast-fifo@npm:1.3.2"
  checksum: 10c0/d53f6f786875e8b0529f784b59b4b05d4b5c31c651710496440006a398389a579c8dbcd2081311478b5bf77f4b0b21de69109c5a4eabea9d8e8783d1eb864e4c
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.9":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.4"
  checksum: 10c0/42baad7b9cd40b63e42039132bde27ca2cb3a4950d0a0f9abe4639ea1aa9d3e3b40f98b1fe31cbc0cc17b664c9ea7447d911a152fa34ec5b72977b125a6fc845
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^3.0.0":
  version: 3.0.0
  resolution: "fast-levenshtein@npm:3.0.0"
  dependencies:
    fastest-levenshtein: "npm:^1.0.7"
  checksum: 10c0/9e147c682bd0ca54474f1cbf906f6c45262fd2e7c051d2caf2cc92729dcf66949dc809f2392de6adbe1c8716fdf012f91ce38c9422aef63b5732fc688eee4046
  languageName: node
  linkType: hard

"fast-querystring@npm:^1.1.1":
  version: 1.1.2
  resolution: "fast-querystring@npm:1.1.2"
  dependencies:
    fast-decode-uri-component: "npm:^1.0.1"
  checksum: 10c0/e8223273a9b199722f760f5a047a77ad049a14bd444b821502cb8218f5925e3a5fffb56b64389bca73ab2ac6f1aa7aebbe4e203e5f6e53ff5978de97c0fde4e3
  languageName: node
  linkType: hard

"fast-url-parser@npm:^1.1.3":
  version: 1.1.3
  resolution: "fast-url-parser@npm:1.1.3"
  dependencies:
    punycode: "npm:^1.3.2"
  checksum: 10c0/d85c5c409cf0215417380f98a2d29c23a95004d93ff0d8bdf1af5f1a9d1fc608ac89ac6ffe863783d2c73efb3850dd35390feb1de3296f49877bfee0392eb5d3
  languageName: node
  linkType: hard

"fastest-levenshtein@npm:^1.0.7":
  version: 1.0.16
  resolution: "fastest-levenshtein@npm:1.0.16"
  checksum: 10c0/7e3d8ae812a7f4fdf8cad18e9cde436a39addf266a5986f653ea0d81e0de0900f50c0f27c6d5aff3f686bcb48acbd45be115ae2216f36a6a13a7dbbf5cad878b
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.17.1
  resolution: "fastq@npm:1.17.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/1095f16cea45fb3beff558bb3afa74ca7a9250f5a670b65db7ed585f92b4b48381445cd328b3d87323da81e43232b5d5978a8201bde84e0cd514310f1ea6da34
  languageName: node
  linkType: hard

"filelist@npm:^1.0.4":
  version: 1.0.4
  resolution: "filelist@npm:1.0.4"
  dependencies:
    minimatch: "npm:^5.0.1"
  checksum: 10c0/426b1de3944a3d153b053f1c0ebfd02dccd0308a4f9e832ad220707a6d1f1b3c9784d6cadf6b2f68f09a57565f63ebc7bcdc913ccf8012d834f472c46e596f41
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.14.0":
  version: 1.15.6
  resolution: "follow-redirects@npm:1.15.6"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/9ff767f0d7be6aa6870c82ac79cf0368cd73e01bbc00e9eb1c2a16fbb198ec105e3c9b6628bb98e9f3ac66fe29a957b9645bcb9a490bb7aa0d35f908b6b85071
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/028f1d41000553fcfa6c4bb5c372963bf3d9bf0b1f25a87d1a6253014343fb69dfb1b42d9625d7cf44c8ba429940f3d0ff718b62105d4d4a4f6ef8ca0a53faa2
  languageName: node
  linkType: hard

"form-data@npm:^2.2.0":
  version: 2.5.1
  resolution: "form-data@npm:2.5.1"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.6"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/7e8fb913b84a7ac04074781a18d0f94735bbe82815ff35348803331f6480956ff0035db5bcf15826edee09fe01e665cfac664678f1526646a6374ee13f960e56
  languageName: node
  linkType: hard

"fs-constants@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs-constants@npm:1.0.0"
  checksum: 10c0/a0cde99085f0872f4d244e83e03a46aa387b74f5a5af750896c6b05e9077fac00e9932fdf5aef84f2f16634cd473c63037d7a512576da7d5c2b9163d1909f3a8
  languageName: node
  linkType: hard

"fs-extra@npm:9.1.0, fs-extra@npm:^9.1.0":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: "npm:^1.0.0"
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/9b808bd884beff5cb940773018179a6b94a966381d005479f00adda6b44e5e3d4abf765135773d849cc27efe68c349e4a7b86acd7d3306d5932c14f3a4b17a92
  languageName: node
  linkType: hard

"fs-jetpack@npm:4.3.1":
  version: 4.3.1
  resolution: "fs-jetpack@npm:4.3.1"
  dependencies:
    minimatch: "npm:^3.0.2"
    rimraf: "npm:^2.6.3"
  checksum: 10c0/5d27e829233de005505417bae2f55412ae65ff63a57b68ac6d3cd8dde29ed9f0797c2a83356d20237bf74f516db8e40636c5fc238b49b4414b3d9339e60f7914
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/703d16522b8282d7299337539c3ed6edddd1afe82435e4f5b76e34a79cd74e488a8a0e26a636afc2440e1a23b03878e2122e3a2cfe375a5cf63c37d92b86a004
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.4":
  version: 1.2.4
  resolution: "get-intrinsic@npm:1.2.4"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    has-proto: "npm:^1.0.1"
    has-symbols: "npm:^1.0.3"
    hasown: "npm:^2.0.0"
  checksum: 10c0/0a9b82c16696ed6da5e39b1267104475c47e3a9bdbe8b509dfe1710946e38a87be70d759f4bb3cda042d76a41ef47fe769660f3b7c0d1f68750299344ffb15b7
  languageName: node
  linkType: hard

"get-iterator@npm:^1.0.2":
  version: 1.0.2
  resolution: "get-iterator@npm:1.0.2"
  checksum: 10c0/d4096a21ca860678326ab059dabf1bf2d5cfe5dda59ce987d0c6b43c41706b92797bac4c6b75bf5e58341be70763a6961af826e79f5c606115d68ad982eaff79
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: 10c0/e34cdf447fdf1902a1f6d5af737eaadf606d2ee3518287abde8910e04159368c268568174b2e71102b87b26c2020486f126bfca9c4fb1ceb986ff99b52ecd1be
  languageName: node
  linkType: hard

"get-port@npm:^3.1.0":
  version: 3.2.0
  resolution: "get-port@npm:3.2.0"
  checksum: 10c0/1b6c3fe89074be3753d9ddf3d67126ea351ab9890537fe53fefebc2912d1d66fdc112451bbc76d33ae5ceb6ca70be2a91017944e3ee8fb0814ac9b295bf2a5b8
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10c0/49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob@npm:9.3.5":
  version: 9.3.5
  resolution: "glob@npm:9.3.5"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    minimatch: "npm:^8.0.2"
    minipass: "npm:^4.2.4"
    path-scurry: "npm:^1.6.1"
  checksum: 10c0/2f6c2b9ee019ee21dc258ae97a88719614591e4c979cb4580b1b9df6f0f778a3cb38b4bdaf18dfa584637ea10f89a3c5f2533a5e449cf8741514ad18b0951f2e
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.1.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 10c0/b39511b4afe4bd8a7aead3a27c4ade2b9968649abab0a6c28b1a90141b96ca68ca5db1302f7c7bd29eab66bf51e13916b8e0a3d0ac08f75e1e84a39b35691189
  languageName: node
  linkType: hard

"gluegun@npm:5.1.6":
  version: 5.1.6
  resolution: "gluegun@npm:5.1.6"
  dependencies:
    apisauce: "npm:^2.1.5"
    app-module-path: "npm:^2.2.0"
    cli-table3: "npm:0.6.0"
    colors: "npm:1.4.0"
    cosmiconfig: "npm:7.0.1"
    cross-spawn: "npm:7.0.3"
    ejs: "npm:3.1.8"
    enquirer: "npm:2.3.6"
    execa: "npm:5.1.1"
    fs-jetpack: "npm:4.3.1"
    lodash.camelcase: "npm:^4.3.0"
    lodash.kebabcase: "npm:^4.1.1"
    lodash.lowercase: "npm:^4.3.0"
    lodash.lowerfirst: "npm:^4.3.1"
    lodash.pad: "npm:^4.5.1"
    lodash.padend: "npm:^4.6.1"
    lodash.padstart: "npm:^4.6.1"
    lodash.repeat: "npm:^4.1.0"
    lodash.snakecase: "npm:^4.1.1"
    lodash.startcase: "npm:^4.4.0"
    lodash.trim: "npm:^4.5.1"
    lodash.trimend: "npm:^4.5.1"
    lodash.trimstart: "npm:^4.5.1"
    lodash.uppercase: "npm:^4.3.0"
    lodash.upperfirst: "npm:^4.3.1"
    ora: "npm:4.0.2"
    pluralize: "npm:^8.0.0"
    semver: "npm:7.3.5"
    which: "npm:2.0.2"
    yargs-parser: "npm:^21.0.0"
  bin:
    gluegun: bin/gluegun
  checksum: 10c0/acf43806b4733369c778cb3abd03a415b83a97508903021ca0966cf377947ff44e741961a580d79bb5824de9a17bd6fc8290062ea6f450a303db96c95adac893
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.1.3"
  checksum: 10c0/505c05487f7944c552cee72087bf1567debb470d4355b1335f2c262d218ebbff805cd3715448fe29b4b380bae6912561d0467233e4165830efd28da241418c63
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphql-import-node@npm:^0.0.5":
  version: 0.0.5
  resolution: "graphql-import-node@npm:0.0.5"
  peerDependencies:
    graphql: "*"
  checksum: 10c0/97de408098985f9e5c5d3f2a898c700ea8222c578dc898289bbfea0066be73eb88cc58b1e3a8ae1c71a81651541d8da16bbddfb7a352afcc88edf026ad1fa13c
  languageName: node
  linkType: hard

"graphql@npm:15.5.0":
  version: 15.5.0
  resolution: "graphql@npm:15.5.0"
  checksum: 10c0/b856750240ae12d852c918dc03699cfab61b5b4d1822ae0e4dfe201673eb0b5d99dbb7ff7c9cf83c43d0a87428555640ec392580364685f544e61192f7ffc274
  languageName: node
  linkType: hard

"graphql@npm:^16.6.0":
  version: 16.8.2
  resolution: "graphql@npm:16.8.2"
  checksum: 10c0/921a6321c4efe2540fd5a752093c1542f96cd10b3907b1a8044819f054e6e8a6bca78d93a8eada065a07dfd4e53d89eef99ebdced2c372453e05480e12d8652e
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10c0/1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1":
  version: 1.0.3
  resolution: "has-proto@npm:1.0.3"
  checksum: 10c0/35a6989f81e9f8022c2f4027f8b48a552de714938765d019dbea6bb547bd49ce5010a3c7c32ec6ddac6e48fc546166a3583b128f5a7add8b058a6d8b4afec205
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: 10c0/e6922b4345a3f37069cdfe8600febbca791c94988c01af3394d86ca3360b4b93928bbf395859158f88099cb10b19d98e3bbab7c9ff2c1bd09cf665ee90afa2c3
  languageName: node
  linkType: hard

"hash-base@npm:^3.0.0":
  version: 3.1.0
  resolution: "hash-base@npm:3.1.0"
  dependencies:
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.6.0"
    safe-buffer: "npm:^5.2.0"
  checksum: 10c0/663eabcf4173326fbb65a1918a509045590a26cc7e0964b754eef248d281305c6ec9f6b31cb508d02ffca383ab50028180ce5aefe013e942b44a903ac8dc80d0
  languageName: node
  linkType: hard

"hash.js@npm:1.1.7, hash.js@npm:^1.0.0, hash.js@npm:^1.0.3, hash.js@npm:^1.1.7":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: "npm:^2.0.3"
    minimalistic-assert: "npm:^1.0.1"
  checksum: 10c0/41ada59494eac5332cfc1ce6b7ebdd7b88a3864a6d6b08a3ea8ef261332ed60f37f10877e0c825aaa4bddebf164fbffa618286aeeec5296675e2671cbfa746c4
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: "npm:^1.0.3"
    minimalistic-assert: "npm:^1.0.0"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10c0/f3d9ba31b40257a573f162176ac5930109816036c59a09f901eb2ffd7e5e705c6832bedfff507957125f2086a0ab8f853c0df225642a88bf1fcaea945f20600d
  languageName: node
  linkType: hard

"http-basic@npm:^8.1.1":
  version: 8.1.3
  resolution: "http-basic@npm:8.1.3"
  dependencies:
    caseless: "npm:^0.12.0"
    concat-stream: "npm:^1.6.2"
    http-response-object: "npm:^3.0.1"
    parse-cache-control: "npm:^1.0.1"
  checksum: 10c0/dbc67b943067db7f43d1dd94539f874e6b78614227491c0a5c0acb9b0490467a4ec97247da21eb198f8968a5dc4089160165cb0103045cadb9b47bb844739752
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10c0/ce1319b8a382eb3cbb4a37c19f6bfe14e5bb5be3d09079e885e8c513ab2d3cd9214902f8a31c9dc4e37022633ceabfc2d697405deeaf1b8f3552bb4ed996fdfc
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"http-response-object@npm:^3.0.1":
  version: 3.0.2
  resolution: "http-response-object@npm:3.0.2"
  dependencies:
    "@types/node": "npm:^10.0.3"
  checksum: 10c0/f161db99184087798563cb14c48a67eebe9405668a5ed2341faf85d3079a2c00262431df8e0ccbe274dc6415b6729179f12b09f875d13ad33d83401e4b1ed22e
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.5
  resolution: "https-proxy-agent@npm:7.0.5"
  dependencies:
    agent-base: "npm:^7.0.2"
    debug: "npm:4"
  checksum: 10c0/2490e3acec397abeb88807db52cac59102d5ed758feee6df6112ab3ccd8325e8a1ce8bce6f4b66e5470eca102d31e425ace904242e4fa28dbe0c59c4bafa7b2c
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10c0/695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"hyperlinker@npm:^1.0.0":
  version: 1.0.0
  resolution: "hyperlinker@npm:1.0.0"
  checksum: 10c0/7b980f51611fb5efb62ad5aa3a8af9305b7fb0c203eb9d8915e24e96cdb43c5a4121e2d461bfd74cf47d4e01e39ce473700ea0e2353cb1f71758f94be37a44b0
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.1
  resolution: "ignore@npm:5.3.1"
  checksum: 10c0/703f7f45ffb2a27fb2c5a8db0c32e7dee66b33a225d28e8db4e1be6474795f606686a6e3bcc50e1aa12f2042db4c9d4a7d60af3250511de74620fbed052ea4cd
  languageName: node
  linkType: hard

"immutable@npm:4.2.1":
  version: 4.2.1
  resolution: "immutable@npm:4.2.1"
  checksum: 10c0/27b29dbbd5ca722a973c688c0ad91f2d09ea12058ff763045730abf12e20b6171da45379722c0bfb0220103a535244fbff9030fe73854eb22f017526643b1cc0
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/7f882953aa6b740d1f0e384d0547158bc86efbf2eea0f1483b8900a6f65c5a5123c2cf09b0d542cc419d0b98a759ecaeb394237e97ea427f2da221dc3cd80cc3
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10c0/1e1904ddb0cb3d6cce7cd09e27a90184908b7a5d5c21b92e232c93579d314f0b83c246ffb035493d0504b1e9147ba2c9b21df0030f48673fba0496ecd698161f
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.1, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"interface-datastore@npm:^6.0.2":
  version: 6.1.1
  resolution: "interface-datastore@npm:6.1.1"
  dependencies:
    interface-store: "npm:^2.0.2"
    nanoid: "npm:^3.0.2"
    uint8arrays: "npm:^3.0.0"
  checksum: 10c0/f81f12be4d2dc7c4457284515ad3fff932927456700c8bc38c17580f8a0491da5b9ae1ee6e3ced7ba45bbc7e5f9244bd2fb72256c56a4395ca5c4b6b2eb15918
  languageName: node
  linkType: hard

"interface-store@npm:^2.0.2":
  version: 2.0.2
  resolution: "interface-store@npm:2.0.2"
  checksum: 10c0/182dc874ac67dbdb72c1f8a122f1272c5e8840a3f106182c4ea60a1d7945672eb614006b833868350806719cfba9d664b6642e46333895c5a5058199761540bd
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ip-regex@npm:^4.0.0":
  version: 4.3.0
  resolution: "ip-regex@npm:4.3.0"
  checksum: 10c0/f9ef1f5d0df05b9133a882974e572ae525ccd205260cb103dae337f1fc7451ed783391acc6ad688e56dd2598f769e8e72ecbb650ec34763396af822a91768562
  languageName: node
  linkType: hard

"ipfs-core-types@npm:^0.9.0":
  version: 0.9.0
  resolution: "ipfs-core-types@npm:0.9.0"
  dependencies:
    interface-datastore: "npm:^6.0.2"
    multiaddr: "npm:^10.0.0"
    multiformats: "npm:^9.4.13"
  checksum: 10c0/3fc4abd4186aa696655f84e09c632b7941003290f46a9a064200e11d8c7613e1abf4776ddf6abacde591da5758b18fbff257d5b31b101bbb0db5cc0c659f5343
  languageName: node
  linkType: hard

"ipfs-core-utils@npm:^0.13.0":
  version: 0.13.0
  resolution: "ipfs-core-utils@npm:0.13.0"
  dependencies:
    any-signal: "npm:^2.1.2"
    blob-to-it: "npm:^1.0.1"
    browser-readablestream-to-it: "npm:^1.0.1"
    debug: "npm:^4.1.1"
    err-code: "npm:^3.0.1"
    ipfs-core-types: "npm:^0.9.0"
    ipfs-unixfs: "npm:^6.0.3"
    ipfs-utils: "npm:^9.0.2"
    it-all: "npm:^1.0.4"
    it-map: "npm:^1.0.4"
    it-peekable: "npm:^1.0.2"
    it-to-stream: "npm:^1.0.0"
    merge-options: "npm:^3.0.4"
    multiaddr: "npm:^10.0.0"
    multiaddr-to-uri: "npm:^8.0.0"
    multiformats: "npm:^9.4.13"
    nanoid: "npm:^3.1.23"
    parse-duration: "npm:^1.0.0"
    timeout-abort-controller: "npm:^2.0.0"
    uint8arrays: "npm:^3.0.0"
  checksum: 10c0/dab4472c25a4066c8f18763567d54fc8125623f5f29a6df8dfd951241013834f0fe8384836fc70779153927ddd5b22e7872f301cb1c4c3b380ba4ffed05fb490
  languageName: node
  linkType: hard

"ipfs-http-client@npm:55.0.0":
  version: 55.0.0
  resolution: "ipfs-http-client@npm:55.0.0"
  dependencies:
    "@ipld/dag-cbor": "npm:^7.0.0"
    "@ipld/dag-json": "npm:^8.0.1"
    "@ipld/dag-pb": "npm:^2.1.3"
    abort-controller: "npm:^3.0.0"
    any-signal: "npm:^2.1.2"
    debug: "npm:^4.1.1"
    err-code: "npm:^3.0.1"
    ipfs-core-types: "npm:^0.9.0"
    ipfs-core-utils: "npm:^0.13.0"
    ipfs-utils: "npm:^9.0.2"
    it-first: "npm:^1.0.6"
    it-last: "npm:^1.0.4"
    merge-options: "npm:^3.0.4"
    multiaddr: "npm:^10.0.0"
    multiformats: "npm:^9.4.13"
    native-abort-controller: "npm:^1.0.3"
    parse-duration: "npm:^1.0.0"
    stream-to-it: "npm:^0.2.2"
    uint8arrays: "npm:^3.0.0"
  checksum: 10c0/245fa33400dfd68c8bbc8f412695285210c3341094c8b69b70a70071d955a504cac59cb029bb1aa152117908e863b10fa3e04e9f41083ee4d593c96737c642dd
  languageName: node
  linkType: hard

"ipfs-unixfs@npm:^6.0.3":
  version: 6.0.9
  resolution: "ipfs-unixfs@npm:6.0.9"
  dependencies:
    err-code: "npm:^3.0.1"
    protobufjs: "npm:^6.10.2"
  checksum: 10c0/4be0794f1113af69fb0533c84b72f0001e894b823e30af88451db07c51d118eec53ff23fa7a04e0f6b3749c34c845f3110f1452f7a123f59eca8dc5a42d894ad
  languageName: node
  linkType: hard

"ipfs-utils@npm:^9.0.2":
  version: 9.0.14
  resolution: "ipfs-utils@npm:9.0.14"
  dependencies:
    any-signal: "npm:^3.0.0"
    browser-readablestream-to-it: "npm:^1.0.0"
    buffer: "npm:^6.0.1"
    electron-fetch: "npm:^1.7.2"
    err-code: "npm:^3.0.1"
    is-electron: "npm:^2.2.0"
    iso-url: "npm:^1.1.5"
    it-all: "npm:^1.0.4"
    it-glob: "npm:^1.0.1"
    it-to-stream: "npm:^1.0.0"
    merge-options: "npm:^3.0.4"
    nanoid: "npm:^3.1.20"
    native-fetch: "npm:^3.0.0"
    node-fetch: "npm:^2.6.8"
    react-native-fetch-api: "npm:^3.0.0"
    stream-to-it: "npm:^0.2.2"
  checksum: 10c0/c2b91a85d7d927c071d122ae4d70b7de7d47860c4ffbc18ce34bec7582e047194c2a52755caebc8c12cb68ec2106bd467246b835564b047738a2bddb03811401
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10c0/e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 10c0/e828365958d155f90c409cdbe958f64051d99e8aedc2c8c4cd7c89dcf35329daed42f7b99346f7828df013e27deb8f721cf9408ba878c76eb9e8290235fbcdcc
  languageName: node
  linkType: hard

"is-electron@npm:^2.2.0":
  version: 2.2.2
  resolution: "is-electron@npm:2.2.2"
  checksum: 10c0/327bb373f7be01b16cdff3998b5ddaa87d28f576092affaa7fe0659571b3306fdd458afbf0683a66841e7999af13f46ad0e1b51647b469526cd05a4dd736438a
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-hex-prefixed@npm:1.0.0":
  version: 1.0.0
  resolution: "is-hex-prefixed@npm:1.0.0"
  checksum: 10c0/767fa481020ae654ab085ca24c63c518705ff36fdfbfc732292dc69092c6f8fdc551f6ce8c5f6ae704b0a19294e6f62be1b4b9859f0e1ac76e3b1b0733599d94
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 10c0/dd47904dbf286cd20aa58c5192161be1a67138485b9836d5a70433b21a45442e9611b8498b8ab1f839fc962c7620667a50535fdfb4a6bc7989b8858645c06b4d
  languageName: node
  linkType: hard

"is-ip@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-ip@npm:3.1.0"
  dependencies:
    ip-regex: "npm:^4.0.0"
  checksum: 10c0/4cb643c831314b8fc72770c93a795c0d3dde339f36c8430544c36727956027e2cb329641ace73c5951085ecf93ac608c898859d3d4f7b117d405e1e13c703c76
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 10c0/85fee098ae62ba6f1e24cf22678805473c7afd0fb3978a3aa260e354cb7bcb3a5806cf0a98403188465efedec41ab4348e8e4e79305d409601323855b3839d4d
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-plain-obj@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: 10c0/e5c9814cdaa627a9ad0a0964ded0e0491bfd9ace405c49a5d63c88b30a162f1512c069d5b80997893c4d0181eadc3fed02b4ab4b81059aba5620bfcdfdeb9c53
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 10c0/a6fa2d370d21be487c0165c7a440d567274fbba1a817f2f0bfa41cc5e3af25041d84267baa22df66696956038a43973e72fca117918c91431920bdef490fa25e
  languageName: node
  linkType: hard

"isarray@npm:0.0.1":
  version: 0.0.1
  resolution: "isarray@npm:0.0.1"
  checksum: 10c0/ed1e62da617f71fe348907c71743b5ed550448b455f8d269f89a7c7ddb8ae6e962de3dab6a74a237b06f5eb7f6ece7a45ada8ce96d87fe972926530f91ae3311
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"iso-url@npm:^1.1.5":
  version: 1.2.1
  resolution: "iso-url@npm:1.2.1"
  checksum: 10c0/73be82eaaf5530acb1b6a46829e0dfb050c62790b8dc04d7fb7e290b63c88846b4d861ecf3a6bc7e0a3d74e569ea53c0fb951d596e06d6c6dd0cf4342d59ecc9
  languageName: node
  linkType: hard

"isomorphic-ws@npm:^4.0.1":
  version: 4.0.1
  resolution: "isomorphic-ws@npm:4.0.1"
  peerDependencies:
    ws: "*"
  checksum: 10c0/7cb90dc2f0eb409825558982fb15d7c1d757a88595efbab879592f9d2b63820d6bbfb5571ab8abe36c715946e165a413a99f6aafd9f40ab1f514d73487bc9996
  languageName: node
  linkType: hard

"it-all@npm:^1.0.4":
  version: 1.0.6
  resolution: "it-all@npm:1.0.6"
  checksum: 10c0/366b5f7b9ceda9c1183d6d67c94e9e8216e21d6a037068881941c6e625aa76e47833a82c328263d118c66d5c3fcf3ebb482a38d6cfa8aebe03c56db791a711f6
  languageName: node
  linkType: hard

"it-first@npm:^1.0.6":
  version: 1.0.7
  resolution: "it-first@npm:1.0.7"
  checksum: 10c0/b6a741f3f761fe4bc74dcccc8bae287ae02982d4c92cfe19d67568026ec40380d3ced526097480034fbc887b6a5d1f9f56ddfc76f7167e43e643854824eedfe4
  languageName: node
  linkType: hard

"it-glob@npm:^1.0.1":
  version: 1.0.2
  resolution: "it-glob@npm:1.0.2"
  dependencies:
    "@types/minimatch": "npm:^3.0.4"
    minimatch: "npm:^3.0.4"
  checksum: 10c0/461f6b80fefbba7b915f29b297038f473b965d32bfde33507ba938da408eccccf5b2ea98aad55b0afe3aff7d4f6bbb7af5a92e27b25ae7e4590a859b339e4f88
  languageName: node
  linkType: hard

"it-last@npm:^1.0.4":
  version: 1.0.6
  resolution: "it-last@npm:1.0.6"
  checksum: 10c0/2f8e030e3d05c2388c953aacb30b14e1052547872881cee39182bfb45997c886d75a5507ef6a0ebfca718a4b18192cfd0ee5a9f22b961381794f74407ffad7d3
  languageName: node
  linkType: hard

"it-map@npm:^1.0.4":
  version: 1.0.6
  resolution: "it-map@npm:1.0.6"
  checksum: 10c0/bd50936f0534e19cc1110add5150a1791037d2ef56756c1bbb1f3cfbea9d3e143788bef3005481edfadc48364d2139451298ecb139581683c46705efdf8b80a5
  languageName: node
  linkType: hard

"it-peekable@npm:^1.0.2":
  version: 1.0.3
  resolution: "it-peekable@npm:1.0.3"
  checksum: 10c0/f8c770d6d74e839e147f14b364ff1ca769705342e88fe615b5adf4f27287980b11a877ffc190924a47f385af780df79911fcd8cac2a1123fa0d3962d210cd419
  languageName: node
  linkType: hard

"it-to-stream@npm:^1.0.0":
  version: 1.0.0
  resolution: "it-to-stream@npm:1.0.0"
  dependencies:
    buffer: "npm:^6.0.3"
    fast-fifo: "npm:^1.0.0"
    get-iterator: "npm:^1.0.2"
    p-defer: "npm:^3.0.0"
    p-fifo: "npm:^1.0.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/92415ba12aac6df438ab941bcb8ca3bda4c6d1ce50756d3aa55916e8ab0e85e069c571b45cd27a0c85b0370adb062d843995a18257c93f4ef2237b21f66666b4
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jake@npm:^10.8.5":
  version: 10.9.1
  resolution: "jake@npm:10.9.1"
  dependencies:
    async: "npm:^3.2.3"
    chalk: "npm:^4.0.2"
    filelist: "npm:^1.0.4"
    minimatch: "npm:^3.1.2"
  bin:
    jake: bin/cli.js
  checksum: 10c0/dda972431a926462f08fcf583ea8997884216a43daa5cce81cb42e7e661dc244f836c0a802fde23439c6e1fc59743d1c0be340aa726d3b17d77557611a5cd541
  languageName: node
  linkType: hard

"jayson@npm:4.0.0":
  version: 4.0.0
  resolution: "jayson@npm:4.0.0"
  dependencies:
    "@types/connect": "npm:^3.4.33"
    "@types/node": "npm:^12.12.54"
    "@types/ws": "npm:^7.4.4"
    JSONStream: "npm:^1.3.5"
    commander: "npm:^2.20.3"
    delay: "npm:^5.0.0"
    es6-promisify: "npm:^5.0.0"
    eyes: "npm:^0.1.8"
    isomorphic-ws: "npm:^4.0.1"
    json-stringify-safe: "npm:^5.0.1"
    uuid: "npm:^8.3.2"
    ws: "npm:^7.4.5"
  bin:
    jayson: bin/jayson.js
  checksum: 10c0/d531520be7427c540e3083de596011c98373c25353bae97abe905a0533e84f844b974d4e8ab3b7763c925adf896df0cadb8b7a0066a6a67ac5fd9315eab36960
  languageName: node
  linkType: hard

"js-sha3@npm:0.8.0":
  version: 0.8.0
  resolution: "js-sha3@npm:0.8.0"
  checksum: 10c0/43a21dc7967c871bd2c46cb1c2ae97441a97169f324e509f382d43330d8f75cf2c96dba7c806ab08a425765a9c847efdd4bffbac2d99c3a4f3de6c0218f40533
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:3.14.1, js-yaml@npm:^3.14.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10c0/140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-stringify-safe@npm:^5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 10c0/7dbf35cd0411d1d648dceb6d59ce5857ec939e52e4afc37601aa3da611f0987d5cee5b38d58329ceddf3ed48bd7215229c8d52059ab01f2444a338bf24ed0f37
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jsonparse@npm:^1.2.0":
  version: 1.3.1
  resolution: "jsonparse@npm:1.3.1"
  checksum: 10c0/89bc68080cd0a0e276d4b5ab1b79cacd68f562467008d176dc23e16e97d4efec9e21741d92ba5087a8433526a45a7e6a9d5ef25408696c402ca1cfbc01a90bf0
  languageName: node
  linkType: hard

"keccak@npm:^3.0.0":
  version: 3.0.4
  resolution: "keccak@npm:3.0.4"
  dependencies:
    node-addon-api: "npm:^2.0.0"
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.2.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/153525c1c1f770beadb8f8897dec2f1d2dcbee11d063fe5f61957a5b236bfd3d2a111ae2727e443aa6a848df5edb98b9ef237c78d56df49087b0ca8a232ca9cd
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10c0/3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: 10c0/fcba15d21a458076dd309fce6b1b4bf611d84a0ec252cb92447c948c533ac250b95d2e00955801ebc367e5af5ed288b996d75d37d2035260a937008e14eaf432
  languageName: node
  linkType: hard

"lodash.kebabcase@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.kebabcase@npm:4.1.1"
  checksum: 10c0/da5d8f41dbb5bc723d4bf9203d5096ca8da804d6aec3d2b56457156ba6c8d999ff448d347ebd97490da853cb36696ea4da09a431499f1ee8deb17b094ecf4e33
  languageName: node
  linkType: hard

"lodash.lowercase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.lowercase@npm:4.3.0"
  checksum: 10c0/6b2e6bea51d8ce06e85449275722f97de7a72267395d31001ad43b6323e60abc0aa2144139f4b97fcecca080a9083ca9924630f8f3fb7a7b11d6d3eede8067b9
  languageName: node
  linkType: hard

"lodash.lowerfirst@npm:^4.3.1":
  version: 4.3.1
  resolution: "lodash.lowerfirst@npm:4.3.1"
  checksum: 10c0/25b148bfbeb4fc0c6f2616728d4b62fd615864b616cec838ee6877e0da609dc8bf9231a0d03799f41d9e0952dcb9471da777ea9ded4915e2d3cc89bf8c8accf8
  languageName: node
  linkType: hard

"lodash.pad@npm:^4.5.1":
  version: 4.5.1
  resolution: "lodash.pad@npm:4.5.1"
  checksum: 10c0/e90e3f789588332b644db32cf4b74d2cac0ee2019e39c68821f5d3fabde2183f199fb5f279af0d2f324f40908754926c13c3d012131bbce2b7235cec2c1fab5b
  languageName: node
  linkType: hard

"lodash.padend@npm:^4.6.1":
  version: 4.6.1
  resolution: "lodash.padend@npm:4.6.1"
  checksum: 10c0/da10eae6e7862541e431d97e652ea66690307104676a30793398e2f66d0fd9a62b07f199451d2185560d9b4627dc6652d33dc7cceb7ab9d843f6e15addec56f5
  languageName: node
  linkType: hard

"lodash.padstart@npm:^4.6.1":
  version: 4.6.1
  resolution: "lodash.padstart@npm:4.6.1"
  checksum: 10c0/13c6c867d92a4dddd340484bc18ba89f08598c1afdd4d4eb9f0deb0d00842643cf134fab5262518407f6d3f0d2ab4fb3a8bcc7fcec02acbabfb1810de860485f
  languageName: node
  linkType: hard

"lodash.repeat@npm:^4.1.0":
  version: 4.1.0
  resolution: "lodash.repeat@npm:4.1.0"
  checksum: 10c0/c82078982e22b5887e65a4ee9e8a6cecf2feb2f62d66b838e9f21e6daf8f805a5da98d445860147a5d9a4cb32caa9f9a3ef0b6561a052377c3883abb363feed1
  languageName: node
  linkType: hard

"lodash.snakecase@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.snakecase@npm:4.1.1"
  checksum: 10c0/f0b3f2497eb20eea1a1cfc22d645ecaeb78ac14593eb0a40057977606d2f35f7aaff0913a06553c783b535aafc55b718f523f9eb78f8d5293f492af41002eaf9
  languageName: node
  linkType: hard

"lodash.startcase@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.startcase@npm:4.4.0"
  checksum: 10c0/bd82aa87a45de8080e1c5ee61128c7aee77bf7f1d86f4ff94f4a6d7438fc9e15e5f03374b947be577a93804c8ad6241f0251beaf1452bf716064eeb657b3a9f0
  languageName: node
  linkType: hard

"lodash.trim@npm:^4.5.1":
  version: 4.5.1
  resolution: "lodash.trim@npm:4.5.1"
  checksum: 10c0/5e81316d8fb02ff63c92d73cc737cc264ea49114a0f42e083ceb7c39679be30cd3497c0c1f6a3b79c36e322fa0b52dd619fc26a9fb693cc5201988d967d5292c
  languageName: node
  linkType: hard

"lodash.trimend@npm:^4.5.1":
  version: 4.5.1
  resolution: "lodash.trimend@npm:4.5.1"
  checksum: 10c0/5f70418bb3e8d61c8b515710f17d836f2a3a099815b8032db60161689829afc76ee20b276a6410a650a0e5225d028f79c2e38fce4d6ade6a1bedb8b4b056df96
  languageName: node
  linkType: hard

"lodash.trimstart@npm:^4.5.1":
  version: 4.5.1
  resolution: "lodash.trimstart@npm:4.5.1"
  checksum: 10c0/de0d4b1da63fb98cdae1e9ce02e845e5791b0cae78285f0dc6a8029878e7663e586bf6660ab49c5c12302432583f5f7ec2effe735645b208cbf758eba5f9f0d8
  languageName: node
  linkType: hard

"lodash.uppercase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.uppercase@npm:4.3.0"
  checksum: 10c0/aa5d8ec335b369dc00ab79f54a128c39ee814799eb7f48a316095eb40e9771b34b4e713c7de15ac327d2997e64089033056b48838acb890b127c273bd46ebed6
  languageName: node
  linkType: hard

"lodash.upperfirst@npm:^4.3.1":
  version: 4.3.1
  resolution: "lodash.upperfirst@npm:4.3.1"
  checksum: 10c0/435625da4b3ee74e7a1367a780d9107ab0b13ef4359fc074b2a1a40458eb8d91b655af62f6795b7138d493303a98c0285340160341561d6896e4947e077fa975
  languageName: node
  linkType: hard

"log-symbols@npm:^3.0.0":
  version: 3.0.0
  resolution: "log-symbols@npm:3.0.0"
  dependencies:
    chalk: "npm:^2.4.2"
  checksum: 10c0/d11582a1b499b76aa1415988234ad54d9fb3f888f4cb4186cbc20ee4d314ac4b5f3d9fe9edd828748d2c0d372df2ea9f5dfd89100510988a8ce5ddf483ae015e
  languageName: node
  linkType: hard

"long@npm:^4.0.0":
  version: 4.0.0
  resolution: "long@npm:4.0.0"
  checksum: 10c0/50a6417d15b06104dbe4e3d4a667c39b137f130a9108ea8752b352a4cfae047531a3ac351c181792f3f8768fe17cca6b0f406674a541a86fb638aaac560d83ed
  languageName: node
  linkType: hard

"long@npm:^5.2.0":
  version: 5.2.3
  resolution: "long@npm:5.2.3"
  checksum: 10c0/6a0da658f5ef683b90330b1af76f06790c623e148222da9d75b60e266bbf88f803232dd21464575681638894a84091616e7f89557aa087fd14116c0f4e0e43d9
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^10.2.0":
  version: 10.2.2
  resolution: "lru-cache@npm:10.2.2"
  checksum: 10c0/402d31094335851220d0b00985084288136136992979d0e015f0f1697e15d1c86052d7d53ae86b614e5b058425606efffc6969a31a091085d7a2b80a8a1e26d6
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/cb53e582785c48187d7a188d3379c181b5ca2a9c78d2bce3e7dee36f32761d1c42983da3fe12b55cb74e1779fa94cdc2e5367c028a9b35317184ede0c07a30a9
  languageName: node
  linkType: hard

"make-error@npm:^1.1.1":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: 10c0/171e458d86854c6b3fc46610cfacf0b45149ba043782558c6875d9f42f222124384ad0b468c92e996d815a8a2003817a710c0a160e49c1c394626f76fa45396f
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^13.0.0":
  version: 13.0.1
  resolution: "make-fetch-happen@npm:13.0.1"
  dependencies:
    "@npmcli/agent": "npm:^2.0.0"
    cacache: "npm:^18.0.0"
    http-cache-semantics: "npm:^4.1.1"
    is-lambda: "npm:^1.0.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^3.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^0.6.3"
    proc-log: "npm:^4.2.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^10.0.0"
  checksum: 10c0/df5f4dbb6d98153b751bccf4dc4cc500de85a96a9331db9805596c46aa9f99d9555983954e6c1266d9f981ae37a9e4647f42b9a4bb5466f867f4012e582c9e7e
  languageName: node
  linkType: hard

"matchstick-as@npm:^0.6.0":
  version: 0.6.0
  resolution: "matchstick-as@npm:0.6.0"
  dependencies:
    wabt: "npm:1.0.24"
  checksum: 10c0/ada0c5039d4b5b85c60600b19338f8b8dd2e566d3afac4da12c0cf612f3ae97d5e0db8ac0ca501975b3e815b1d9a8d8a028a8cc2be24a6c87241242050f64204
  languageName: node
  linkType: hard

"md5.js@npm:^1.3.4":
  version: 1.3.5
  resolution: "md5.js@npm:1.3.5"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.1.2"
  checksum: 10c0/b7bd75077f419c8e013fc4d4dada48be71882e37d69a44af65a2f2804b91e253441eb43a0614423a1c91bb830b8140b0dc906bc797245e2e275759584f4efcc5
  languageName: node
  linkType: hard

"merge-options@npm:^3.0.4":
  version: 3.0.4
  resolution: "merge-options@npm:3.0.4"
  dependencies:
    is-plain-obj: "npm:^2.1.0"
  checksum: 10c0/02b5891ceef09b0b497b5a0154c37a71784e68ed71b14748f6fd4258ffd3fe4ecd5cb0fd6f7cae3954fd11e7686c5cb64486daffa63c2793bbe8b614b61c7055
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4":
  version: 4.0.7
  resolution: "micromatch@npm:4.0.7"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/58fa99bc5265edec206e9163a1d2cec5fabc46a5b473c45f4a700adce88c2520456ae35f2b301e4410fb3afb27e9521fb2813f6fc96be0a48a89430e0916a772
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: 10c0/96730e5601cd31457f81a296f521eb56036e6f69133c0b18c13fe941109d53ad23a4204d946a0d638d7f3099482a0cec8c9bb6d642604612ce43ee536be3dddd
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 10c0/790ecec8c5c73973a4fbf2c663d911033e8494d5fb0960a4500634766ab05d6107d20af896ca2132e7031741f19888154d44b2408ada0852446705441383e9f8
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.2, minimatch@npm:^3.0.4, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/3defdfd230914f22a8da203747c42ee3c405c39d4d37ffda284dac5e45b7e1f6c49aa8be606509002898e73091ff2a3bbfc59c2c6c71d4660609f63aa92f98e3
  languageName: node
  linkType: hard

"minimatch@npm:^8.0.2":
  version: 8.0.4
  resolution: "minimatch@npm:8.0.4"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/a0a394c356dd5b4cb7f821720841a82fa6f07c9c562c5b716909d1b6ec5e56a7e4c4b5029da26dd256b7d2b3a3f38cbf9ddd8680e887b9b5282b09c05501c1ca
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.5
  resolution: "minipass-fetch@npm:3.0.5"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^2.1.2"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/9d702d57f556274286fdd97e406fc38a2f5c8d15e158b498d7393b1105974b21249289ec571fa2b51e038a4872bfc82710111cf75fae98c662f3d6f95e72152b
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^4.2.4":
  version: 4.2.8
  resolution: "minipass@npm:4.2.8"
  checksum: 10c0/4ea76b030d97079f4429d6e8a8affd90baf1b6a1898977c8ccce4701c5a2ba2792e033abc6709373f25c2c4d4d95440d9d5e9464b46b7b76ca44d2ce26d939ce
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 10c0/a91d8043f691796a8ac88df039da19933ef0f633e3d7f0d35dcd5373af49131cf2399bfc355f41515dc495e3990369c3858cd319e5c2722b4753c90bf3152462
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: 10c0/64fae024e1a7d0346a1102bb670085b17b7f95bf6cfdf5b128772ec8faf9ea211464ea4add406a3a6384a7d87a0cd1a96263692134323477b4fb43659a6cab78
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.1":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/e2e2be789218807b58abced04e7b49851d9e46e88a2f9539242cc8a92c9b5c3a0b9bab360bd3014e02a140fc4fbc58e31176c408b493f8a2a6f4986bd7527b01
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 10c0/a437714e2f90dbf881b5191d35a6db792efbca5badf112f87b9e1c712aace4b4b9b742dd6537f3edf90fd6f684de897cec230abde57e87883766712ddda297cc
  languageName: node
  linkType: hard

"ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"multiaddr-to-uri@npm:^8.0.0":
  version: 8.0.0
  resolution: "multiaddr-to-uri@npm:8.0.0"
  dependencies:
    multiaddr: "npm:^10.0.0"
  checksum: 10c0/9600880b2c9cf788b4f01be5c1cba98dfbcddc7e61ac40270f15e24d30989e40bb9ed0f694c65cdd34797c4e31e7ae2eb58ba1adce947f46fbc873a788df296e
  languageName: node
  linkType: hard

"multiaddr@npm:^10.0.0":
  version: 10.0.1
  resolution: "multiaddr@npm:10.0.1"
  dependencies:
    dns-over-http-resolver: "npm:^1.2.3"
    err-code: "npm:^3.0.1"
    is-ip: "npm:^3.1.0"
    multiformats: "npm:^9.4.5"
    uint8arrays: "npm:^3.0.0"
    varint: "npm:^6.0.0"
  checksum: 10c0/948d0c69d75992d754fd36154db4d4a435b977c56a617183a9d8c2075081e2cfcacda3bedb72bbd6759bb329956a45b216e804829e708af8d2f780f8152a4bf6
  languageName: node
  linkType: hard

"multiformats@npm:^9.4.13, multiformats@npm:^9.4.2, multiformats@npm:^9.4.5, multiformats@npm:^9.5.4":
  version: 9.9.0
  resolution: "multiformats@npm:9.9.0"
  checksum: 10c0/1fdb34fd2fb085142665e8bd402570659b50a5fae5994027e1df3add9e1ce1283ed1e0c2584a5c63ac0a58e871b8ee9665c4a99ca36ce71032617449d48aa975
  languageName: node
  linkType: hard

"mustache@npm:^4.2.0":
  version: 4.2.0
  resolution: "mustache@npm:4.2.0"
  bin:
    mustache: bin/mustache
  checksum: 10c0/1f8197e8a19e63645a786581d58c41df7853da26702dbc005193e2437c98ca49b255345c173d50c08fe4b4dbb363e53cb655ecc570791f8deb09887248dd34a2
  languageName: node
  linkType: hard

"nanoid@npm:^3.0.2, nanoid@npm:^3.1.20, nanoid@npm:^3.1.23":
  version: 3.3.7
  resolution: "nanoid@npm:3.3.7"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/e3fb661aa083454f40500473bb69eedb85dc160e763150b9a2c567c7e9ff560ce028a9f833123b618a6ea742e311138b591910e795614a629029e86e180660f3
  languageName: node
  linkType: hard

"native-abort-controller@npm:^1.0.3, native-abort-controller@npm:^1.0.4":
  version: 1.0.4
  resolution: "native-abort-controller@npm:1.0.4"
  peerDependencies:
    abort-controller: "*"
  checksum: 10c0/34e597cb3fcc05a2386b61e79e46d42e2f5708a6533fae43c82a8422d2979ef4221eb11b83e385f77659e672361db5fc1a7fa57919720d5fe730efa53d800e03
  languageName: node
  linkType: hard

"native-fetch@npm:^3.0.0":
  version: 3.0.0
  resolution: "native-fetch@npm:3.0.0"
  peerDependencies:
    node-fetch: "*"
  checksum: 10c0/737cdd209dd366df8b748dabac39340089d57a2bcc460ffc029ec145f30aeffea0c6a6f177013069d6f7f04ffc8c3e39cfb8e3825e7071a373c4f86b187ae1b5
  languageName: node
  linkType: hard

"natural-orderby@npm:^2.0.3":
  version: 2.0.3
  resolution: "natural-orderby@npm:2.0.3"
  checksum: 10c0/e46508c89b8217c752a25feb251dd9229354cbbb7f3cc9263db94138732ef2cf0b3428e5ad517cffe8c9a295512721123a1c88d560dab3ae2ad5d9e8d83868c7
  languageName: node
  linkType: hard

"negotiator@npm:^0.6.3":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 10c0/3e677139c7fb7628a6f36335bf11a885a62c21d5390204590a1a214a5631fcbe5ea74ef6a610b60afe84b4d975cbe0566a23f20ee17c77c73e74b80032108dea
  languageName: node
  linkType: hard

"node-addon-api@npm:^2.0.0":
  version: 2.0.2
  resolution: "node-addon-api@npm:2.0.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/ade6c097ba829fa4aee1ca340117bb7f8f29fdae7b777e343a9d5cbd548481d1f0894b7b907d23ce615c70d932e8f96154caed95c3fa935cfe8cf87546510f64
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.8":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.2.0":
  version: 4.8.1
  resolution: "node-gyp-build@npm:4.8.1"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 10c0/e36ca3d2adf2b9cca316695d7687207c19ac6ed326d6d7c68d7112cebe0de4f82d6733dff139132539fcc01cf5761f6c9082a21864ab9172edf84282bc849ce7
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 10.2.0
  resolution: "node-gyp@npm:10.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^13.0.0"
    nopt: "npm:^7.0.0"
    proc-log: "npm:^4.1.0"
    semver: "npm:^7.3.5"
    tar: "npm:^6.2.1"
    which: "npm:^4.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/00630d67dbd09a45aee0a5d55c05e3916ca9e6d427ee4f7bc392d2d3dc5fad7449b21fc098dd38260a53d9dcc9c879b36704a1994235d4707e7271af7e9a835b
  languageName: node
  linkType: hard

"nopt@npm:^7.0.0":
  version: 7.2.1
  resolution: "nopt@npm:7.2.1"
  dependencies:
    abbrev: "npm:^2.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/a069c7c736767121242037a22a788863accfa932ab285a1eb569eb8cd534b09d17206f68c37f096ae785647435e0c5a5a0a67b42ec743e481a455e5ae6a6df81
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10c0/6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"number-to-bn@npm:1.7.0":
  version: 1.7.0
  resolution: "number-to-bn@npm:1.7.0"
  dependencies:
    bn.js: "npm:4.11.6"
    strip-hex-prefix: "npm:1.0.0"
  checksum: 10c0/83d1540173c4fc60ef4e91e88ed17f2c38418c8e5e62f469d62404527efba48d9c40f364da5c5e6857234a6c1154ff32b3642d80f873ba6cb8d2dd05fb6bc303
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.0":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.1":
  version: 1.13.1
  resolution: "object-inspect@npm:1.13.1"
  checksum: 10c0/fad603f408e345c82e946abdf4bfd774260a5ed3e5997a0b057c44153ac32c7271ff19e3a5ae39c858da683ba045ccac2f65245c12763ce4e8594f818f4a648d
  languageName: node
  linkType: hard

"object-treeify@npm:^1.1.33":
  version: 1.1.33
  resolution: "object-treeify@npm:1.1.33"
  checksum: 10c0/5b735ac552200bf14f9892ce58295303e8d15a8cc7a0fd4fe6ff99923ab0c196fb70a870ab2a0eefc6820c4acb49e614b88c72d344b9c6bd22584a3efbd386fe
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"open@npm:8.4.2":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: "npm:^2.0.0"
    is-docker: "npm:^2.1.1"
    is-wsl: "npm:^2.2.0"
  checksum: 10c0/bb6b3a58401dacdb0aad14360626faf3fb7fba4b77816b373495988b724fb48941cad80c1b65d62bb31a17609b2cd91c41a181602caea597ca80dfbcc27e84c9
  languageName: node
  linkType: hard

"ora@npm:4.0.2":
  version: 4.0.2
  resolution: "ora@npm:4.0.2"
  dependencies:
    chalk: "npm:^2.4.2"
    cli-cursor: "npm:^3.1.0"
    cli-spinners: "npm:^2.2.0"
    is-interactive: "npm:^1.0.0"
    log-symbols: "npm:^3.0.0"
    strip-ansi: "npm:^5.2.0"
    wcwidth: "npm:^1.0.1"
  checksum: 10c0/870b8acf35765aa84a05421d54b8fc3b372576d377b9bb03cfd71854f436a0ff37bde42e2afaa72c9dcafcfea92c9db3ad5a7ee974e0c81285e935ae8bac0f77
  languageName: node
  linkType: hard

"p-defer@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-defer@npm:3.0.0"
  checksum: 10c0/848eb9821785b9a203def23618217ddbfa5cd909574ad0d66aae61a1981c4dcfa084804d6f97abe027bd004643471ddcdc823aa8df60198f791a9bd985e01bee
  languageName: node
  linkType: hard

"p-fifo@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-fifo@npm:1.0.0"
  dependencies:
    fast-fifo: "npm:^1.0.0"
    p-defer: "npm:^3.0.0"
  checksum: 10c0/b9e5b9c14c0fea63801c55c116028dce60770ff0be06dff459981c83c014028cf7b671acb12f169a4bdb3e7e1c5ec75c6d69542aebeccd1c13e3ddd764e7450d
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 10c0/592c05bd6262c466ce269ff172bb8de7c6975afca9b50c975135b974e9bdaafbfe80e61aaaf5be6d1200ba08b30ead04b88cfa7e25ff1e3b93ab28c9f62a2c75
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse-cache-control@npm:^1.0.1":
  version: 1.0.1
  resolution: "parse-cache-control@npm:1.0.1"
  checksum: 10c0/330a0d9e3a22a7b0f6e8a973c0b9f51275642ee28544cd0d546420273946d555d20a5c7b49fca24d68d2e698bae0186f0f41f48d62133d3153c32454db05f2df
  languageName: node
  linkType: hard

"parse-duration@npm:^1.0.0":
  version: 1.1.0
  resolution: "parse-duration@npm:1.1.0"
  checksum: 10c0/26fd83ed8f34b11a6ddb4553c97f4a75decc7a42ffa10c3e998287930cdbd91f27b7958bf5461be3b709bd064245ddccf43566ad8f4daaed2c983f5a7c7f8aed
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10c0/77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"password-prompt@npm:^1.1.2":
  version: 1.1.3
  resolution: "password-prompt@npm:1.1.3"
  dependencies:
    ansi-escapes: "npm:^4.3.2"
    cross-spawn: "npm:^7.0.3"
  checksum: 10c0/f6c2ec49e8bb91a421ed42809c00f8c1d09ee7ea8454c05a40150ec3c47e67b1f16eea7bceace13451accb7bb85859ee3e8d67e8fa3a85f622ba36ebe681ee51
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1, path-scurry@npm:^1.6.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10c0/666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"pbkdf2@npm:^3.0.17":
  version: 3.1.2
  resolution: "pbkdf2@npm:3.1.2"
  dependencies:
    create-hash: "npm:^1.1.2"
    create-hmac: "npm:^1.1.4"
    ripemd160: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
    sha.js: "npm:^2.4.8"
  checksum: 10c0/5a30374e87d33fa080a92734d778cf172542cc7e41b96198c4c88763997b62d7850de3fbda5c3111ddf79805ee7c1da7046881c90ac4920b5e324204518b05fd
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.1
  resolution: "picocolors@npm:1.0.1"
  checksum: 10c0/c63cdad2bf812ef0d66c8db29583802355d4ca67b9285d846f390cc15c2f6ccb94e8cb7eb6a6e97fc5990a6d3ad4ae42d86c84d3146e667c739a4234ed50d400
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"pluralize@npm:^8.0.0":
  version: 8.0.0
  resolution: "pluralize@npm:8.0.0"
  checksum: 10c0/2044cfc34b2e8c88b73379ea4a36fc577db04f651c2909041b054c981cd863dd5373ebd030123ab058d194ae615d3a97cfdac653991e499d10caf592e8b3dc33
  languageName: node
  linkType: hard

"prettier@npm:3.0.3":
  version: 3.0.3
  resolution: "prettier@npm:3.0.3"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10c0/f950887bc03c5b970d8c6dd129364acfbbc61e7b46aec5d5ce17f4adf6404e2ef43072c98b51c4786e0eaca949b307d362a773fd47502862d754b5a328fa2b26
  languageName: node
  linkType: hard

"prettier@npm:^3.3.3":
  version: 3.4.2
  resolution: "prettier@npm:3.4.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10c0/99e076a26ed0aba4ebc043880d0f08bbb8c59a4c6641cdee6cdadf2205bdd87aa1d7823f50c3aea41e015e99878d37c58d7b5f0e663bba0ef047f94e36b96446
  languageName: node
  linkType: hard

"proc-log@npm:^4.1.0, proc-log@npm:^4.2.0":
  version: 4.2.0
  resolution: "proc-log@npm:4.2.0"
  checksum: 10c0/17db4757c2a5c44c1e545170e6c70a26f7de58feb985091fb1763f5081cab3d01b181fb2dd240c9f4a4255a1d9227d163d5771b7e69c9e49a561692db865efb9
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10c0/bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"promise@npm:^8.0.0":
  version: 8.3.0
  resolution: "promise@npm:8.3.0"
  dependencies:
    asap: "npm:~2.0.6"
  checksum: 10c0/6fccae27a10bcce7442daf090279968086edd2e3f6cebe054b71816403e2526553edf510d13088a4d0f14d7dfa9b9dfb188cab72d6f942e186a4353b6a29c8bf
  languageName: node
  linkType: hard

"protobufjs@npm:^6.10.2":
  version: 6.11.4
  resolution: "protobufjs@npm:6.11.4"
  dependencies:
    "@protobufjs/aspromise": "npm:^1.1.2"
    "@protobufjs/base64": "npm:^1.1.2"
    "@protobufjs/codegen": "npm:^2.0.4"
    "@protobufjs/eventemitter": "npm:^1.1.0"
    "@protobufjs/fetch": "npm:^1.1.0"
    "@protobufjs/float": "npm:^1.0.2"
    "@protobufjs/inquire": "npm:^1.1.0"
    "@protobufjs/path": "npm:^1.1.2"
    "@protobufjs/pool": "npm:^1.1.0"
    "@protobufjs/utf8": "npm:^1.1.0"
    "@types/long": "npm:^4.0.1"
    "@types/node": "npm:>=13.7.0"
    long: "npm:^4.0.0"
  bin:
    pbjs: bin/pbjs
    pbts: bin/pbts
  checksum: 10c0/c244d7b9b6d3258193da5c0d1e558dfb47f208ae345e209f90ec45c9dca911b90fa17e937892a9a39a4136ab9886981aae9efdf6039f7baff4f7225f5eeb9812
  languageName: node
  linkType: hard

"pump@npm:^1.0.0":
  version: 1.0.3
  resolution: "pump@npm:1.0.3"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10c0/fa8c1ef9c82d596872446ba0aae5a1b01cb51fcf876b8a93edd7b8abd03e0dc5ffe2683a939c2db142aa587f86dec69028c49a043ef37145e8639859bb57401e
  languageName: node
  linkType: hard

"punycode@npm:^1.3.2":
  version: 1.4.1
  resolution: "punycode@npm:1.4.1"
  checksum: 10c0/354b743320518aef36f77013be6e15da4db24c2b4f62c5f1eb0529a6ed02fbaf1cb52925785f6ab85a962f2b590d9cd5ad730b70da72b5f180e2556b8bd3ca08
  languageName: node
  linkType: hard

"pvtsutils@npm:^1.3.2, pvtsutils@npm:^1.3.5":
  version: 1.3.5
  resolution: "pvtsutils@npm:1.3.5"
  dependencies:
    tslib: "npm:^2.6.1"
  checksum: 10c0/d425aed316907e0b447a459bfb97c55d22270c3cfdba5a07ec90da0737b0e40f4f1771a444636f85bb6a453de90ff8c6b5f4f6ddba7597977166af49974b4534
  languageName: node
  linkType: hard

"pvutils@npm:^1.1.3":
  version: 1.1.3
  resolution: "pvutils@npm:1.1.3"
  checksum: 10c0/23489e6b3c76b6afb6964a20f891d6bef092939f401c78bba186b2bfcdc7a13904a0af0a78f7933346510f8c1228d5ab02d3c80e968fd84d3c76ff98d8ec9aac
  languageName: node
  linkType: hard

"qs@npm:^6.4.0":
  version: 6.12.1
  resolution: "qs@npm:6.12.1"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10c0/439e6d7c6583e7c69f2cab2c39c55b97db7ce576e4c7c469082b938b7fc8746e8d547baacb69b4cd2b6666484776c3f4840ad7163a4c5326300b0afa0acdd84b
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 10c0/50395efda7a8c94f5dffab564f9ff89736064d32addf0cc7e8bf5e4166f09f8ded7a0849ca6c2d2a59478f7d90f78f20d8048bca3cdf8be09d8e8a10790388f3
  languageName: node
  linkType: hard

"react-native-fetch-api@npm:^3.0.0":
  version: 3.0.0
  resolution: "react-native-fetch-api@npm:3.0.0"
  dependencies:
    p-defer: "npm:^3.0.0"
  checksum: 10c0/324071bb7535ac80006ba38405724350c2a82f47326f356bc6f168f95d5bfdf0aa12cd5ebb4e7766c6a23ae0871f1e9f798f757661ba5f67822ffdad18f592ac
  languageName: node
  linkType: hard

"readable-stream@npm:^2.2.2, readable-stream@npm:^2.3.0, readable-stream@npm:^2.3.5":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:~1.0.26-4":
  version: 1.0.34
  resolution: "readable-stream@npm:1.0.34"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.1"
    isarray: "npm:0.0.1"
    string_decoder: "npm:~0.10.x"
  checksum: 10c0/02272551396ed8930ddee1a088bdf0379f0f7cc47ac49ed8804e998076cb7daec9fbd2b1fd9c0490ec72e56e8bb3651abeb8080492b8e0a9c3f2158330908ed6
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"receptacle@npm:^1.3.2":
  version: 1.3.2
  resolution: "receptacle@npm:1.3.2"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10c0/213dc9e4e80969cde60c5877fae08d8438f0bf7dd10bf4ea47916a10c053ca05d6581bda374d8f22ce15e6b50739efe319d847362f5ec9e1a4cbdcbde3ddf355
  languageName: node
  linkType: hard

"redeyed@npm:~2.1.0":
  version: 2.1.1
  resolution: "redeyed@npm:2.1.1"
  dependencies:
    esprima: "npm:~4.0.0"
  checksum: 10c0/350f5e39aebab3886713a170235c38155ee64a74f0f7e629ecc0144ba33905efea30c2c3befe1fcbf0b0366e344e7bfa34e6b2502b423c9a467d32f1306ef166
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: "npm:^5.1.0"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/8051a371d6aa67ff21625fa94e2357bd81ffdc96267f3fb0fc4aaf4534028343836548ef34c240ffa8c25b280ca35eb36be00b3cb2133fa4f51896d7e73c6b4f
  languageName: node
  linkType: hard

"retimer@npm:^3.0.0":
  version: 3.0.0
  resolution: "retimer@npm:3.0.0"
  checksum: 10c0/dc3e07997c77c2b9113072bc57bcad9898a388a5cdf59a07a9fb3974f1dd8958e371c97ac93eb312b184d63d907faa5430bb5ff859a8b2d285f4db1082beb96a
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: 10c0/c19ef26e4e188f408922c46f7ff480d38e8dfc55d448310dfb518736b23ed2c4f547fb64a6ed5bdba92cd7e7ddc889d36ff78f794816d5e71498d645ef476107
  languageName: node
  linkType: hard

"rimraf@npm:^2.6.3":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: ./bin.js
  checksum: 10c0/4eef73d406c6940927479a3a9dee551e14a54faf54b31ef861250ac815172bade86cc6f7d64a4dc5e98b65e4b18a2e1c9ff3b68d296be0c748413f092bb0dd40
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"ripemd160@npm:^2.0.0, ripemd160@npm:^2.0.1":
  version: 2.0.2
  resolution: "ripemd160@npm:2.0.2"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
  checksum: 10c0/f6f0df78817e78287c766687aed4d5accbebc308a8e7e673fb085b9977473c1f139f0c5335d353f172a915bb288098430755d2ad3c4f30612f4dd0c901cd2c3a
  languageName: node
  linkType: hard

"rlp@npm:^2.2.4":
  version: 2.2.7
  resolution: "rlp@npm:2.2.7"
  dependencies:
    bn.js: "npm:^5.2.0"
  bin:
    rlp: bin/rlp
  checksum: 10c0/166c449f4bc794d47f8e337bf0ffbcfdb26c33109030aac4b6e5a33a91fa85783f2290addeb7b3c89d6d9b90c8276e719494d193129bed0a60a2d4a6fd658277
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:^5.1.1, safe-buffer@npm:^5.1.2, safe-buffer@npm:^5.2.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"scrypt-js@npm:^3.0.0":
  version: 3.0.1
  resolution: "scrypt-js@npm:3.0.1"
  checksum: 10c0/e2941e1c8b5c84c7f3732b0153fee624f5329fc4e772a06270ee337d4d2df4174b8abb5e6ad53804a29f53890ecbc78f3775a319323568c0313040c0e55f5b10
  languageName: node
  linkType: hard

"secp256k1@npm:^4.0.1":
  version: 4.0.3
  resolution: "secp256k1@npm:4.0.3"
  dependencies:
    elliptic: "npm:^6.5.4"
    node-addon-api: "npm:^2.0.0"
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.2.0"
  checksum: 10c0/de0a0e525a6f8eb2daf199b338f0797dbfe5392874285a145bb005a72cabacb9d42c0197d0de129a1a0f6094d2cc4504d1f87acb6a8bbfb7770d4293f252c401
  languageName: node
  linkType: hard

"semver@npm:7.3.5":
  version: 7.3.5
  resolution: "semver@npm:7.3.5"
  dependencies:
    lru-cache: "npm:^6.0.0"
  bin:
    semver: bin/semver.js
  checksum: 10c0/d450455b2601396dbc7d9f058a6709b1c0b99d74a911f9436c77887600ffcdb5f63d5adffa0c3b8f0092937d8a41cc61c6437bcba375ef4151cb1335ebe4f1f9
  languageName: node
  linkType: hard

"semver@npm:7.4.0":
  version: 7.4.0
  resolution: "semver@npm:7.4.0"
  dependencies:
    lru-cache: "npm:^6.0.0"
  bin:
    semver: bin/semver.js
  checksum: 10c0/6bcdd77e3f8fd5a0463358ede593f5b88fb9cf6d1c5f185fb5e1d0e516993cd1ec99b628e300935650d2af39decc3bb67b3101627340de6c39043ea96dfcbd61
  languageName: node
  linkType: hard

"semver@npm:^7.3.5":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 10c0/88f33e148b210c153873cb08cfe1e281d518aaa9a666d4d148add6560db5cd3c582f3a08ccb91f38d5f379ead256da9931234ed122057f40bb5766e65e58adaf
  languageName: node
  linkType: hard

"semver@npm:^7.3.7":
  version: 7.6.2
  resolution: "semver@npm:7.6.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/97d3441e97ace8be4b1976433d1c32658f6afaff09f143e52c593bae7eef33de19e3e369c88bd985ce1042c6f441c80c6803078d1de2a9988080b66684cbb30c
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.1":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10c0/5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.0, sha.js@npm:^2.4.8":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  bin:
    sha.js: ./bin.js
  checksum: 10c0/b7a371bca8821c9cc98a0aeff67444a03d48d745cb103f17228b96793f455f0eb0a691941b89ea1e60f6359207e36081d9be193252b0f128e0daf9cfea2815a5
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6":
  version: 1.0.6
  resolution: "side-channel@npm:1.0.6"
  dependencies:
    call-bind: "npm:^1.0.7"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.4"
    object-inspect: "npm:^1.13.1"
  checksum: 10c0/d2afd163dc733cc0a39aa6f7e39bf0c436293510dbccbff446733daeaf295857dbccf94297092ec8c53e2503acac30f0b78830876f0485991d62a90e9cad305f
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    astral-regex: "npm:^2.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
  checksum: 10c0/6c25678db1270d4793e0327620f1e0f9f5bea4630123f51e9e399191bc52c87d6e6de53ed33538609e5eacbd1fab769fae00f3705d08d029f02102a540648918
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.4
  resolution: "socks-proxy-agent@npm:8.0.4"
  dependencies:
    agent-base: "npm:^7.1.1"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/345593bb21b95b0508e63e703c84da11549f0a2657d6b4e3ee3612c312cb3a907eac10e53b23ede3557c6601d63252103494caa306b66560f43af7b98f53957a
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/d54a52bf9325165770b674a67241143a3d8b4e4c8884560c4e0e078aace2a728dffc7f70150660f51b85797c4e1a3b82f9b7aa25e0a0ceae1a243365da5c51a7
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"split-ca@npm:^1.0.0":
  version: 1.0.1
  resolution: "split-ca@npm:1.0.1"
  checksum: 10c0/f339170b84c6b4706fcf4c60cc84acb36574c0447566bd713301a8d9b4feff7f4627efc8c334bec24944a3e2f35bc596bd58c673c9980d6bfe3137aae1116ba7
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.6
  resolution: "ssri@npm:10.0.6"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/e5a1e23a4057a86a97971465418f22ea89bd439ac36ade88812dd920e4e61873e8abd6a9b72a03a67ef50faa00a2daf1ab745c5a15b46d03e0544a0296354227
  languageName: node
  linkType: hard

"stream-to-it@npm:^0.2.2":
  version: 0.2.4
  resolution: "stream-to-it@npm:0.2.4"
  dependencies:
    get-iterator: "npm:^1.0.2"
  checksum: 10c0/3d40440a6c73a964e3e6070daabf8f4313d8d519e7ddff45dec7f0e0a0f3df048017510c0306a1e8da26d22e5b033164be79d849c6716fb2ebce4b7893449255
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 10c0/fbd9aecc2621364384d157f7e59426f4bfd385e8b424b5aaa79c83a6f5a1c8fd2e4e3289e95de1eb3511cb96bb333d6281a9919fafce760e4edb35b2cd2facab
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.0.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"string_decoder@npm:~0.10.x":
  version: 0.10.31
  resolution: "string_decoder@npm:0.10.31"
  checksum: 10c0/1c628d78f974aa7539c496029f48e7019acc32487fc695464f9d6bdfec98edd7d933a06b3216bc2016918f6e75074c611d84430a53cb0e43071597d6c1ac5e25
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10c0/b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^5.2.0":
  version: 5.2.0
  resolution: "strip-ansi@npm:5.2.0"
  dependencies:
    ansi-regex: "npm:^4.1.0"
  checksum: 10c0/de4658c8a097ce3b15955bc6008f67c0790f85748bdc025b7bc8c52c7aee94bc4f9e50624516150ed173c3db72d851826cd57e7a85fe4e4bb6dbbebd5d297fdf
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10c0/bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-hex-prefix@npm:1.0.0":
  version: 1.0.0
  resolution: "strip-hex-prefix@npm:1.0.0"
  dependencies:
    is-hex-prefixed: "npm:1.0.0"
  checksum: 10c0/ec9a48c334c2ba4afff2e8efebb42c3ab5439f0e1ec2b8525e184eabef7fecade7aee444af802b1be55d2df6da5b58c55166c32f8461cc7559b401137ad51851
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10c0/6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.1.1":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^2.2.0":
  version: 2.3.0
  resolution: "supports-hyperlinks@npm:2.3.0"
  dependencies:
    has-flag: "npm:^4.0.0"
    supports-color: "npm:^7.0.0"
  checksum: 10c0/4057f0d86afb056cd799602f72d575b8fdd79001c5894bcb691176f14e870a687e7981e50bc1484980e8b688c6d5bcd4931e1609816abb5a7dc1486b7babf6a1
  languageName: node
  linkType: hard

"sync-request@npm:6.1.0":
  version: 6.1.0
  resolution: "sync-request@npm:6.1.0"
  dependencies:
    http-response-object: "npm:^3.0.1"
    sync-rpc: "npm:^1.2.1"
    then-request: "npm:^6.0.0"
  checksum: 10c0/02b31c5d543933ce8cc2cdfa7dd7b278e2645eb54299d56f3bc9c778de3130301370f25d54ecc3f6b8b2c7bfb034daabd2b866e0c18badbde26404513212c1f5
  languageName: node
  linkType: hard

"sync-rpc@npm:^1.2.1":
  version: 1.3.6
  resolution: "sync-rpc@npm:1.3.6"
  dependencies:
    get-port: "npm:^3.1.0"
  checksum: 10c0/2abaa0e6482fe8b72e29af1f7d5f484fac5a8ea0132969bf370f59b044c4f2eb109f95b222cb06e037f89b42b374a2918e5f90aff5fb7cf3e146d8088c56f6db
  languageName: node
  linkType: hard

"tar-fs@npm:~1.16.3":
  version: 1.16.3
  resolution: "tar-fs@npm:1.16.3"
  dependencies:
    chownr: "npm:^1.0.1"
    mkdirp: "npm:^0.5.1"
    pump: "npm:^1.0.0"
    tar-stream: "npm:^1.1.2"
  checksum: 10c0/d7e63dc1727fe2628b896df6d3dbfeaef599416f899b22c3f2e025c8a1ccc229b130c3750b29ce6826f99140a0d5831205989e34992756a052e727e522065285
  languageName: node
  linkType: hard

"tar-stream@npm:^1.1.2":
  version: 1.6.2
  resolution: "tar-stream@npm:1.6.2"
  dependencies:
    bl: "npm:^1.0.0"
    buffer-alloc: "npm:^1.2.0"
    end-of-stream: "npm:^1.0.0"
    fs-constants: "npm:^1.0.0"
    readable-stream: "npm:^2.3.0"
    to-buffer: "npm:^1.1.1"
    xtend: "npm:^4.0.0"
  checksum: 10c0/ab8528d2cc9ccd0906d1ce6d8089030b2c92a578c57645ff4971452c8c5388b34c7152c04ed64b8510d22a66ffaf0fee58bada7d6ab41ad1e816e31993d59cf3
  languageName: node
  linkType: hard

"tar@npm:^6.1.0, tar@npm:^6.1.11, tar@npm:^6.2.1":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 10c0/a5eca3eb50bc11552d453488344e6507156b9193efd7635e98e867fab275d527af53d8866e2370cd09dfe74378a18111622ace35af6a608e5223a7d27fe99537
  languageName: node
  linkType: hard

"then-request@npm:^6.0.0":
  version: 6.0.2
  resolution: "then-request@npm:6.0.2"
  dependencies:
    "@types/concat-stream": "npm:^1.6.0"
    "@types/form-data": "npm:0.0.33"
    "@types/node": "npm:^8.0.0"
    "@types/qs": "npm:^6.2.31"
    caseless: "npm:~0.12.0"
    concat-stream: "npm:^1.6.0"
    form-data: "npm:^2.2.0"
    http-basic: "npm:^8.1.1"
    http-response-object: "npm:^3.0.1"
    promise: "npm:^8.0.0"
    qs: "npm:^6.4.0"
  checksum: 10c0/9d2998c3470d6aa5b49993612be40627c57a89534cff5bbcc1d57f18457c14675cf3f59310816a1f85fdd40fa66feb64c63c5b76fb2163221f57223609c47949
  languageName: node
  linkType: hard

"through@npm:>=2.2.7 <3":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: 10c0/4b09f3774099de0d4df26d95c5821a62faee32c7e96fb1f4ebd54a2d7c11c57fe88b0a0d49cf375de5fee5ae6bf4eb56dbbf29d07366864e2ee805349970d3cc
  languageName: node
  linkType: hard

"timeout-abort-controller@npm:^2.0.0":
  version: 2.0.0
  resolution: "timeout-abort-controller@npm:2.0.0"
  dependencies:
    abort-controller: "npm:^3.0.0"
    native-abort-controller: "npm:^1.0.4"
    retimer: "npm:^3.0.0"
  checksum: 10c0/0f5ef9676ab1da4219f12bbace9663e9f6d6c1ebab0f136656b59fc2e14987e60869b00cf5cbf1ba51bb4d8cf974c5be056810ffb241ef27555f363e89181219
  languageName: node
  linkType: hard

"tmp-promise@npm:3.0.3":
  version: 3.0.3
  resolution: "tmp-promise@npm:3.0.3"
  dependencies:
    tmp: "npm:^0.2.0"
  checksum: 10c0/23b47dcb2e82b14bbd8f61ed7a9d9353cdb6a6f09d7716616cfd27d0087040cd40152965a518e598d7aabe1489b9569bf1eebde0c5fadeaf3ec8098adcebea4e
  languageName: node
  linkType: hard

"tmp@npm:^0.2.0":
  version: 0.2.3
  resolution: "tmp@npm:0.2.3"
  checksum: 10c0/3e809d9c2f46817475b452725c2aaa5d11985cf18d32a7a970ff25b568438e2c076c2e8609224feef3b7923fa9749b74428e3e634f6b8e520c534eef2fd24125
  languageName: node
  linkType: hard

"to-buffer@npm:^1.1.1":
  version: 1.1.1
  resolution: "to-buffer@npm:1.1.1"
  checksum: 10c0/fb9fc6a0103f2b06e2e01c3d291586d0148759d5584f35d0973376434d1b58bd6ee5df9273f0bb1190eb2a5747c894bf49fed571325a7ac10208a48f31736439
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"ts-node@npm:^10.9.1":
  version: 10.9.2
  resolution: "ts-node@npm:10.9.2"
  dependencies:
    "@cspotcode/source-map-support": "npm:^0.8.0"
    "@tsconfig/node10": "npm:^1.0.7"
    "@tsconfig/node12": "npm:^1.0.7"
    "@tsconfig/node14": "npm:^1.0.0"
    "@tsconfig/node16": "npm:^1.0.2"
    acorn: "npm:^8.4.1"
    acorn-walk: "npm:^8.1.1"
    arg: "npm:^4.1.0"
    create-require: "npm:^1.1.0"
    diff: "npm:^4.0.1"
    make-error: "npm:^1.1.1"
    v8-compile-cache-lib: "npm:^3.0.1"
    yn: "npm:3.1.1"
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: 10c0/5f29938489f96982a25ba650b64218e83a3357d76f7bede80195c65ab44ad279c8357264639b7abdd5d7e75fc269a83daa0e9c62fd8637a3def67254ecc9ddc2
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.3.1, tslib@npm:^2.4.0, tslib@npm:^2.5.0, tslib@npm:^2.6.1, tslib@npm:^2.6.2":
  version: 2.6.3
  resolution: "tslib@npm:2.6.3"
  checksum: 10c0/2598aef53d9dbe711af75522464b2104724d6467b26a60f2bdac8297d2b5f1f6b86a71f61717384aa8fd897240467aaa7bcc36a0700a0faf751293d1331db39a
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10c0/902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"typedarray@npm:^0.0.6":
  version: 0.0.6
  resolution: "typedarray@npm:0.0.6"
  checksum: 10c0/6005cb31df50eef8b1f3c780eb71a17925f3038a100d82f9406ac2ad1de5eb59f8e6decbdc145b3a1f8e5836e17b0c0002fb698b9fe2516b8f9f9ff602d36412
  languageName: node
  linkType: hard

"uint8arrays@npm:^3.0.0":
  version: 3.1.1
  resolution: "uint8arrays@npm:3.1.1"
  dependencies:
    multiformats: "npm:^9.4.2"
  checksum: 10c0/9946668e04f29b46bbb73cca3d190f63a2fbfe5452f8e6551ef4257d9d597b72da48fa895c15ef2ef772808a5335b3305f69da5f13a09f8c2924896b409565ff
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 10c0/bb673d7876c2d411b6eb6c560e0c571eef4a01c1c19925175d16e3a30c4c428181fb8d7ae802a261f283e4166a0ac435e2f505743aa9e45d893f9a3df017b501
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: 10c0/68e659a98898d6a836a9a59e6adf14a5d799707f5ea629433e025ac90d239f75e408e2e5ff086afc3cace26f8b26ee52155293564593fbb4a2f666af57fc59bf
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: "npm:^4.0.0"
  checksum: 10c0/6363e40b2fa758eb5ec5e21b3c7fb83e5da8dcfbd866cc0c199d5534c42f03b9ea9ab069769cc388e1d7ab93b4eeef28ef506ab5f18d910ef29617715101884f
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/cb811d9d54eb5821b81b18205750be84cb015c20a4a44280794e915f5a0a70223ce39066781a354e872df3572e8155c228f43ff0cce94c7cbf4da2cc7cbdd635
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"urlpattern-polyfill@npm:^8.0.0":
  version: 8.0.2
  resolution: "urlpattern-polyfill@npm:8.0.2"
  checksum: 10c0/5388bbe8459dbd8861ee7cb97904be915dd863a9789c2191c528056f16adad7836ec22762ed002fed44e8995d0f98bdfb75a606466b77233e70d0f61b969aaf9
  languageName: node
  linkType: hard

"utf8@npm:3.0.0":
  version: 3.0.0
  resolution: "utf8@npm:3.0.0"
  checksum: 10c0/675d008bab65fc463ce718d5cae8fd4c063540f269e4f25afebce643098439d53e7164bb1f193e0c3852825c7e3e32fbd8641163d19a618dbb53f1f09acb0d5a
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/bcbb807a917d374a49f475fae2e87fdca7da5e5530820ef53f65ba1d12131bd81a92ecf259cc7ce317cbe0f289e7d79fdfebcef9bfa3087c8c8a2fa304c9be54
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.1":
  version: 3.0.1
  resolution: "v8-compile-cache-lib@npm:3.0.1"
  checksum: 10c0/bdc36fb8095d3b41df197f5fb6f11e3a26adf4059df3213e3baa93810d8f0cc76f9a74aaefc18b73e91fe7e19154ed6f134eda6fded2e0f1c8d2272ed2d2d391
  languageName: node
  linkType: hard

"varint@npm:^6.0.0":
  version: 6.0.0
  resolution: "varint@npm:6.0.0"
  checksum: 10c0/737fc37088a62ed3bd21466e318d21ca7ac4991d0f25546f518f017703be4ed0f9df1c5559f1dd533dddba4435a1b758fd9230e4772c1a930ef72b42f5c750fd
  languageName: node
  linkType: hard

"wabt@npm:1.0.24":
  version: 1.0.24
  resolution: "wabt@npm:1.0.24"
  bin:
    wasm-decompile: bin/wasm-decompile
    wasm-interp: bin/wasm-interp
    wasm-objdump: bin/wasm-objdump
    wasm-opcodecnt: bin/wasm-opcodecnt
    wasm-strip: bin/wasm-strip
    wasm-validate: bin/wasm-validate
    wasm2c: bin/wasm2c
    wasm2wat: bin/wasm2wat
    wat2wasm: bin/wat2wasm
  checksum: 10c0/d3fa20e3c783a3f0d0c293ffb98fab9047ee941cb00ba71922802d2acc8a1f8c5d133fb0e966f21ebc92a72ae5cb4b65b986566de0b1eac184e14fba6a5b0cdc
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: "npm:^1.0.3"
  checksum: 10c0/5b61ca583a95e2dd85d7078400190efd452e05751a64accb8c06ce4db65d7e0b0cde9917d705e826a2e05cc2548f61efde115ffa374c3e436d04be45c889e5b4
  languageName: node
  linkType: hard

"web-streams-polyfill@npm:^3.2.1":
  version: 3.3.3
  resolution: "web-streams-polyfill@npm:3.3.3"
  checksum: 10c0/64e855c47f6c8330b5436147db1c75cb7e7474d924166800e8e2aab5eb6c76aac4981a84261dd2982b3e754490900b99791c80ae1407a9fa0dcff74f82ea3a7f
  languageName: node
  linkType: hard

"web3-eth-abi@npm:1.7.0":
  version: 1.7.0
  resolution: "web3-eth-abi@npm:1.7.0"
  dependencies:
    "@ethersproject/abi": "npm:5.0.7"
    web3-utils: "npm:1.7.0"
  checksum: 10c0/e7803e72bad829b009c848a8c1e0ce762cbf983a43cdb09135b3aba98d0418d470a0338ff41a2e818fb0caf2234e38581a97d514f2719740d351129f024c4ac3
  languageName: node
  linkType: hard

"web3-utils@npm:1.7.0":
  version: 1.7.0
  resolution: "web3-utils@npm:1.7.0"
  dependencies:
    bn.js: "npm:^4.11.9"
    ethereum-bloom-filters: "npm:^1.0.6"
    ethereumjs-util: "npm:^7.1.0"
    ethjs-unit: "npm:0.1.6"
    number-to-bn: "npm:1.7.0"
    randombytes: "npm:^2.1.0"
    utf8: "npm:3.0.0"
  checksum: 10c0/524f058028c706c96ccb62c80fce99fd3fef30f60e4b750a08a09d7f529df0a288d2859d242991ef9581a6ce8ff0dffbe55a579c1d586d51524f424e74df424d
  languageName: node
  linkType: hard

"webcrypto-core@npm:^1.8.0":
  version: 1.8.0
  resolution: "webcrypto-core@npm:1.8.0"
  dependencies:
    "@peculiar/asn1-schema": "npm:^2.3.8"
    "@peculiar/json-schema": "npm:^1.1.12"
    asn1js: "npm:^3.0.1"
    pvtsutils: "npm:^1.3.5"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d4158af402500eb26d0de6e088baa0fbef41c43a3e3b5f53b8326c8c517e55037b3d8a17672cf48bdccfd13526599857544ea8485e2172bb14c9ee4561d706a5
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which@npm:2.0.2, which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^4.0.0":
  version: 4.0.0
  resolution: "which@npm:4.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/449fa5c44ed120ccecfe18c433296a4978a7583bf2391c50abce13f76878d2476defde04d0f79db8165bdf432853c1f8389d0485ca6e8ebce3bbcded513d5e6a
  languageName: node
  linkType: hard

"widest-line@npm:^3.1.0":
  version: 3.1.0
  resolution: "widest-line@npm:3.1.0"
  dependencies:
    string-width: "npm:^4.0.0"
  checksum: 10c0/b1e623adcfb9df35350dd7fc61295d6d4a1eaa65a406ba39c4b8360045b614af95ad10e05abf704936ed022569be438c4bfa02d6d031863c4166a238c301119f
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 10c0/7ed2e44f3c33c5c3e3771134d2b0aee4314c9e49c749e37f464bf69f2bcdf0cbf9419ca638098e2717cff4875c47f56a007532f6111c3319f557a2ca91278e92
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"ws@npm:^7.4.5":
  version: 7.5.9
  resolution: "ws@npm:7.5.9"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/aec4ef4eb65821a7dde7b44790f8699cfafb7978c9b080f6d7a98a7f8fc0ce674c027073a78574c94786ba7112cc90fa2cc94fc224ceba4d4b1030cff9662494
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yaml@npm:1.10.2, yaml@npm:^1.10.0, yaml@npm:^1.10.2":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: 10c0/5c28b9eb7adc46544f28d9a8d20c5b3cb1215a886609a2fd41f51628d8aaa5878ccd628b755dbcd29f6bb4921bd04ffbc6dcc370689bb96e594e2f9813d2605f
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.0.0":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 10c0/0732468dd7622ed8a274f640f191f3eaf1f39d5349a1b72836df484998d7d9807fbea094e2f5486d6b0cd2414aad5775972df0e68f8604db89a239f0f4bf7443
  languageName: node
  linkType: hard
