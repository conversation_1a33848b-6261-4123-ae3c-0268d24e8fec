specVersion: 0.0.8
schema:
  file: ./schema.graphql
dataSources:
  - name: Factory
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: Factory
      address: "{{Factory.address}}"
      startBlock: {{Factory.block}}
    mapping:
      file: ./src/mappings/Factory.ts
      language: wasm/assemblyscript
      kind: ethereum/events
      apiVersion: 0.0.7
      entities:
        - Global
        - DenManager
        - Token
        - PriceFeed
        - Vault
      abis:
        - name: Factory
          file: ./abis/Factory.json
        - name: ERC20
          file: ./abis/ERC20.json
        - name: PriceFeed
          file: ./abis/PriceFeed.json
        - name: StabilityPool
          file: ./abis/StabilityPool.json
        - name: LSPGetter
          file: ./abis/LSPGetter.json
        - name: CollVault
          file: ./abis/CollVault.json
        - name: CollVaultRouter
          file: ./abis/CollVaultRouter.json
      eventHandlers:
        - event: NewDeployment(address,address,address,address)
          handler: handleDenManagerDeployed
  - name: LiquidationManager
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: LiquidationManager
      address: "{{LiquidationManager.address}}"
      startBlock: {{LiquidationManager.block}}
    mapping:
      file: ./src/mappings/LiquidationManager.ts
      language: wasm/assemblyscript
      kind: ethereum/events
      apiVersion: 0.0.7
      entities:
        - Global
        - DenManager
        - Den
        - DenChange
        - Token
        - Liquidation
        - Redemption
      abis:
        - name: LiquidationManager
          file: ./abis/LiquidationManager.json
        - name: ERC20
          file: ./abis/ERC20.json
        - name: StabilityPool
          file: ./abis/StabilityPool.json
        - name: LSPGetter
          file: ./abis/LSPGetter.json
      eventHandlers:
        - event: DenUpdated(indexed address,indexed address,uint256,uint256,uint256,uint8)
          handler: handleDenUpdated
        - event: Liquidation(indexed address,uint256,uint256,uint256,uint256)
          handler: handleLiquidation
        - event: DenLiquidated(indexed address,indexed address,uint256,uint256,uint8)
          handler: handleDenLiquidated
  - name: BorrowerOperations
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: BorrowerOperations
      address: "{{BorrowerOperations.address}}"
      startBlock: {{BorrowerOperations.block}}
    mapping:
      file: ./src/mappings/BorrowerOperations.ts
      language: wasm/assemblyscript
      kind: ethereum/events
      apiVersion: 0.0.7
      entities:
        - Global
        - DenManager
        - Den
        - DenChange
        - Token
        - Liquidation
        - Redemption
        - Vault
      abis:
        - name: BorrowerOperations
          file: ./abis/BorrowerOperations.json
        - name: StabilityPool
          file: ./abis/StabilityPool.json
        - name: LSPGetter
          file: ./abis/LSPGetter.json
        - name: CollVault
          file: ./abis/CollVault.json
        - name: CollVaultRouter
          file: ./abis/CollVaultRouter.json
        - name: ERC20
          file: ./abis/ERC20.json
          
      eventHandlers:
        - event: DenCreated(indexed address,indexed address,uint256)
          handler: handleDenCreated
        - event: DenUpdated(indexed address,indexed address,uint256,uint256,uint256,uint8)
          handler: handleDenUpdated
        - event: BorrowingFeePaid(indexed address,indexed address,uint256)
          handler: handleBorrowingFeePaid
        - event: DenManagerRemoved(address)
          handler: handleDenManagerRemoved
  - name: StabilityPool
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: StabilityPool
      address: "{{StabilityPool.address}}"
      startBlock: {{StabilityPool.block}}
    mapping:
      file: ./src/mappings/StabilityPool.ts
      language: wasm/assemblyscript
      kind: ethereum/events
      apiVersion: 0.0.7
      entities:
        - Global
        - DenManager
        - Den
        - DenChange
        - Token
        - Liquidation
        - Redemption
        - StabilityPool
        - StabilityDeposit
        - StabilityDepositChange
      abis:
        - name: StabilityPool
          file: ./abis/StabilityPool.json
        - name: LSPGetter
          file: ./abis/LSPGetter.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Deposit(indexed address,indexed address,uint256,uint256)
          handler: handleUserDeposit
        - event: Withdraw(indexed address,indexed address,indexed address,uint256,uint256)
          handler: handleUserWithdraw
        - event: Offset(address,uint256,uint256,uint256)
          handler: handleOffset
        - event: Rebalance(indexed address,indexed address,uint256,uint256,uint256,uint256)
          handler: handleRebalance


    context:
      lspGetterAddress: 
        type: String
        data: "{{LSPGetter.address}}"
      lspGetterStartBlock:
        type: String
        data: "{{LSPGetter.block}}"
  - name: PriceFeed
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: PriceFeed
      address: "{{PriceFeed.address}}"
      startBlock: {{PriceFeed.block}}
    mapping:
      file: ./src/mappings/PriceFeed.ts
      language: wasm/assemblyscript
      kind: ethereum/events
      apiVersion: 0.0.7
      entities:
        - Global
        - DenManager
        - Den
        - DenChange
        - Token
        - Liquidation
        - Redemption
        - StabilityPool
        - StabilityDeposit
        - StabilityDepositChange
        - Vault
        - VaultPosition
        - VaultPositionChange
      abis:
        - name: PriceFeed
          file: ./abis/PriceFeed.json
        - name: ERC20
          file: ./abis/ERC20.json
        - name: StabilityPool
          file: ./abis/StabilityPool.json
        - name: LSPGetter
          file: ./abis/LSPGetter.json
        - name: BorrowerOperations
          file: ./abis/BorrowerOperations.json
        - name: CollVault
          file: ./abis/CollVault.json
        - name: CollVaultRouter
          file: ./abis/CollVaultRouter.json
      eventHandlers:
        - event: NewOracleRegistered(address,address,address)
          handler: handleNewOracleRegistered
        - event: NewCollVaultRegistered(address,bool)
          handler: handleNewCollVaultRegistered
        - event: NewSpotOracleRegistered(address,address)
          handler: handleNewSpotOracleRegistered
      blockHandlers:
        - handler: handleBlockWithCallToContract
          filter:
            kind: polling
            # 450 blocks = 2 * 450 = 900 seconds (15 mins)
            every: 450
  - name: CollateralVault-HONEY-WBERA
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-HONEY-WBERA.address}}"
      startBlock: {{Vault-HONEY-WBERA.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-WBERA-WBTC
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-WBERA-WBTC.address}}"
      startBlock: {{Vault-WBERA-WBTC.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-WBERA-WETH
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-WBERA-WETH.address}}"
      startBlock: {{Vault-WBERA-WETH.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-BYUSD-HONEY
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-BYUSD-HONEY.address}}"
      startBlock: {{Vault-BYUSD-HONEY.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized


  - name: CollateralVault-HONEY-USDCe
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-HONEY-USDCe.address}}"
      startBlock: {{Vault-HONEY-USDCe.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-iBGT
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-iBGT.address}}"
      startBlock: {{Vault-iBGT.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-NAV-WBERA
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-NAV-WBERA.address}}"
      startBlock: {{Vault-NAV-WBERA.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-bm-WBERA
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-bm-WBERA.address}}"
      startBlock: {{Vault-bm-WBERA.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-YEET-WBERA
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-YEET-WBERA.address}}"
      startBlock: {{Vault-YEET-WBERA.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-USDbr-HONEY
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-USDbr-HONEY.address}}"
      startBlock: {{Vault-USDbr-HONEY.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-WBERA-RAMEN
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-WBERA-RAMEN.address}}"
      startBlock: {{Vault-WBERA-RAMEN.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-OHM-WBERA
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-OHM-WBERA.address}}"
      startBlock: {{Vault-OHM-WBERA.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-HONEY-BERA
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-HONEY-BERA.address}}"
      startBlock: {{Vault-HONEY-BERA.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-WBTC-uniBTC
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-WBTC-uniBTC.address}}"
      startBlock: {{Vault-WBTC-uniBTC.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-WBTC-WBERA
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-WBTC-WBERA.address}}"
      startBlock: {{Vault-WBTC-WBERA.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-WBERA-LBGT
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-WBERA-LBGT.address}}"
      startBlock: {{Vault-WBERA-LBGT.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-WBERA-DINERO
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-WBERA-DINERO.address}}"
      startBlock: {{Vault-WBERA-DINERO.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-WETH-beraeth
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-WETH-beraeth.address}}"
      startBlock: {{Vault-WETH-beraeth.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVault-RUSD-HONEY
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
      address: "{{Vault-RUSD-HONEY.address}}"
      startBlock: {{Vault-RUSD-HONEY.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Vault
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
  - name: CollateralVaultRegistry
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVaultRegistry
      address: "{{CollateralVaultRegistry.address}}"
      startBlock: {{CollateralVaultRegistry.block}}
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVaultRegistry.ts
      entities:
        - Global
        - Holding
        - Token
        - Vault
        - VaultPosition
        - VaultPositionChange
      abis:
        - name: CollVaultRegistry
          file: ./abis/CollVaultRegistry.json
        - name: CollVault
          file: ./abis/CollVault.json
        - name: CollVaultRouter
          file: ./abis/CollVaultRouter.json
        - name: ERC20
          file: ./abis/ERC20.json
        - name: StabilityPool
          file: ./abis/StabilityPool.json
        - name: LSPGetter
          file: ./abis/LSPGetter.json
      eventHandlers:
        - event: NewCollateralVault(indexed address,uint256,uint8)
          handler: handleNewCollateralVault
        - event: VaultModified(indexed address,uint256,uint8)
          handler: handleVaultModified
templates:
  - name: DenManager
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: DenManager
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      entities:
        - Global
        - DenManager
        - Den
        - DenChange
        - Token
        - Liquidation
        - Redemption
        - Vault
      abis:
        - name: DenManager
          file: ./abis/DenManager.json
        - name: StabilityPool
          file: ./abis/StabilityPool.json
        - name: LSPGetter
          file: ./abis/LSPGetter.json
        - name: CollVault
          file: ./abis/CollVault.json
        - name: CollVaultRouter
          file: ./abis/CollVaultRouter.json
        - name: ERC20
          file: ./abis/ERC20.json
      eventHandlers:
        - event: DenUpdated(indexed address,uint256,uint256,uint256,uint8)
          handler: handleDenUpdated
        - event: Redemption(indexed address,uint256,uint256,uint256,uint256)
          handler: handleRedemption
        - event: LTermsUpdated(uint256,uint256)
          handler: handleLTermsUpdated
        - event: TotalStakesUpdated(uint256)
          handler: handleTotalStakesUpdated
        - event: BaseRateUpdated(uint256)
          handler: handleBaseRateUpdated
        - event: NewParameters((uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256,address))
          handler: handleNewParameterSet
      file: ./src/mappings/DenManager.ts

  - name: CollateralVault
    kind: ethereum/contract
    network: {{network}}
    source:
      abi: CollVault
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      file: ./src/mappings/CollVault.ts
      entities:
        - Global
        - Holding
        - Token
        - Vault
        - VaultPosition
        - VaultPositionChange
      abis:
        - name: CollVault
          file: ./abis/CollVault.json
        - name: CollVaultRouter
          file: ./abis/CollVaultRouter.json
        - name: ERC20
          file: ./abis/ERC20.json
        - name: StabilityPool
          file: ./abis/StabilityPool.json
        - name: LSPGetter
          file: ./abis/LSPGetter.json
      eventHandlers:
        - event: Initialized(uint64)
          handler: handleInitialized
        - event: Deposit(indexed address,indexed address,uint256,uint256)
          handler: handleDeposit
        - event: Withdraw(indexed address,indexed address,indexed address,uint256,uint256)
          handler: handleWithdraw
        - event: Transfer(indexed address,indexed address,uint256)
          handler: handleTransfer
        - event: Rebalance(indexed address,uint256,uint256,uint256,uint256)
          handler: handleRebalance