<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Beraborrow</title>
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#ec6f15" />
    <link rel="icon" type="image/png" sizes="192x192" href="/android-icon-192x192.png" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="msapplication-TileColor" content="#ec6f15" />
    <meta name="theme-color" content="#ec6f15" />
    <meta name="description" content="Beraborrow unlocks instant liquidity against Berachain native assets through our stablecoin called Nectar ($NECT)." />
    <meta name="og:title" content="Beraborrow" />
    <meta name="og:url" content="https://app.beraborrow.com/" />
    <meta name="og:image" content="/beraborrowShare.png" />
    <meta name="og:image:width" content="1200" />
    <meta name="og:image:heigh" content="630" />
    <meta name="og:description" content="Beraborrow unlocks instant liquidity against Berachain native assets through our stablecoin called Nectar ($NECT)." />
    <meta name="og:type" content="website" />
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:image" content="/beraborrowShare.png" />
    <meta name="twitter:title" content="Beraborrow" />
    <meta name="twitter:description" content="Beraborrow unlocks instant liquidity against Berachain native assets through our stablecoin called Nectar ($NECT)." />
    <!-- Set Default Consent Mode Before GTM -->
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      const consentGiven = localStorage.getItem("consentMode") === "true";
      const consent = {
        ad_storage: consentGiven ? "granted" : "denied",
        analytics_storage: consentGiven ? "granted" : "denied",
        functionality_storage: "granted",
        personalization_storage: consentGiven ? "granted" : "denied",
        security_storage: "granted",
      };
      gtag("consent", "default", consent);
    </script>
  </head>
  <body style="background-color: rgba(130, 106, 95, 0.25)">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
