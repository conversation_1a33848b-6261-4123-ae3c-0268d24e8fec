# Changelog

## [2.4.0](https://github.com/Beraborrowofficial/sdk/compare/v2.3.6...v2.4.0) (2025-06-20)


### Features

* add withdraw request query for managed vaults ([8bd2dcb](https://github.com/Beraborrowofficial/sdk/commit/8bd2dcbcf666db0be6e185d7b5db5685609e2df9))
* add withdraw request query for managed vaults ([f2c1dda](https://github.com/Beraborrowofficial/sdk/commit/f2c1ddaab417eb910699ff3bf67cfa89ef5a1237))


### Bug Fixes

* enso zaps enabled ([bb15617](https://github.com/Beraborrowofficial/sdk/commit/bb156179b92a754305bc0a0ab134ba9478aba7a8))
* enso zaps enabled ([d3eff5e](https://github.com/Beraborrowofficial/sdk/commit/d3eff5e28d014b4e66ac95d2b203746607aa79a0))
* vault id in query ([16a4da1](https://github.com/Beraborrowofficial/sdk/commit/16a4da1328da5d0c2e4c5dfe4b1489e60da54529))

## [2.3.6](https://github.com/Beraborrowofficial/sdk/compare/v2.3.5...v2.3.6) (2025-06-19)


### Bug Fixes

* added maagedvault queries ([d000e25](https://github.com/Beraborrowofficial/sdk/commit/d000e2553e7a941a6dcc563626c46f313c52f75c))
* added maagedvault queries ([1a7a012](https://github.com/Beraborrowofficial/sdk/commit/1a7a012ad67f9f56393aa5178cc82622976917fb))

## [2.3.5](https://github.com/Beraborrowofficial/sdk/compare/v2.3.4...v2.3.5) (2025-06-02)


### Bug Fixes

* lp no wrapped ([#237](https://github.com/Beraborrowofficial/sdk/issues/237)) ([63a0211](https://github.com/Beraborrowofficial/sdk/commit/63a021122314c7d4b68a3612ecb50e7e2dc2a6b8))

## [2.3.4](https://github.com/Beraborrowofficial/sdk/compare/v2.3.3...v2.3.4) (2025-06-02)


### Bug Fixes

* sPollen camel case in queries ([#235](https://github.com/Beraborrowofficial/sdk/issues/235)) ([65d3f77](https://github.com/Beraborrowofficial/sdk/commit/65d3f7734e0b27222135ef50ff68cea8321a5cda))

## [2.3.3](https://github.com/Beraborrowofficial/sdk/compare/v2.3.2...v2.3.3) (2025-05-30)


### Bug Fixes

* minor improvements for the API ([c212105](https://github.com/Beraborrowofficial/sdk/commit/c212105a34b4c460a44e7a73d5c6350d24953f3b))
* minor improvements for the API ([80bce23](https://github.com/Beraborrowofficial/sdk/commit/80bce237b6d000abab536e88b46957afa31ee28c))
* users via subgraph ([34c02a5](https://github.com/Beraborrowofficial/sdk/commit/34c02a59cb67d9ebb251aaccfb4cdf4afe09c53f))

## [2.3.2](https://github.com/Beraborrowofficial/sdk/compare/v2.3.1...v2.3.2) (2025-05-28)


### Bug Fixes

* all lp protocols query ([#231](https://github.com/Beraborrowofficial/sdk/issues/231)) ([7f91969](https://github.com/Beraborrowofficial/sdk/commit/7f9196972c9ad65226ac2c77ff8d3a74ba454d03))

## [2.3.1](https://github.com/Beraborrowofficial/sdk/compare/v2.3.0...v2.3.1) (2025-05-26)


### Bug Fixes

* Updated POLLEN Staging Subgraph Link ([#229](https://github.com/Beraborrowofficial/sdk/issues/229)) ([067bdb5](https://github.com/Beraborrowofficial/sdk/commit/067bdb56c154cc208aa5983d1ce3b01d1c9c7bc7))

## [2.3.0](https://github.com/Beraborrowofficial/sdk/compare/v2.2.4...v2.3.0) (2025-05-26)


### Features

* POLLEN Staging Setup ([#227](https://github.com/Beraborrowofficial/sdk/issues/227)) ([9fd4473](https://github.com/Beraborrowofficial/sdk/commit/9fd447337b0035c482b167b0c816ff2ba8a0e381))

## [2.2.4](https://github.com/Beraborrowofficial/sdk/compare/v2.2.3...v2.2.4) (2025-05-23)


### Bug Fixes

* add collvault price ([dd05a82](https://github.com/Beraborrowofficial/sdk/commit/dd05a8281bdce134c9332ace182907621dd206c0))
* add collvault price ([a11db13](https://github.com/Beraborrowofficial/sdk/commit/a11db13865b506cc63f4e1fc08d6dbee0a26e14f))

## [2.2.3](https://github.com/Beraborrowofficial/sdk/compare/v2.2.2...v2.2.3) (2025-05-23)


### Bug Fixes

* increase query limit from 1 to 2 for efficentcy ([1565367](https://github.com/Beraborrowofficial/sdk/commit/156536753b4a96e70857e6daa78f5dc13a511041))

## [2.2.2](https://github.com/Beraborrowofficial/sdk/compare/v2.2.1...v2.2.2) (2025-05-21)


### Bug Fixes

* make collaterals great again ([b3695b3](https://github.com/Beraborrowofficial/sdk/commit/b3695b37c42d701d107fdd83d789197de6fb0100))
* make collaterals great again ([ad431c1](https://github.com/Beraborrowofficial/sdk/commit/ad431c1d2a3440b8033e1b520fa0ee7d1fafe570))

## [2.2.1](https://github.com/Beraborrowofficial/sdk/compare/v2.2.0...v2.2.1) (2025-05-21)


### Bug Fixes

* infrared points queries for den, managedVault and vault ([9fb229f](https://github.com/Beraborrowofficial/sdk/commit/9fb229f2a430f7ef4b22f0dcd0a8719f5f792974))

## [2.2.0](https://github.com/Beraborrowofficial/sdk/compare/v2.1.7...v2.2.0) (2025-05-19)


### Features

* New POLLEN Contract Changes ([#217](https://github.com/Beraborrowofficial/sdk/issues/217)) ([4a71a4d](https://github.com/Beraborrowofficial/sdk/commit/4a71a4d128a8865466704dabee30ec4a12e7498d))

## [2.1.7](https://github.com/Beraborrowofficial/sdk/compare/v2.1.6...v2.1.7) (2025-05-09)


### Bug Fixes

* merge ([0fd4673](https://github.com/Beraborrowofficial/sdk/commit/0fd4673178854a4d05c82bf3da3c537c7101d008))

## [2.1.6](https://github.com/Beraborrowofficial/sdk/compare/v2.1.5...v2.1.6) (2025-05-07)


### Bug Fixes

* correct deposit ([58eac84](https://github.com/Beraborrowofficial/sdk/commit/58eac847a20779110ce651cef3a83cd5d1478e84))

## [2.1.5](https://github.com/Beraborrowofficial/sdk/compare/v2.1.4...v2.1.5) (2025-05-07)


### Bug Fixes

* added min amount ([3969488](https://github.com/Beraborrowofficial/sdk/commit/3969488aabe1577f02145a0b33f1bd1076313f16))

## [2.1.4](https://github.com/Beraborrowofficial/sdk/compare/v2.1.3...v2.1.4) (2025-05-06)


### Bug Fixes

* added correct address ([989bdc4](https://github.com/Beraborrowofficial/sdk/commit/989bdc4c647ddd6fbda79c09b4af08c21e23f1ad))

## [2.1.3](https://github.com/Beraborrowofficial/sdk/compare/v2.1.2...v2.1.3) (2025-05-06)


### Bug Fixes

* changes ([e3f374f](https://github.com/Beraborrowofficial/sdk/commit/e3f374f2de1773a94ee8878ba1009ac6d1b873b7))

## [2.1.2](https://github.com/Beraborrowofficial/sdk/compare/v2.1.1...v2.1.2) (2025-05-06)


### Bug Fixes

* some changes ([28a1f55](https://github.com/Beraborrowofficial/sdk/commit/28a1f55e2a053903f3a89334cf4824eacabab4ff))

## [2.1.1](https://github.com/Beraborrowofficial/sdk/compare/v2.1.0...v2.1.1) (2025-05-06)


### Bug Fixes

* fix ([34d9c29](https://github.com/Beraborrowofficial/sdk/commit/34d9c29fa3f792d7fedf6f6222f616817e24a6f5))

## [2.1.0](https://github.com/Beraborrowofficial/sdk/compare/v2.0.0...v2.1.0) (2025-05-06)


### Features

* managed Vault exlcuding getters ([c03cbb8](https://github.com/Beraborrowofficial/sdk/commit/c03cbb8a320bbb3e57ed2874779a7a1d49a6eb7b))

## [2.0.0](https://github.com/Beraborrowofficial/sdk/compare/v1.7.2...v2.0.0) (2025-05-02)


### ⚠ BREAKING CHANGES

* subprotocol up with testing addresses

### Features

* subprotocol up with testing addresses ([9d8577d](https://github.com/Beraborrowofficial/sdk/commit/9d8577df9802fdce6cbbcf7ae1fac88c8cac1d6b))


### Bug Fixes

* check addresses in JSON ([0828222](https://github.com/Beraborrowofficial/sdk/commit/08282223fef568fc2d8469a9115398f972b97de2))

## [1.7.2](https://github.com/Beraborrowofficial/sdk/compare/v1.7.1...v1.7.2) (2025-04-27)


### Bug Fixes

* min asset withdrawn on dens ([3e6e789](https://github.com/Beraborrowofficial/sdk/commit/3e6e78910efe92324659f3f0d29a26f51172dea9))
* min asset withdrawn on dens ([958b8db](https://github.com/Beraborrowofficial/sdk/commit/958b8dbb1af26dc2b9ddba4eefc3c81e0c9b2173))

## [1.7.1](https://github.com/Beraborrowofficial/sdk/compare/v1.7.0...v1.7.1) (2025-04-17)


### Bug Fixes

* POLLEN calls build ([#195](https://github.com/Beraborrowofficial/sdk/issues/195)) ([dc3b57f](https://github.com/Beraborrowofficial/sdk/commit/dc3b57f1108b05ef3b3aec05984020aa70ea392c))

## [1.7.0](https://github.com/Beraborrowofficial/sdk/compare/v1.6.3...v1.7.0) (2025-04-17)


### Features

* POLLEN Calls ([#188](https://github.com/Beraborrowofficial/sdk/issues/188)) ([1d8b433](https://github.com/Beraborrowofficial/sdk/commit/1d8b433f9f489190b1276f3022f5996b7d0991ef))

## [1.6.3](https://github.com/Beraborrowofficial/sdk/compare/v1.6.2...v1.6.3) (2025-04-16)


### Bug Fixes

* added vault details ([9deb08a](https://github.com/Beraborrowofficial/sdk/commit/9deb08a648a1499b3c8fd36c43221247418f4bb3))
* apy via api ([d6d2323](https://github.com/Beraborrowofficial/sdk/commit/d6d2323ac5cefe92485d0de114a120169addc87e))

## [1.6.2](https://github.com/Beraborrowofficial/sdk/compare/v1.6.1...v1.6.2) (2025-04-15)


### Bug Fixes

* sugraph key added ([cf0072a](https://github.com/Beraborrowofficial/sdk/commit/cf0072a3bf2a8429ab8bfcc1d9576c5fccda7728))
* sugraph key added ([de301c5](https://github.com/Beraborrowofficial/sdk/commit/de301c5bd8014d5f399734eadf26ba62c7db1872))
* sugraph key added ([53c28ae](https://github.com/Beraborrowofficial/sdk/commit/53c28aeca11878a9ca8f9d84f28684e114fbb9b1))

## [1.6.1](https://github.com/Beraborrowofficial/sdk/compare/v1.6.0...v1.6.1) (2025-04-11)


### Bug Fixes

* move deployment JSON to interface ([b9211c2](https://github.com/Beraborrowofficial/sdk/commit/b9211c229126d761a7ed090435078d25e7177519))

## [1.6.0](https://github.com/Beraborrowofficial/sdk/compare/v1.5.1...v1.6.0) (2025-04-11)


### Features

* upgrade vaultPosition query for withdrawn ([85bc8bc](https://github.com/Beraborrowofficial/sdk/commit/85bc8bc67745afa14cfbd22396c5aaaa9f5ce2df))
* upgrade vaultPosition query for withdrawn ([60ee967](https://github.com/Beraborrowofficial/sdk/commit/60ee967829b35dd39c42bb2beb8742acb6edde46))

## [1.5.1](https://github.com/Beraborrowofficial/sdk/compare/v1.5.0...v1.5.1) (2025-04-11)


### Bug Fixes

* solv and btc vualts ([ab92481](https://github.com/Beraborrowofficial/sdk/commit/ab924810eace65281a7014372f01937088b70ee8))

## [1.5.0](https://github.com/Beraborrowofficial/sdk/compare/v1.4.0...v1.5.0) (2025-04-10)


### Features

* apy via slots ([1574ac5](https://github.com/Beraborrowofficial/sdk/commit/1574ac59abc0aa884e653eba5353bfe046c86d6e))


### Bug Fixes

* contract apy on balance too ([91c8a13](https://github.com/Beraborrowofficial/sdk/commit/91c8a13d3c94ab8153e309f557b73a8d57b31713))
* correction for LTV ([6ad3fad](https://github.com/Beraborrowofficial/sdk/commit/6ad3fad42b7a7838b691447cf3249f841411c010))
* dont use cum values ([74c5aed](https://github.com/Beraborrowofficial/sdk/commit/74c5aed628a106734af6ab15e57887c0dace3889))
* LSp shareposition rate limit bug ([08d87a2](https://github.com/Beraborrowofficial/sdk/commit/08d87a25f7c95765a430c78a881f902304d391c1))
* no cum ([5ebd5cc](https://github.com/Beraborrowofficial/sdk/commit/5ebd5ccd8bfad4dbd69168850f48875a9d21cf1f))
* remove solvBTC ([ee4e6a7](https://github.com/Beraborrowofficial/sdk/commit/ee4e6a7d7f973d715545ce41a946f04a94bef4a8))
* vaults ([f6cacb6](https://github.com/Beraborrowofficial/sdk/commit/f6cacb6b2b28476d26510f280b8ff9556040f58b))

## [1.4.0](https://github.com/Beraborrowofficial/sdk/compare/v1.3.14...v1.4.0) (2025-04-08)


### Features

* snect vault ([84cfa59](https://github.com/Beraborrowofficial/sdk/commit/84cfa5963ad73b610dd91e73a318be9f7ae5eeea))
* snect vault ([7c987d5](https://github.com/Beraborrowofficial/sdk/commit/7c987d58d2e549f33171bc8fd5728c4ae9e1765d))

## [1.3.14](https://github.com/Beraborrowofficial/sdk/compare/v1.3.13...v1.3.14) (2025-04-03)


### Bug Fixes

* upgrade subgraph ([310f11f](https://github.com/Beraborrowofficial/sdk/commit/310f11fd03388e84da3353be30271ebe13576886))

## [1.3.13](https://github.com/Beraborrowofficial/sdk/compare/v1.3.12...v1.3.13) (2025-04-02)


### Bug Fixes

* added vaults ([d93463e](https://github.com/Beraborrowofficial/sdk/commit/d93463e9f4d012696e5010b97a0d4e160c5f9e26))

## [1.3.12](https://github.com/Beraborrowofficial/sdk/compare/v1.3.11...v1.3.12) (2025-04-01)


### Bug Fixes

* apy remove outliers ([e22e5b9](https://github.com/Beraborrowofficial/sdk/commit/e22e5b90d173a9151115b5d13ced576e3e3190a0))

## [1.3.11](https://github.com/Beraborrowofficial/sdk/compare/v1.3.10...v1.3.11) (2025-04-01)


### Bug Fixes

* added vaults ([4daa72a](https://github.com/Beraborrowofficial/sdk/commit/4daa72a51c840e76602c68d35d435f2795adb14e))
* apy via subgraph ([43b8e73](https://github.com/Beraborrowofficial/sdk/commit/43b8e73a65225efad86d59b8e1908841e79016b3))
* comments & format ([5ca30cc](https://github.com/Beraborrowofficial/sdk/commit/5ca30cc88f7f3d85faad4a15a57d37ab68e168b2))
* comments & format ([015b71b](https://github.com/Beraborrowofficial/sdk/commit/015b71b58e5adacb0f95315944de9dd50e29ecf6))
* deleted lp token ([3f6c306](https://github.com/Beraborrowofficial/sdk/commit/3f6c306d2b01b649c0b3c41828d9f250561eae67))
* drop outliner APY ([4192a3b](https://github.com/Beraborrowofficial/sdk/commit/4192a3b13b04e5b7d1567f67abb2a0966cc54994))
* extreme outliers only ([8dba3f8](https://github.com/Beraborrowofficial/sdk/commit/8dba3f8ae0b65211e9be4e10d64a99fb453d222d))
* types ([58f7123](https://github.com/Beraborrowofficial/sdk/commit/58f712382c37d28a3d27ceb83efd5b7c1b4f5c08))

## [1.3.10](https://github.com/Beraborrowofficial/sdk/compare/v1.3.9...v1.3.10) (2025-03-31)


### Bug Fixes

* checksum ibera Address ([4d55319](https://github.com/Beraborrowofficial/sdk/commit/4d5531946a6387265817ea2cbc81cf1230ada9f1))
* ioptional to lower case ([5701a08](https://github.com/Beraborrowofficial/sdk/commit/5701a08b98c19d55ff8c172a8c5701e20679c38a))

## [1.3.9](https://github.com/Beraborrowofficial/sdk/compare/v1.3.8...v1.3.9) (2025-03-30)


### Bug Fixes

* extend the rebalance event lookback ([96e3c06](https://github.com/Beraborrowofficial/sdk/commit/96e3c06e99178d92286b22803c9f79e0ff7a715a))

## [1.3.8](https://github.com/Beraborrowofficial/sdk/compare/v1.3.7...v1.3.8) (2025-03-28)


### Bug Fixes

* remove address import ([25e51ad](https://github.com/Beraborrowofficial/sdk/commit/25e51ad31343693048b9583560c20541a426896f))

## [1.3.7](https://github.com/Beraborrowofficial/sdk/compare/v1.3.6...v1.3.7) (2025-03-28)


### Bug Fixes

* apy via rebalance events ([3000896](https://github.com/Beraborrowofficial/sdk/commit/3000896ba6c0f761d15492cc865416b51723c3c1))

## [1.3.6](https://github.com/Beraborrowofficial/sdk/compare/v1.3.5...v1.3.6) (2025-03-27)


### Bug Fixes

* ibera ([7191dd9](https://github.com/Beraborrowofficial/sdk/commit/7191dd9fee8b6fb600a2e587fd96f78de107ffcc))
* ibera ([7a3a394](https://github.com/Beraborrowofficial/sdk/commit/7a3a394bfa9dfce48e6dc67581528bd23afd3bf6))

## [1.3.5](https://github.com/Beraborrowofficial/sdk/compare/v1.3.4...v1.3.5) (2025-03-27)


### Bug Fixes

* new vaults added pending ramen ([ddb0d20](https://github.com/Beraborrowofficial/sdk/commit/ddb0d20b7b3ad260ca43fe29fd5f8dff4d1e2743))
* new vaults added pending ramen ([14b951e](https://github.com/Beraborrowofficial/sdk/commit/14b951e8fd64fc0ccad33d701a24359a4804391a))

## [1.3.4](https://github.com/Beraborrowofficial/sdk/compare/v1.3.3...v1.3.4) (2025-03-27)


### Bug Fixes

* nav address to checksum ([e95bd31](https://github.com/Beraborrowofficial/sdk/commit/e95bd318dc81e1c74ff3ae3a25ee0e5350c1b8d8))

## [1.3.3](https://github.com/Beraborrowofficial/sdk/compare/v1.3.2...v1.3.3) (2025-03-26)


### Bug Fixes

* add ([5fed252](https://github.com/Beraborrowofficial/sdk/commit/5fed2522eab4ec5b60f330c87e00473c29960ee0))
* fix providers ([610ff7a](https://github.com/Beraborrowofficial/sdk/commit/610ff7a7feccb9c7f602b8ade3a97f78b10ecb88))
* hide den ([3c8aa75](https://github.com/Beraborrowofficial/sdk/commit/3c8aa7525f2435878686576db05d5e90a7e10795))

## [1.3.2](https://github.com/Beraborrowofficial/sdk/compare/v1.3.1...v1.3.2) (2025-03-26)


### Bug Fixes

* den ibera-wbera ([19de9fb](https://github.com/Beraborrowofficial/sdk/commit/19de9fb9d31b8f4440a2c587011f8dfe4177909a))

## [1.3.1](https://github.com/Beraborrowofficial/sdk/compare/v1.3.0...v1.3.1) (2025-03-26)


### Bug Fixes

* ibgt den ([1356d32](https://github.com/Beraborrowofficial/sdk/commit/1356d32e47f170cd06a79f716bd11007829aecbe))

## [1.3.0](https://github.com/Beraborrowofficial/sdk/compare/v1.2.3...v1.3.0) (2025-03-26)


### Features

* wbera-ibgt den ([82809ab](https://github.com/Beraborrowofficial/sdk/commit/82809abffd01b954ef98f222009a5f438d119e06))

## [1.2.3](https://github.com/Beraborrowofficial/sdk/compare/v1.2.2...v1.2.3) (2025-03-24)


### Bug Fixes

* weth-beraEth ([aaf799d](https://github.com/Beraborrowofficial/sdk/commit/aaf799dde824b29a78feba58d9028357cd42a8b7))

## [1.2.2](https://github.com/Beraborrowofficial/sdk/compare/v1.2.1...v1.2.2) (2025-03-24)


### Bug Fixes

* added ohm-honey ([749c98f](https://github.com/Beraborrowofficial/sdk/commit/749c98fde157b89ca566752835842082b36390f6))

## [1.2.1](https://github.com/Beraborrowofficial/sdk/compare/v1.2.0...v1.2.1) (2025-03-24)


### Bug Fixes

* vault start block ([b6b47ee](https://github.com/Beraborrowofficial/sdk/commit/b6b47ee886f1cace424680b850b295be02fbb6e2))

## [1.2.0](https://github.com/Beraborrowofficial/sdk/compare/v1.1.6...v1.2.0) (2025-03-24)


### Features

* commonJs and LSP APY upgrade ([480161d](https://github.com/Beraborrowofficial/sdk/commit/480161dd0c2988f718db9ed5112a38be0c6c0324))

## [1.1.3](https://github.com/Beraborrowofficial/sdk/compare/v1.1.2...v1.1.3) (2025-03-20)


### Bug Fixes

* added own ibgt Vault to track rebalance ([9375e96](https://github.com/Beraborrowofficial/sdk/commit/9375e9609ab0ca5f8cc72349b7a7436417e42f3f))
* collIndex to handle subprotocol ([4c7983e](https://github.com/Beraborrowofficial/sdk/commit/4c7983e3c332e82e6451bb4d5a51303a04b37dd8))

## [1.1.2](https://github.com/Beraborrowofficial/sdk/compare/v1.1.1...v1.1.2) (2025-03-19)


### Bug Fixes

* new den added ([a5932c8](https://github.com/Beraborrowofficial/sdk/commit/a5932c827345778bf2bd7f0ebf6b493c8ebdeddc))

## [1.1.1](https://github.com/Beraborrowofficial/sdk/compare/v1.1.0...v1.1.1) (2025-03-16)


### Bug Fixes

* ibgt balance on set vaults ([8d2b2ed](https://github.com/Beraborrowofficial/sdk/commit/8d2b2ed385cec535b4914aa9a172e00ef36e4584))
* ibgt balance on set vaults ([c0ad39d](https://github.com/Beraborrowofficial/sdk/commit/c0ad39dc4d782ea5f0b6ce2fe11cdb452920e0b8))

## [1.1.0](https://github.com/Beraborrowofficial/sdk/compare/v1.0.2...v1.1.0) (2025-03-13)


### Features

* BBiBGt vault added and tests removed ([e677ac1](https://github.com/Beraborrowofficial/sdk/commit/e677ac1768dc354b8172006bca3610b11484bf41))

## [1.0.2](https://github.com/Beraborrowofficial/sdk/compare/v1.0.1...v1.0.2) (2025-03-10)


### Bug Fixes

* apy start when rebalanced ([7f18ce3](https://github.com/Beraborrowofficial/sdk/commit/7f18ce33cfb5332e0e2fe7e50f0563642134f5ad))
* latest block ([5ed3f15](https://github.com/Beraborrowofficial/sdk/commit/5ed3f15edae3a170b98193d9ac81b4871bf8d030))

## [1.0.1](https://github.com/Beraborrowofficial/sdk/compare/v1.0.0...v1.0.1) (2025-03-06)


### Bug Fixes

* update start block to first deposit ([42d91d0](https://github.com/Beraborrowofficial/sdk/commit/42d91d0d68c1c76dd062b709da73130d5d6e047a))

## [1.0.0](https://github.com/Beraborrowofficial/sdk/compare/v0.7.1...v1.0.0) (2025-03-04)


### ⚠ BREAKING CHANGES

* This update introduces a breaking change to collvaults.

### Features

* upgrade collvaults ([42c95ee](https://github.com/Beraborrowofficial/sdk/commit/42c95ee7d5a2b5705ab0e729acd7f449192c1f98))


### Bug Fixes

* add beraEth ([2e7b158](https://github.com/Beraborrowofficial/sdk/commit/2e7b1588d0e79253d1284f4108a034000fc8af07))
* allow vaults ([4d199a8](https://github.com/Beraborrowofficial/sdk/commit/4d199a816f56afa93585ed96faea30bab5eeaef4))
* deployment config update ([1f50899](https://github.com/Beraborrowofficial/sdk/commit/1f50899eae2beaa5fb84d62f20cd72616824192f))
* redeem to one for dev ([a57b16d](https://github.com/Beraborrowofficial/sdk/commit/a57b16df8f5145f74082478132025b420c20d0a3))
* vault price optional ([5a6e8e6](https://github.com/Beraborrowofficial/sdk/commit/5a6e8e6b614f9066a2ad7acb9019950eb649cb56))

## [0.7.1](https://github.com/Beraborrowofficial/sdk/compare/v0.7.0...v0.7.1) (2025-02-20)


### Bug Fixes

* corrected all for new subgraph Schema and used subgraph for APY ([ba51605](https://github.com/Beraborrowofficial/sdk/commit/ba516057a047e7ccdf761f12c4dd571288e378df))
* subgraph updated, testing pending indexing ([60d8e96](https://github.com/Beraborrowofficial/sdk/commit/60d8e969e91b5589c14e1496defb52df63799c9b))

## [0.7.0](https://github.com/Beraborrowofficial/sdk/compare/v0.6.0...v0.7.0) (2025-01-30)


### Features

* boyco prevaultdeployment ([2419738](https://github.com/Beraborrowofficial/sdk/commit/24197386a85828c10d5f5cae69740e8138476c8e))

## [0.6.0](https://github.com/Beraborrowofficial/sdk/compare/v0.5.5...v0.6.0) (2025-01-21)


### Features

* allow the use of boyco denmanagers ([2f38c80](https://github.com/Beraborrowofficial/sdk/commit/2f38c80f65e7029a84dfaf743e29e6f8b0183ced))


### Bug Fixes

* upgade LSP and use redeem function to withdraw underlying assets ([0acde78](https://github.com/Beraborrowofficial/sdk/commit/0acde78fc56aa5d9a1ca472ef7fc447772aab27c))

## [0.5.5](https://github.com/Beraborrowofficial/sdk/compare/v0.5.4...v0.5.5) (2025-01-10)


### Bug Fixes

* basic rewards setuo ([b92bda0](https://github.com/Beraborrowofficial/sdk/commit/b92bda09d88aee8eefe1d64fe661d7b7c3ded280))
* den totals correctly showing ([96fc953](https://github.com/Beraborrowofficial/sdk/commit/96fc9535fd0337fcd42399de3a6327f14733c082))

## [0.5.4](https://github.com/Beraborrowofficial/sdk/compare/v0.5.3...v0.5.4) (2025-01-02)


### Bug Fixes

* added redistributions ([7abd2f5](https://github.com/Beraborrowofficial/sdk/commit/7abd2f58a867ea5252d88f747ba979be7c87a9b5))

## [0.5.3](https://github.com/Beraborrowofficial/sdk/compare/v0.5.2...v0.5.3) (2024-12-04)


### Bug Fixes

* use correct CR for leveraging debt Amount ([db1dc5d](https://github.com/Beraborrowofficial/sdk/commit/db1dc5d239e48096d766ba86478e9ed205c8f8de))

## [0.5.2](https://github.com/Beraborrowofficial/sdk/compare/v0.5.1...v0.5.2) (2024-11-28)


### Bug Fixes

* added URL for APi ([ae56bf3](https://github.com/Beraborrowofficial/sdk/commit/ae56bf3e9c4a9d74ee8b127bbd2b13895fdb2996))

## [0.5.1](https://github.com/Beraborrowofficial/sdk/compare/v0.5.0...v0.5.1) (2024-11-26)


### Bug Fixes

* added health check for oogabooga ([4fecf71](https://github.com/Beraborrowofficial/sdk/commit/4fecf713e1470ce8521ec1f0156e16b6970fbaa8))

## [0.5.0](https://github.com/Beraborrowofficial/sdk/compare/v0.4.5...v0.5.0) (2024-11-26)


### Features

* leverage enabled ([00a57d9](https://github.com/Beraborrowofficial/sdk/commit/00a57d992924a3a40459074faa1cf6d76887b5d8))

## [0.4.5](https://github.com/Beraborrowofficial/sdk/compare/v0.4.4...v0.4.5) (2024-11-19)


### Bug Fixes

* depsoit dens use collateral shares not collateral amount ([c4bd561](https://github.com/Beraborrowofficial/sdk/commit/c4bd561f29871d15bdbb41e223169d6dec4e437e))

## [0.4.4](https://github.com/Beraborrowofficial/sdk/compare/v0.4.3...v0.4.4) (2024-11-12)


### Bug Fixes

* division by zero den open ([1cf2c2d](https://github.com/Beraborrowofficial/sdk/commit/1cf2c2d31668300c0b81e2f26e58a8f8fbf20a23))

## [0.4.3](https://github.com/Beraborrowofficial/sdk/compare/v0.4.2...v0.4.3) (2024-11-11)


### Bug Fixes

* prevent floating point bigint conversion ([26f1ce0](https://github.com/Beraborrowofficial/sdk/commit/26f1ce0eb8c9fd043e0fcf0500208db703f2178a))

## [0.4.2](https://github.com/Beraborrowofficial/sdk/compare/v0.4.1...v0.4.2) (2024-11-05)


### Bug Fixes

* devision of zero ([9c551ac](https://github.com/Beraborrowofficial/sdk/commit/9c551ac661b8d7973fc871172baa2161c02ef8ac))

## [0.4.1](https://github.com/Beraborrowofficial/sdk/compare/v0.4.0...v0.4.1) (2024-11-05)


### Bug Fixes

* handled vaulted assets and set system constants ([6058820](https://github.com/Beraborrowofficial/sdk/commit/605882094fdc1b2fdd5c77f22c76288f17f5e719))

## [0.4.0](https://github.com/Beraborrowofficial/sdk/compare/v0.3.4...v0.4.0) (2024-11-01)


### Features

* LSP router to see underlying assets and select preferred token ([e17987f](https://github.com/Beraborrowofficial/sdk/commit/e17987f03f2367c82eb70b1e2f8df9a671c6c56b))


### Bug Fixes

* LpsRouter addresses added and working ([8ab81e5](https://github.com/Beraborrowofficial/sdk/commit/8ab81e5b41f58b80b8a9ea0037a4597c02cf436b))

## [0.3.4](https://github.com/Beraborrowofficial/sdk/compare/v0.3.3...v0.3.4) (2024-10-23)


### Bug Fixes

* all timestamps are seconds ([8205168](https://github.com/Beraborrowofficial/sdk/commit/8205168822237fdc6862df322cd2b7114a535a16))

## [0.3.3](https://github.com/Beraborrowofficial/sdk/compare/v0.3.2...v0.3.3) (2024-10-23)


### Bug Fixes

* eslint checks ([f6c16bc](https://github.com/Beraborrowofficial/sdk/commit/f6c16bcfd5b420579bba62055dd1827035c7c59b))
* test name corrected ([07177a5](https://github.com/Beraborrowofficial/sdk/commit/07177a552fdb3d210372a8f9b5703bfd68906d1d))

## [0.3.2](https://github.com/Beraborrowofficial/sdk/compare/v0.3.1...v0.3.2) (2024-10-23)


### Bug Fixes

* expose deployment Date and pending timestamp improvement ([7041608](https://github.com/Beraborrowofficial/sdk/commit/704160808d636e76c5c00c85194f027a88060bc1))

## [0.3.1](https://github.com/Beraborrowofficial/sdk/compare/v0.3.0...v0.3.1) (2024-10-21)


### Bug Fixes

* correct address ([c94fdda](https://github.com/Beraborrowofficial/sdk/commit/c94fdda7120fffef1140dad85e4bbe0f874b599e))

## [0.3.0](https://github.com/Beraborrowofficial/sdk/compare/v0.2.21...v0.3.0) (2024-10-21)


### Features

* **LSP:** deploy LSP Getter contract ([2d79ef5](https://github.com/Beraborrowofficial/sdk/commit/2d79ef584e4b730dc53e5f1205206590ec870132))

## [0.2.21](https://github.com/Beraborrowofficial/sdk/compare/v0.2.20...v0.2.21) (2024-10-18)


### Bug Fixes

* bump out of alpha ([77a5077](https://github.com/Beraborrowofficial/sdk/commit/77a50771b2e88aff3e2b3837c2ced7e7dc51f859))

## [0.2.19-alpha](https://github.com/Beraborrowofficial/sdk/compare/v0.2.18-alpha...v0.2.19-alpha) (2024-10-18)


### Bug Fixes

* fetch price of collateral token not vaulted token ([d008e99](https://github.com/Beraborrowofficial/sdk/commit/d008e995afa361cf66f6466bdfb73eda6284c213))

## [0.2.18-alpha](https://github.com/Beraborrowofficial/sdk/compare/v0.2.17-alpha...v0.2.18-alpha) (2024-10-17)


### Bug Fixes

* remove conventional commits alpha release ([ab511a9](https://github.com/Beraborrowofficial/sdk/commit/ab511a93d1040a90e30a1ef89611b1ec8738eda0))

## [0.2.17-alpha](https://github.com/Beraborrowofficial/sdk/compare/v0.2.16-alpha...v0.2.17-alpha) (2024-10-17)


### Bug Fixes

* yarnrc working as expected ([0fe4088](https://github.com/Beraborrowofficial/sdk/commit/0fe4088bddeea5ea9a92f5819f4bc07ae51dc597))

## [0.2.16-alpha](https://github.com/Beraborrowofficial/sdk/compare/v0.2.15-alpha...v0.2.16-alpha) (2024-10-16)


### Bug Fixes

* correct token ([e40dde7](https://github.com/Beraborrowofficial/sdk/commit/e40dde76a87b719d84e68c552cb904a93b18cf36))
