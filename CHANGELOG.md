# Changelog

## [1.9.15](https://github.com/Beraborrowofficial/interface/compare/v1.9.14...v1.9.15) (2025-05-28)


### Bug Fixes

* added usdt0-rusd ([b7af64c](https://github.com/Beraborrowofficial/interface/commit/b7af64c7af6e158dcb15e847b8ef1de1cdda7c86))
* interest rate for subprotocl ([80e69ff](https://github.com/Beraborrowofficial/interface/commit/80e69ffba99322e05557b7ec2cc78def98947773))
* interest rate for subprotocl ([3dd2908](https://github.com/Beraborrowofficial/interface/commit/3dd29081144df9b36b018ba8db2a40c2e270bf5f))

## [1.9.14](https://github.com/Beraborrowofficial/interface/compare/v1.9.13...v1.9.14) (2025-05-26)


### Bug Fixes

* mlv corrected coll to underlyingg coll not shares ([2511d87](https://github.com/Beraborrowofficial/interface/commit/2511d872c3afd4a40b603fea0fc43e5f87b6b83f))
* mlv corrected coll to underlyingg coll not shares ([cceb453](https://github.com/Beraborrowofficial/interface/commit/cceb453d548477d5eb0393b97211fcdc080ed5da))

## [1.9.13](https://github.com/Beraborrowofficial/interface/compare/v1.9.12...v1.9.13) (2025-05-22)


### Bug Fixes

* managed vaults epoch and apy, added categories for tokens ([f04dd3e](https://github.com/Beraborrowofficial/interface/commit/f04dd3e36870c4580df32781dc014a465b6bed4e))
* unibtc apy over 2 days ([d35850a](https://github.com/Beraborrowofficial/interface/commit/d35850a5a1cda563b01fbdfda84ac348a0f6587b))
* wgbera added ([e68b7d6](https://github.com/Beraborrowofficial/interface/commit/e68b7d6252edd9eb4600837b90b6150d40fe7bbf))

## [1.9.12](https://github.com/Beraborrowofficial/interface/compare/v1.9.11...v1.9.12) (2025-05-20)


### Bug Fixes

* usdt0-nect, layout and den selctor ([c7e5963](https://github.com/Beraborrowofficial/interface/commit/c7e5963f3e7ad8aeee74b205e9ef536061bd8268))

## [1.9.11](https://github.com/Beraborrowofficial/interface/compare/v1.9.10...v1.9.11) (2025-05-18)


### Bug Fixes

* ebtc change, interesate rates ([77b7666](https://github.com/Beraborrowofficial/interface/commit/77b7666479f669d38030d2b9f1a0bda37a1d88a7))

## [1.9.10](https://github.com/Beraborrowofficial/interface/compare/v1.9.9...v1.9.10) (2025-05-15)


### Bug Fixes

* solvbtc.bera added ([c9fb18e](https://github.com/Beraborrowofficial/interface/commit/c9fb18ebc85c215de94daf556419d8cc3b58ab31))

## [1.9.9](https://github.com/Beraborrowofficial/interface/compare/v1.9.8...v1.9.9) (2025-05-12)


### Bug Fixes

* boyco withdrawals ([a1c5605](https://github.com/Beraborrowofficial/interface/commit/a1c5605a9ee90195fac1e3c0072984e7b1c5cc31))

## [1.9.8](https://github.com/Beraborrowofficial/interface/compare/v1.9.7...v1.9.8) (2025-05-11)


### Bug Fixes

* added ylsteth ([3932d78](https://github.com/Beraborrowofficial/interface/commit/3932d789657e065597bedd82acc33dc57f2f38e6))
* added ylsteth ([b4ef77f](https://github.com/Beraborrowofficial/interface/commit/b4ef77f028a994f9dc23b8636f5aa76dd24cf1bb))

## [1.9.7](https://github.com/Beraborrowofficial/interface/compare/v1.9.6...v1.9.7) (2025-05-09)


### Bug Fixes

* den interest group call ([a73f2a1](https://github.com/Beraborrowofficial/interface/commit/a73f2a195f1407ed8da80bbf5f5f928337289645))
* honey only redemption ([2a55e60](https://github.com/Beraborrowofficial/interface/commit/2a55e60563846fcb316572ad74b95094677f34b5))

## [1.9.6](https://github.com/Beraborrowofficial/interface/compare/v1.9.5...v1.9.6) (2025-05-08)


### Bug Fixes

* borrow rate ([0b3417d](https://github.com/Beraborrowofficial/interface/commit/0b3417dde8bdf5d881005cf963ca725faf93d1e7))

## [1.9.5](https://github.com/Beraborrowofficial/interface/compare/v1.9.4...v1.9.5) (2025-05-07)


### Bug Fixes

* added back vault and withdrawal queue ([7cebc2c](https://github.com/Beraborrowofficial/interface/commit/7cebc2cf7f952bf791a22a17b711980dbc7be170))
* cancel redeemIntent ([5aede15](https://github.com/Beraborrowofficial/interface/commit/5aede15ce1b8b24ca144c61093e7810e93541757))
* look back more epochs ([9ab1089](https://github.com/Beraborrowofficial/interface/commit/9ab10899b2d95d4ac9cb593443f32927d52017c8))

## [1.9.4](https://github.com/Beraborrowofficial/interface/compare/v1.9.3...v1.9.4) (2025-05-07)


### Bug Fixes

* deposits enabled ([ec01908](https://github.com/Beraborrowofficial/interface/commit/ec01908fd9c187d3643c1682d41cffefa4169bbd))

## [1.9.3](https://github.com/Beraborrowofficial/interface/compare/v1.9.2...v1.9.3) (2025-05-07)


### Bug Fixes

* new swapper ([3a94c7b](https://github.com/Beraborrowofficial/interface/commit/3a94c7b708a3da7725ca3961ce4edc77c31ac3b5))

## [1.9.2](https://github.com/Beraborrowofficial/interface/compare/v1.9.1...v1.9.2) (2025-05-07)


### Bug Fixes

* min amount ([ddebe76](https://github.com/Beraborrowofficial/interface/commit/ddebe76f75f7f2ec408525a1751de8b18a59f920))
* new checks on withdraws ([b46a4aa](https://github.com/Beraborrowofficial/interface/commit/b46a4aa9ebfe8c3132f02678443afcea337e9bb4))
* warning unclaimabe ([ce5d096](https://github.com/Beraborrowofficial/interface/commit/ce5d096a03a8fa7a2db61e88fadbc3f8793e117d))

## [1.9.1](https://github.com/Beraborrowofficial/interface/compare/v1.9.0...v1.9.1) (2025-05-06)


### Bug Fixes

* changes for timing ([44260fa](https://github.com/Beraborrowofficial/interface/commit/44260fa594fbfd043335022c2bbce1b5cc13df03))
* message ([92408a6](https://github.com/Beraborrowofficial/interface/commit/92408a6ff61f86fad4e0d45891831fd3128bdbe5))

## [1.9.0](https://github.com/Beraborrowofficial/interface/compare/v1.8.5...v1.9.0) (2025-05-06)


### Features

* managed dens ([f0ce069](https://github.com/Beraborrowofficial/interface/commit/f0ce0692666ea8e73904aae76774c7becbd44975))
* managed dens ([6cdef19](https://github.com/Beraborrowofficial/interface/commit/6cdef190fb571da12f47f18e8abafa19a6f282b0))

## [1.8.5](https://github.com/Beraborrowofficial/interface/compare/v1.8.4...v1.8.5) (2025-05-02)


### Bug Fixes

* added snect-usdce-honey vault ([6f91099](https://github.com/Beraborrowofficial/interface/commit/6f91099d6ee6cfbde3a525c25f736bdbb47ea9a1))

## [1.8.4](https://github.com/Beraborrowofficial/interface/compare/v1.8.3...v1.8.4) (2025-04-27)


### Bug Fixes

* enso endpoints ([2ee1900](https://github.com/Beraborrowofficial/interface/commit/2ee1900cd72b57b7dc62d957514b9d5558bb5b3c))
* maxdebt and Manged vaults name ([b5d9494](https://github.com/Beraborrowofficial/interface/commit/b5d9494b6e98f7010b605b36061a0e3553579e2f))
* min asset withdraw sdk fix ([d207748](https://github.com/Beraborrowofficial/interface/commit/d20774826dae910a5f63415d9968fd000277846f))

## [1.8.3](https://github.com/Beraborrowofficial/interface/compare/v1.8.2...v1.8.3) (2025-04-24)


### Bug Fixes

* added dens and no address bug ([e1575cd](https://github.com/Beraborrowofficial/interface/commit/e1575cdec39ebd99125d8d05420530ed6868cd44))
* casing issue ([084f39b](https://github.com/Beraborrowofficial/interface/commit/084f39b25acde2fedc3a614c1c74860190c136bd))
* ui bugs for layout, ltv, promo banner ([9f9ddab](https://github.com/Beraborrowofficial/interface/commit/9f9ddabaf4b1a259c34445294c571b96c0a663dd))

## [1.8.2](https://github.com/Beraborrowofficial/interface/compare/v1.8.1...v1.8.2) (2025-04-17)


### Bug Fixes

* add rpc ([3dad76d](https://github.com/Beraborrowofficial/interface/commit/3dad76d3b44089d83906b3ef384770273e7cadb6))
* add rpc ([595ed76](https://github.com/Beraborrowofficial/interface/commit/595ed763c839374635c91763418c562c146a7c36))

## [1.8.1](https://github.com/Beraborrowofficial/interface/compare/v1.8.0...v1.8.1) (2025-04-17)


### Bug Fixes

* remove 0 values to - ([384a28d](https://github.com/Beraborrowofficial/interface/commit/384a28d0096afef97a485f42f175daa855c67ff1))
* use older RPC ([513961e](https://github.com/Beraborrowofficial/interface/commit/513961edb01b6bac5fd3d8772115b714430b7066))

## [1.8.0](https://github.com/Beraborrowofficial/interface/compare/v1.7.4...v1.8.0) (2025-04-17)


### Features

* display vaultPositionChanges history ([88835c9](https://github.com/Beraborrowofficial/interface/commit/88835c9ff137b1669157acb1ff33e6c9568d4014))


### Bug Fixes

* added new dens and vaults ([70c5782](https://github.com/Beraborrowofficial/interface/commit/70c5782ef7552a0026ec822305390951c65d9d3e))
* apy and tvl from the api ([a907064](https://github.com/Beraborrowofficial/interface/commit/a907064513106cd4a4f8ee6798663bfef8a5b8a1))
* den-page styles ([b9c4967](https://github.com/Beraborrowofficial/interface/commit/b9c4967848c3a968c810c6bcb9198b96e21ed1ec))
* icons ([300bcf2](https://github.com/Beraborrowofficial/interface/commit/300bcf203368039eb264f79432290e1837c3daf0))
* merge issues ([8b0ebf9](https://github.com/Beraborrowofficial/interface/commit/8b0ebf9d50d6635a1a05d125e65c4421144d8a64))
* ordered dens ([b2e91bd](https://github.com/Beraborrowofficial/interface/commit/b2e91bd5443781535d35d33f6297fa0fcd76954c))
* positions sort ([77a6699](https://github.com/Beraborrowofficial/interface/commit/77a6699bcf2e6349bee8f317b178cebe8273c6b9))

## [1.7.4](https://github.com/Beraborrowofficial/interface/compare/v1.7.3...v1.7.4) (2025-04-15)


### Bug Fixes

* added link for pollen ([06f0c62](https://github.com/Beraborrowofficial/interface/commit/06f0c6248d86c53b3988b844d3abf5482d5201f1))
* pollen wbera added ([08a2d9a](https://github.com/Beraborrowofficial/interface/commit/08a2d9ac7db974cf8a3be73c0d25f944170addc1))

## [1.7.3](https://github.com/Beraborrowofficial/interface/compare/v1.7.2...v1.7.3) (2025-04-11)


### Bug Fixes

* move deployment to interface ([6156071](https://github.com/Beraborrowofficial/interface/commit/6156071a4145cbcb1003467ec702e706d0b8f41f))
* package upgrade ([f4f621e](https://github.com/Beraborrowofficial/interface/commit/f4f621eb2b4909df5ea83c3b7b4782b7c0a8b4a7))

## [1.7.2](https://github.com/Beraborrowofficial/interface/compare/v1.7.1...v1.7.2) (2025-04-11)


### Bug Fixes

* btc dens added ([bb2aeb6](https://github.com/Beraborrowofficial/interface/commit/bb2aeb699207c30a804513edb2d144d6d34aaa87))
* decimals for LP tokens ([9ab8a88](https://github.com/Beraborrowofficial/interface/commit/9ab8a88409096c4386feac5433374fab391a3e1c))

## [1.7.1](https://github.com/Beraborrowofficial/interface/compare/v1.7.0...v1.7.1) (2025-04-10)


### Bug Fixes

* corrected image for burrbear ([1f84c18](https://github.com/Beraborrowofficial/interface/commit/****************************************))

## [1.7.0](https://github.com/Beraborrowofficial/interface/compare/v1.6.8...v1.7.0) (2025-04-10)


### Features

* slot APY ([df8dd1e](https://github.com/Beraborrowofficial/interface/commit/df8dd1ece3a463c6301c44aa5a3fbaa251675e20))


### Bug Fixes

* font size ([d89b1f8](https://github.com/Beraborrowofficial/interface/commit/d89b1f87dd83e959562eb11017a3357e60992b11))

## [1.6.8](https://github.com/Beraborrowofficial/interface/compare/v1.6.7...v1.6.8) (2025-04-08)


### Bug Fixes

* snect vault and codeowners ([46a099e](https://github.com/Beraborrowofficial/interface/commit/46a099e23d89c970dec83b663dc69653f6d3831e))

## [1.6.7](https://github.com/Beraborrowofficial/interface/compare/v1.6.6...v1.6.7) (2025-04-08)


### Bug Fixes

* use LSP withdraw ([114e351](https://github.com/Beraborrowofficial/interface/commit/114e3515eee3b719bb92d26232189452360a84a7))
* withdraw closed ([27229f3](https://github.com/Beraborrowofficial/interface/commit/27229f3545c5c16f44331da360fff18050d26093))

## [1.6.6](https://github.com/Beraborrowofficial/interface/compare/v1.6.5...v1.6.6) (2025-04-08)


### Bug Fixes

* nect back on ([57d2838](https://github.com/Beraborrowofficial/interface/commit/57d2838fa7ef525ab399578ef332e161ea72c796))
* nect back on ([241bc1e](https://github.com/Beraborrowofficial/interface/commit/241bc1ef403b37674e670317316a419f027da8d3))

## [1.6.5](https://github.com/Beraborrowofficial/interface/compare/v1.6.4...v1.6.5) (2025-04-08)


### Bug Fixes

* redeem reduce the number of tokens ([0dccbef](https://github.com/Beraborrowofficial/interface/commit/0dccbef601df6a1fb1eb1d9c2dc969f8c5bbec71))
* redeem reduce the number of tokens ([bbe550e](https://github.com/Beraborrowofficial/interface/commit/bbe550e5374deb1f6e3e5ff5967be4bb193541ee))

## [1.6.4](https://github.com/Beraborrowofficial/interface/compare/v1.6.3...v1.6.4) (2025-04-08)


### Bug Fixes

* remove pollen points ([1b41dad](https://github.com/Beraborrowofficial/interface/commit/1b41dade21959240d84e82136c293b0c97f54c10))

## [1.6.3](https://github.com/Beraborrowofficial/interface/compare/v1.6.2...v1.6.3) (2025-04-07)


### Bug Fixes

* remove nect from redeem array ([9087b00](https://github.com/Beraborrowofficial/interface/commit/9087b00ba3d964316f379c267e9a0b3e22534dfe))

## [1.6.2](https://github.com/Beraborrowofficial/interface/compare/v1.6.1...v1.6.2) (2025-04-07)


### Bug Fixes

* allow withdraw from pool ([b435aae](https://github.com/Beraborrowofficial/interface/commit/b435aae6bd5777d7bc3b5d338e45f3d29e08ea3c))

## [1.6.1](https://github.com/Beraborrowofficial/interface/compare/v1.6.0...v1.6.1) (2025-04-03)


### Bug Fixes

* vault APY limits ([8831542](https://github.com/Beraborrowofficial/interface/commit/88315427ea3ea6d69d252d73accf899e76529955))

## [1.6.0](https://github.com/Beraborrowofficial/interface/compare/v1.5.10...v1.6.0) (2025-04-02)


### Features

* vault page redesign ([7676007](https://github.com/Beraborrowofficial/interface/commit/76760075a4c7a62a2440f79a43e32aa351974f04))
* vault page redesign ([d1050a5](https://github.com/Beraborrowofficial/interface/commit/d1050a56fa6e3ef168c8f7556c9f1758572d5528))


### Bug Fixes

* vaults with new vault page live ([23ad4b2](https://github.com/Beraborrowofficial/interface/commit/23ad4b25826cd1f120dcb581268b6e1460943f69))

## [1.5.10](https://github.com/Beraborrowofficial/interface/compare/v1.5.9...v1.5.10) (2025-04-01)


### Bug Fixes

* apy ([aad4a3d](https://github.com/Beraborrowofficial/interface/commit/aad4a3df983d0d2a062552598711d0e9a3263d50))
* use subgraph APY ([26cbe70](https://github.com/Beraborrowofficial/interface/commit/26cbe7043f54d34d815bd1f4c31ae1b3b569d7eb))

## [1.5.9](https://github.com/Beraborrowofficial/interface/compare/v1.5.8...v1.5.9) (2025-03-31)


### Bug Fixes

* package updrage for ibera ([d6eb3e1](https://github.com/Beraborrowofficial/interface/commit/d6eb3e180679b33f989d70554574f2fdf3fa0f9a))
* remove +1 ([089ab8e](https://github.com/Beraborrowofficial/interface/commit/089ab8e060c46f94b574081c7f9e271a43e2370d))
* remove walletConnect import ([6525a8d](https://github.com/Beraborrowofficial/interface/commit/6525a8d9cda17e8be785d914eb819e8ead41765a))
* wallet connect issue ([e1ff3b1](https://github.com/Beraborrowofficial/interface/commit/e1ff3b1ebc82ed578ce021e859db797ae7fc1721))

## [1.5.8](https://github.com/Beraborrowofficial/interface/compare/v1.5.7...v1.5.8) (2025-03-30)


### Bug Fixes

* lookback 2hours on APY ([ab828a6](https://github.com/Beraborrowofficial/interface/commit/ab828a69d7fa4d1bcab6c64adc7a0d826800a89e))

## [1.5.7](https://github.com/Beraborrowofficial/interface/compare/v1.5.6...v1.5.7) (2025-03-28)


### Bug Fixes

* better apy ([0061dfe](https://github.com/Beraborrowofficial/interface/commit/0061dfea629a630dc7052911295c56fa3e1a5918))

## [1.5.6](https://github.com/Beraborrowofficial/interface/compare/v1.5.5...v1.5.6) (2025-03-28)


### Bug Fixes

* apr set only off ([53ebed6](https://github.com/Beraborrowofficial/interface/commit/53ebed6133a2205ab93e33f3ea21a0aa03962818))

## [1.5.5](https://github.com/Beraborrowofficial/interface/compare/v1.5.4...v1.5.5) (2025-03-28)


### Bug Fixes

* apr set only ([2c4961b](https://github.com/Beraborrowofficial/interface/commit/2c4961b97d025c546e2088df072a9697eed782e2))

## [1.5.4](https://github.com/Beraborrowofficial/interface/compare/v1.5.3...v1.5.4) (2025-03-28)


### Bug Fixes

* ibera ([08ca5a5](https://github.com/Beraborrowofficial/interface/commit/08ca5a5f97643a720c95d1cf9f94c268dbbca4cc))

## [1.5.3](https://github.com/Beraborrowofficial/interface/compare/v1.5.2...v1.5.3) (2025-03-27)


### Bug Fixes

* added vaults ([6da76ad](https://github.com/Beraborrowofficial/interface/commit/6da76ad46c07204840129837e361f8147e628b9a))

## [1.5.2](https://github.com/Beraborrowofficial/interface/compare/v1.5.1...v1.5.2) (2025-03-27)


### Bug Fixes

* apr backup and minor fixes ([b0db8a7](https://github.com/Beraborrowofficial/interface/commit/b0db8a7c394795e1db1c8cdc023afb8f4a0e8dc5))
* apr backup and minor fixes ([973acb4](https://github.com/Beraborrowofficial/interface/commit/973acb421426d409c231c7e93a9d2813202a4d19))

## [1.5.1](https://github.com/Beraborrowofficial/interface/compare/v1.5.0...v1.5.1) (2025-03-26)


### Bug Fixes

* ibgt den ([18488fc](https://github.com/Beraborrowofficial/interface/commit/18488fc4f30f0b0df37a09892f5d12203fea68a1))
* nav added ([b901bf1](https://github.com/Beraborrowofficial/interface/commit/b901bf1e509cad9bc0f2fc8bb128b589b2741f7b))
* new den ibera-wbera ([f747955](https://github.com/Beraborrowofficial/interface/commit/f74795501f986667f3f1371fec6fffbca725ca15))

## [1.5.0](https://github.com/Beraborrowofficial/interface/compare/v1.4.13...v1.5.0) (2025-03-26)


### Features

* new wbera-ibgt vault and den ([1d878bf](https://github.com/Beraborrowofficial/interface/commit/1d878bfccdf30d30f6025c696e05573846009325))

## [1.4.13](https://github.com/Beraborrowofficial/interface/compare/v1.4.12...v1.4.13) (2025-03-25)


### Bug Fixes

* decimal places on APY ([9ebcff2](https://github.com/Beraborrowofficial/interface/commit/9ebcff2fdbd509b895979b6a78eaf57f0e0f763c))

## [1.4.12](https://github.com/Beraborrowofficial/interface/compare/v1.4.11...v1.4.12) (2025-03-25)


### Bug Fixes

* decimal checks, apy min ([7f192eb](https://github.com/Beraborrowofficial/interface/commit/7f192ebad11808ce77ef2480fb8a61d4ba4c1920))

## [1.4.11](https://github.com/Beraborrowofficial/interface/compare/v1.4.10...v1.4.11) (2025-03-24)


### Bug Fixes

* all vaults ([d05abc4](https://github.com/Beraborrowofficial/interface/commit/d05abc49942888cc1c016487081d6915b045a6f5))

## [1.4.10](https://github.com/Beraborrowofficial/interface/compare/v1.4.9...v1.4.10) (2025-03-24)


### Bug Fixes

* weth-beraeth ([26e3dad](https://github.com/Beraborrowofficial/interface/commit/26e3dade7af564b44e9306043d8632e1ac198392))

## [1.4.9](https://github.com/Beraborrowofficial/interface/compare/v1.4.8...v1.4.9) (2025-03-24)


### Bug Fixes

* added ohm-honey ([adfe7a8](https://github.com/Beraborrowofficial/interface/commit/adfe7a81ec0252bc400a1ff9e47b5959935e05e7))

## [1.4.8](https://github.com/Beraborrowofficial/interface/compare/v1.4.7...v1.4.8) (2025-03-24)


### Bug Fixes

* apy start block ([8a910fe](https://github.com/Beraborrowofficial/interface/commit/8a910fef3e0d138eb789f743de76e51469c3d033))
* disable tsc cehck ([bc19d57](https://github.com/Beraborrowofficial/interface/commit/bc19d579b4b085722c4c1496125b2be8d61ea205))
* vault preselect issue ([621c178](https://github.com/Beraborrowofficial/interface/commit/621c178213bc947ef70920e1be011b490b7c16a4))

## [1.4.5](https://github.com/Beraborrowofficial/interface/compare/v1.4.4...v1.4.5) (2025-03-20)


### Bug Fixes

* decimals ([033ad96](https://github.com/Beraborrowofficial/interface/commit/033ad96e19bd5b9d836eb31e4ec61658a544f05e))

## [1.4.4](https://github.com/Beraborrowofficial/interface/compare/v1.4.3...v1.4.4) (2025-03-20)


### Bug Fixes

* added dens ([ee304ff](https://github.com/Beraborrowofficial/interface/commit/ee304fffba06596f7af2f7c789cfbb8329e3bf4b))
* build errors ([e24e809](https://github.com/Beraborrowofficial/interface/commit/e24e809824d6bd981242e861b9e40353ce4c5825))
* coll index fix ([25b2fc1](https://github.com/Beraborrowofficial/interface/commit/25b2fc114d85e3aaa9a8ccb51eed5f194ed8421e))
* collateral fallback price ([01ca443](https://github.com/Beraborrowofficial/interface/commit/01ca4430d70890d847ac8bd6625e45293bd02cda))
* collateralBalance correction ([567a20f](https://github.com/Beraborrowofficial/interface/commit/567a20f88f3f34ff43d0548add5e21a7ddb3a29c))
* redploy ([faa3dda](https://github.com/Beraborrowofficial/interface/commit/faa3dda19246981aa89b567fe1bcb0ec26e1938f))

## [1.4.3](https://github.com/Beraborrowofficial/interface/compare/v1.4.2...v1.4.3) (2025-03-16)


### Bug Fixes

* apy for ibgt ([43feed6](https://github.com/Beraborrowofficial/interface/commit/43feed68a56aab9c0b45f522b95579ee30c44268))

## [1.4.2](https://github.com/Beraborrowofficial/interface/compare/v1.4.1...v1.4.2) (2025-03-15)


### Bug Fixes

* no den apy ([438dc9f](https://github.com/Beraborrowofficial/interface/commit/438dc9fe4fa7e2161e01fdeecfebb53fac33d6f6))
* no den apy ([0db1711](https://github.com/Beraborrowofficial/interface/commit/0db1711786af2f8cd3fd64b4b6f27130b9d36d4b))
* no den apy ([8cf01d5](https://github.com/Beraborrowofficial/interface/commit/8cf01d55a08b7c6e047725cfd00db4b6ed8449ca))

## [1.4.1](https://github.com/Beraborrowofficial/interface/compare/v1.4.0...v1.4.1) (2025-03-14)


### Bug Fixes

* vault position ([ed49c55](https://github.com/Beraborrowofficial/interface/commit/ed49c554bb4543d6f2b25c5122c8fc1a078bd514))
* vault position ([9e1727b](https://github.com/Beraborrowofficial/interface/commit/9e1727bffd6dc2e2b8b92dc015ad566e42a89f9e))

## [1.4.0](https://github.com/Beraborrowofficial/interface/compare/v1.3.2...v1.4.0) (2025-03-13)


### Features

* iBGT vault ([d578cb0](https://github.com/Beraborrowofficial/interface/commit/d578cb0462bfe958d2865086f34370e7a0b216a2))


### Bug Fixes

* build sdk ([6dae075](https://github.com/Beraborrowofficial/interface/commit/6dae075278cedb5dd1b6468832bb92884ce1d7ff))

## [1.3.2](https://github.com/Beraborrowofficial/interface/compare/v1.3.1...v1.3.2) (2025-03-12)


### Bug Fixes

* readd func ([cde353f](https://github.com/Beraborrowofficial/interface/commit/cde353f10bd6bbf5bd82acb45e9f4fa00448e73a))

## [1.3.1](https://github.com/Beraborrowofficial/interface/compare/v1.3.0...v1.3.1) (2025-03-10)


### Bug Fixes

* new sdk ([c82a6bb](https://github.com/Beraborrowofficial/interface/commit/c82a6bb9aa3f0c19e13ab6e75a65f17934388949))

## [1.3.0](https://github.com/Beraborrowofficial/interface/compare/v1.2.5...v1.3.0) (2025-03-10)


### Features

* add berchain mainnet ([7eee66f](https://github.com/Beraborrowofficial/interface/commit/7eee66ffd115dbae2ee96e76fe365d89e18db93f))
* bex vaults autocompound ([b59c0f3](https://github.com/Beraborrowofficial/interface/commit/b59c0f39be0c2f46e3989f56688bb2bc6c28efad))
* boyco positions on dev site and dependancies security ([84b7571](https://github.com/Beraborrowofficial/interface/commit/84b75719a1d1c3b1fd9870f5114c8e319720f25f))
* boyco vaults added and setup ([9fe4a7c](https://github.com/Beraborrowofficial/interface/commit/9fe4a7c4d756023db76fee3cfebc421e9406d05e))
* public vaults ([5266b69](https://github.com/Beraborrowofficial/interface/commit/5266b69335fc087202dcedbcc31e268df4a9a00a))
* sentry integrated ([572729d](https://github.com/Beraborrowofficial/interface/commit/572729d368cf092434e74ad8e61e322a6cf9231d))


### Bug Fixes

* all final fixes and needs for post launch ([01cf326](https://github.com/Beraborrowofficial/interface/commit/01cf326d00b93f2cb6e6e391801347bcf6f4e0fb))
* apy upgrade SDK ([2177313](https://github.com/Beraborrowofficial/interface/commit/2177313d5ebca673b2c5cfa6980020e288d76672))
* connectors ([36abdd7](https://github.com/Beraborrowofficial/interface/commit/36abdd760ffb2e10cef9a93135a7e4ca33f82e8d))
* den views for boyco vaults ([cbea89d](https://github.com/Beraborrowofficial/interface/commit/cbea89dc11c37d5778140c0a6ff2cf8f019050c9))
* disable vaults ([b13422f](https://github.com/Beraborrowofficial/interface/commit/b13422f11bfdf47ebd2a879c30686b961464aa80))
* for DenManager ([97a3042](https://github.com/Beraborrowofficial/interface/commit/97a3042e52119de60d345a097d4d0b15734d8cd7))
* github actions to deploy to mainnet ([c1dada3](https://github.com/Beraborrowofficial/interface/commit/c1dada3dd5af0cd19407a706e63881fc9413f9db))
* latest SDK ([849de5a](https://github.com/Beraborrowofficial/interface/commit/849de5a0123a6166c060902ede47f0a7e7757cc8))
* launch predens ([b64e67e](https://github.com/Beraborrowofficial/interface/commit/b64e67eb411896eaf939730feee5870e94a87391))
* lsp fix and fireblock upgrades ([70a7249](https://github.com/Beraborrowofficial/interface/commit/70a724917f5d87b3aeacd123c1098a606b9fb720))
* redeem via PSM bond ([0fb8008](https://github.com/Beraborrowofficial/interface/commit/0fb8008bff9d0b7b393dee8ca2fa7f5ba51c4141))
* remove den TVL ([5f1f0ed](https://github.com/Beraborrowofficial/interface/commit/5f1f0edae7074104428eb6292958ebed83aae03d))
* remove redeemUnderlying, basic rewards work, peding SDK PR ([bba3c79](https://github.com/Beraborrowofficial/interface/commit/bba3c79fe5fe075c48c7c7e99ee9f306d4b5560e))
* remove withdraw fees on vaultComp ([d5b7c34](https://github.com/Beraborrowofficial/interface/commit/d5b7c34712fda4363c6fd651a7fffc97faf7b0e2))
* sdk updated ([cc6f9b4](https://github.com/Beraborrowofficial/interface/commit/cc6f9b46052f82d27a665e9ff57da014352b06d6))
* upgraded subgraph, subgraph APY, preload Den Selector ([b47dddf](https://github.com/Beraborrowofficial/interface/commit/b47dddf8b4a1ccb7fbb995863f32a29520909bcd))
* vaults corrected ([2422de5](https://github.com/Beraborrowofficial/interface/commit/2422de54cf024aac3cc53f36a16ff4b0a04379d3))
* vite build for dev ([a2ec2bc](https://github.com/Beraborrowofficial/interface/commit/a2ec2bcdecd9bf2bfbadd7ed4c48891f1974cf7c))

## [1.2.5](https://github.com/Beraborrowofficial/interface/compare/v1.2.4...v1.2.5) (2025-01-14)


### Bug Fixes

* basic setup ([335f938](https://github.com/Beraborrowofficial/interface/commit/****************************************))
* CR corrected for vaulted assetrs ([13556fd](https://github.com/Beraborrowofficial/interface/commit/13556fd8c5880e64b029643e49fa139f51dba308))

## [1.2.4](https://github.com/Beraborrowofficial/interface/compare/v1.2.3...v1.2.4) (2025-01-03)


### Bug Fixes

* wallet icons ([a6100dc](https://github.com/Beraborrowofficial/interface/commit/a6100dc87cc3be8a8948b4b8ccee3461dba2e6a3))

## [1.2.3](https://github.com/Beraborrowofficial/interface/compare/v1.2.2...v1.2.3) (2025-01-03)


### Bug Fixes

* add tomo wallet ([475d708](https://github.com/Beraborrowofficial/interface/commit/475d7081ac7f94f1f84abd63b28a3a4a9d1730b6))
* redistributions applied to dens as well as fallback RPC ([f900652](https://github.com/Beraborrowofficial/interface/commit/f900652c66fa6f44e0a2f087e4ce5b10bc7778fa))

## [1.2.2](https://github.com/Beraborrowofficial/interface/compare/v1.2.1...v1.2.2) (2024-12-05)


### Bug Fixes

* added CR for leveraging ([af79407](https://github.com/Beraborrowofficial/interface/commit/af7940786678e7bfff5cc07abf8b0ae6c70bf737))

## [1.2.1](https://github.com/Beraborrowofficial/interface/compare/v1.2.0...v1.2.1) (2024-11-29)


### Bug Fixes

* leverage slippage atom set to 1% and slipapge on flashloanFee ([9a91742](https://github.com/Beraborrowofficial/interface/commit/9a9174250e5bf63df1481cf30f286f92ca0c5a49))
* post launch Leverage bugs: notifications, approval, vault style ([9fbee91](https://github.com/Beraborrowofficial/interface/commit/9fbee9152d4b4fdea73fe59100edd97594c8fae0))

## [1.2.0](https://github.com/Beraborrowofficial/interface/compare/v1.1.3...v1.2.0) (2024-11-28)


### Features

* leverage enabled via OogaBooga ([376fb91](https://github.com/Beraborrowofficial/interface/commit/376fb91195a8b399decd66a0cc0f562a2a12b6e9))


### Bug Fixes

* hide rewards ([52a076f](https://github.com/Beraborrowofficial/interface/commit/52a076ffebab0ddfe1342cc7699b73ff1464a8f1))
* make use of API URL ([c69093e](https://github.com/Beraborrowofficial/interface/commit/c69093e0fa58729543e02e6d90529901834d059e))
* merge issues with preferredunderlying ([f182079](https://github.com/Beraborrowofficial/interface/commit/f18207936980f6989f602866df968f2a68df965c))

## [1.1.3](https://github.com/Beraborrowofficial/interface/compare/v1.1.2...v1.1.3) (2024-11-19)


### Bug Fixes

* adding use of collateral Shares not just collateral ([09aaad5](https://github.com/Beraborrowofficial/interface/commit/09aaad54bf174a316b534254fbd2101ccdd53e3b))

## [1.1.2](https://github.com/Beraborrowofficial/interface/compare/v1.1.1...v1.1.2) (2024-11-12)


### Bug Fixes

* open den with liquidation reserve ([928aa79](https://github.com/Beraborrowofficial/interface/commit/928aa79a9db873afcbcdd5336bcba6213b7c3278))

## [1.1.1](https://github.com/Beraborrowofficial/interface/compare/v1.1.0...v1.1.1) (2024-11-11)


### Bug Fixes

* APY calc corrected ([ab845ba](https://github.com/Beraborrowofficial/interface/commit/ab845ba13e48821c9404cb2d4a9d682e275bf78d))

## [1.1.0](https://github.com/Beraborrowofficial/interface/compare/v1.0.7...v1.1.0) (2024-11-06)


### Features

* added connectors and site details ([84a13ad](https://github.com/Beraborrowofficial/interface/commit/84a13ad58647ef2d3a16c200baa0d0d4b17c7461))

## [1.0.7](https://github.com/Beraborrowofficial/interface/compare/v1.0.6...v1.0.7) (2024-10-23)


### Bug Fixes

* webP job corrected ([b1d2614](https://github.com/Beraborrowofficial/interface/commit/b1d261444e0b6cb65d5d558edd2b81403a11d1fc))

## [1.0.6](https://github.com/Beraborrowofficial/interface/compare/v1.0.5...v1.0.6) (2024-10-23)


### Bug Fixes

* history date, webp improvement ([408b493](https://github.com/Beraborrowofficial/interface/commit/408b4931469535821091c4d369ee463694c3fbf7))

## [1.0.5](https://github.com/Beraborrowofficial/interface/compare/v1.0.4...v1.0.5) (2024-10-21)


### Bug Fixes

* history by param ([1d55748](https://github.com/Beraborrowofficial/interface/commit/1d557483889fe5ee75dfc5bf685414772bcdaf2c))

## [1.0.4](https://github.com/Beraborrowofficial/interface/compare/v1.0.3...v1.0.4) (2024-10-18)


### Bug Fixes

* remove f ([8feae65](https://github.com/Beraborrowofficial/interface/commit/8feae65782425b31c7525e1f41ff2b569ab012ab))

## [1.0.3](https://github.com/Beraborrowofficial/interface/compare/v1.0.2...v1.0.3) (2024-10-18)


### Bug Fixes

* link to build corrected ([d59fbbd](https://github.com/Beraborrowofficial/interface/commit/d59fbbd943ee2f6d8606e5649afbe03c28aeab3b))

## [1.0.2](https://github.com/Beraborrowofficial/interface/compare/v1.0.1...v1.0.2) (2024-10-18)


### Bug Fixes

* corrected prod workflow ([0adcc53](https://github.com/Beraborrowofficial/interface/commit/0adcc53e1b2967d5072ff5d2795419d1d953bf52))

## [1.0.1](https://github.com/Beraborrowofficial/interface/compare/v1.0.0...v1.0.1) (2024-10-18)


### Bug Fixes

* add token to wallet, correct token price ([e128adb](https://github.com/Beraborrowofficial/interface/commit/e128adb80eb8cac7f2e7531daec9506ef28167ee))
* aligin add token to right ([59723fe](https://github.com/Beraborrowofficial/interface/commit/59723fe6f946cb115754e7545507911943458b6f))
* copy address ([25c9a54](https://github.com/Beraborrowofficial/interface/commit/25c9a54cf94e1b05cf3428da097d14facaa0517c))

## 1.0.0 (2024-10-17)


### Features

* added withdraw designs ([f8f624b](https://github.com/Beraborrowofficial/interface/commit/f8f624b2ec0ce89dedbc82383a8a70205f5d57f8))
* basic den Page and minor bug fixes ([d854f48](https://github.com/Beraborrowofficial/interface/commit/d854f48acfab07c94f2874822a10f402e326ad98))
* basic lock ([6b17fa0](https://github.com/Beraborrowofficial/interface/commit/6b17fa048252955e33ed2c27c915d9c1c953e3f5))
* deployment ready ([b765a04](https://github.com/Beraborrowofficial/interface/commit/b765a04aac8be209e9687204c4ee89de8619fab5))
* deposit and withdraw from Den in progress ([f94fa7c](https://github.com/Beraborrowofficial/interface/commit/f94fa7cca55e5e02f765f3c7d4f2a3d83079321f))
* **redeem:** working redeem with improvements on Den ([05de0c8](https://github.com/Beraborrowofficial/interface/commit/05de0c84227506413cd3ff32b8e6437f21122833))


### Bug Fixes

* added private pacakge ([3632a97](https://github.com/Beraborrowofficial/interface/commit/3632a976135ca5a566cd03c93c62ba3a04662693))
* form validation reworked ([fc52613](https://github.com/Beraborrowofficial/interface/commit/fc526133646f72ff9855f72cffe02f7f6ae18659))
* honey delegate feature ([4e75089](https://github.com/Beraborrowofficial/interface/commit/4e7508954add582e40d36594db913ba128ec4d4b))
* honey delegate feature package version ([a5296f4](https://github.com/Beraborrowofficial/interface/commit/a5296f48ff212d95c1f245063373dd2a0a889690))
* log safe autoconnector ([74ac1aa](https://github.com/Beraborrowofficial/interface/commit/74ac1aaccae02ee09fcc9ecec744d27f54fbe9ff))
* **notifications:** reworked the notifcations to be reactive and added withdraw ([9fba782](https://github.com/Beraborrowofficial/interface/commit/9fba782fbe1750c452772f8ec70e4daa41b2cb7f))
* **transactions:** cleaned up Transaction Atoms ([debdebc](https://github.com/Beraborrowofficial/interface/commit/debdebc7eb6e3fdc209ad01ae7ffbe140121d46d))
* use GITHUB token directly ([6daf46d](https://github.com/Beraborrowofficial/interface/commit/6daf46d43742f4896fc004d37541986334456413))
* use GITHUB token in install ([14d016d](https://github.com/Beraborrowofficial/interface/commit/14d016d8ec57bd6e485eae524f788a3549d2f30e))
