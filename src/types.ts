import { <PERSON><PERSON>, Hex, PublicClient, StateOverride, TestClient } from "viem";
import { Collateral } from "./Contracts/Collateral/Contract";
import { CollateralVault } from "./Contracts/CollateralVaults/Contract";
import { DenManager } from "./Contracts/DenManager/Contract";
import { MultiCollateralHintHelpers } from "./Contracts/MultiCollateralHintHelpers/Contract";
import { DenManagerGetters } from "./Contracts/DenManagerGetters/Contract";
import { BorrowerOperationsHandler } from "./BorrowerOperations/BorrowerOperationsHandler";
import { ManagedVault } from "./Contracts/ManagedVaults/Contract";

export interface TransactionOptions {
  account?: Hex | undefined;
  blockNumber?: bigint | undefined;
  stateOverride?: StateOverride | undefined;
  gas?: bigint;
}

/**
 * Thrown when trying to connect to a network where BeraBorrow is not deployed.
 * @public
 */
export class UnsupportedNetworkError extends Error {
  /** Chain ID of the unsupported network. */
  readonly chainId: number;

  /** @internal */
  constructor(chainId: number) {
    super(`Unsupported network (chainId = ${chainId})`);
    this.name = "UnsupportedNetworkError";
    this.chainId = chainId;
  }
}

export interface BeraBorrowConnection {
  readonly publicClient: PublicClient;

  readonly testClient?: TestClient;
  /**
   * Users Wallet Address
   */
  readonly userAddress: Hex;

  /** Chain ID of the connected network. */
  readonly chainId: number;

  /** Number of block in which the first BeraBorrow contract was deployed. */
  readonly startBlock: number;

  /** Time period (in seconds) after `deploymentTimestamp` during which redemptions are disabled. */
  readonly bootstrapPeriod: number;

  /** Average time in seconds per block. */
  readonly averageBlockTime: number;

  /** timestamp of deployment */
  readonly deploymentTimestamp: number;

  /** A mapping of BeraBorrow contracts' names to their addresses. */
  readonly addresses: BeraBorrowContractAddresses;
  /**
   * Amount of NECT that's reserved for compensating the liquidator of a Den.
   */
  readonly liquidationReserve: bigint;

  /**
   * A Den must always have at least this much debt on top of the liquidation reserve
   *
   * @remarks
   * Any transaction that would result in a Den with less net debt than this will be reverted.
   *
   * @public
   */
  readonly minimumDebt: bigint;
  /**
   *  URL for Subgraph API
   */
  readonly SUBGRAPH_API_URL: string;
  SUBGRAPH_API_KEY?: string;
  readonly BERABORROW_API_URL: string;
  BERABORROW_API_KEY?: string;

  readonly SUBGRAPH_POLLEN_API_URL: string;
  SUBGRAPH_POLLEN_API_KEY?: string;
}

export type Token = {
  contractAddress: Hex;
  ticker: string;
  decimals: number;
  lpToken?: { token1: Hex; token2: Hex; provider: string };
};
export type ManagedVaultObj = { denManager: DenManager; collateral: Collateral; vault: CollateralVault; managedVault: ManagedVault; getterAddress: Hex };
export type DenManagerObj = { denManager: DenManager; collateral: Collateral; vault?: CollateralVault; wrappedCollateral?: Collateral; permissioned?: boolean };
export type Protocol = {
  denManagerGetters: DenManagerGetters;
  multiCollateralHintHelpers: MultiCollateralHintHelpers;
  borrowerOperationsHandler: BorrowerOperationsHandler;
};

export interface BeraBorrowContractAddresses {
  multicall: Hex;
  pollenToken: Token;
  pollenLpToken: Token;
  sPollenToken: Token;
  lpPollenStaking: Hex;
  sPollenStaking: Hex;
  lpVotingEscrow: Hex;
  lpVeFeeDistributor: Hex;
  sPollenVotingEscrow: Hex;
  sPollenVeFeeDistributor: Hex;
  stagingPriceFeed: Hex;
  stabilityPool: Hex;
  LSPGetters: Hex;
  LSPRouter: Hex;
  priceFeed: Hex;
  debtToken: Token;
  leverageRouter: Hex;
  defaultProtocol: Hex;
  protocols: {
    [key: Hex]: {
      beraborrowCore: Hex;
      borrowerOperations: Hex;
      denManagerGetters: Hex;
      multiCollateralHintHelpers: Hex;
      collVaultRouter: Hex;
    };
  };
  psmBond: Hex;
  CollateralVaultRegistry: Hex;
  ibgtVault: Hex;
  collateralTokens: {
    [key: Hex]: {
      ticker: string;
      decimals: number;
      lpToken?: { token1: Hex; token2: Hex; token3?: Hex; provider: string };
      getUrl?: string;
      protocol?: string;
      category?: ("bera" | "eth" | "btc" | "stable")[];
    };
  };
  wrappedDenManagers: { [key: Hex]: Hex };
  denManagers: {
    [key: Hex]: {
      denManager: Hex;
      collateral: Hex;
      sortedDens: Hex;
      vault: Hex;
      wrappedCollateral?: Hex;
      index: number;
      permissioned?: boolean;
      protocol?: Hex;
    };
  };
  managedVaults: { [key: Hex]: { contractAddress: Hex; collateral: Hex; denManager: Hex; getterAddress: Hex } };
  vaults: {
    [key: Hex]: {
      startBlock: number;
      collateral: Hex;
      ticker: string;
      decimals: number;
      public?: boolean;
      apyType?: "none" | "assets" | "ibgt";
      oracleless?: boolean;
    };
  };

  psmBondCollaterals: Hex[];
}
export interface BeraBorrowDeploymentJSON {
  readonly chainId: number;
  readonly addresses: BeraBorrowContractAddresses;
  readonly startBlock: number;
  readonly bootstrapPeriod: number;
  readonly deploymentTimestamp: number;
  readonly averageBlockTime: number;
  readonly liquidationReserve: number;
  readonly minimumDebt: number;
  readonly SUBGRAPH_API_URL: string;
  readonly SUBGRAPH_POLLEN_API_URL: string;
  readonly SUBGRAPH_API_KEY?: string;
  readonly BERABORROW_API_URL: string;
  readonly BERABORROW_API_KEY?: string;
}
export type ContractCall = {
  address: Hex;
  abi: Abi;
  functionName: string;
  args: unknown[];
  overrides?: Record<string, unknown>;
};
