import { Den } from "./Den";

export type _CollateralDeposit<T> = { depositCollateral: T };
export type _CollateralWithdrawal<T> = { withdrawCollateral: T };
export type _NECTBorrowing<T> = { borrowNECT: T };
export type _NECTRepayment<T> = { repayNECT: T };

export type _NoCollateralDeposit = Partial<_CollateralDeposit<undefined>>;
export type _NoCollateralWithdrawal = Partial<_CollateralWithdrawal<undefined>>;
export type _NoNECTBorrowing = Partial<_NECTBorrowing<undefined>>;
export type _NoNECTRepayment = Partial<_NECTRepayment<undefined>>;

export type _CollateralChange<T> = (_CollateralDeposit<T> & _NoCollateralWithdrawal) | (_CollateralWithdrawal<T> & _NoCollateralDeposit);

export type _NoCollateralChange = _NoCollateralDeposit & _NoCollateralWithdrawal;

export type _DebtChange<T> = (_NECTBorrowing<T> & _NoNECTRepayment) | (_NECTRepayment<T> & _NoNECTBorrowing);

export type _NoDebtChange = _NoNECTBorrowing & _NoNECTRepayment;

export type DenCreationParams<T = unknown> = _CollateralDeposit<T> & _NoCollateralWithdrawal & _NECTBorrowing<T> & _NoNECTRepayment;

export type DenClosureParams<T> = _CollateralWithdrawal<T> & _NoCollateralDeposit & Partial<_NECTRepayment<T>> & _NoNECTBorrowing;

export type DenAdjustmentParams<T = unknown> = (_CollateralChange<T> & _NoDebtChange) | (_DebtChange<T> & _NoCollateralChange) | (_CollateralChange<T> & _DebtChange<T>);

export type DenCreationError = "missingLiquidationReserve";

export type DenChange<T> =
  | { type: "invalidCreation"; invalidDen: Den; error: DenCreationError }
  | { type: "creation"; params: DenCreationParams<T> }
  | { type: "closure"; params: DenClosureParams<T> }
  | { type: "adjustment"; params: DenAdjustmentParams<T>; setToZero?: "collateral" | "debt" };

// This might seem backwards, but this way we avoid spamming the .d.ts and generated docs
export type InvalidDenCreation = Extract<DenChange<never>, { type: "invalidCreation" }>;
export type DenCreation<T> = Extract<DenChange<T>, { type: "creation" }>;
export type DenClosure<T> = Extract<DenChange<T>, { type: "closure" }>;
export type DenAdjustment<T> = Extract<DenChange<T>, { type: "adjustment" }>;
export type AllowedKey<T> = Exclude<
  {
    [P in keyof T]: T[P] extends undefined ? never : P;
  }[keyof T],
  undefined
>;
export type UserDenStatus = "nonExistent" | "open" | "closedByOwner" | "closedByLiquidation" | "closedByRedemption";

export enum BackendDenStatus {
  nonExistent,
  active,
  closedByOwner,
  closedByLiquidation,
  closedByRedemption,
}

export type BackendDens = {
  owner: `0x${string}`;
  debt: bigint;
  coll: bigint;
  stake: bigint;
  snapshotCollateral: bigint;
  snapshotDebt: bigint;
}[];
export interface BorrowingOperationOptionalParams {
  /**
   * Maximum acceptable BorrowRate
   * (default: current borrowing rate plus 0.5%).
   */
  maxBorrowingRate?: bigint;

  /**
   * Control the amount of extra gas included attached to the transaction.
   *
   * @remarks
   * Transactions that borrow NECT must pay a variable borrowing fee, which is added to the Den's
   * debt. This fee increases whenever a redemption occurs, and otherwise decays exponentially.
   * Due to this decay, a Den's collateral ratio can end up being higher than initially calculated
   * if the transaction is pending for a long time. When this happens, the backend has to iterate
   * over the sorted list of Dens to find a new position for the Den, which costs extra gas.
   *
   * The SDK can estimate how much the gas costs of the transaction may increase due to this decay,
   * and can include additional gas to ensure that it will still succeed, even if it ends up pending
   * for a relatively long time. This parameter specifies the length of time that should be covered
   * by the extra gas.
   *
   * Default: 10 minutes.
   */
  borrowingFeeDecayToleranceMinutes?: bigint;
}
