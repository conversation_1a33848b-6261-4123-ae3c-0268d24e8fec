import { panic } from "../utils";
import { Den, DenWithPendingRedistribution } from "./Den";
import { BackendDenStatus, BackendDens, UserDenStatus } from "./types";

export const convertBigIntToDate = (timestamp: bigint) => new Date(Number(timestamp.toString()));
export const userDenStatusFrom = (backendStatus: BackendDenStatus): UserDenStatus =>
  backendStatus === BackendDenStatus.nonExistent
    ? "nonExistent"
    : backendStatus === BackendDenStatus.active
      ? "open"
      : backendStatus === BackendDenStatus.closedByOwner
        ? "closedByOwner"
        : backendStatus === BackendDenStatus.closedByLiquidation
          ? "closedByLiquidation"
          : backendStatus === BackendDenStatus.closedByRedemption
            ? "closedByRedemption"
            : panic(new Error(`invalid backendStatus ${typeof backendStatus}`));

export const mapBackendDens = (Dens: BackendDens): DenWithPendingRedistribution[] =>
  Dens.map(({ owner, debt, coll, stake, snapshotCollateral, snapshotDebt }) => {
    return new DenWithPendingRedistribution(
      owner,
      "open", // These Dens are coming from the SortedDens list, so they must be open
      coll,
      debt,
      stake,
      new Den(snapshotCollateral, snapshotDebt)
    );
  });
