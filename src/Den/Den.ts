import { MINIMUM_BORROWING_MINT_RATE, SCALING_FACTOR } from "../constants";
import {
  DenAdjustmentParams,
  DenClosure,
  DenClosureParams,
  DenCreation,
  DenCreationParams,
  DenAdjustment,
  AllowedKey,
  _CollateralChange,
  _DebtChange,
  DenC<PERSON><PERSON>,
  UserDenStatus,
} from "./types";
import { assertCheck } from "../utils";

// const invalidDenCreation = (invalidDen: Den, error: DenCreationError): InvalidDenCreation => ({
//   type: "invalidCreation",
//   invalidDen,
//   error,
// });

const denCreation = <T>(params: DenCreationParams<T>): DenCreation<T> => ({
  type: "creation",
  params,
});

const denClosure = <T>(params: DenClosureParams<T>): DenClosure<T> => ({
  type: "closure",
  params,
});

const denAdjustment = <T>(params: DenAdjustmentParams<T>, setToZero?: "collateral" | "debt"): DenAdjustment<T> => ({
  type: "adjustment",
  params,
  setToZero,
});

const valueIsDefined = <T>(entry: [string, T | undefined]): entry is [string, T] => entry[1] !== undefined;

const allowedDenCreationKeys: AllowedKey<DenCreationParams>[] = ["depositCollateral", "borrowNECT"];

function checkAllowedDenCreationKeys<T>(entries: [string, T][]): asserts entries is [AllowedKey<DenCreationParams>, T][] {
  const badKeys = entries.filter(([k]) => !(allowedDenCreationKeys as string[]).includes(k)).map(([k]) => `'${k}'`);

  if (badKeys.length > 0) {
    throw new Error(`DenCreationParams: property ${badKeys.join(", ")} not allowed`);
  }
}

const denCreationParamsFromEntries = <T>(entries: [AllowedKey<DenCreationParams>, T][]): DenCreationParams<T> => {
  const params = Object.fromEntries(entries) as Record<AllowedKey<DenCreationParams>, T>;
  const missingKeys = allowedDenCreationKeys.filter((k) => !(k in params)).map((k) => `'${k}'`);

  if (missingKeys.length > 0) {
    throw new Error(`DenCreationParams: property ${missingKeys.join(", ")} missing`);
  }

  return params;
};

const nonZero = <T>([, v]: [T, bigint]): boolean => !(v === 0n);

export const _normalizeDenCreation = (params: Record<string, bigint | undefined>): DenCreationParams<bigint> => {
  const definedEntries = Object.entries(params).filter(valueIsDefined);
  checkAllowedDenCreationKeys(definedEntries);
  const nonZeroEntries = definedEntries;

  return denCreationParamsFromEntries(nonZeroEntries);
};

const allowedDenAdjustmentKeys: AllowedKey<DenAdjustmentParams>[] = ["depositCollateral", "withdrawCollateral", "borrowNECT", "repayNECT"];

function checkAllowedDenAdjustmentKeys<T>(entries: [string, T][]): asserts entries is [AllowedKey<DenAdjustmentParams>, T][] {
  const badKeys = entries.filter(([k]) => !(allowedDenAdjustmentKeys as string[]).includes(k)).map(([k]) => `'${k}'`);

  if (badKeys.length > 0) {
    throw new Error(`DenAdjustmentParams: property ${badKeys.join(", ")} not allowed`);
  }
}

const collateralChangeFrom = <T>({ depositCollateral, withdrawCollateral }: Partial<Record<AllowedKey<DenAdjustmentParams>, T>>): _CollateralChange<T> | undefined => {
  if (depositCollateral !== undefined && withdrawCollateral !== undefined) {
    throw new Error("DenAdjustmentParams: 'depositCollateral' and 'withdrawCollateral' " + "can't be present at the same time");
  }

  if (depositCollateral !== undefined) {
    return { depositCollateral };
  }

  if (withdrawCollateral !== undefined) {
    return { withdrawCollateral };
  }
};

const debtChangeFrom = <T>({ borrowNECT, repayNECT }: Partial<Record<AllowedKey<DenAdjustmentParams>, T>>): _DebtChange<T> | undefined => {
  if (borrowNECT !== undefined && repayNECT !== undefined) {
    throw new Error("DenAdjustmentParams: 'borrowNECT' and 'repayNECT' can't be present at the same time");
  }

  if (borrowNECT !== undefined) {
    return { borrowNECT };
  }

  if (repayNECT !== undefined) {
    return { repayNECT };
  }
};

const denAdjustmentParamsFromEntries = <T>(entries: [AllowedKey<DenAdjustmentParams>, T][]): DenAdjustmentParams<T> => {
  const params = Object.fromEntries(entries) as Partial<Record<AllowedKey<DenAdjustmentParams>, T>>;

  const collateralChange = collateralChangeFrom(params);
  const debtChange = debtChangeFrom(params);

  if (collateralChange !== undefined && debtChange !== undefined) {
    return { ...collateralChange, ...debtChange };
  }

  if (collateralChange !== undefined) {
    return collateralChange;
  }

  if (debtChange !== undefined) {
    return debtChange;
  }

  throw new Error("DenAdjustmentParams: must include at least one non-zero parameter");
};

export const _normalizeDenAdjustment = (params: Record<string, bigint | undefined>): DenAdjustmentParams<bigint> => {
  const definedEntries = Object.entries(params).filter(valueIsDefined);
  checkAllowedDenAdjustmentKeys(definedEntries);
  const nonZeroEntries = definedEntries.filter(nonZero);

  return denAdjustmentParamsFromEntries(nonZeroEntries);
};

const applyFee = (borrowingRate: bigint, debtIncrease: bigint) => debtIncrease + (debtIncrease * borrowingRate) / SCALING_FACTOR;

const unapplyFee = (borrowingRate: bigint, newDebt: bigint): bigint => (newDebt * SCALING_FACTOR) / SCALING_FACTOR + borrowingRate;

/**
 * A combination of collateral and debt.
 *
 * @public
 */
export class Den {
  /** Amount of native currency (e.g. iBGT) collateralized. */
  readonly collateral: bigint;

  /** Amount of Debt owed. */
  readonly debt: bigint;
  /** Amount of leverage . */
  readonly leverage?: bigint;

  constructor(collateral = 0n, debt = 0n, leverage?: bigint) {
    this.collateral = collateral;
    this.debt = debt;
    this.leverage = leverage;
  }

  get isEmpty(): boolean {
    return this.collateral === 0n && this.debt === 0n;
  }

  /**
   * Amount of Debt that must be repaid to close this Den.
   *
   * @remarks
   * This doesn't include the liquidation reserve, which is refunded in case of normal closure.
   */
  netDebt = (NECT_LIQUIDATION_RESERVE: bigint): bigint => {
    if (this.debt < NECT_LIQUIDATION_RESERVE) {
      return 0n;
    }

    return this.debt - NECT_LIQUIDATION_RESERVE;
  };

  get _nominalCollateralRatio(): bigint {
    const NICR_PRECISION = 100000000000000000000n; //1e20;
    if (this.debt === 0n) return 0n;
    return (this.collateral * NICR_PRECISION) / this.debt;
  }

  /** Calculate the Den's collateralization ratio at a given price. */
  collateralRatio(price: bigint, shareRatio = SCALING_FACTOR): bigint {
    if (this.collateral === 0n) return 0n;
    if (this.debt === 0n) return 0n;
    return (this.getCollateralShares(shareRatio) * price) / this.debt;
  }

  /** Calculate the Den's LTV at a given price. */
  ltv(price: bigint, shareRatio = SCALING_FACTOR): bigint {
    if (this.collateral === 0n) return 0n;
    if (this.debt === 0n) return 0n;
    if (price === 0n) return 0n;
    if (shareRatio === 0n) return 0n;
    const collateralValue = (this.getCollateralShares(shareRatio) * price) / SCALING_FACTOR;
    return (this.debt * SCALING_FACTOR) / collateralValue;
  }
  /**
   * Whether the Den is undercollateralized at a given price.
   *
   * @returns
   * `true` if the Den's collateralization ratio is less than the
   */
  collateralRatioIsBelowMinimum(price: bigint, mcr: bigint, shareRatio = SCALING_FACTOR): boolean {
    return this.collateralRatio(price, shareRatio) <= mcr;
  }

  /**
   * Whether the collateralization ratio is less than the  CRITICAL COLLATERAL RATIO at a
   * given price.
   *
   */
  collateralRatioIsBelowCritical(price: bigint, criticalCollateralRatio: bigint, shareRatio = SCALING_FACTOR): boolean {
    return this.collateralRatio(price, shareRatio) < criticalCollateralRatio;
  }

  toString(): string {
    return `{ collateral: ${this.collateral}, debt: ${this.debt} }`;
  }

  equals(that: Den): boolean {
    return this.collateral === that.collateral && this.debt === that.debt;
  }

  add(that: Den): Den {
    return new Den(this.collateral + that.collateral, this.debt + that.debt);
  }

  addCollateral(collateral: bigint): Den {
    return new Den(this.collateral + collateral, this.debt);
  }

  addDebt(debt: bigint): Den {
    return new Den(this.collateral, this.debt + debt);
  }

  subtract(that: Den): Den {
    const { collateral, debt } = that;

    return new Den(this.collateral > collateral ? this.collateral - collateral : 0n, this.debt > debt ? this.debt - debt : 0n);
  }

  subtractCollateral(collateral: bigint): Den {
    return new Den(this.collateral > collateral ? this.collateral - collateral : 0n, this.debt);
  }

  subtractDebt(debt: bigint): Den {
    return new Den(this.collateral, this.debt > debt ? this.debt - debt : 0n);
  }

  multiply(multiplier: bigint): Den {
    return new Den(this.collateral * multiplier, this.debt * multiplier);
  }

  setCollateral(collateral: bigint): Den {
    return new Den(collateral, this.debt);
  }

  setDebt(debt: bigint): Den {
    return new Den(this.collateral, debt);
  }
  getCollateralShares(shareRatio = SCALING_FACTOR, collateral?: bigint) {
    collateral ??= this.collateral;
    return (collateral * shareRatio) / SCALING_FACTOR;
  }
  convertCollateralToCollateralShares(shareRatio = SCALING_FACTOR) {
    return new Den((this.collateral * shareRatio) / SCALING_FACTOR, this.debt);
  }
  calculateDebt(collateral: bigint, collateralPrice: bigint, shareRatio = SCALING_FACTOR, collateralRatio?: bigint, getFees?: (debt: bigint) => bigint): bigint {
    if (collateral === 0n) return 0n;
    const collateralShares = (collateral * shareRatio) / SCALING_FACTOR;
    if (collateralRatio == undefined) {
      collateralRatio = this.collateralRatio(collateralPrice, shareRatio);
    }
    if (collateralRatio === 0n) return 0n;
    const preliminaryDebt = (collateralShares * collateralPrice) / collateralRatio;
    if (getFees) {
      const borrowFees = getFees(preliminaryDebt - this.debt);
      return (collateralShares * collateralPrice) / collateralRatio - borrowFees;
    }

    return preliminaryDebt;
  }

  calculateCollateral(debt: bigint, collateralPrice: bigint, shareRatio = SCALING_FACTOR, collateralRatio?: bigint): bigint {
    if (debt === 0n) return 0n;
    if (collateralRatio == undefined) {
      collateralRatio = this.collateralRatio(collateralPrice, shareRatio);
    }
    return (((debt * collateralRatio) / collateralPrice) * SCALING_FACTOR) / shareRatio;
  }

  calculateLiquidationPrice(mcr: bigint): bigint {
    if (this.collateral === 0n) return 0n;

    return (mcr * this.debt) / this.collateral;
  }

  getAdjustVariables(targetDen: Den): DenAdjustmentParams<bigint> {
    return {
      depositCollateral: this.collateral < targetDen.collateral ? targetDen.collateral - this.collateral : undefined,
      withdrawCollateral: this.collateral > targetDen.collateral ? this.collateral - targetDen.collateral : undefined,
      repayNECT: this.debt > targetDen.debt ? this.debt - targetDen.debt : undefined,
      borrowNECT: this.debt < targetDen.debt ? targetDen.debt - this.debt : undefined,
    } as DenAdjustmentParams<bigint>;
  }
  getDepositVariables(): DenAdjustmentParams<bigint> {
    return {
      depositCollateral: this.collateral,
      borrowNECT: this.debt,
    };
  }
  getWithdrawVariables(): DenAdjustmentParams<bigint> {
    return {
      withdrawCollateral: this.collateral,
      repayNECT: this.debt,
    };
  }

  //Leverage functions
  get leveragedCollateral() {
    return (this.collateral * (this.leverage ?? SCALING_FACTOR)) / SCALING_FACTOR;
  }
  getFlashLoanFee(flashLoanFeeRate: bigint, collateral?: bigint, leverage?: bigint): bigint {
    leverage ??= this.leverage;
    collateral ??= this.collateral;

    if (leverage === undefined) {
      return 0n;
    }
    const collAssetsToDeposit = (collateral * leverage) / SCALING_FACTOR;
    return (collAssetsToDeposit * flashLoanFeeRate) / SCALING_FACTOR;
  }
  calculateLeverage(ratio: bigint): bigint {
    const ltv = (SCALING_FACTOR * SCALING_FACTOR) / ratio;
    return (SCALING_FACTOR * SCALING_FACTOR) / (SCALING_FACTOR - ltv);
  }

  calculateLeveragedCollateral = (collateral?: bigint, leverage?: bigint) => {
    leverage ??= this.leverage ?? SCALING_FACTOR;
    collateral ??= this.collateral;

    return (collateral * leverage) / SCALING_FACTOR;
  };
  calculateLeveragedDebt(marginInShares: bigint, leverage: bigint, borrowingRate: bigint, collateralPrice: bigint, debtPrice: bigint): bigint {
    const ratio = this.getRatioFromLeverage(leverage);
    const collVaultNectPrice = (collateralPrice * SCALING_FACTOR) / debtPrice;
    const numerator = marginInShares * collVaultNectPrice * leverage * SCALING_FACTOR;
    const denominator = ratio * SCALING_FACTOR * (SCALING_FACTOR - borrowingRate);
    return numerator / denominator;
  }

  getRatioFromLeverage = (leverage: bigint): bigint => {
    const numerator = leverage * SCALING_FACTOR * SCALING_FACTOR;
    const denominator = leverage * SCALING_FACTOR - SCALING_FACTOR * SCALING_FACTOR;
    return numerator / denominator;
  };

  applyLeverage = (marginInShares: bigint, borrowingRate: bigint, collateralPrice: bigint, debtPrice: bigint, collateral?: bigint, leverage?: bigint): Den => {
    leverage ??= this.leverage;
    collateral ??= this.collateral;
    if (leverage === undefined) {
      return new Den(this.collateral, this.debt, leverage);
    }
    const collAssetsToDeposit = this.calculateLeveragedCollateral(collateral, leverage);
    return new Den(collAssetsToDeposit, this.calculateLeveragedDebt(marginInShares, leverage, borrowingRate, collateralPrice, debtPrice));
  };

  private _debtChange({ debt }: Den, borrowingRate: bigint): _DebtChange<bigint> {
    return debt > this.debt ? { borrowNECT: unapplyFee(borrowingRate, debt - this.debt) } : { repayNECT: this.debt - debt };
  }

  private _collateralChange({ collateral }: Den): _CollateralChange<bigint> {
    return collateral > this.collateral ? { depositCollateral: collateral - this.collateral } : { withdrawCollateral: this.collateral - collateral };
  }

  /**
   * Calculate the difference between this Den and another.
   *
   * @param that - The other Den.
   * @param borrowingRate - Borrowing rate to use when calculating a borrowed amount.
   * @param liquidationReserve - amount Reserved for liquidation
   *
   * @returns
   * An object representing the change, or `undefined` if the Dens are equal.
   */
  whatChanged(that: Den, liquidationReserve: bigint, borrowingRate: bigint = MINIMUM_BORROWING_MINT_RATE): DenChange<bigint> | undefined {
    if (this.collateral === that.collateral && this.debt === that.debt) {
      return undefined;
    }

    if (this.isEmpty) {
      return denCreation({
        depositCollateral: that.collateral,
        borrowNECT: unapplyFee(borrowingRate, that.netDebt(liquidationReserve)),
      });
    }

    if (that.isEmpty) {
      return denClosure(
        this.netDebt(liquidationReserve) !== 0n ? { withdrawCollateral: this.collateral, repayNECT: this.netDebt(liquidationReserve) } : { withdrawCollateral: this.collateral }
      );
    }

    return this.collateral === that.collateral
      ? denAdjustment<bigint>(this._debtChange(that, borrowingRate), that.debt === 0n ? "debt" : undefined)
      : this.debt === that.debt
        ? denAdjustment<bigint>(this._collateralChange(that), that.collateral === 0n ? "collateral" : undefined)
        : denAdjustment<bigint>(
            {
              ...this._debtChange(that, borrowingRate),
              ...this._collateralChange(that),
            },
            (that.debt === 0n ? "debt" : undefined) ?? (that.collateral === 0n ? "collateral" : undefined)
          );
  }

  /**
   * Make a new Den by applying a {@link DenChange} to this Den.
   *
   * @param change - The change to apply.
   * @param borrowingRate - Borrowing rate to use when adding a borrowed amount to the Den's debt.
   */
  apply(change: DenChange<bigint> | undefined, borrowingRate: bigint = MINIMUM_BORROWING_MINT_RATE, liquidationReserve?: bigint): Den {
    if (!change) {
      return this;
    }

    switch (change.type) {
      case "invalidCreation":
        if (!this.isEmpty) {
          throw new Error("Can't create onto existing Den");
        }

        return change.invalidDen;

      case "creation": {
        if (!this.isEmpty) {
          throw new Error("Can't create onto existing Den");
        }
        if (liquidationReserve === undefined) {
          throw new Error("Can't create  Den without Liquidation reserve");
        }
        const { depositCollateral, borrowNECT } = change.params;

        return new Den(depositCollateral, liquidationReserve + applyFee(borrowingRate, borrowNECT));
      }

      case "closure":
        if (this.isEmpty) {
          throw new Error("Can't close empty Den");
        }

        return _emptyDen;

      case "adjustment": {
        const {
          setToZero,
          params: { depositCollateral, withdrawCollateral, borrowNECT, repayNECT },
        } = change;

        const collateralDecrease = withdrawCollateral ?? 0n;
        const collateralIncrease = depositCollateral ?? 0n;
        const debtDecrease = repayNECT ?? 0n;
        const debtIncrease = borrowNECT ? applyFee(borrowingRate, borrowNECT) : 0n;

        return setToZero === "collateral"
          ? this.setCollateral(0n).addDebt(debtIncrease).subtractDebt(debtDecrease)
          : setToZero === "debt"
            ? this.setDebt(0n).addCollateral(collateralIncrease).subtractCollateral(collateralDecrease)
            : this.add(new Den(collateralIncrease, debtIncrease)).subtract(new Den(collateralDecrease, debtDecrease));
      }
    }
  }

  /**
   * Calculate the result of an  openDen transaction.
   *
   * @param params - Parameters of the transaction.
   * @param borrowingRate - Borrowing rate to use when calculating the Den's debt.
   */
  static create(params: DenCreationParams<bigint>, liquidationReserve: bigint, borrowingRate?: bigint): Den {
    return _emptyDen.apply(denCreation(_normalizeDenCreation(params)), borrowingRate, liquidationReserve);
  }

  /**
   * Calculate the parameters of an  openDen transaction
   * that will result in the given Den.
   *
   * @param that - The Den to recreate.
   * @param borrowingRate - Current borrowing rate.
   * @param liquidationReserve - amount Reserved for liquidation
   */
  static recreate(that: Den, liquidationReserve: bigint, borrowingRate?: bigint): DenCreationParams<bigint> | undefined {
    const change = _emptyDen.whatChanged(that, liquidationReserve, borrowingRate);
    assertCheck(change?.type === "creation", "change must be of creation");
    return change?.type === "creation" ? change.params : undefined;
  }

  /**
   * Calculate the result of an  adjustDen transaction
   * on this Den.
   *
   * @param params - Parameters of the transaction.
   * @param borrowingRate - Borrowing rate to use when adding to the Den's debt.
   */
  adjust(params: DenAdjustmentParams<bigint>, borrowingRate?: bigint): Den {
    return this.apply(denAdjustment(_normalizeDenAdjustment(params)), borrowingRate);
  }

  /**
   * Calculate the parameters of an  adjustDen
   * transaction that will change this Den into the given Den.
   *
   * @param that - The desired result of the transaction.
   * @param borrowingRate - Current borrowing rate.
   * @param liquidationReserve - amount Reserved for liquidation
   */
  adjustTo(that: Den, liquidationReserve: bigint, borrowingRate?: bigint): DenAdjustmentParams<bigint> | undefined {
    const change = this.whatChanged(that, liquidationReserve, borrowingRate);
    assertCheck(change?.type === "adjustment", "change must be of type Adjustment");
    return change?.type === "adjustment" ? change.params : undefined;
  }
}

export const _emptyDen = new Den();

/**
 * Represents whether a UserDen is open or not, or why it was closed.
 *
 * @public
 */

/**
 * A Den that is associated with a single owner.
 *
 * @remarks
 * The SDK uses the base Den class as a generic container of collateral and debt, for
 * example to represent the total collateral and debt locked up
 * in the protocol.
 *
 * The `UserDen` class extends `Den` with extra information that's only available for Dens
 * that are associated with a single owner (such as the owner's address, or the Den's status).
 *
 * @public
 */
export class UserDen extends Den {
  /** Address that owns this Den. */
  readonly ownerAddress: `0x${string}`;

  /** Provides more information when the UserDen is empty. */
  readonly status: UserDenStatus;

  constructor(ownerAddress: `0x${string}`, status: UserDenStatus, collateral?: bigint, debt?: bigint) {
    super(collateral, debt);

    this.ownerAddress = ownerAddress;
    this.status = status;
  }

  equals(that: UserDen): boolean {
    return super.equals(that) && this.ownerAddress === that.ownerAddress && this.status === that.status;
  }

  toString(): string {
    return `{ ownerAddress: "${this.ownerAddress}"` + `, collateral: ${this.collateral}` + `, debt: ${this.debt}` + `, status: "${this.status}" }`;
  }
}

/**
 * A Den in its state after the last direct modification.
 *
 * @remarks
 * The Den may have received collateral and debt shares from liquidations since then.
 * Use applyRedistribution() to
 * calculate the Den's most up-to-date state.
 *
 * @public
 */
export class DenWithPendingRedistribution extends UserDen {
  private readonly stake: bigint;
  private readonly snapshotOfTotalRedistributed: Den;

  constructor(ownerAddress: `0x${string}`, status: UserDenStatus, collateral?: bigint, debt?: bigint, stake = 0n, snapshotOfTotalRedistributed = _emptyDen) {
    super(ownerAddress, status, collateral, debt);

    this.stake = stake;
    this.snapshotOfTotalRedistributed = snapshotOfTotalRedistributed;
  }

  applyRedistribution(totalRedistributed: Den): UserDen {
    const afterRedistribution = this.add(totalRedistributed.subtract(this.snapshotOfTotalRedistributed).multiply(this.stake));

    return new UserDen(this.ownerAddress, this.status, afterRedistribution.collateral, afterRedistribution.debt);
  }

  equals(that: DenWithPendingRedistribution): boolean {
    return super.equals(that) && this.stake === that.stake && this.snapshotOfTotalRedistributed.equals(that.snapshotOfTotalRedistributed);
  }
}
