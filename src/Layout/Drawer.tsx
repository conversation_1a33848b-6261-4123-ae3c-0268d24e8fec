import Box from "@mui/material/Box";
import { routerPaths } from "../Routes/routes";
import { Link } from "react-router-dom";
import { Typography, useTheme, useMediaQuery } from "@mui/material";
import { useSetAtom, useAtomValue } from "jotai";
import { navigationAtom } from "../Atoms/Account";
import { getCollateralDetailsAtom } from "../Atoms/Tokens";
import { NavigationOptions } from "../@type/Account";
export default function Drawer() {
  const theme = useTheme();
  const isLgUp = useMediaQuery(theme.breakpoints.up("lg"));
  const setNavigation = useSetAtom(navigationAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const handleMouseEnter = (id: NavigationOptions) => {
    isLgUp && setNavigation(id);
  };
  const handleMouseLeave = () => {
    isLgUp && setNavigation("default");
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", width: "100%" }}>
      {routerPaths.map((item) => (
        <Link
          to={item.path + (item.ticker ? `/${collateralDetails.ticker}` : "")}
          key={item.id}
          style={{ width: "100%", padding: "8px", textDecoration: "unset !important" }}
          onMouseEnter={() => handleMouseEnter(item.id)}
          onMouseLeave={handleMouseLeave}
        >
          <Box
            sx={{
              display: "flex",
              p: { xs: 1, sm: 1.5, xl: 2 },
              borderRadius: 4,
              gap: 2,
              alignItems: "center",
              width: "100%",
              background: "#00000077",
              ...(isLgUp
                ? {
                    "&:hover": {
                      background: "var(--primary-main)",
                      color: "var(--text-primary)",
                    },
                    "&:hover img": {
                      opacity: 0.8,
                    },
                    "&:hover .hover-text": {
                      color: "white ",
                      textDecoration: "unset",
                    },
                  }
                : {}),
            }}
          >
            <img src={item.icon} alt="icon" width={64} height={64} />
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              <Typography className="hover-text" variant="h5" sx={{ lineHeight: "inherit", fontSize: 20, color: "var(--text-primary)" }}>
                {item.label}
              </Typography>
              <Typography className="hover-text" variant="subtitle1" sx={{ lineHeight: "inherit", fontSize: 14, color: "var(--text-secondary)" }}>
                {item.description}
              </Typography>
            </Box>
          </Box>
        </Link>
      ))}
    </Box>
  );
}
