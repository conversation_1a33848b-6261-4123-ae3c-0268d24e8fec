import React, { Suspense } from "react";
import { Outlet } from "react-router-dom";
import Footer from "./Footer";
import { Box } from "@mui/material";
import { NotificationProvider } from "../providers/NotificationsProvider";
import { TransactionProvider } from "../providers/TransactionProvider";
import Navbar from "./Navbar";
import { useBackgroundImage } from "../Hooks/useBackgroundImage";
import { usePostHog } from "../Hooks/usePostHog";
import { useServiceWorker } from "../Hooks/useServiceWorker";

const OutletProvider: React.FC = () => {
  const { backgroundPadding, backgroundSxProps, justifyContent, backgroundColor } = useBackgroundImage();
  useServiceWorker();
  usePostHog();
  const FOOTER_HEIGHT = 52;
  const HEADER_HEIGHT = 106;
  const RECOVERY_BANNER_HEIGHT = 40;
  const heightOffset = RECOVERY_BANNER_HEIGHT + HEADER_HEIGHT + FOOTER_HEIGHT;
  return (
    <TransactionProvider>
      <NotificationProvider>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            minHeight: "100vh",
            backgroundRepeat: "no-repeat",
            backgroundColor: backgroundColor,
            ...backgroundSxProps,
          }}
        >
          <Navbar />
          <Box
            flex={1}
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: justifyContent, // Center children vertically
              alignItems: "center", // Center children horizontally
              minHeight: { lg: `calc(100vh - ${heightOffset}px)` }, // Adjust this based on your navbar height
            }}
            component="main"
          >
            <Box pt={backgroundPadding} width="100%">
              <Suspense>
                <Outlet context={{}} />
              </Suspense>
            </Box>
          </Box>
          <Footer />
        </Box>
      </NotificationProvider>
    </TransactionProvider>
  );
};

export default OutletProvider;
