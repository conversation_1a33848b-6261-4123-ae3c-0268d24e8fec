import { Box, Container, <PERSON>, Typography, useTheme } from "@mui/material";
import { StatusBar } from "../components/Banners/StatusBar";
const Footer: React.FC = () => {
  const theme = useTheme();
  const socials = [
    {
      icon: "/icons/x.png",
      link: "https://twitter.com/beraborrow",
    },
    {
      icon: "/icons/discord.png",
      link: "https://discord.com/invite/beraborrowofficial",
    },
    {
      icon: "/icons/documentation.png",
      link: "https://beraborrow.gitbook.io/docs",
    },
  ];
  return (
    <Box sx={{ background: theme.palette.primary.main, py: "10px" }}>
      <Container sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", px: { lg: 8, xl: 15 } }} maxWidth={false}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {socials.map((social) => (
            <Link href={social.link} target="_blank" key={social.link} sx={{ borderRadius: "50%", background: "var(--secondary-main)", p: 1, width: 32, height: 32 }}>
              <img src={social.icon} width={"100%"} height={"100%"} />
            </Link>
          ))}
        </Box>
        <Box sx={{ display: { xs: "none", sm: "none", lg: "flex" } }}>
          <StatusBar />
        </Box>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography fontSize={12} color={"var(--text-primary)"}>
            © Beraborrow 2024
          </Typography>
          <Link target="_blank" href="https://www.beraborrow.com/legal">
            <Typography
              component="span"
              sx={{
                pl: 1,
                fontWeight: "bold",
                textDecoration: "underline",
                fontSize: 12,
                color: "var(--text-primary)",
              }}
            >
              Legal
            </Typography>
          </Link>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
