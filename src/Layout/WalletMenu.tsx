import { <PERSON><PERSON>, <PERSON>u, <PERSON>uI<PERSON>, Typography } from "@mui/material";
import { useState, useEffect } from "react";
import { Avatar } from "../components/Avatar";
import LoginIcon from "@mui/icons-material/Login";
import { useAccount, useDisconnect } from "wagmi";
import { ChevronRight, ExpandLess, ExpandMore } from "@mui/icons-material";
import { truncateAddress } from "../utils/helpers";
import { useAppKit } from "@reown/appkit/react";
const Component: React.FC = () => {
  const { disconnect } = useDisconnect();
  const { address } = useAccount();
  const { open } = useAppKit();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const isOpen = Boolean(anchorEl);
  const [buttonWidth, setButtonWidth] = useState<number>(0);
  // Use useEffect to set the button width when the component is rendered or when the button is clicked
  useEffect(() => {
    if (anchorEl) {
      setButtonWidth(anchorEl.clientWidth);
    }
  }, [anchorEl]); // Update the width when anchorEl changes
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleDisconnect = () => {
    disconnect();
  };
  const handleAccount = () => {
    open({ view: "Account" });
    setAnchorEl(null);
  };
  return (
    <>
      <Button
        id="basic-button"
        aria-controls={isOpen ? "basic-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={isOpen ? "true" : undefined}
        onClick={handleClick}
        variant="outlined"
        sx={{
          alignItems: "center",
          color: "white",
          px: { xs: "8px", sm: 2 },
          py: { xs: "8px", sm: "12px" },
          gap: 1,
          border: "1px solid var(--border-color)",
          borderRadius: 5,
          background: "var(--background-default)",
          backdropFilter: "blur(5px)",
        }}
      >
        <Avatar width={32} height={32} />
        <Typography
          sx={{
            fontSize: { xs: 14, sm: 16 },
            display: "flex",
            alignItems: "center", // Vertically align content
            justifyContent: "space-between", // Align icon and text

            width: "100%",
          }}
        >
          {truncateAddress(address, 4)} {!isOpen ? <ExpandMore /> : <ExpandLess />}
        </Typography>
      </Button>

      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={isOpen}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
        slotProps={{
          paper: {
            sx: { width: buttonWidth ? buttonWidth : "auto" }, // Set menu width equal to button width
          },
        }}
      >
        <MenuItem
          onClick={handleAccount}
          sx={{
            display: "flex",
            alignItems: "center", // Vertically align content
            justifyContent: "space-between", // Align icon and text
            borderRadius: "14px",
            border: "1px solid var(--Primary-Background-Fill, rgba(130, 106, 95, 0.25))",
            backdropFilter: "blur(15px)",
            width: "100%", // Make sure it matches the button's width
          }}
        >
          <Typography variant="h3">View Account</Typography>
          <ChevronRight />
        </MenuItem>

        <MenuItem
          onClick={handleDisconnect}
          sx={{
            display: "flex",
            alignItems: "center", // Vertically align content
            justifyContent: "space-between", // Align icon and text
            borderRadius: "14px",
            border: "1px solid var(--Primary-Background-Fill, rgba(130, 106, 95, 0.25))",
            background:
              "linear-gradient(0deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.50) 100%), linear-gradient(172deg, rgba(67, 55, 49, 0.75) 6.08%, rgba(39, 32, 29, 0.38) 93.92%)",
            backdropFilter: "blur(14.700000762939453px)",
            width: "100%", // Make sure it matches the button's width
          }}
        >
          <Typography variant="h3" color="var(--text-secondary)">
            Disconnect
          </Typography>
          <LoginIcon />
        </MenuItem>
      </Menu>
    </>
  );
};
const WalletMenu = Component;
export default WalletMenu;
