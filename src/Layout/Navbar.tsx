import * as React from "react";
import Box from "@mui/material/Box";
import { Drawer as MuiDrawer } from "@mui/material";
import Header from "./Header";
import { StatusBar } from "../components/Banners/StatusBar";
import Drawer from "./Drawer";
import ModeSwitch from "../components/ModeSwitch";
import { RecoverModeBanner } from "../components/Banners/RecoveryMode";
import { PromoBar } from "../components/Banners/PromoBar";
interface Props {
  window?: () => Window;
}

const drawerWidth = "100%";
const drawerHeight = "60vh";

export default function Navbar(props: Props) {
  const { window } = props;
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const handleDrawerToggle = () => {
    setMobileOpen((prevState) => !prevState);
  };

  const container = window !== undefined ? () => window().document.body : undefined;

  return (
    <>
      <PromoBar />
      <Box sx={{ display: { xs: "flex", lg: "none" } }}>
        <StatusBar />
      </Box>

      <RecoverModeBanner />
      <Header handleDrawerToggle={handleDrawerToggle} />
      <nav>
        <MuiDrawer
          container={container}
          variant="temporary"
          anchor="bottom"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: "block", lg: "none" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
              height: drawerHeight,
              borderBottomRightRadius: 0,
              borderBottomLeftRadius: 0,
              backdropFilter: "blur(10px)",
              background: "var(--background-default)",
            },
            "& .MuiBackdrop-root": { opacity: 1 },
          }}
        >
          <Box onClick={handleDrawerToggle} sx={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 1 }}>
            <Drawer />
            <Box sx={{ display: { xs: "flex", md: "none" } }}>
              <ModeSwitch />
            </Box>
          </Box>
        </MuiDrawer>
      </nav>
    </>
  );
}
