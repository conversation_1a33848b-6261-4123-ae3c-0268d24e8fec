import <PERSON>raBorrowLogo from "../components/BeraBorrowLogo";
import { AppBar, Box, Divider, Toolbar, Typography } from "@mui/material";
import { Link, useLocation } from "react-router-dom";
import ModeSwitch from "../components/ModeSwitch";
import { FaucetButton } from "../components/FaucetButton";
import MenuIcon from "@mui/icons-material/Menu";
import IconButton from "@mui/material/IconButton";
import { routerPaths } from "../Routes/routes";
import { useAtomValue } from "jotai";
import { getCollateralDetailsAtom } from "../Atoms/Tokens";
import { simpleModeAtom } from "../Atoms/Account";
import WalletMenu from "./WalletMenu";
import ConnectButton from "../components/ConnectButton/component";

const Header = ({ handleDrawerToggle }: { handleDrawerToggle: () => void }) => {
  const location = useLocation();
  const simpleMode = useAtomValue(simpleModeAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);

  return (
    <AppBar
      component="nav"
      elevation={0}
      sx={{
        transition: "width 0.2s ease-out !important",
        backgroundColor: "transparent",
        backgroundImage: "unset",
        borderRadius: 0,
        position: "sticky",
        top: 0,
        py: { xs: "5px", sm: "5px", md: "8px", xl: "12px" },
      }}
    >
      <Toolbar sx={{ display: "flex", justifyContent: "space-between", py: 1, px: 0 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: { xs: 2, xl: 4 } }}>
          <BeraBorrowLogo label="Beraborrow" sx={{ display: "flex", alignItems: "center" }} />
          <Divider orientation="vertical" flexItem sx={{ borderColor: "white", my: "12px", mx: 1, display: { xs: "none", sm: "none", lg: "flex" } }} />
          <Box
            sx={{
              display: { xs: "none", sm: "none", lg: "flex" },
              gap: 5,
              alignItems: "center",
              px: { xs: "12px", sm: 2 },
              py: { xs: "8px", sm: "12px" },
              borderRadius: 5,
              ...(simpleMode ? {} : { border: "1px solid var(--border-color)", background: "var(--background-default)", backdropFilter: "blur(5px)" }),
            }}
          >
            {routerPaths.map((item) => (
              <Link to={item.path + (item.ticker ? `/${collateralDetails.ticker}` : "")} key={item.path}>
                <Typography
                  variant="h4"
                  sx={{ whiteSpace: "nowrap", lineHeight: "32px", color: "white", fontWeight: 700, opacity: location.pathname.startsWith(item.parentPath ?? item.path) ? 1 : 0.5 }}
                >
                  {item.label}
                </Typography>
              </Link>
            ))}
          </Box>
        </Box>

        <Box sx={{ display: { sm: "none", xs: "flex" }, alignItems: "center", gap: 1 }}>
          <FaucetButton />
          <Box sx={{ gap: "20px", alignItems: "center" }}>
            <ConnectButton sx={{ alignItems: "center", px: 2, py: "12px" }} enforce={true}>
              <WalletMenu />
            </ConnectButton>
          </Box>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Box sx={{ display: { xs: "none", md: "flex" } }}>
            <ModeSwitch />
          </Box>
          <Box sx={{ display: { xs: "none", sm: "flex" } }}>
            <FaucetButton />{" "}
          </Box>
          <Box sx={{ gap: "20px", alignItems: "center", display: { xs: "none", sm: "flex" }, mr: 2 }}>
            <ConnectButton sx={{ alignItems: "center", px: 2, py: "12px" }} enforce={true}>
              <WalletMenu />
            </ConnectButton>
          </Box>
          <IconButton
            disabled={location.pathname === "/"}
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              display: { lg: "none" },
              border: "1px solid var(--border-color)",
              borderRadius: "18px",
              background: "var(--background-default)",
              backdropFilter: "blur(5px)",

              padding: { xs: "10px", sm: "14px" },
            }}
          >
            <MenuIcon />
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
};
export default Header;
