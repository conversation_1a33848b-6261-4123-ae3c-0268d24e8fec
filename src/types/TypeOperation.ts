import { poolOperation, VaultSlotBucketType, VaultSnapshotType } from "./enums";

enum BorrowerOperation {
  openDen,
  closeDen,
  adjustDen,
}

export function getDenOperationFromBorrowerOperation(operation: BorrowerOperation, isLiquidation: boolean = false): string {
  switch (operation) {
    case BorrowerOperation.openDen:
      return "openDen";
    case BorrowerOperation.closeDen:
      return isLiquidation ? "liquidateInNormalMode" : "closeDen";
    case BorrowerOperation.adjustDen:
      return "adjustDen";
  }

  // AssemblyScript can't tell we will never reach this, so it insists on a return statement
  return "unreached";
}

export function isBorrowerOperation(DenOperation: string): boolean {
  return DenOperation == "openDen" || DenOperation == "closeDen" || DenOperation == "adjustDen";
}

enum DenManagerOperation {
  applyPendingRewards,
  liquidateInNormalMode,
  liquidateInRecoveryMode,
  redeemCollateral,
}

export function getDenOperationFromDenManagerOperation(operation: DenManagerOperation): string {
  switch (operation) {
    case DenManagerOperation.applyPendingRewards:
      return "accrueRewards";
    case DenManagerOperation.liquidateInNormalMode:
      return "liquidateInNormalMode";
    case DenManagerOperation.liquidateInRecoveryMode:
      return "liquidateInRecoveryMode";
    case DenManagerOperation.redeemCollateral:
      return "redeemCollateral";
  }

  // AssemblyScript can't tell we will never reach this, so it insists on a return statement
  return "unreached";
}

export function isLiquidation(DenOperation: string): boolean {
  return DenOperation == "liquidateInNormalMode" || DenOperation == "liquidateInRecoveryMode";
}

export function isRecoveryModeLiquidation(DenOperation: string): boolean {
  return DenOperation == "liquidateInRecoveryMode";
}

export function isRedemption(DenOperation: string): boolean {
  return DenOperation == "redeemCollateral";
}

export function getPoolOperation(operation: poolOperation): string {
  switch (operation) {
    case poolOperation.DEPOSIT:
      return "DEPOSIT";
    case poolOperation.WITHDRAW:
      return "WITHDRAW";
    case poolOperation.TRANSFER:
      return "TRANSFER";
  }
  return "TRANSFER";
}

export function getVaultSnapshotType(operation: VaultSnapshotType): string {
  switch (operation) {
    case VaultSnapshotType.EVENT:
      return "EVENT";
    case VaultSnapshotType.TIMED:
      return "TIMED";
    case VaultSnapshotType.REBALANCE:
      return "REBALANCE";
  }
  return "EVENT";
}
export function getVaultSlotBucket(operation: VaultSlotBucketType): string {
  switch (operation) {
    case VaultSlotBucketType.HOURLY:
      return "HOURLY";
    case VaultSlotBucketType.DAILY:
      return "DAILY";
    case VaultSlotBucketType.WEEKLY:
      return "WEEKLY";
  }
  return "WEEKLY";
}
