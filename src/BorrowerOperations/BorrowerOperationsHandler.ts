import { Hex, zeroAddress } from "viem";
import { DenAdjustmentParams, DenCreationParams } from "../Den/types";
import { BeraBorrowConnection, TransactionOptions } from "../types";
import { BorrowerOperations } from "../Contracts/BorrowerOperations/Contract";
import { CollVaultRouter } from "../Contracts/CollVaultRouter/Contract";
import { CollateralVault } from "../Contracts/CollateralVaults/Contract";
import { DEFAULT_SLIPPAGE_TOLERANCE, SCALING_FACTOR } from "../constants";
import { BeraborrowCore } from "../Contracts/BeraborrowCore/Contract";

export class BorrowerOperationsHandler {
  private readonly connection: BeraBorrowConnection;
  readonly borrowerOperations: BorrowerOperations;
  readonly collVaultRouter: CollVaultRouter;
  private readonly protocol: Hex;
  private BeraborrowCore: BeraborrowCore;

  constructor(connection: <PERSON>raBorrowConnection, protocol: Hex) {
    this.connection = connection;
    this.protocol = protocol;
    this.borrowerOperations = new BorrowerOperations(connection, this.protocol);
    this.collVaultRouter = new CollVaultRouter(connection, this.protocol);
    this.BeraborrowCore = new BeraborrowCore(connection, this.protocol);
  }
  /**
   *  * Open a new Den by depositing collateral and borrowing Debt.
   * @param dmAddr Den Manager Address
   * @param borrower Borrowers Address
   * @param params DenCreationParameters
   * @param maxBorrowingRate BorrowingOperationOptionalParameters
   */
  async openDen(_dmAddr: Hex, borrower: Hex, params: DenCreationParams<bigint>, maxBorrowingRate: bigint, overrides?: TransactionOptions) {
    const collVault = this.connection.addresses.denManagers[_dmAddr].vault;
    if (collVault) {
      const native = _dmAddr === zeroAddress;
      const wrappedDenManager = this.connection.addresses.wrappedDenManagers[_dmAddr] as Hex | undefined;
      const wrapperHookAddress = wrappedDenManager && !native ? _dmAddr : undefined;
      const dmAddr = wrappedDenManager ?? _dmAddr; //override dmAddr to user underlining denManager
      const vault = new CollateralVault(this.connection, collVault);
      const SharesMinted = await vault.previewDeposit(params.depositCollateral);
      const minSharesMinted = SharesMinted - (SharesMinted * DEFAULT_SLIPPAGE_TOLERANCE) / SCALING_FACTOR;
      return this.collVaultRouter.openDenVault(dmAddr, collVault, borrower, params, maxBorrowingRate, minSharesMinted, native, wrapperHookAddress, overrides);
    } else {
      return this.borrowerOperations.openDen(_dmAddr, borrower, params, maxBorrowingRate, overrides);
    }
  }
  /**
   * Adjust an existing Den by depositing/withdrawing collateral and/or borrowing Debt.
   * @param dmAddr Den Manager Address
   * @param isDebtIncrease if the new Nect will be minted
   * @param params DenCreationParameters
   * @param maxBorrowingRate Max borrowing rate
   */
  async adjustDen(_dmAddr: Hex, address: Hex, isDebtIncrease: boolean, params: DenAdjustmentParams<bigint>, maxBorrowingRate: bigint, overrides?: TransactionOptions) {
    const collVault = this.connection.addresses.denManagers[_dmAddr].vault;
    if (collVault) {
      const native = _dmAddr === zeroAddress;
      const wrappedDenManager = this.connection.addresses.wrappedDenManagers[_dmAddr] as Hex | undefined;
      const wrapperHookAddress = wrappedDenManager && !native ? _dmAddr : undefined;
      const dmAddr = wrappedDenManager ?? _dmAddr; //override dmAddr to user underlining denManager
      const vault = new CollateralVault(this.connection, collVault);
      const SharesMinted = params.depositCollateral ? await vault.previewDeposit(params.depositCollateral) : 0n;
      const minSharesMinted = SharesMinted ? SharesMinted - (SharesMinted * DEFAULT_SLIPPAGE_TOLERANCE) / SCALING_FACTOR : 0n;
      const sharesBurned = params.withdrawCollateral ? await vault.previewRedeem(params.withdrawCollateral) : 0n;
      const minSharesBurned = sharesBurned ? sharesBurned - (sharesBurned * DEFAULT_SLIPPAGE_TOLERANCE) / SCALING_FACTOR : 0n;
      return this.collVaultRouter.adjustDenVault(
        dmAddr,
        collVault,
        address,
        isDebtIncrease,
        params,
        maxBorrowingRate,
        minSharesMinted,
        minSharesBurned,
        native,
        wrapperHookAddress,
        overrides
      );
    } else {
      return this.borrowerOperations.adjustDen(_dmAddr, address, isDebtIncrease, params, maxBorrowingRate, overrides);
    }
  }

  /**
   * Closes a Den by repaying all outstanding Debt and receiving the Collateral in return
   */
  async closeDen(_dmAddr: Hex, address: Hex, overrides?: TransactionOptions) {
    const collVault = this.connection.addresses.denManagers[_dmAddr].vault;
    const wrappedDenManager = this.connection.addresses.wrappedDenManagers[_dmAddr] as Hex | undefined;
    const dmAddr = wrappedDenManager ?? _dmAddr; //override dmAddr to user underlining denManager
    if (collVault) {
      return this.collVaultRouter.closeDen(dmAddr, collVault, address, 0n, overrides);
    } else {
      return this.borrowerOperations.closeDen(dmAddr, address, overrides);
    }
  }

  getInternalBorrowOperationsAddress(dmAddr: Hex) {
    const collVault = this.connection.addresses.denManagers[dmAddr].vault;
    const wrappedDenManager = this.connection.addresses.denManagers[dmAddr].wrappedCollateral;

    if (collVault) {
      return this.collVaultRouter.contractAddress;
    } else if (wrappedDenManager) {
      //wrappedDenMangers user wrapper as ID
      return dmAddr;
    } else {
      return this.borrowerOperations.contractAddress;
    }
  }
  getTCR() {
    return this.borrowerOperations.getTCR();
  }
  getGlobalSystemBalances() {
    return this.borrowerOperations.getGlobalSystemBalances();
  }
  getBalances() {
    return this.borrowerOperations.getBalances();
  }
  getCCR() {
    return this.BeraborrowCore.getCCR();
  }

  async isApprovedDelegate(dmAddr: Hex, delegatedAddress: Hex, address: Hex, overrides?: TransactionOptions) {
    const collVault = this.connection.addresses.denManagers[dmAddr].vault;
    if (collVault) {
      return this.borrowerOperations.isApprovedDelegate(delegatedAddress, address, overrides);
    } else {
      return true;
    }
  }

  async setDelegateApproval(delegatedAddress: Hex, approval: boolean, overrides?: TransactionOptions) {
    return this.borrowerOperations.setDelegateApproval(delegatedAddress, approval, overrides);
  }
}
