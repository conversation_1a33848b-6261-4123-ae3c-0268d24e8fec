import { BorrowingOperationOptionalParams } from "../Den/types";
import { SCALING_FACTOR } from "../constants";

export const DEFAULT_BORROWING_RATE_SLIPPAGE_TOLERANCE = (SCALING_FACTOR / 1000n) * 5n; // 0.5%
export const DEFAULT_BORROWING_FEE_DECAY_TOLERANCE_MINUTES = 10n;
export const normalizeBorrowingOperationOptionalParams = (
  maxBorrowingRateOrOptionalParams: bigint | BorrowingOperationOptionalParams | undefined,
  currentBorrowingRate: bigint | undefined
): {
  maxBorrowingRate: bigint;
  borrowingFeeDecayToleranceMinutes: bigint;
} => {
  if (maxBorrowingRateOrOptionalParams === undefined) {
    return {
      maxBorrowingRate: (currentBorrowingRate ?? 0n) + (DEFAULT_BORROWING_RATE_SLIPPAGE_TOLERANCE ?? 0n),
      borrowingFeeDecayToleranceMinutes: DEFAULT_BORROWING_FEE_DECAY_TOLERANCE_MINUTES,
    };
  } else if (typeof maxBorrowingRateOrOptionalParams === "string" || typeof maxBorrowingRateOrOptionalParams === "bigint") {
    return {
      maxBorrowingRate: maxBorrowingRateOrOptionalParams,
      borrowingFeeDecayToleranceMinutes: DEFAULT_BORROWING_FEE_DECAY_TOLERANCE_MINUTES,
    };
  } else {
    const { maxBorrowingRate, borrowingFeeDecayToleranceMinutes } = maxBorrowingRateOrOptionalParams;

    return {
      maxBorrowingRate: maxBorrowingRate !== undefined ? maxBorrowingRate : (currentBorrowingRate ?? 0n) + DEFAULT_BORROWING_RATE_SLIPPAGE_TOLERANCE,

      borrowingFeeDecayToleranceMinutes: borrowingFeeDecayToleranceMinutes ?? DEFAULT_BORROWING_FEE_DECAY_TOLERANCE_MINUTES,
    };
  }
};
