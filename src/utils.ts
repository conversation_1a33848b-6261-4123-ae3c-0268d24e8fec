import { isAddress } from "viem";
import { BeraBorrowConnection, TransactionOptions } from "./types";

export const panic = <T>(e: unknown): T => {
  throw e;
};

/** @internal */
export const getBlockTimestamp = (connection: BeraBorrowConnection, blockNumber?: bigint): Promise<bigint> =>
  connection.publicClient.getBlock({ blockNumber }).then((block) => {
    if (block) {
      return block.timestamp;
    }
    throw Error("Block not found");
  });

/** @internal */
export const requireAddress = (connection: BeraBorrowConnection, overrides?: TransactionOptions): `0x${string}` => overrides?.account ?? connection.userAddress;

/** @internal */
export const assertCheck = (value: unknown, message?: string) => {
  if (!value) {
    throw new Error(message || "an Unexpected Error Occurred");
  }
};
/** @internal */
export const assertAddress = (value: string) => {
  if (!isAddress(value, { strict: true })) {
    throw new Error("Invalid Address: " + value);
  }
};
