import { Hex } from "viem";
import { DenAdjustmentParams, DenCreationParams } from "@Beraborrowofficial/sdk";
export type BaseTransactionStates<T> = TransactionError | TransactionSuccess<T> | TransactionPending | TransactionIdle;
export type TransactionStates = BaseTransactionStates<Hex> | TransactionConfirmed;

type TransactionIdle = {
  type: "idle";
  payload?: undefined;
};

type TransactionPending = {
  type: "pending";
  payload?: undefined;
};
type TransactionError = {
  type: "error";
  payload: unknown;
};

type TransactionSuccess<T> = {
  type: "success";
  payload: T;
};
type TransactionConfirmed = {
  type: "confirmed";
  payload: Hex;
};

export type TransactionProps =
  | DepositDebtTransaction
  | WithdrawDebtTransaction
  | WithdrawDebtOnlyTransaction
  | redeemDebtOnlyTransaction
  | ClaimCollateralGainsTransaction
  | OpenDenTransaction
  | ApproveCollateralTransaction
  | ApproveLspTransaction
  | AdjustDenTransaction
  | CloseDenTransaction
  | RedeemDebtTransaction
  | RedeemPsmTransaction
  | ClaimCollateralSurplus
  | ApproveDelegate
  //   | OpenLeverageTransaction
  //   | AdjustLeverageTransaction
  //   | RedeemPreferredUnderlying Managed
  | ApproveVaultTransaction
  | DepositVaultTransaction
  | RedeemVaultTransaction
  | ApproveManagedVaultTransaction
  | DepositManagedVaultTransaction
  | RedeemIntentManagedVaultTransaction
  | ClaimIntentManagedVaultTransaction
  | CancelIntentManagedVaultTransaction
  | WithdrawBoyco
  | WrapPollenTransaction
  | UnwrapPollenTransaction
  | ClaimPollenRewardsTransaction
  | ClaimLpPollenRewardsTransaction
  | StakePollenTransaction
  | UnstakePollenTransaction
  | StakeLpPollenTransaction
  | UnstakeLpPollenTransaction
  | IncreaseLockPositionTransaction
  | IncreaseLpLockPositionTransaction
  | WithdrawVotingEscrowTransaction
  | WithdrawLpVotingEscrowTransaction;

export type DenTransactions = OpenDenTransaction | AdjustDenTransaction; //| OpenLeverageTransaction | AdjustLeverageTransaction;
type DepositDebtTransaction = {
  type: "depositDebt";
  variables: [bigint];
};
export type WithdrawDebtTransaction = {
  type: "withdrawDebt";
  variables: [shares: bigint, assetsWithdrawn: bigint, assets: Hex[], assetsBalance: bigint[], unwrappedLength: bigint];
};
export type WithdrawDebtOnlyTransaction = {
  type: "withdrawDebtOnly";
  variables: [assetsWithdrawn: bigint];
};
export type redeemDebtOnlyTransaction = {
  type: "redeemDebtOnly";
  variables: [sharesRedeemed: bigint];
};
type ClaimCollateralGainsTransaction = {
  type: "claimCollateralGains";
  variables: [recipient: Hex, collateralIndexes: bigint[]];
};

type ApproveCollateralTransaction = {
  type: "approveCollateral";
  variables: [amount: bigint, contractAddress?: Hex];
};
// type OpenLeverageTransaction = {
//   type: "openLeverage";
//   variables: [leverage: bigint, margin: bigint, params: DenCreationParams<bigint>, currentBorrowingRate: bigint, slippage: bigint]; //nativeToken: boolean
// };
// type AdjustLeverageTransaction = {
//   type: "adjustLeverage";
//   variables: [leverage: bigint, margin: bigint, params: DenAdjustmentParams<bigint>, currentBorrowingRate: bigint, slippage: bigint];
// };
type ApproveLspTransaction = {
  type: "approveLsp";
  variables: [amount: bigint];
};

export type OpenDenTransaction = {
  type: "openDen";
  variables: [params: DenCreationParams<bigint>, maxBorrowingRate: bigint];
};
type AdjustDenTransaction = {
  type: "adjustDen";
  variables: [params: DenAdjustmentParams<bigint>, maxBorrowingRate: bigint];
};
type CloseDenTransaction = {
  type: "closeDen";
  variables: [];
};

type RedeemDebtTransaction = {
  type: "redeemDebt";
  variables: [amount: bigint, maxRedemptionRate: bigint];
};

type RedeemPsmTransaction = {
  type: "redeemPsm";
  variables: [stable: Hex, amount: bigint, maxRedemptionRate: bigint];
};
type ClaimCollateralSurplus = {
  type: "claimCollateralSurplus";
  variables: [];
};
type ApproveDelegate = {
  type: "approveDelegate";
  variables: [address: Hex];
};
// export type RedeemPreferredUnderlying = {
//   type: "redeemPreferredUnderlying";
//   variables: [shares: bigint, assetsWithdrawn: bigint, assets: Hex[], redeemedTokensLength: number, slippage?: bigint];
// };
type ApproveVaultTransaction = {
  type: "approveVault";
  variables: [amount: bigint];
};
type DepositVaultTransaction = {
  type: "depositVault";
  variables: [amount: bigint];
};
type RedeemVaultTransaction = {
  type: "redeemVault";
  variables: [amount: bigint];
};
type ApproveManagedVaultTransaction = {
  type: "approveManagedVault";
  variables: [amount: bigint];
};
type DepositManagedVaultTransaction = {
  type: "depositManagedVault";
  variables: [amount: bigint];
};
type RedeemIntentManagedVaultTransaction = {
  type: "redeemIntentManagedVault";
  variables: [amount: bigint];
};
type ClaimIntentManagedVaultTransaction = {
  type: "claimIntentManagedVault";
  variables: [epoch: bigint, minAmount: bigint];
};
type CancelIntentManagedVaultTransaction = {
  type: "cancelIntentManagedVault";
  variables: [epoch: bigint, amount: bigint];
};
type WithdrawBoyco = {
  type: "withdrawBoyco";
  variables: [contractAddress: Hex, amount: bigint];
};

type WrapPollenTransaction = {
  type: "wrapPollen";
  variables: [amount: bigint];
};

type UnwrapPollenTransaction = {
  type: "unwrapPollen";
  variables: [amount: bigint];
};

type ClaimPollenRewardsTransaction = {
  type: "claimPollenRewards";
  variables: [];
};

type ClaimLpPollenRewardsTransaction = {
  type: "claimLpPollenRewards";
  variables: [];
};

type StakePollenTransaction = {
  type: "stakePollen";
  variables: [amount: bigint];
};

type UnstakePollenTransaction = {
  type: "unstakePollen";
  variables: [amount: bigint];
};

type StakeLpPollenTransaction = {
  type: "stakeLpPollen";
  variables: [amount: bigint];
};

type UnstakeLpPollenTransaction = {
  type: "unstakeLpPollen";
  variables: [amount: bigint];
};

type IncreaseLockPositionTransaction = {
  type: "increaseLockPosition";
  variables: [amount: bigint, expiry: bigint];
};

type IncreaseLpLockPositionTransaction = {
  type: "increaseLpLockPosition";
  variables: [amount: bigint, expiry: bigint];
};

type WithdrawVotingEscrowTransaction = {
  type: "withdrawVotingEscrow";
  variables: [];
};

type WithdrawLpVotingEscrowTransaction = {
  type: "withdrawLpVotingEscrow";
  variables: [];
};
