import { Den, UserDen } from "@Beraborrowofficial/sdk";
import { Hex } from "viem";

export type DenManagerDetails = {
  contractAddress: Hex;
  collateralTicker: string;
  collateralDecimals: number;
  vaultDecimals: number;
  collateralAddress: Hex;
  vaultAddress?: Hex;
  wrappedCollateral?: Hex;
  wrappedCollateralTicker?: string;
  wrappedCollateralDecimals?: number;
};

export type DenMangersFullDetails = DenManagerDetails & { contractAddress?: `0x${string}`; den?: UserDen; densTotal?: Den; price?: bigint };
