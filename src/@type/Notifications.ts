import { ReactNode } from "react";
import { Hex } from "viem";
export type NotificationTypes = TransactionError | TransactionConfirmed | TransactionBroadcast;
type TransactionBroadcast = {
  type: "broadcast";
  payload: Hex;
};

type TransactionConfirmed = {
  type: "confirmed";
  payload: Hex;
};

type TransactionError = {
  type: "error";
  payload: string;
};
export interface ModalProps {
  id: string;
  title?: string;
  dismissable?: boolean;
  hideBackdrop?: boolean;
  maxWidth?: string;
  onClose?: () => void;
  Component: ReactNode;
}

export interface ToastProps {
  id: string;
  onClose?: () => void;
  message: string;
  time?: number;
  severity?: "success" | "info" | "warning" | "error";
}
