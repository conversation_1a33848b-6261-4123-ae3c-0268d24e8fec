import { Hex } from "viem";

export type PostHogEvent = {
  type:
    | "redeem"
    | "poolDeposit"
    | "poolWithdraw"
    | "denOpen"
    | "denDeposit"
    | "denWithdraw"
    | "denClose"
    | "delegateBorrow"
    | "approveBorrow"
    | "approveVault"
    | "vaultDeposit"
    | "vaultWithdraw"
    | "approveManagedVault"
    | "managedVaultDeposit"
    | "managedVaultWithdraw";
  [key: string]: string;
};

export type HistoryDenFilter = "all" | "dens" | "pool" | "vaults" | "redemptions" | Hex;

export type BorrowLevel = "Gold" | "Silver" | "Bronze" | "Basic";

export type NavigationOptions = "default" | "redeem" | "borrow" | "stake" | "pool" | "dashboard" | "vault";

export type ProfileAssetType = "Image" | "Video";
export type ProfileAsset = {
  url: string;
  type: ProfileAssetType;
};
