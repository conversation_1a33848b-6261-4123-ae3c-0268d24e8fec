import { Hex } from "viem";
import { TransactionDto, Holding } from "@Beraborrowofficial/sdk";

export type CollateralTokenDto = [
  string,
  {
    ticker: string;
    decimals: number;
    lpToken?: {
      token1: Hex;
      token2: Hex;
      provider: string;
    };
    getUrl?: string;
    protocol?: string;
  },
];

export type VaultHistoryDto = {
  id: string;
  assets: string;
  shares: string;
  deposited: string;
  withdrawn: (Holding & {
    id?: string;
  })[];
  shareChange: string;
  vaultOperation: string;
  transaction: TransactionDto;
  token?: CollateralTokenDto;
  __typename?: "VaultPositionChange";
};
