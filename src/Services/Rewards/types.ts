import { Hex } from "viem";

export type Reward = {
  id: string;
  action: string;
  rewardRate: bigint;
  rewardRateLabel: string;
  contractAddress1: Hex;
  contractAddress2?: Hex;
  tvl: bigint;
  balance: bigint;
  provider: string;
  link: string;
};
export type RewardJson = {
  id: string;
  action: string;
  rewardRate: number;
  rewardRateLabel: string;
  contractAddress1: Hex;
  contractAddress2?: Hex;
  tvl: number;
  provider: string;
  link: string;
};
