import axios, { AxiosError, AxiosInstance } from "axios";
import { BeraBorrowConnection } from "../../types";
import { RewardJson } from "./types";

export class RewardsServices {
  private readonly connection: BeraBorrowConnection;
  private axiosAPI: AxiosInstance;

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.axiosAPI = axios.create({
      baseURL: `${this.connection.BERABORROW_API_URL}/v1/Rewards`,
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        ...(this.connection.BERABORROW_API_KEY ? { "api-key": this.connection.BERABORROW_API_KEY } : {}),
      },
    });
  }

  async getPartners(): Promise<{ data: RewardJson[] } | { data: null; error: string }> {
    try {
      const response = (await this.axiosAPI<{ data: <PERSON><PERSON><PERSON><PERSON>[]; error?: string }>("/all")).data;
      return response;
    } catch (error) {
      console.error(error);
      const e = error as AxiosError;
      return { data: null, error: e.message || "Partners not available" };
    }
  }
}
