import { Hex } from "viem";

export type VaultsDetails = { vaultAddress: Hex; tokenAddress: Hex; price: number; collPrice: number; apy: number; tvl: number; protocol: string; mintUrl: string }[];
export type ManagedVaultsDetails = {
  contractAddress: Hex;
  denManagerAddress: Hex;
  vaultAddress: Hex;
  tokenAddress: Hex;
  apy: number;
  price: number;
  tvl: number;
  nectMinted: number;
  mintUrl: string;
}[];

export type ApiResponse<T> = { data: T } | { data: null; error: string };
export type TokenBalanceResponse = {
  token: string;
  amount: string;
  decimals: number;
  price: number;
};

export type TokenBalanceWithDetails = TokenBalanceResponse & TokenDetails;

export type TokenDetails = {
  address: Hex;
  chainId: number;
  type: string;
  decimals: number;
  symbol: string;
  name: string;
  logoUri: string | null;
  logosUri: string[];
};

export type ensoSwapResponse = {
  gas: string;
  amountOut: string;
  priceImpact: number;
  feeAmount: [string];
  createdAt: number;
  tx: {
    data: string;
    to: Hex;
    from: Hex;
    value: string;
  };
  route: [
    {
      tokenIn: Hex[];
      tokenOut: Hex[];
      protocol: string;
      action: string;
      primary: string;
      internalRoutes: string[];
    },
  ];
};
