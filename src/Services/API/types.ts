import { Hex } from "viem";

export type VaultsDetails = { vaultAddress: Hex; tokenAddress: Hex; price: number; collPrice: number; apy: number; tvl: number; protocol: string; mintUrl: string }[];
export type ManagedVaultsDetails = {
  contractAddress: Hex;
  denManagerAddress: Hex;
  vaultAddress: Hex;
  tokenAddress: Hex;
  apy: number;
  price: number;
  tvl: number;
  nectMinted: number;
  mintUrl: string;
}[];

export type ApiResponse<T> = { data: T } | { data: null; error: string };
export type TokenBalanceResponse = {
  token: string;
  amount: string;
  decimals: number;
  price: number;
};

export type TokenBalanceWithDetails = TokenBalanceResponse & TokenDetails;

export type TokenDetails = {
  address: string;
  chainId: number;
  type: string;
  decimals: number;
  symbol: string;
  name: string;
  logosUri: [string];
  underlyingTokens: [
    {
      address: string;
      chainId: number;
      type: string;
      decimals: number;
      symbol: string;
      name: string;
      logosUri: [string];
    },
  ];
  project: string;
  protocolSlug: string;
  apy: number;
  apyBase: number;
  apyReward: number;
  tvl: number;
  primaryAddress: string;
};
