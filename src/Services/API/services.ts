import axios, { AxiosInstance } from "axios";
import { BeraBorrowConnection } from "../../types";
import { ApiResponse, ManagedVaultsDetails, TokenBalanceWithDetails, VaultsDetails } from "./types";
import { Hex } from "viem";

export class ApiServices {
  private readonly connection: BeraBorrowConnection;
  private axiosAPI: AxiosInstance;

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.axiosAPI = axios.create({
      baseURL: `${this.connection.BERABORROW_API_URL}/v1`,
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        ...(this.connection.SUBGRAPH_API_KEY ? { "api-key": this.connection.BERABORROW_API_KEY } : {}),
      },
    });
  }

  async getHealth(): Promise<{ healthy: boolean; error?: string }> {
    return (await this.axiosAPI<{ healthy: boolean; error?: string }>("/health")).data;
  }

  async getVaults(): Promise<VaultsDetails> {
    return (await this.axiosAPI<VaultsDetails>("/vaults")).data;
  }
  async getManagedVaults(): Promise<ManagedVaultsDetails> {
    return (await this.axiosAPI<ManagedVaultsDetails>("/managed-vaults")).data;
  }

  async getEnsoHealth(): Promise<boolean> {
    return !!(await this.axiosAPI<ApiResponse<boolean>>("/enso/health")).data.data;
  }
  async getEnsoTokenBalances(userAddress: Hex): Promise<boolean> {
    return !!(await this.axiosAPI<ApiResponse<TokenBalanceWithDetails>>(`/enso/balances/${userAddress}`)).data.data;
  }
  async getEnsoSwap(tokenIn: Hex, tokenOut: Hex, userAddress: Hex, amount: bigint, slippage = 0.5): Promise<boolean> {
    const searchParams = new URLSearchParams();
    searchParams.append("tokenIn", tokenIn);
    searchParams.append("tokenOut", tokenOut);
    searchParams.append("to", userAddress);
    searchParams.append("amount", amount.toString());
    searchParams.append("slippage", slippage.toString()); // Convert to basic points

    return !!(await this.axiosAPI<ApiResponse<TokenBalanceWithDetails>>(`/enso/swap/?${searchParams.toString()}`)).data.data;
  }
}
