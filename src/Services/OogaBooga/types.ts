import { Hex } from "viem";

export type TokenPrices = {
  address: Hex;
  price: number;
}[];

interface Token {
  address: Hex;
  name: string;
  symbol: string;
  decimals: number;
}

export interface Route {
  poolAddress: Hex;
  poolType: string;
  poolName: string;
  liquiditySource: string;
  poolFee: number;
  tokenFrom: number;
  tokenTo: number;
  share: number;
  assumedAmountIn: string;
  assumedAmountOut: string;
}

interface Tx {
  to: Hex;
  data: Hex;
  value: string;
}

interface RouterParams {
  swapTokenInfo: {
    inputToken: Hex;
    inputAmount: string;
    outputToken: Hex;
    outputQuote: string;
    outputMin: string;
    outputReceiver: Hex;
  };
  pathDefinition: Hex;
  executor: Hex;
  referralCode: string | number;
  value: string;
}

export interface SwapResponseBase {
  status: "Success" | "Partial";
  tokenFrom: number;
  tokenTo: number;
  price: number;
  priceImpact: number;
  tokens: Token[];
  amountIn: string;
  amountOutFee: string;
  assumedAmountOut: string;
  route: Route[];
}

export interface SwapResponseWithExecutionArgs extends SwapResponseBase {
  status: "Success" | "Partial";
  tokenFrom: number;
  tokenTo: number;
  price: number;
  priceImpact: number;
  tokens: Token[];
  amountIn: string;
  amountOutFee: string;
  assumedAmountOut: string;
  route: Route[];
  tx: Tx;
  routerAddr: Hex;
  routerParams: RouterParams;
}
export type SwapNowWay = { status: "NoWay" };
export type SwapResponse = SwapResponseWithExecutionArgs | SwapResponseBase;
