import axios, { AxiosError, AxiosInstance } from "axios";
import { BeraBorrowConnection } from "../../types";
import { SwapNowWay, SwapResponseWithExecutionArgs, TokenPrices } from "./types";

export class OogaBoogaServices {
  private readonly connection: BeraBorrowConnection;
  private axiosAPI: AxiosInstance;

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.axiosAPI = axios.create({
      baseURL: `${this.connection.BERABORROW_API_URL}/v1/oogabooga`,
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        ...(this.connection.BERABORROW_API_KEY ? { "api-key": this.connection.BERABORROW_API_KEY } : {}),
      },
    });
  }

  async getHealth(): Promise<{ healthy: boolean; error?: string }> {
    return (await this.axiosAPI<{ healthy: boolean; error?: string }>("/health")).data;
  }

  async getPrices(): Promise<TokenPrices> {
    return (await this.axiosAPI<TokenPrices>("/prices")).data;
  }

  async getSwap(
    tokenIn: `0x${string}`,
    tokenOut: `0x${string}`,
    to: `0x${string}`,
    amount: string,
    slippage?: string
  ): Promise<{ data: SwapResponseWithExecutionArgs } | { data: null; error: string }> {
    try {
      const params = new URLSearchParams({
        tokenIn,
        tokenOut,
        amount,
        to,
        slippage: slippage ?? "0.02",
      });

      const response = await this.axiosAPI.get<{ data: SwapResponseWithExecutionArgs | SwapNowWay }>(`/swap?${params.toString()}`);
      if (response.data.data.status === "Success") {
        return {
          data: response.data.data,
        };
      }
      return { data: null, error: "OoogaBooga Error:" + "Swap not Possible" };
    } catch (error) {
      // Log full error details for debugging
      // Send only a simplified error message to avoid circular structure
      const errorMessage = error instanceof AxiosError ? error.message : "An error occurred";

      return { data: null, error: "OoogaBooga Error:" + errorMessage };
    }
  }
}
