import { ApolloClient, HttpLink, InMemoryCache, NormalizedCacheObject, ApolloQueryResult, DefaultOptions, DocumentNode, OperationVariables } from "@apollo/client/core";
import {
  denChangesOfUserDens,
  denManagerQuery,
  denManagersQuery,
  denQuery,
  densByDenManagerQuery,
  densQuery,
  historicalBorrowingFeeRatesQuery,
  historicalSnapshotsQuery,
  liquidationsByLiquidator,
  poolQuery,
  snapshotAtQuery,
  tokensQuery,
  tokenWithPriceHistoryQuery,
  userDepositVolumeInDenQuery,
  redemptionsByRedeemer,
  poolPositionQuery,
  sharePositionChangesQuery,
  interestRateQuery,
  vaultWithSharePriceHistoryQuery,
  sharePoolWithSharePriceHistoryQuery,
  vaultByTypeWithSharePriceHistoryQuery,
  vaultPositionChangesQuery,
  vaultSlotByTypeWithSharePriceHistoryQuery,
  interestRatesQuery,
  vaultPositionShareAtBlock,
  denPositionShareAtBlock,
  vaultUsers,
  denPositionOwners,
} from "./queries";
import { Hex } from "viem";
import { BeraBorrowConnection } from "../types";
import {
  BorrowingFeeRate,
  DenChangeDto,
  DenDto,
  DenManagerDto,
  denPositionShareAtBlockDto,
  LiquidationDto,
  LSPRewardsDto,
  LSPShareChangeDto,
  PortfolioDto,
  RedemptionDto,
  SharePoolSharePriceDto,
  TokenDto,
  TransactionDto,
  VaultDto,
  VaultOperationsDto,
  vaultPositionShareAtBlockDto,
  VaultWithVaultSlotDto,
} from "./types";
import { SCALING_FACTOR } from "../constants";
import { managedVaultPositionShareAtBlock, managedVaultUsers } from "./queries/managedVaults";

/**
 * @notice Apollo client object to fetch data from subgraph
 *
 */
export class SubgraphClient {
  private apolloClient: ApolloClient<NormalizedCacheObject>;
  private connection: BeraBorrowConnection;

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    const defaultOptions: DefaultOptions = {
      watchQuery: {
        fetchPolicy: "no-cache",
        errorPolicy: "ignore",
      },
      query: {
        fetchPolicy: "no-cache",
        errorPolicy: "all",
      },
    };

    this.apolloClient = new ApolloClient({
      link: new HttpLink({
        uri: this.connection.SUBGRAPH_API_URL,
        fetch,
        headers: this.connection.SUBGRAPH_API_KEY ? { Authorization: `Bearer ${this.connection.SUBGRAPH_API_KEY}` } : undefined,
      }),
      cache: new InMemoryCache(),
      defaultOptions,
    });
  }

  /**
   * @notice This executes any graphal query
   * we will generate static graphql query rather than dynamic one, cuz static ones are much faster than dynamic queries.
   * @param query GraphQL Query to be executed
   * @param variables Variabels to be passed to construct query
   * @returns ApolloQueryResult<T>
   */
  async execute<T>(query: DocumentNode, variables?: OperationVariables): Promise<ApolloQueryResult<T>> {
    return this.apolloClient.query({
      query,
      variables,
    });
  }

  /**
   * @notice Get all den managers. We assume that denManagers don't exeed 1000 length
   * @returns DenManagers
   */
  async getDenManagers(): Promise<DenManagerDto[]> {
    const res = await this.execute<{
      denManagers: DenManagerDto[];
    }>(denManagersQuery());

    return res.data.denManagers;
  }

  /**
   * @notice Get specific den manager
   * @param id DenManager address (id)
   * @returns DenManager
   */
  async getDenManager(id: string): Promise<DenManagerDto> {
    const res = await this.execute<{
      denManager: DenManagerDto;
    }>(denManagerQuery(), {
      id: id?.toLowerCase(),
    });

    return res.data.denManager;
  }

  /**
   * @notice Get whole dens
   * @param first dens length limit
   * @param skip for pagination
   * @returns Dens
   */
  async getDens(first?: number, skip?: number): Promise<DenDto[]> {
    const res = await this.execute<{
      dens: DenDto[];
    }>(densQuery(), {
      first: first || 1000,
      skip: skip || 0,
    });

    return res.data.dens;
  }

  /**
   * @notice Get Dens in specific Den Manager
   * @param denManager DenManager ID
   * @param first dens length limit
   * @param skip for pagination
   * @returns Dens
   */
  async getDensByDenManager(denManager: string, first?: number, skip?: number): Promise<DenDto[]> {
    const res = await this.execute<{
      dens: DenDto[];
    }>(densByDenManagerQuery(), {
      denManagerID: denManager?.toLowerCase(),
      first: first || 1000,
      skip: skip || 0,
    });

    return res.data.dens;
  }

  /**
   * @notice Get Dens of user
   * @param user user address
   * @returns Dens
   */
  async getDensByOwner(user: string): Promise<DenDto[]> {
    const res = await this.execute<{
      dens: DenDto[];
    }>(densQuery(), {
      user: user?.toLowerCase(),
    });

    return res.data.dens;
  }

  /**
   * @notice Get Dens of user with historical changes
   * @param collateral collateral of Den
   * @param user user address
   * @param start start timestamp in seconds
   * @param end end timestamp in seconds
   * @returns Dens
   */
  async getDenChangesOfUserDens(user: string, collateral?: string, start?: number, end?: number): Promise<DenChangeDto[]> {
    const res = await this.execute<{
      denChanges: DenChangeDto[];
    }>(denChangesOfUserDens(user, collateral, start, end), {
      user: user?.toLowerCase(),
      collateral: collateral?.toLowerCase() || null,
      start,
      end,
    });
    return res.data?.denChanges ?? [];
  }

  /**
   * @notice Get redemptions which are triggered by redeemer
   * @param redeemer redeemer address
   * @returns Redemptions
   */
  async getRedemptionsByRedeemer(redeemer: string, start?: number, end?: number): Promise<(RedemptionDto & { transaction: TransactionDto })[]> {
    const res = await this.execute<{
      redemptions: (RedemptionDto & { transaction: TransactionDto })[];
    }>(redemptionsByRedeemer(redeemer, start, end), {
      redeemer: redeemer?.toLowerCase(),
      start,
      end,
    });

    return res.data.redemptions;
  }

  /**
   * @notice Get liquidations which are triggered by liquidator
   * @param liquidator liquidator address
   * @returns Liquidations
   */
  async getLiquidationsByLiquidator(liquidator: string, start?: number, end?: number): Promise<LiquidationDto[]> {
    const res = await this.execute<{
      liquidations: LiquidationDto[];
    }>(liquidationsByLiquidator(liquidator, start, end), {
      liquidator: liquidator?.toLowerCase(),
      start,
      end,
    });

    return res.data.liquidations;
  }

  /**
   * @notice Get historical changes of liquidity stability pool
   * @param user user address
   * @param start start timestamp in seconds
   * @param end end timestamp in seconds
   * @returns pool change history
   */
  async getUserLSPShareChanges(user: string, start?: number, end?: number): Promise<LSPShareChangeDto[]> {
    const res = await this.execute<{
      sharePositionChanges: LSPShareChangeDto[];
    }>(sharePositionChangesQuery(user, start, end), {
      user: user?.toLowerCase(),
      start,
      end,
    });

    return res.data.sharePositionChanges;
  }

  /**
   * @notice Get user's pool operations
   * @param user user address
   * @param start start timestamp in seconds
   * @param end end timestamp in seconds
   * @param vaultOperation type of operation
   * @returns user's pool operations
   */
  async getUserVaultOperations(user: string, start?: number, end?: number): Promise<VaultOperationsDto[]> {
    const res = await this.execute<{
      vaultPositions: VaultOperationsDto[];
    }>(vaultPositionChangesQuery(start, end), {
      user: user?.toLowerCase(),
      start,
      end,
    });

    return res.data.vaultPositions;
  }

  /**
   * @notice Get den data for specific den id
   * @param id den ID
   * @returns Den
   */
  async getDen(id: string): Promise<DenDto> {
    const res = await this.execute<{
      den: DenDto;
    }>(denQuery(), {
      id: id,
    });

    return res.data.den;
  }

  /**
   * @notice Get borrowing fee rate between specific timestamps
   * @param denManager denmanager address
   * @param start start timestamp
   * @param end end timestamp
   * @returns borrowing fee list
   */
  async getBorrowingFeeRates(denManager: string, start: string, end: string): Promise<BorrowingFeeRate[]> {
    const res = await this.execute<{
      borrowingRateSnapshots: BorrowingFeeRate[];
    }>(historicalBorrowingFeeRatesQuery(), {
      denManager: denManager?.toLowerCase(),
      start,
      end,
    });

    return res.data.borrowingRateSnapshots;
  }

  /**
   * @notice Get all tokens with its current price.
   * @returns token list
   */
  async getAllTokens(): Promise<TokenDto[]> {
    const res = await this.execute<{
      tokens: TokenDto[];
    }>(tokensQuery(), {});

    return res.data.tokens;
  }

  /**
   * @notice Get token with its historical price data.
   * @param tokenAddress token address
   * @param from from timestamp for price history
   * @param to to timestamp for price history
   * @returns token data with price history
   */
  async getTokenPriceHistory(tokenAddress: string, from: string, to: string): Promise<TokenDto> {
    const res = await this.execute<{
      token: TokenDto;
    }>(tokenWithPriceHistoryQuery(), {
      tokenAddress: tokenAddress?.toLowerCase(),
      from,
      to,
    });

    return res.data.token;
  }

  /**
   * @notice Get vault with its historical sharePrice data.
   * @param vaultAddress token address
   * @param from from timestamp for sharePrice history
   * @param to to timestamp for sharePrice history
   * @returns Vault data with sharePrice history
   */
  async getVaultSharePriceHistory(vaultAddress: string, from: number, to: number, limit = 96, orderDirection = "asc"): Promise<VaultDto | undefined> {
    const res = await this.execute<{
      vault: VaultDto;
    }>(vaultWithSharePriceHistoryQuery(), {
      vaultAddress: vaultAddress?.toLowerCase(),
      from: from.toString(),
      to: to.toString(),
      limit,
      orderDirection,
    });
    return res?.data?.vault;
  }

  /**
   * @notice Get vault with its historical sharePrice data.
   * @param vaultAddress token address
   * @param from from timestamp for sharePrice history
   * @param to to timestamp for sharePrice history
   * @returns Vault data with sharePrice history
   */
  async getVaultSharePriceHistoryByType(
    vaultAddress: Hex,
    from: number,
    to: number,
    limit = 300,
    orderDirection = "desc",
    type: "REBALANCE" | "EVENT" | "TIMED" = "REBALANCE"
  ): Promise<VaultDto | undefined> {
    const res = await this.execute<{
      vault: VaultDto;
    }>(vaultByTypeWithSharePriceHistoryQuery(), {
      vaultAddress: vaultAddress?.toLowerCase(),
      from: from.toString(),
      to: to.toString(),
      limit,
      orderDirection,
      type,
    });
    return res?.data?.vault;
  }
  async getVaultSlotByType(vaultAddress: Hex, limit = 2, type: "HOURLY" | "DAILY" | "WEEKLY" = "DAILY", minObservations = 12): Promise<VaultWithVaultSlotDto | undefined> {
    const res = await this.execute<{
      vault: VaultWithVaultSlotDto;
    }>(vaultSlotByTypeWithSharePriceHistoryQuery(), {
      vaultAddress: vaultAddress?.toLowerCase(),
      limit,
      type,
      minObservations,
    });
    return res?.data?.vault;
  }
  async getVaultPositionOwners(vaultAddress: Hex): Promise<{ owner: { id: Hex } }[] | undefined> {
    const res = await this.execute<{
      vaultPositions: { owner: { id: Hex } }[];
    }>(vaultUsers(), {
      vaultAddress: vaultAddress?.toLowerCase(),
    });
    return res?.data?.vaultPositions;
  }

  async getVaultPositionShareAtBlock(vaultAddress: Hex, blockNumber = Number.MAX_SAFE_INTEGER): Promise<vaultPositionShareAtBlockDto[] | undefined> {
    const res = await this.execute<{
      vaultPositions: vaultPositionShareAtBlockDto[];
    }>(vaultPositionShareAtBlock(), {
      vaultAddress: vaultAddress?.toLowerCase(),
      blockNumber: Math.floor(blockNumber),
    });
    return res?.data?.vaultPositions;
  }
  async getDenPositionOwners(denManagerAddress: Hex): Promise<{ owner: { id: Hex } }[] | undefined> {
    const res = await this.execute<{
      dens: { owner: { id: Hex } }[];
    }>(denPositionOwners(), {
      denManagerAddress: denManagerAddress?.toLowerCase(),
    });
    return res?.data?.dens;
  }
  async getDenPositionShareAtBlock(denManagerAddress: Hex, blockNumber = Number.MAX_SAFE_INTEGER): Promise<denPositionShareAtBlockDto[] | undefined> {
    const res = await this.execute<{
      dens: denPositionShareAtBlockDto[];
    }>(denPositionShareAtBlock(), {
      denManagerAddress: denManagerAddress?.toLowerCase(),
      blockNumber: Math.floor(blockNumber),
    });
    return res?.data?.dens;
  }
  async getManagedVaultPositionOwners(managedVaultAddress: Hex): Promise<{ owner: { id: Hex } }[] | undefined> {
    const res = await this.execute<{
      managedVaultPositions: { owner: { id: Hex } }[];
    }>(managedVaultUsers(), {
      managedVaultAddress: managedVaultAddress?.toLowerCase(),
    });
    return res?.data?.managedVaultPositions;
  }
  async getManagedVaultPositionShareAtBlock(managedVaultAddress: Hex, blockNumber = Number.MAX_SAFE_INTEGER): Promise<vaultPositionShareAtBlockDto[] | undefined> {
    const res = await this.execute<{
      vaultPositions: vaultPositionShareAtBlockDto[];
    }>(managedVaultPositionShareAtBlock(), {
      managedVaultAddress: managedVaultAddress?.toLowerCase(),
      blockNumber: Math.floor(blockNumber),
    });
    return res?.data?.vaultPositions;
  }
  /**
   * @notice Get LSP with its historical sharePrice data.
   * @param from from timestamp for sharePrice history
   * @param to to timestamp for sharePrice history
   * @returns LSP data with sharePrice history
   */
  async getSharePoolSharePriceHistory(from: number, to: number, limit = 96, orderDirection = "asc"): Promise<SharePoolSharePriceDto | undefined> {
    const res = await this.execute<{
      sharePool: SharePoolSharePriceDto;
    }>(sharePoolWithSharePriceHistoryQuery(), {
      from: from.toString(),
      to: to.toString(),
      limit,
      orderDirection,
    });
    return res?.data?.sharePool;
  }

  /**
   * @notice Get current composited assets of pool
   * @returns portfolio
   */
  async getCurrentPoolCompositions(): Promise<PortfolioDto> {
    const res = await this.execute<{
      sharePool: {
        portfolio: PortfolioDto;
      };
    }>(poolQuery());

    return res.data.sharePool.portfolio;
  }

  /**
   * @notice Get historical snapshot of pool assets
   * @param from
   * @param to
   * @returns portfolio
   */
  async getHistoricalPoolSnapshots(from: string, to: string): Promise<PortfolioDto[]> {
    const res = await this.execute<{
      portfolios: PortfolioDto[];
    }>(historicalSnapshotsQuery(), {
      from,
      to,
    });

    return res.data.portfolios;
  }

  /**
   * @notice Get portfolio snapshot at specific timestamp
   * @param timestamp
   */
  async getPoolCompositionsAt(timestamp: string): Promise<PortfolioDto> {
    const res = await this.execute<{
      portfolios: PortfolioDto[];
    }>(snapshotAtQuery(), {
      at: timestamp,
    });

    return res.data.portfolios?.[0];
  }

  /**
   * @notice Get user's total deposit volume in den managers
   */
  async getUserDepositVolumeInDen(address: `0x${string}`): Promise<bigint> {
    const res = await this.execute<{
      user: {
        id: string;
        totalDepositVolumeInDen: number;
      };
    }>(userDepositVolumeInDenQuery(), {
      user: address?.toLowerCase(),
    });

    return BigInt(res.data.user?.totalDepositVolumeInDen || 0) / SCALING_FACTOR;
  }

  /**
   * @dev Get LSP rewards including emissions
   */
  async getUserLSPRewards(address: Hex): Promise<LSPRewardsDto> {
    let res = await this.execute<{
      sharePositions: {
        shareAmount: string;
      }[];
    }>(poolPositionQuery(), {
      owner: address?.toLowerCase(),
    });

    if (!res.data?.sharePositions || res.data?.sharePositions?.length == 0) {
      return {
        shares: 0,
        totalShares: 0,
        collaterals: [],
        emissions: [],
      };
    }

    const shareAmount = res.data.sharePositions[0].shareAmount;

    const portfolio = await this.getCurrentPoolCompositions();
    const { totalShares, holdings, emissions } = portfolio;

    return {
      shares: shareAmount,
      totalShares,
      collaterals: holdings.filter((item) => item.asset.id?.toLowerCase() != this.connection.addresses.debtToken.contractAddress?.toLowerCase()),
      emissions,
    };
  }

  /**
   * @dev Get interest rate of den manager
   */
  async getInterestRate(denManager: Hex): Promise<number> {
    let res = await this.execute<{
      denManager: {
        interestRate: string;
      };
    }>(interestRateQuery(), {
      id: denManager?.toLowerCase(),
    });

    return Number(res?.data?.denManager?.interestRate || 0);
  }
  /**
   * @dev Get interest rate of all den managers
   */
  async getInterestRates(): Promise<
    {
      interestRate: string;
      id: string;
    }[]
  > {
    let res = await this.execute<{
      denManagers: [
        {
          interestRate: string;
          id: string;
        },
      ];
    }>(interestRatesQuery());

    return res?.data?.denManagers;
  }
}
