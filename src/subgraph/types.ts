import { Hex } from "viem";

export type TokenPriceDto = {
  price: string;
  timestamp: string;
};
export type TokenDto = {
  id: Hex;
  price: TokenPriceDto;
  symbol: string;
  name: string;
  decimals: number;
  priceHistory?: TokenPriceDto[];
};
export type SharePriceDto = {
  sharePrice: string;
  timestamp: string;
  totalAssets: string;
  iBGT: string;
  totalSupply: string;
};
export type VaultDto = {
  id: Hex;
  collateral: { id: Hex };
  asset: { id: Hex };
  vaultSnapshot: SharePriceDto[];
};
export type VaultWithVaultSlotDto = {
  id: Hex;
  vaultSlot: VaultSlotDto[];
};
export type VaultSlotDto = {
  startTime: string;
  startBalance: string;
  startTotalSupply: string;
  lastTime: string;
  lastBalance: string;
  lastTotalSupply: string;
  cumTime: string;
  cumBalance: string;
  cumTotalSupply: string;
  observations: number;
};
export type vaultPositionShareAtBlockDto = {
  id: Hex;
  changes: vaultPositionShareAtBlockChangeDto[];
};
export type vaultPositionShareAtBlockChangeDto = {
  id: Hex;
  owner: {
    id: Hex;
  };
  shares: string;
};
export type denPositionShareAtBlockDto = {
  id: Hex;
  changes: denPositionShareAtBlockChangeDto[];
};
export type denPositionShareAtBlockChangeDto = {
  id: Hex;
  owner: {
    id: Hex;
  };
  collateralAfter: string;
};
export type SharePoolSharePriceDto = {
  id: Hex;
  snapshots: SharePriceDto[];
};
export type DenDto = {
  accuredCollateralRewards: string;
  accuredDebtRewards: string;
  id: Hex;
  owner: {
    id: Hex;
  };
  denManager: {
    id: Hex;
    collateral: TokenDto;
    __typename: "DenManager";
  };
  status: string;
  collateral: string;
  debt: string;
  rawCollateral: string;
  rawDebt: string;
  rawStake: string;
  __typename: "Den";

  changes?: DenChangeDto[];
};

export type DenManagerDto = {
  id: Hex;
  collateral: TokenDto;
  totalCollateral: string;
  totalDebt: string;
  totalCollateralRatio: string;
  borrowingFeeRate: string;
  dens: DenDto;
};

export type BorrowingFeeRate = {
  id: Hex;
  blockNumber: string;
  timestamp: string;
  feeRate: string;
};

export type Holding = {
  asset: TokenDto;
  price: TokenPriceDto;
  balance: string;
};

export type PortfolioDto = {
  timestamp: string;
  totalShares: string;
  totalAssets: string;
  holdings: Holding[];
  emissions: Holding[];
};

export type TransactionDto = {
  id: Hex;
  timestamp: number;
  sequenceNumber: number;
  blockNumber: number;
};
export type ChangeDto = {
  transaction: TransactionDto;
};

export type DenChangeDto = {
  transaction: TransactionDto;
  redemption: RedemptionDto | null;
  liquidation: LiquidationDto | null;
  denOperation: string;
  collateralChange: string;
  debtChange: string;
  collateralRatioBefore: string | null;
  collateralRatioAfter: string | null;
  borrowingFee: string | null;
  denManager: { id: Hex };
  den: DenDto;
  __typename: "DenChange";
};

export type RedemptionDto = {
  id: Hex;
  denManager: { id: Hex };
  redeemer: UserDto;
  tokensAttemptedToRedeem: string;
  tokensActuallyRedeemed: string;
  collateralRedeemed: string;
  partial: boolean;
  fee: string;
  __typename: "Redemption";
};

export type LiquidationDto = {
  id: Hex;
  liquidator: UserDto;
  liquidatedDebt: string;
  liquidatedCollateral: string;
  collGasCompensation: string;
  tokenGasCompensation: string;
  __typename: "Liquidation";
};

export type UserDto = {
  id: Hex;
};

export type LSPRewardsDto = {
  shares: string | number | bigint;
  totalShares: string | number | bigint;
  collaterals: Holding[];
  emissions: Holding[];
};

export type LSPShareChangeDto = {
  id: string;
  transaction: TransactionDto;
  shareOperation: string;
  shareAmountChange: string;
  shareAmountBefore: string;
  shareAmountAfter: string;
  __typename: "SharePositionChange";
};

type VaultOperationType = "DEPOSIT" | "WITHDRAW";

type VaultChangesDto = {
  id: string;
  assets: string;
  shares: string;
  deposited: string;
  withdrawn: Holding[];
  shareChange: string;
  vaultOperation: VaultOperationType;
  transaction: TransactionDto;
};

export type VaultOperationsDto = {
  id: string;
  assets: string;
  deposited: string;
  withdrawn: Holding[];
  shares: string;
  vault: VaultDto;
  changes: VaultChangesDto[];
  __typename: "VaultPosition";
};

export type ManagedVaultOperationsDto = {
  id: string;
  assets: string;
  deposited: string;
  withdrawn: Holding[];
  shares: string;
  managedVault: {
    asset: {
      id: string;
    };
  };
  changes: VaultChangesDto[];
  __typename: "ManagedVaultPosition";
};

export type ManagedVaultWithdrawRequestState = "EXECUTED" | "PENDING" | "CANCELED";

export type ManagedVaultWithdrawRequest = {
  id: string;
  managedVaultEpoch: {
    epochNumber: bigint;
  };
  withdrawShares: bigint;
  managedVaultWithdrawRequestState: ManagedVaultWithdrawRequestState;
};
