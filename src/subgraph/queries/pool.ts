import { DocumentNode, gql } from "@apollo/client/core";
import { PortfolioFragment } from "./fragments/portfolio.fragment";
import { SharePositionChangeFragment } from "./fragments/change.fragment";

export const poolQuery = (): DocumentNode => {
  return gql`
    ${PortfolioFragment}
    query GetPool {
      sharePool(id: "SharePool") {
        portfolio {
          ...portfolioFragment
        }
      }
    }
  `;
};

export const poolPositionQuery = (): DocumentNode => {
  return gql`
    query GetPoolPosition($owner: ID!) {
      sharePositions(where: { owner: $owner }) {
        id
        shareAmount
      }
    }
  `;
};

export const sharePoolWithSharePriceHistoryQuery = (): DocumentNode => {
  return gql`
    query GetSharePoolWithSharePriceHistoryQuery($from: BigInt!, $to: BigInt!, $limit: Int, $orderDirection: OrderDirection!) {
      sharePool(id: "SharePool") {
        snapshots(where: { timestamp_gte: $from, timestamp_lte: $to }, orderBy: timestamp, orderDirection: $orderDirection, first: $limit) {
          timestamp
          sharePrice
        }
      }
    }
  `;
};
export const historicalSnapshotsQuery = (): DocumentNode => {
  return gql`
    ${PortfolioFragment}
    query GetPortfolioSnapshots($from: BigInt, $to: BigInt) {
      portfolios(where: { timestamp_gte: $from, timestamp_lte: $to }) {
        ...portfolioFragment
      }
    }
  `;
};

export const snapshotAtQuery = (): DocumentNode => {
  return gql`
    ${PortfolioFragment}
    query GetPortfolioSnapshotAt($at: BigInt) {
      portfolios(where: { timestamp_lte: $at }, orderBy: timestamp, orderDirection: desc, first: 1) {
        ...portfolioFragment
      }
    }
  `;
};

export const sharePositionChangesQuery = (user: string, start?: number, end?: number): DocumentNode => {
  // Build the filter conditions
  let whereClause = `sharePosition_: { owner: $user }`;

  if (start && end) {
    whereClause += `, transaction_: { timestamp_gte: $start, timestamp_lte: $end }`;
  } else if (start) {
    whereClause += `, transaction_: { timestamp_gte: $start }`;
  } else if (end) {
    whereClause += `, transaction_: { timestamp_lte: $end }`;
  }

  return gql`
		${SharePositionChangeFragment}
		query GetSharePositionChanges($user: ID!, $start: Int, $end: Int) {
			sharePositionChanges(where: { ${whereClause} }) {
				...sharePositionChangeFragment
			}
    	}
	`;
};
