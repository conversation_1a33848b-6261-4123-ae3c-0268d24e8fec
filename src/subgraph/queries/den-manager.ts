import { DocumentNode, gql } from "@apollo/client/core";
import { TokenFragment } from "./fragments/token.fragment";
import { DenFragment } from "./fragments/den.fragment";

export const denManagersQuery = (): DocumentNode => {
  return gql`
    ${TokenFragment}
    ${DenFragment}
    query GetDenManagers {
      denManagers {
        id
        collateral {
          ...tokenFragment
        }
        totalCollateral
        totalDebt
        totalCollateralRatio
        borrowingFeeRate
        dens {
          ...denFragment
        }
      }
    }
  `;
};

export const denManagerQuery = (): DocumentNode => {
  return gql`
    ${TokenFragment}
    ${DenFragment}
    query GetDenManager($id: ID!) {
      denManager(id: $id) {
        id
        collateral {
          ...tokenFragment
        }
        totalCollateral
        totalDebt
        totalCollateralRatio
        borrowingFeeRate
        dens {
          ...denFragment
        }
      }
    }
  `;
};

export const interestRateQuery = (): DocumentNode => {
  return gql`
    query GetDenManager($id: ID!) {
      denManager(id: $id) {
        interestRate
      }
    }
  `;
};
export const interestRatesQuery = (): DocumentNode => {
  return gql`
    query GetDenManagersInterestRate {
      denManagers {
        interestRate
        id
      }
    }
  `;
};
export const denPositionShareAtBlock = (): DocumentNode => {
  return gql`
    query denPositionAtBlock($denManagerAddress: String!, $blockNumber: Int!) {
      dens(where: { denManager: $denManagerAddress }, first: 1000) {
        changes(where: { blockNumber_lte: $blockNumber }, orderBy: blockNumber, orderDirection: desc, first: 2) {
          blockNumber
          owner {
            id
          }
          collateralAfter
          transaction {
            id
          }
        }
      }
    }
  `;
};
export const denPositionOwners = (): DocumentNode => {
  return gql`
    query denPositionAtBlock($denManagerAddress: String!) {
      dens(where: { denManager: $denManagerAddress }, first: 1000) {
        owner {
          id
        }
      }
    }
  `;
};
