import { DocumentNode, gql } from "@apollo/client/core";
import { TokenFragment } from "./fragments/token.fragment";

export const tokensQuery = (): DocumentNode => {
  return gql`
			${TokenFragment}
		query GetTokens() {
			tokens {
				...tokenFragment
			}
		}
	`;
};

export const tokenWithPriceHistoryQuery = (): DocumentNode => {
  return gql`
    ${TokenFragment}
    query GetTokenWithPriceHistory($tokenAddress: ID, $from: BigInt, $to: BigInt) {
      token(id: $tokenAddress) {
        ...tokenFragment
        priceHistory(where: { timestamp_gte: $from, timestamp_lte: $to }) {
          timestamp
          price
        }
      }
    }
  `;
};
