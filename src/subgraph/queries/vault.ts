import { DocumentNode, gql } from "@apollo/client/core";
import { VaultFragment, VaultPositionFragment } from "./fragments/vault.fragment";
import { TokenFragment } from "./fragments/token.fragment";

export const vault = (): DocumentNode => {
  return gql`
    ${TokenFragment}
    ${VaultPositionFragment}
    query getVault($vaultAddress: ID!) {
      vault(id: $vaultAddress) {
        id
        asset {
          ...tokenFragment
        }
        vaultPositions {
          ...vaultPositionFragment
        }
      }
    }
  `;
};

export const vaultWithSharePriceHistoryQuery = (): DocumentNode => {
  return gql`
    ${VaultFragment}
    query GetVaultWithSharePriceHistoryQuery($vaultAddress: ID!, $from: BigInt!, $to: BigInt!, $limit: Int, $orderDirection: OrderDirection!) {
      vault(id: $vaultAddress) {
        vaultSnapshot(where: { timestamp_gte: $from, timestamp_lte: $to }, orderBy: timestamp, orderDirection: $orderDirection, first: $limit) {
          timestamp
          sharePrice
          totalAssets
          iBGT
          totalSupply
        }
      }
    }
  `;
};

export const vaultByTypeWithSharePriceHistoryQuery = (): DocumentNode => {
  return gql`
    query GetVaultWithSharePriceHistoryQuery($vaultAddress: ID!, $from: BigInt!, $to: BigInt!, $limit: Int, $orderDirection: OrderDirection!, $type: VaultSnapshotEvent) {
      vault(id: $vaultAddress) {
        vaultSnapshot(where: { timestamp_gte: $from, timestamp_lte: $to, snapshotEvent: $type }, orderBy: timestamp, orderDirection: $orderDirection, first: $limit) {
          timestamp
          sharePrice
        }
      }
    }
  `;
};
export const vaultSlotByTypeWithSharePriceHistoryQuery = (): DocumentNode => {
  return gql`
    query GetVaultWithSharePriceHistoryQuery($vaultAddress: ID!, $limit: Int, $type: VaultSlotBucket, $minObservations: Int) {
      vault(id: $vaultAddress) {
        vaultSlot(where: { bucket: $type, observations_gt: $minObservations }, orderBy: slotIndex, orderDirection: desc, first: $limit) {
          startTime
          startBalance
          startTotalSupply
          lastTime
          lastBalance
          lastTotalSupply
          cumTime
          cumBalance
          cumTotalSupply
          observations
        }
      }
    }
  `;
};
export const vaultPositionChangesQuery = (start?: number, end?: number): DocumentNode => {
  // Condition for the timestamp
  let whereClause = "";

  if (start && end) {
    whereClause = `transaction_: { timestamp_gte: $start, timestamp_lte: $end } `;
  } else if (start) {
    whereClause = `transaction_: { timestamp_gte: $start }`;
  } else if (end) {
    whereClause = `transaction_: { timestamp_lte: $end }`;
  }

  return gql`
   query vaultPosition($user: ID!, $start: Int, $end: Int) {
    vaultPositions(where: {owner: $user}) {
      __typename
      id
      vault{
        asset {
          id
        }
      }
      assets
      deposited
      shares
      withdrawn {
        id
        asset {
          symbol
        }
        balance
      }
      changes(where: { ${whereClause} }) {
        id
        assets
        shares
        deposited
        shareChange
        vaultOperation
        withdrawn {
          id
          asset {
            symbol
          }
          balance
        }
        transaction {
          timestamp
          id
        }
      }
    }
  }
  `;
};

export const vaultPositionShareAtBlock = (): DocumentNode => {
  return gql`
    query vaultPositionAtBlock($vaultAddress: String!, $blockNumber: Int!) {
      vaultPositions(where: { vault: $vaultAddress }, first: 1000) {
        changes(where: { blockNumber_lte: $blockNumber }, orderBy: blockNumber, orderDirection: desc, first: 2) {
          owner {
            id
          }
          shares
        }
      }
    }
  `;
};

export const vaultUsers = (): DocumentNode => {
  return gql`
    query vaultUsers($vaultAddress: String!) {
      vaultPositions(where: { vault: $vaultAddress }, first: 1000) {
        owner {
          id
        }
      }
    }
  `;
};
