import { gql } from "@apollo/client/core";
import { TransactionFragment } from "./transaction.fragment";
import { DenFragment } from "./den.fragment";
import { RedemptionFragment } from "./redemption.fragment";
import { LiquidationFragment } from "./liquidation.fragment";

export const ChangeFragment = gql`
  ${TransactionFragment}
  fragment changeFragment on Change {
    transaction {
      ...transactionFragment
    }
  }
`;

export const DenChangeFragment = gql`
  ${DenFragment}
  ${ChangeFragment}
  ${RedemptionFragment}
  ${LiquidationFragment}
  fragment denChangeFragment on DenChange {
    ...changeFragment
    denManager {
      id
    }
    den {
      ...denFragment
    }
    denOperation
    collateralChange
    debtChange
    collateralRatioBefore
    collateralRatioAfter
    borrowingFee
    redemption {
      ...redemptionFragment
    }
    liquidation {
      ...liquidationFragment
    }
  }
`;

export const DenChangeWithRedemptionFragment = gql`
  ${ChangeFragment}
  ${RedemptionFragment}
  fragment denChangeWithRedemptionFragment on DenChange {
    ...changeFragment
    redemption {
      ...redemptionFragment
    }
  }
`;

export const DenChangeWithLiquidationFragment = gql`
  ${ChangeFragment}
  ${LiquidationFragment}
  fragment denChangeWithLiquidationFragment on DenChange {
    ...changeFragment
    liquidation {
      ...liquidationFragment
    }
  }
`;

export const SharePositionChangeFragment = gql`
  ${ChangeFragment}
  fragment sharePositionChangeFragment on SharePositionChange {
    ...changeFragment
    shareOperation
    shareAmountChange
    shareAmountBefore
    shareAmountAfter
    __typename
  }
`;
