import { gql } from "@apollo/client/core";
import { TokenFragment } from "./token.fragment";

export const DenFragment = gql`
  ${TokenFragment}
  fragment denFragment on Den {
    id
    owner {
      id
    }
    denManager {
      collateral {
        ...tokenFragment
      }
    }
    status
    collateral
    debt
    rawCollateral
    rawDebt
    rawStake
    accuredDebtRewards
    accuredCollateralRewards
  }
`;
