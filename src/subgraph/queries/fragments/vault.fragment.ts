import { gql } from "@apollo/client/core";
import { TokenFragment } from "./token.fragment";

export const VaultSnapshotFragment = gql`
  fragment VaultSnapshotFragment on VaultSnapshot {
    timestamp
    sharePrice
  }
`;

export const VaultFragment = gql`
  ${TokenFragment}
  fragment VaultFragment on Vault {
    id
    asset {
      ...tokenFragment
    }
  }
`;

export const VaultPositionFragment = gql`
  fragment VaultPositionFragment on VaultPosition {
    id
    owner {
      id
    }
    shares
  }
`;
