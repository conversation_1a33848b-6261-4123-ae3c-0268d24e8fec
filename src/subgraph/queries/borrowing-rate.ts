import { DocumentNode, gql } from "@apollo/client/core";

export const historicalBorrowingFeeRatesQuery = (): DocumentNode => {
  return gql`
    query GetHistoricalBorrowingFeeRates($denManager: ID, $start: BigInt, $end: BigInt) {
      borrowingRateSnapshots(where: { denManager: $denManager, timestamp_gte: $start, timestamp_lte: $end }) {
        id
        blockNumber
        timestamp
        feeRate
      }
    }
  `;
};
