import { DocumentNode, gql } from "@apollo/client/core";

export const usersQuery = (): DocumentNode => {
  return gql`
		query GetUsers() {
			users {
				id
                totalDepositVolumeInDen
			}
		}
	`;
};

export const userDepositVolumeInDenQuery = (): DocumentNode => {
  return gql`
    query GetUser($user: ID) {
      user(id: $user) {
        id
        totalDepositVolumeInDen
      }
    }
  `;
};
