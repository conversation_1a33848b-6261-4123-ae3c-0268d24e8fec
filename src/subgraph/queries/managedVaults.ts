import { DocumentNode, gql } from "@apollo/client/core";

export const managedVaultPositionShareAtBlock = (): DocumentNode => {
  return gql`
    query managedVaulPositionAtBlock($managedVaultAddress: String!, $blockNumber: Int!) {
      managedVaultPositions(where: { managedVault: $managedVaultAddress }, first: 1000) {
        changes(where: { blockNumber_lte: $blockNumber }, orderBy: blockNumber, orderDirection: desc, first: 2) {
          owner {
            id
          }
          id
          blockNumber
          shareChange
          shares
          vaultOperation
          transaction {
            id
          }
        }
      }
    }
  `;
};

export const managedVaultUsers = (): DocumentNode => {
  return gql`
    query managedVaultUsers($managedVaultAddress: String!) {
      managedVaultPositions(where: { managedVault: $managedVaultAddress }, first: 1000) {
        owner {
          id
        }
        id
      }
    }
  `;
};

export const managedVaultWithdrawRequests = (): DocumentNode => {
  return gql`
    query managedVaultWithdrawRequests($user: String!, $state: ManagedVaultWithdrawRequestState) {
      managedVaultWithdrawRequests(first: 1000, where: { owner: $user, managedVaultWithdrawRequestState: $state }) {
        id
        managedVault {
          id
        }
        managedVaultEpoch {
          epochNumber
        }
        withdrawShares
      }
    }
  `;
};

export const managedVaultPositionChangesQuery = (start?: number, end?: number): DocumentNode => {
  // Condition for the timestamp
  let whereClause = "";

  if (start && end) {
    whereClause = `transaction_: { timestamp_gte: $start, timestamp_lte: $end } `;
  } else if (start) {
    whereClause = `transaction_: { timestamp_gte: $start }`;
  } else if (end) {
    whereClause = `transaction_: { timestamp_lte: $end }`;
  }

  return gql`
   query managedVaultPosition($user: String!, $start: Int, $end: Int) {
    managedVaultPositions(where: {owner: $user}) {
      __typename
      id
      managedVault{
        asset {
          id
        }
      }
      assets
      deposited
      shares
      withdrawn {
        id
        asset {
          symbol
        }
        balance
      }
      changes(where: { ${whereClause} }) {
        id
        assets
        shares
        deposited
        shareChange
        vaultOperation
        withdrawn {
          id
          asset {
            symbol
          }
          balance
        }
        transaction {
          timestamp
          id
        }
      }
    }
  }
  `;
};
