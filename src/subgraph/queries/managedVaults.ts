import { DocumentNode, gql } from "@apollo/client/core";

export const managedVaultPositionShareAtBlock = (): DocumentNode => {
  return gql`
    query managedVaulPositionAtBlock($managedVaultAddress: String!, $blockNumber: Int!) {
      managedVaultPositions(where: { managedVault: $managedVaultAddress }, first: 1000) {
        changes(where: { blockNumber_lte: $blockNumber }, orderBy: blockNumber, orderDirection: desc, first: 2) {
          owner {
            id
          }
          id
          blockNumber
          shareChange
          shares
          vaultOperation
          transaction {
            id
          }
        }
      }
    }
  `;
};

export const managedVaultUsers = (): DocumentNode => {
  return gql`
    query managedVaultUsers($managedVaultAddress: String!) {
      managedVaultPositions(where: { managedVault: $managedVaultAddress }, first: 1000) {
        owner {
          id
        }
        id
      }
    }
  `;
};
