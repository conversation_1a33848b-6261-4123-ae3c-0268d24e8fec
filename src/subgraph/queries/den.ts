import { DocumentNode, gql } from "@apollo/client/core";
import { TokenFragment } from "./fragments/token.fragment";
import { DenFragment } from "./fragments/den.fragment";
import { DenChangeFragment } from "./fragments/change.fragment";
import { RedemptionFragment } from "./fragments/redemption.fragment";
import { TransactionFragment } from "./fragments/transaction.fragment";
import { LiquidationFragment } from "./fragments/liquidation.fragment";

export const densQuery = (): DocumentNode => {
  return gql`
    ${TokenFragment}
    ${DenFragment}
    query GetDens($first: Int, $skip: Int) {
      dens(first: $first, skip: $skip) {
        ...denFragment
      }
    }
  `;
};

export const densByDenManagerQuery = (): DocumentNode => {
  return gql`
    ${TokenFragment}
    ${DenFragment}
    query GetDens($denManagerID: ID!, $first: Int, $skip: Int) {
      dens(where: { denManager: $denManagerID }, first: $first, skip: $skip) {
        ...denFragment
      }
    }
  `;
};

export const densByOwnerQuery = (): DocumentNode => {
  return gql`
    ${TokenFragment}
    ${DenFragment}
    query GetDens($user: ID!) {
      dens(where: { owner: $user }) {
        ...denFragment
      }
    }
  `;
};

export const denChangesOfUserDens = (user: string, collateral?: string, start?: number, end?: number): DocumentNode => {
  // Build the filter conditions
  let whereClause = `den_: { owner: $user }`;

  if (collateral) {
    whereClause += `, denManager_: { collateral: $collateral }`;
  }

  if (start && end) {
    whereClause += `, transaction_: { timestamp_gte: $start, timestamp_lte: $end }`;
  } else if (start) {
    whereClause += `, transaction_: { timestamp_gte: $start }`;
  } else if (end) {
    whereClause += `, transaction_: { timestamp_lte: $end }`;
  }

  return gql`
		${DenChangeFragment}
		query GetDenChangesOfUserDens($user: ID!, $collateral: ID, $start: Int, $end: Int) {
			denChanges(where: { ${whereClause} }) {
				...denChangeFragment
			}
    	}
	`;
};

export const denQuery = (): DocumentNode => {
  return gql`
    ${TokenFragment}
    ${DenFragment}
    query GetDens($id: ID!) {
      den(id: $id) {
        ...denFragment
      }
    }
  `;
};

export const redemptionsByRedeemer = (redeemer: string, start?: number, end?: number): DocumentNode => {
  // Build the filter conditions
  let whereClause = `redeemer: $redeemer`;

  if (start && end) {
    whereClause += `, transaction_: { timestamp_gte: $start, timestamp_lte: $end }`;
  } else if (start) {
    whereClause += `, transaction_: { timestamp_gte: $start }`;
  } else if (end) {
    whereClause += `, transaction_: { timestamp_lte: $end }`;
  }

  return gql`
		${RedemptionFragment}
		${TransactionFragment}
		query GetRedemptionsByRedeemer($redeemer: ID!, $start: Int, $end: Int) {
			redemptions(where: { ${whereClause} }) {
				transaction {
					...transactionFragment
				}
				...redemptionFragment
			}
		}
	`;
};

export const liquidationsByLiquidator = (liquidator: string, start?: number, end?: number): DocumentNode => {
  // Build the filter conditions
  let whereClause = `liquidator: $liquidator`;

  if (start && end) {
    whereClause += `, transaction_: { timestamp_gte: $start, timestamp_lte: $end }`;
  } else if (start) {
    whereClause += `, transaction_: { timestamp_gte: $start }`;
  } else if (end) {
    whereClause += `, transaction_: { timestamp_lte: $end }`;
  }

  return gql`
		${LiquidationFragment}
		${TransactionFragment}
		query GetLiquidationsByLiquidator($liquidator: ID!, $start: Int, $end: Int) {
			liquidations(where: { ${whereClause} }) {
				transaction {
					...transactionFragment
				}
				...liquidationFragment
			}
		}
	`;
};
