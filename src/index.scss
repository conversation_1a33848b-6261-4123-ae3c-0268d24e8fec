// Variables
:root {
  --background-default: rgba(130, 106, 95, 0.25);
  --background-default-half: rgba(130, 106, 95, 0.6);
  --background-default-full: rgba(130, 106, 95);

  --background-accent: rgba(24, 20, 18, 0.75);
  --background-accent-full: rgba(24, 20, 18);

  --background-tertiary: rgba(36, 30, 27, 0.5);

  --primary-light: #f8e9b3;
  --primary-main: #ec6f15;
  --primary-dark: #eaab12;

  --secondary-light: #ed6843;
  --secondary-main: #d55002;
  --secondary-dark: #a32e0e;

  --text-primary: #ffedd4;
  --text-success: #3db250;
  --text-error: #f00;

  --text-secondary: #8d7366;
  --text-disabled: rgba(141, 115, 102, 0.25);

  --error-light: #ff755f;
  --error-main: #dc2c10;
  --error-dark: #c62828;

  --warning-light: #ff9800;
  --warning-main: #fd9d28;
  --warning-dark: #e65100;

  --info-light: #03a9f4;
  --info-main: #1542cd;
  --info-dark: #01579b;

  --success-light: #4caf50;
  --success-main: #1dffad;
  --success-dark: #1b5e20;

  --border-color: #826a5f40;
  --border-dark: #826a5f20;
  --border-dark-secondary: #181412bf;
  --primary-gradient: linear-gradient(180deg, #ec6f15 0%, #d34c00 100%);
  --background-gradient: linear-gradient(180deg, #2f2824 0%, #181412 100%);
  --modal-gradient: linear-gradient(180deg, rgba(67, 55, 49, 0.5), rgba(39, 32, 29, 0.25));
  --success-gradient: linear-gradient(180deg, #12c987 0%, #04914e 100%);
  --error-gradient: linear-gradient(180deg, #ec1515 0%, #d30000 100%);

  --chip-success-bg: #3db2501a;
  --icon-disabled: #8d736640;
  --icon-active: #ffedd4;
}

// Fonts
@font-face {
  font-family: "OpenRunde";
  src: url("/fonts/OpenRunde-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "OpenRunde";
  src: url("/fonts/OpenRunde-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "OpenRunde";
  src: url("/fonts/OpenRunde-SemiBold.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: "OpenRunde";
  src: url("/fonts/OpenRunde-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
}

// Global styles
html,
body,
#root {
  height: 100%;
  color: var(--text-primary);
  background-image: var(--background-gradient);
  background-size: cover; /* Ensure the gradient covers the entire background */
  background-repeat: no-repeat; /* Prevents repeating the gradient */
  background-attachment: fixed; /* Keeps the background fixed on scroll */
}

body {
  margin: 0;

  // If there is more than one modal open
  // Only show the backdrop of the latest one
  .MuiModal-root {
    &:last-of-type,
    &:has(+ .MuiTooltip-popper) {
      .MuiBackdrop-root {
        background-color: rgba(18, 18, 18, 0.7) !important;
        -webkit-backdrop-filter: blur(5px);
        backdrop-filter: blur(5px);
      }
      .MuiBackdrop-invisible {
        background-color: transparent !important;
        -webkit-backdrop-filter: blur(0px) !important;
        backdrop-filter: blur(0px) !important;
      }
    }
    .MuiBackdrop-root,
    .MuiBackdrop-invisible {
      background-color: transparent;
      -webkit-backdrop-filter: blur(0px);
      backdrop-filter: blur(0px);
    }
  }
}

p {
  margin-top: 0;
  margin-bottom: 1.25rem;
}

a {
  color: var(--primary-main);
  text-decoration: none;
  &:hover {
    text-decoration: underline;
  }
}

hr {
  color: rgba(255, 255, 255, 0.12);
  border: none;
  border-top: 1px solid rgba(255, 255, 255, 0.12);
}

.truncated-text-one-line {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.truncated-text-two-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

svg {
  &.custom-svg {
    stroke: var(--text-primary);
    stroke-width: 1.5px;
    transition: all 0.5s ease;
    &.primary {
      stroke: var(--primary-main);
    }
    &.secondary {
      stroke: var(--secondary-main);
    }
    &.disabled {
      stroke: rgba(255, 255, 255, 0.12);
    }
  }
}

/* Override the blue Autocomplete bg color in webkit browsers: */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 1000px var(--background-default) inset !important;
  transition: background-color 5000s ease-in-out 0s;
}

input {
  /* Chrome, Safari, Edge, Opera */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  &[type="number"] {
    appearance: textfield;
    -moz-appearance: textfield;
  }
  &.code-input {
    height: 64px;
    text-align: center;
    font-size: 2rem;
    font-weight: bold;
    padding: 0px 10px;
    @media screen and (min-width: 500px) {
      height: 90px;
      font-size: 2.5rem;
    }
    @media screen and (min-width: 600px) {
      height: 110px;
      font-size: 3rem;
      line-height: 1rem;
    }
  }
}

.MuiAutocomplete-root {
  .MuiInputAdornment-positionStart {
    position: relative;
    left: 5px;
  }
}

.MuiAlert-message {
  font-size: 1rem;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(-50%);
  }
}
