import { createTheme } from "@mui/material/styles";

const colors = {
  primary: "#EC6F15",
  secondary: "#D55002",
  success: "#1DFFAD",
  warning: "#fd9d28", //old
  error: "#FF0000",
  errorHover: "#D30000",
  info: "#1542cd", //old
  white: "#FFEDD4",
  text: "#FFEDD4",
  background: "#826A5F40",
  backgroundSecondary: "#181412BF",
  backgroundSecondarySolid: "#181412",
  backgroundTertiary: "#241E1B80",
  backgroundTertiarySolid: "#241E1B",
  backgroundQuaternary: "#241e1b", // Added for bottom link container
  muted: "#8D7366",
  disabled: "#8D736640",
  textDisabled: "#FFEDD480", // Added for specific disabled text color
};
const gradients = {
  primary: "linear-gradient(#EC6F15,#D34C00)",
  background: "linear-gradient(180deg,#2F2824  0% ,#181412 100% )",
  modal: "linear-gradient(180deg, #43373180 , #27201D40)",
  success: "linear-gradient(180deg,#12C987  0% , #04914E 100% )",
  error: "linear-gradient(180deg,#EC1515  0% ,#D30000 100% )",
};
const borderRadius = 12;
const theme = createTheme({
  palette: {
    primary: {
      main: colors.primary,
    },
    secondary: {
      main: colors.secondary,
    },
    success: {
      main: colors.success,
    },
    warning: {
      main: colors.warning,
    },
    error: {
      main: colors.error,
      dark: colors.errorHover,
    },
    info: {
      main: colors.info,
    },
    text: {
      primary: colors.text,
      secondary: colors.muted,
      disabled: colors.disabled,
    },
    background: {
      default: colors.background,
      paper: colors.backgroundSecondary,
    },
    custom: {
      backgroundQuaternary: colors.backgroundQuaternary,
      backgroundTertiary: colors.backgroundTertiary,
      backgroundTertiarySolid: colors.backgroundTertiarySolid,
      backgroundSecondarySolid: colors.backgroundSecondarySolid,
      textDisabled: colors.textDisabled,
      white: colors.white,
    },
  },

  typography: {
    fontFamily: ["OpenRunde", "-apple-system", "BlinkMacSystemFont", '"Segoe UI"', "Roboto", '"Helvetica Neue"', "sans-serif"].join(", "),

    h1: {
      fontSize: "22px",
      fontWeight: 600,
      lineHeight: "26px",
    },

    h2: {
      fontSize: "20px",
      lineHeight: "24px",
      fontWeight: 600,
    },
    h3: {
      fontSize: "18px",
      lineHeight: "22px",
      fontWeight: 600,
    },
    h4: {
      fontSize: "16px",
      lineHeight: "20px",
      fontWeight: 600,
    },
    h5: {
      fontSize: "14px",
      lineHeight: "18px",
      fontWeight: 500,
    },
    h6: {
      fontSize: "12px",
      lineHeight: "14px",
      fontWeight: 500,
    },
    body1: {
      fontSize: "12px",
      fontWeight: 500,
      lineHeight: "16px",
    },
    body2: {
      fontSize: "9px",
      fontWeight: 700,
      lineHeight: "13px",
    },
    subtitle1: {
      fontSize: "16px",
      fontWeight: 500,
      lineHeight: "22px",
      color: colors.muted,
    },
    subtitle2: {
      fontSize: "14px",
      fontWeight: 500,
      lineHeight: "18px",
    },

    caption: {
      fontSize: "9px",
      fontWeight: 700,
      lineHeight: "13px",
      color: colors.text,
    },
  },
  components: {
    MuiLink: {
      styleOverrides: {
        root: {
          textDecoration: "none",
          color: colors.primary,
          transition: "all 0.2s ease-in-out",
          "&:hover": {
            textDecoration: "underline",
          },

          // Contained variant styles (only apply when the class is used)
          "&.containedLink": {
            textTransform: "inherit",
            textDecoration: "unset",
            fontSize: "12px",
            fontWeight: 600,
            padding: "10px 16px",
            borderRadius: borderRadius,
            backgroundColor: colors.primary,
            color: colors.text,
            border: `1px solid ${colors.primary}`,

            "&:hover": {
              backgroundColor: "transparent",
              color: colors.primary,
              border: `1px solid ${colors.primary}`,
            },
            "&:disabled": {
              backgroundColor: colors.background,
              color: colors.primary,
              border: `1px solid ${colors.background}`,
            },
          },
        },
      },
    },

    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "inherit",
          textDecoration: "unset",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "10px 32px",
          borderRadius: borderRadius,
          "&:disabled": {
            backgroundColor: colors.background,
            color: colors.primary,
            border: `1px solid ${colors.background}`,
          },
        },

        contained: {
          backgroundColor: colors.primary,
          color: colors.text,
          border: `1px solid ${colors.primary}`,
          "&:hover": {
            backgroundColor: "transparent",
            color: colors.primary,
            border: `1px solid ${colors.primary}`,
          },
          "&:disabled": {
            backgroundColor: colors.background,
          },
        },
        outlined: {
          borderColor: colors.primary,
          color: colors.primary,
          backgroundColor: "transparent",
          "&:hover": {
            backgroundColor: colors.primary,
            color: colors.text,
            borderColor: colors.primary,
          },
        },
        text: {
          color: colors.text,
          textDecoration: "underline",
          padding: 0,
          margin: "0 !important",

          "&:hover": {
            backgroundColor: "transparent",
          },
          "&:disabled": {
            color: `${colors.text} !important`,
          },
        },
        sizeSmall: {
          padding: "5px ",
          fontSize: "9px",
          fontWeight: 500,
          lineHeight: "13px",
          color: colors.text,
          backgroundColor: colors.background,
          borderColor: colors.background,
          textDecoration: "unset",
          minWidth: "unset",
          opacity: 1,
          "@media (min-width:600px)": {
            padding: "5px 10px", // Padding for medium screens and up
          },
        },
        sizeMedium: {
          fontSize: "1rem",
        },
        sizeLarge: {
          paddingTop: "10px",
          paddingBottom: "10px",
          fontSize: "1.2rem",
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: "30px",
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "10px", // Padding for medium screens and up
          },
          border: colors.background,
          backgroundColor: "transparent",
          backgroundImage: gradients.modal,
        },
      },
    },
    MuiAccordion: {
      styleOverrides: {
        root: {
          border: 0,
          backgroundColor: colors.backgroundSecondary,
          backgroundImage: "unset",
          "&.Mui-expanded": { margin: "5px 0" },
          ":before": { opacity: 0 },
        },
        rounded: {
          padding: "16px",
          borderRadius: "16px !important",
          backgroundColor: colors.backgroundSecondary,
        },
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        expandIconWrapper: {
          color: colors.white,
        },
        root: {
          minHeight: "unset",
          "&.Mui-expanded": { minHeight: "unset" },
        },
        content: {
          margin: 0,
          "&.Mui-expanded": { margin: "5px 0", minHeight: "unset" },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          position: "relative",
          marginTop: "16px",
          border: `1px solid ${colors.muted}`,
          boxShadow: "0px 4px 8px rgba(41, 49, 71, 0.1)",
          padding: "16px",
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        root: {
          padding: "5px",
          fontSize: "16px",
          lineHeight: "20px",
          fontWeight: 600,
          color: colors.text,
          backgroundColor: colors.background,
          borderColor: colors.background,

          opacity: 1,
          "@media (min-width:600px)": {
            fontSize: "18px",
            lineHeight: "22px",
            padding: "5px",
          },
        },
        standard: {},
        outlined: {
          padding: "5px",

          "@media (min-width:600px)": {
            padding: "5px 10px", // Padding for medium screens and up
          },
        },

        icon: {
          color: "white",
        },
      },
    },
    MuiMenu: {
      styleOverrides: {
        paper: {
          opacity: 0.7,
          borderRadius: "12px 12px",
          padding: 0,
          "@media (min-width:600px)": {
            padding: 0, // Padding for medium screens and up
          },
          backgroundColor: colors.backgroundTertiarySolid,
        },
        list: {
          padding: 0,
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          borderRadius: "12px",
          "&:hover": {
            backgroundColor: colors.backgroundTertiarySolid,
          },
          "&.Mui-selected": {
            backgroundColor: colors.background,
            "&:hover": {
              backgroundColor: colors.backgroundTertiarySolid,
            },
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          padding: "0 8px",
          "@media (min-width:600px)": {
            padding: "8px 16px", // Padding for medium screens and up
          },
          fontSize: "16px",
          lineHeight: "normal",
          fontWeight: 600,
          borderRadius: borderRadius,
          borderColor: colors.background,
          backgroundColor: colors.backgroundSecondary,
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: colors.background,
          },

          input: {
            "&.Mui-disabled": {
              color: colors.text,
              WebkitTextFillColor: colors.text,
            },
          },
        },
      },
    },
    MuiInput: {
      styleOverrides: {
        root: {
          fontSize: "16px",
          lineHeight: "normal",
          fontWeight: 600,
          backgroundColor: "transparent",
          input: {
            "&.Mui-disabled": {
              color: colors.text,
              WebkitTextFillColor: colors.text,
            },
          },
        },
      },
    },
    MuiSlider: {
      styleOverrides: {
        root: {
          marginBottom: "0px",
        },
        thumb: {
          border: "3px solid " + colors.primary,
          color: colors.text,
        },
        rail: {
          color: colors.background,
        },
        track: {
          color: colors.primary,
        },
        mark: {
          color: colors.muted,
        },
        valueLabel: {
          transform: "translateY(100%) translateX(-50%)", // Move the label to the bottom
          top: "45px", // Adjust as needed to fine-tune the position
          "& *": {
            transform: "none",
          },
          ":before": {
            top: "-8px",
          },
          borderRadius: "10px",
          paddingX: "0.25rem 0.75rem",
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        sizeSmall: {
          fontSize: "10px",
          fontWeight: 500,
          lineHeight: "13px",
          padding: "2px 0",
          height: "unset",
        },
        sizeMedium: {
          fontSize: "10px",
          fontWeight: 500,
          lineHeight: "13px",
          padding: "2px 0",
          height: "unset",
        },
        colorPrimary: {
          color: colors.primary,
          backgroundColor: colors.primary + "33",
        },
        colorError: {
          color: colors.error,
          backgroundColor: colors.error + "33",
        },
        colorSuccess: {
          color: colors.success,
          backgroundColor: colors.success + "33",
        },
        colorWarning: {
          color: colors.warning,
          backgroundColor: colors.warning + "33",
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          fontSize: "22px",
          fontWeight: 600,
          textTransform: "unset",
          lineHeight: "26px",
          "&.Mui-selected": {
            color: colors.text,
          },
          "&:hover": {
            color: colors.text,
          },
          "&.Mui-focusVisible": {
            backgroundColor: colors.text,
          },
        },
      },
    },
  },
});

// Extend the theme type to include custom colors
declare module "@mui/material/styles" {
  interface Palette {
    custom: {
      backgroundQuaternary: string;
      backgroundTertiary: string;
      backgroundTertiarySolid: string;
      backgroundSecondarySolid: string;
      textDisabled: string;
      white: string;
    };
  }

  interface PaletteOptions {
    custom?: {
      backgroundQuaternary?: string;
      backgroundTertiary?: string;
      backgroundTertiarySolid?: string;
      backgroundSecondarySolid?: string;
      textDisabled?: string;
      white?: string;
    };
  }
}

export default theme;
