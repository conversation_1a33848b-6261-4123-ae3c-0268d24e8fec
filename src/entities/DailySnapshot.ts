import { Address, BigInt, ethereum } from "@graphprotocol/graph-ts";
import { FinancialDailySnapshot, MarketDailySnapshot, MetricsDailySnapshot } from "../../generated/schema";
import { getGlobal } from "./Global";
import { BIGINT_SCALING_FACTOR, BIGINT_ZERO } from "../utils/bignumbers";
import { createDenManagerSnapshot, getDenManager } from "./DenManager";
import { getSharePool, updatePoolPortfolio } from "./SharePool";
import { BorrowerOperations } from "../../generated/BorrowerOperations/BorrowerOperations";

export const takeFinancialDailySnapshot = (block: ethereum.Block): void => {
  const global = getGlobal();

  const dayID = block.timestamp.toI32() / 86400;
  const previousDayID = dayID - 1; // Previous day ID
  const previousDayStartTimestamp = previousDayID * 86400; // Start timestamp of the previous day
  const snapshotId = previousDayID.toString(); // Previous day snapshot ID

  let snapshot = FinancialDailySnapshot.load(snapshotId);
  if (!snapshot) {
    snapshot = new FinancialDailySnapshot(snapshotId);
    snapshot.dayStartTimestamp = BigInt.fromI32(previousDayStartTimestamp);
    snapshot.blockNumber = block.number;
    snapshot.timestamp = block.timestamp;
    snapshot.dailyBorrowUSD = BIGINT_ZERO;
    snapshot.dailyDepositUSD = BIGINT_ZERO;
    snapshot.totalValueLockedUSD = BIGINT_ZERO;
    snapshot.debtTokenSupplies = BIGINT_ZERO;
    let borrowerOperations_contract = BorrowerOperations.bind(Address.fromString(global.borrowerOperations_address));
    let globalSystemBalances = borrowerOperations_contract.try_getGlobalSystemBalances();
    if (globalSystemBalances.reverted === false) {
      snapshot.totalValueLockedUSD = globalSystemBalances.value.getTotalPricedCollateral().div(BIGINT_SCALING_FACTOR);
      snapshot.debtTokenSupplies = globalSystemBalances.value.getTotalDebt();
    }
    //  a snapshot of LSP and attach ID
    updatePoolPortfolio(block);
    const sharePool = getSharePool(block);
    snapshot.sharePoolPortfolio = sharePool.portfolio;
    let denManagerSnapshots: string[] = [];
    let denManagers = global.denManagers.load();
    for (let i = 0; i < denManagers.length; i++) {
      let denManagerId = Address.fromString(denManagers[i].id);
      denManagerSnapshots.push(createDenManagerSnapshot(denManagerId, block).id);
    }
    snapshot.denManagerSnapshots = denManagerSnapshots;
    snapshot.save();
  }
};

export const getMarketDailySnapshot = (event: ethereum.Event, denManagerAddress: Address): MarketDailySnapshot => {
  const denManager = getDenManager(denManagerAddress);

  const dayID = event.block.timestamp.toI32() / 86400;
  const dayStartTimestamp = dayID * 86400;
  const snapshotId = dayID.toString();

  let snapshot = MarketDailySnapshot.load(snapshotId);
  if (!snapshot) {
    snapshot = new MarketDailySnapshot(snapshotId);
    snapshot.dayStartTimestamp = BigInt.fromI32(dayStartTimestamp);
    snapshot.dailyBorrowUSD = BIGINT_ZERO;
    snapshot.dailyDepositUSD = BIGINT_ZERO;
    snapshot.debtTokenSupplies = BIGINT_ZERO;
  }
  snapshot.blockNumber = event.block.number;
  snapshot.timestamp = event.block.timestamp;

  return snapshot;
};

export const getMetricsDailySnapshot = (event: ethereum.Event): MetricsDailySnapshot => {
  const global = getGlobal();

  const dayID = event.block.timestamp.toI32() / 86400;
  const dayStartTimestamp = dayID * 86400;
  const snapshotId = dayID.toString();

  let snapshot = MetricsDailySnapshot.load(snapshotId);
  if (!snapshot) {
    snapshot = new MetricsDailySnapshot(snapshotId);

    snapshot.dayStartTimestamp = BigInt.fromI32(dayStartTimestamp);
  }

  snapshot.blockNumber = event.block.number;
  snapshot.timestamp = event.block.timestamp;

  snapshot.cumulativeUniqueUsers = global.cumulativeUniqueUsers;
  return snapshot;
};
