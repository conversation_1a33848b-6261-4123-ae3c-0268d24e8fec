import { Address, BigInt, ethereum } from "@graphprotocol/graph-ts";
import { PriceFeed, SharePool, TokenPrice } from "../../generated/schema";
import { BIGINT_ZERO, BIGINT_SCALING_FACTOR } from "../utils/bignumbers";
import { StabilityPool } from "../../generated/StabilityPool/StabilityPool";
import { getToken } from "./Token";
export const generateTokenPriceId = (token_address: Address, block: ethereum.Block): string => {
  return "price" + "/" + token_address.toHex() + "/" + block.number.toString();
};

export const getTokenPrice = (token_address: Address, block: ethereum.Block): TokenPrice => {
  let id = generateTokenPriceId(token_address, block);
  let tokenPrice = TokenPrice.load(id);
  if (tokenPrice == null) {
    tokenPrice = new TokenPrice(id);
    tokenPrice.token = token_address.toHex();
    tokenPrice.price = BIGINT_ZERO;
    tokenPrice.timestamp = block.timestamp;
    tokenPrice.save();
  }

  return tokenPrice;
};
export const updateTokenPrice = (price: BigInt, token_address: Address, block: ethereum.Block): void => {
  let token = getToken(token_address);
  let tokenPrice = getTokenPrice(token_address, block);
  tokenPrice.price = price;
  tokenPrice.save();
  token.price = tokenPrice.id;
  token.save();
};
export const fetchTokenPriceFromContract = (token_address: Address, block: ethereum.Block): BigInt | null => {
  // Get token price
  let id = generateTokenPriceId(token_address, block);

  let tokenPrice = TokenPrice.load(id);
  if (tokenPrice === null) {
    let sharePool = SharePool.load("SharePool");
    if (sharePool != null) {
      let pool_address = sharePool.pool_address;
      if (pool_address != null) {
        let stabilityPool_contract = StabilityPool.bind(Address.fromString(pool_address));
        let price = stabilityPool_contract.try_getPrice(token_address);

        if (price.reverted == false) {
          updateTokenPrice(price.value, token_address, block);
          return price.value; // Return the price as a value
        }
      }
    }
    return null; // Return null if price could not be fetched
  } else {
    return tokenPrice.price; // return existing Price
  }
};
export const getLastAvailableTokenPrice = (token_address: Address): BigInt => {
  let token = getToken(token_address);
  // Check explicitly if token.price is not null before using it
  if (token.price != null) {
    // Ensure it's not null
    let tokenPrice = TokenPrice.load(token.price as string); // Cast to string
    if (tokenPrice != null) {
      // Check if tokenPrice is found
      return tokenPrice.price;
    }
  }
  return BIGINT_ZERO;
};

export const getLatestTokenPrice = (token_address: Address, block: ethereum.Block): BigInt => {
  // Update token price
  let price = fetchTokenPriceFromContract(token_address, block);
  if (price === null) {
    //fetch latest token price
    let latestPrice = getLastAvailableTokenPrice(token_address);
    return latestPrice;
  }

  return price;
};
export function getDebtTokenPrice(block: ethereum.Block): BigInt {
  const pool = SharePool.load("SharePool");
  if (!pool) {
    return BIGINT_SCALING_FACTOR;
  } else {
    let debtTokenAddress = Address.fromString(pool.debt_token);
    return getLatestTokenPrice(debtTokenAddress, block);
  }
}
