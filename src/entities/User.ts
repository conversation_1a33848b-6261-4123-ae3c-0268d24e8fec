import { Address } from "@graphprotocol/graph-ts";
import { User } from "../../generated/schema";
import { BIGINT_ZERO } from "../utils/bignumbers";
import { getGlobal } from "./Global";

export function getUser(_user: Address): User {
  let id = _user.toHexString();
  let userOrNull = User.load(id);

  if (userOrNull != null) {
    return userOrNull as User;
  } else {
    let newUser = new User(id);
    newUser.totalDepositVolumeInDen = BIGINT_ZERO;

    newUser.save();
    let global = getGlobal();
    global.cumulativeUniqueUsers = global.cumulativeUniqueUsers + 1;
    global.save();

    return newUser;
  }
}
