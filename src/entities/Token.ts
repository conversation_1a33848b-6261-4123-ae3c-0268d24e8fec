import { Address } from "@graphprotocol/graph-ts";
import { Token } from "../../generated/schema";
import { ERC20 } from "../../generated/Factory/ERC20";
import { ZERO_ADDRESS } from "../utils/constants";

export const getToken = (token_address: Address): Token => {
  let token = Token.load(token_address.toHex());
  if (token == null) {
    token = new Token(token_address.toHex());
    token.oracle = ZERO_ADDRESS;

    // Fetch these token metadata from erc20 contract
    let erc20 = ERC20.bind(token_address);

    let _decimals = erc20.try_decimals();
    let _symbol = erc20.try_symbol();
    let _name = erc20.try_name();
    if (_decimals.reverted == false) {
      token.decimals = _decimals.value;
    } else {
      token.decimals = 18;
    }

    if (_symbol.reverted == false) {
      token.symbol = _symbol.value;
    } else {
      token.symbol = "";
    }

    if (_name.reverted == false) {
      token.name = _name.value;
    } else {
      token.name = "";
    }

    token.save();
  }

  return token as Token;
};
