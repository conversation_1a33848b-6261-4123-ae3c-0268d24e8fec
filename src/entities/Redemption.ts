import { ethereum, BigInt, Address } from "@graphprotocol/graph-ts";

import { Redemption } from "../../generated/schema";

import { BIGINT_ZERO } from "../utils/bignumbers";

import { getTransaction } from "./Transaction";
import { getDenManager } from "./DenManager";
import { getUser } from "./User";

export function generateRedemptionId(_denManager: Address, event: ethereum.Event): string {
  return "Redemption" + "/" + _denManager.toHex() + "/" + event.transaction.hash.toHex();
}

export function getRedemption(_denManager: Address, event: ethereum.Event): Redemption {
  let id = generateRedemptionId(_denManager, event);
  let newRedemption = Redemption.load(id);
  if (newRedemption == null) {
    newRedemption = new Redemption(id);
    newRedemption.transaction = getTransaction(event).id;
    newRedemption.redeemer = getUser(event.transaction.from).id;
    newRedemption.redeemer = event.transaction.from.toHex();
    newRedemption.tokensAttemptedToRedeem = BIGINT_ZERO;
    newRedemption.tokensActuallyRedeemed = BIGINT_ZERO;
    newRedemption.collateralRedeemed = BIGINT_ZERO;
    newRedemption.partial = false;
    newRedemption.fee = BIGINT_ZERO;

    newRedemption.denManager = _denManager.toHex();
    newRedemption.save();
  }

  return newRedemption as Redemption;
}

export function finishCurrentRedemption(event: ethereum.Event, _attemptedLUSDAmount: BigInt, _actualLUSDAmount: BigInt, _ETHSent: BigInt, _ETHFee: BigInt): void {
  let fee = _ETHFee;

  let currentRedemption = getRedemption(event.address, event);

  currentRedemption.tokensAttemptedToRedeem = _attemptedLUSDAmount;
  currentRedemption.tokensActuallyRedeemed = _actualLUSDAmount;
  currentRedemption.collateralRedeemed = _ETHSent;
  currentRedemption.partial = _actualLUSDAmount < _attemptedLUSDAmount;
  currentRedemption.fee = fee;
  currentRedemption.denManager = event.address.toHex();
  currentRedemption.save();

  let denManager = getDenManager(event.address);

  denManager.totalRedemptionFeesPaid = denManager.totalRedemptionFeesPaid.plus(fee);
  denManager.redemptionCount = denManager.redemptionCount + 1;

  denManager.save();
}
