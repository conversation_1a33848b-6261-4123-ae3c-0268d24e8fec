import { ethereum, log } from "@graphprotocol/graph-ts";
import { Portfolio } from "../../generated/schema";
import { BIGINT_ZERO } from "../utils/bignumbers";

export function generatePortfoliotId(block: ethereum.Block): string {
  return "portfolio" + "/" + block.timestamp.toString();
}

export function createPortfolio(block: ethereum.Block): Portfolio {
  let id = generatePortfoliotId(block);

  let portfolio = Portfolio.load(id);
  if (portfolio == null) {
    portfolio = new Portfolio(id);
    portfolio.pool = "SharePool";
    portfolio.timestamp = block.timestamp;
    portfolio.totalShares = BIGINT_ZERO;
    portfolio.totalAssets = BIGINT_ZERO;
    portfolio.sharePrice = BIGINT_ZERO;
    portfolio.holdings = [];
    portfolio.emissions = [];

    portfolio.save();
  }

  return portfolio;
}

export function usePortfolio(id: string): Portfolio {
  let portfolio = Portfolio.load(id) as Portfolio;

  if (!portfolio) {
    log.critical("Failed to load portfolio {}", [id]);
  }

  return portfolio;
}
