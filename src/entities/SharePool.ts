import { Address, ethereum, log } from "@graphprotocol/graph-ts";
import { Holding, SharePool } from "../../generated/schema";
import { createPortfolio, usePortfolio } from "./Portfolio";
import { StabilityPool } from "../../generated/StabilityPool/StabilityPool";
import { LSPGetter } from "../../generated/StabilityPool/LSPGetter";
import { getHolding, useHolding } from "./Holding";
import { BIGINT_ZERO } from "../utils/bignumbers";
import { getDebtTokenPrice } from "./TokenPrice";
import { LSP_GETTER_ADDRESS, POOL_ADDRESS, DEBT_TOKEN_ADDRESS, LSP_GETTER_STARTBLOCK } from "../utils/constants";

export function getSharePool(block: ethereum.Block): SharePool {
  let id = "SharePool";
  let sharePool = SharePool.load(id);
  if (sharePool == null) {
    sharePool = new SharePool(id);
    sharePool.pool_address = POOL_ADDRESS;
    sharePool.lspGetter_address = LSP_GETTER_ADDRESS;
    sharePool.debt_token = DEBT_TOKEN_ADDRESS;
    sharePool.lspGetter_startBlock = LSP_GETTER_STARTBLOCK;
    let portfolio = createPortfolio(block);
    sharePool.portfolio = portfolio.id;
  }

  return sharePool;
}

// This function is to take snapshot of current pool.
// The portfolio will be changed when liquidation and user deposit/withdraw debt from the pool
export function updatePoolPortfolio(block: ethereum.Block): void {
  let pool = getSharePool(block);
  let portfolio = usePortfolio(pool.portfolio);
  let pool_contract = StabilityPool.bind(Address.fromString(pool.pool_address));
  let getter_contract = LSPGetter.bind(Address.fromString(pool.lspGetter_address));

  // Get total shares
  let totalSharesCall = pool_contract.try_totalSupply();
  let totalAssetsCall = pool_contract.try_totalAssets();
  if (totalSharesCall.reverted || totalAssetsCall.reverted) {
    log.error("Call to totalSupply or totalAssets reverted. Pool: {}, Block: {}", [pool.pool_address, block.number.toString()]);
    return;
  }

  let totalShares = totalSharesCall.value;
  let totalAssets = totalAssetsCall.value;

  // Ignore if no changes
  if (totalShares == portfolio.totalShares && totalAssets == portfolio.totalAssets) return;

  // Process new portfolio
  let new_portfolio = createPortfolio(block);
  new_portfolio.totalShares = totalShares;
  new_portfolio.totalAssets = totalAssets;

  // Track collateral tokens
  let trackable_assets: Address[] = [];
  let trackable_assetsCall = pool_contract.try_getCollateralTokens();
  if (!trackable_assetsCall.reverted) {
    trackable_assets = trackable_assetsCall.value;
  } else {
    log.warning("Call to getTrackableAssets reverted for pool: {}", [pool.id]);
  }
  let debtTokenAddress = Address.fromString(pool.debt_token);
  if (!trackable_assets.includes(debtTokenAddress)) {
    trackable_assets.push(debtTokenAddress);
  }
  const debtPrice = getDebtTokenPrice(block);
  //store share price
  let sharePrice = new_portfolio.totalShares.equals(BIGINT_ZERO) ? debtPrice : new_portfolio.totalAssets.times(debtPrice).div(new_portfolio.totalShares);
  new_portfolio.sharePrice = sharePrice;
  let nextHoldings: Holding[] = [];

  for (let i = 0; i < trackable_assets.length; i++) {
    let asset_address = trackable_assets[i];
    let assetBalanceCall = getter_contract.try_getTokenVirtualBalance(asset_address);
    let assetBalance = assetBalanceCall.reverted ? BIGINT_ZERO : assetBalanceCall.value;
    let match = findHoldingState(
      portfolio.holdings
        .map<Holding | null>((id) => useHolding(id))
        .filter((item) => item !== null)
        .map<Holding>((item) => item as Holding),
      asset_address
    );
    if (match && match.balance == assetBalance) {
      nextHoldings.push(match);
    } else {
      nextHoldings.push(getHolding(block, asset_address, pool_contract._address, assetBalance));
    }
  }
  new_portfolio.holdings = nextHoldings.map<string>((item) => item.id);

  // Update emission rewards data
  // Check block number because we upgraded the LSP contract.
  let emission_assets: Address[] = [];
  if (pool.lspGetter_startBlock !== BIGINT_ZERO && block.number.ge(pool.lspGetter_startBlock)) {
    let extraAssetsCall = getter_contract.try_extraAssets();
    if (!extraAssetsCall.reverted) {
      emission_assets = extraAssetsCall.value;
    } else {
      log.warning("Call to extraAssets reverted. Block: {}, Pool: {}", [block.number.toString(), pool.pool_address]);
    }
  } else {
    let extraAssetsCall = getter_contract.try_extraAssets();
    if (!extraAssetsCall.reverted) {
      emission_assets = extraAssetsCall.value;
    } else {
      log.warning("Call to getExtraAssets reverted. Block: {}, Pool: {}", [block.number.toString(), pool.pool_address]);
    }
  }

  let emission_holdings: Holding[] = new Array<Holding>();
  for (let i = 0; i < emission_assets.length; i++) {
    let asset_address = emission_assets[i];
    let assetBalance = BIGINT_ZERO;

    if (block.number.ge(pool.lspGetter_startBlock)) {
      let tokenVirtualBalanceCall = getter_contract.try_getTokenVirtualBalance(asset_address);
      if (!tokenVirtualBalanceCall.reverted) {
        assetBalance = tokenVirtualBalanceCall.value;
      } else {
        log.warning("Call to getTokenVirtualBalance reverted for asset: {}. Block: {}, Pool: {}", [asset_address.toHexString(), block.number.toString(), pool.pool_address]);
      }
    } else {
      let tokenVirtualBalanceCall = getter_contract.try_getTokenVirtualBalance(asset_address);
      if (!tokenVirtualBalanceCall.reverted) {
        assetBalance = tokenVirtualBalanceCall.value;
      } else {
        log.warning("Call to getTokenVirtualBalance reverted for asset: {}. Block: {}, Pool: {}", [asset_address.toHexString(), block.number.toString(), pool.pool_address]);
      }
    }

    emission_holdings = emission_holdings.concat([getHolding(block, asset_address, pool_contract._address, assetBalance)]);
  }

  new_portfolio.emissions = emission_holdings.map<string>((item) => item.id);
  new_portfolio.save();

  pool.portfolio = new_portfolio.id;
  pool.save();
}

function findHoldingState(holdings: Holding[], asset_address: Address): Holding | null {
  for (let i = 0; i < holdings.length; i++) {
    if (Address.fromString(holdings[i].asset) == asset_address) {
      return holdings[i];
    }
  }

  return null;
}
