import { Value } from "@graphprotocol/graph-ts";

import { Global } from "../../generated/schema";
import { BIGINT_ZERO } from "../utils/bignumbers";
import { BORROWER_OPERATIONS_ADDRESS } from "../utils/constants";

const onlyGlobalId = "only";

export function getGlobal(): Global {
  let globalOrNull = Global.load(onlyGlobalId);

  if (globalOrNull != null) {
    return globalOrNull as Global;
  } else {
    let newGlobal = new Global(onlyGlobalId);

    newGlobal.transactionCount = 0;
    newGlobal.changeCount = 0;

    // for global financial states
    newGlobal.debtTokenSupplies = BIGINT_ZERO;
    newGlobal.cumulativeUniqueUsers = 0;
    newGlobal.borrowerOperations_address = BORROWER_OPERATIONS_ADDRESS;
    newGlobal.save();

    return newGlobal;
  }
}

// @ts-ignore
function increaseCounter(key: string): i32 {
  let global = getGlobal();

  let count = global.get(key);
  if (count != null) {
    let _count = count.toI32();
    global.set(key, Value.fromI32(_count + 1));
    global.save();
    // @ts-ignore
    return _count as i32;
  } else {
    return 0;
  }
}

// @ts-ignore
export function getSequenceNumber(): i32 {
  return increaseCounter("sequenceNumber");
}

// @ts-ignore
export function getTransactionSequenceNumber(): i32 {
  return increaseCounter("transactionCount");
}

// @ts-ignore
export function getChangeSequenceNumber(): i32 {
  return increaseCounter("changeCount");
}

// @ts-ignore
export function getLastChangeSequenceNumber(): i32 {
  let global = getGlobal();

  return global.changeCount - 1;
}
