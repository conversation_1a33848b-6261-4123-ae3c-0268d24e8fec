import { ethereum, BigInt, Address } from "@graphprotocol/graph-ts";

import { Liquidation } from "../../generated/schema";

import { BIGINT_ZERO } from "../utils/bignumbers";

import { getTransaction } from "./Transaction";
import { getUser } from "./User";
import { ZERO_ADDRESS } from "../utils/constants";
import { updatePoolPortfolio } from "./SharePool";

export function generateLiquidationId(_denManager: Address, event: ethereum.Event): string {
  return "Liquidation" + "/" + _denManager.toHex() + "/" + event.transaction.hash.toHex();
}

export function getLiquidation(_denManager: Address, event: ethereum.Event): Liquidation {
  let id = generateLiquidationId(_denManager, event);
  let newLiquidation = Liquidation.load(id);

  if (newLiquidation == null) {
    newLiquidation = new Liquidation(id);
    newLiquidation.transaction = getTransaction(event).id;
    newLiquidation.liquidator = getUser(event.transaction.from).id;
    newLiquidation.liquidatedCollateral = BIGINT_ZERO;
    newLiquidation.liquidatedDebt = BIGINT_ZERO;
    newLiquidation.collGasCompensation = BIGINT_ZERO;
    newLiquidation.tokenGasCompensation = BIGINT_ZERO;
    newLiquidation.denManager = ZERO_ADDRESS;
    newLiquidation.save();
  }
  return newLiquidation as Liquidation;
}

export function finishCurrentLiquidation(
  event: ethereum.Event,
  _denManager: Address,
  _liquidatedColl: BigInt,
  _liquidatedDebt: BigInt,
  _collGasCompensation: BigInt,
  _LUSDGasCompensation: BigInt
): void {
  let currentLiquidation = getLiquidation(_denManager, event);
  currentLiquidation.liquidatedCollateral = _liquidatedColl;
  currentLiquidation.liquidatedDebt = _liquidatedDebt;
  currentLiquidation.collGasCompensation = _collGasCompensation;
  currentLiquidation.tokenGasCompensation = _LUSDGasCompensation;
  currentLiquidation.denManager = _denManager.toHex();
  currentLiquidation.save();

  // Take pool composition snapshots
  updatePoolPortfolio(event.block);
}
