import { Address, BigInt, ethereum, log } from "@graphprotocol/graph-ts";
import { Holding } from "../../generated/schema";
import { getToken } from "./Token";
import { fetchTokenPriceFromContract } from "./TokenPrice";

export function generateHoldingId(block: ethereum.Block, tokenAddress: Address, pool_address: Address, userAddress: Address | null = null, type: string = ""): string {
  let id = `${pool_address.toHex()}/${tokenAddress.toHex()}/${block.timestamp.toString()}`;
  if (userAddress) {
    id += `/${userAddress.toHex()}`;
  }
  if (type) {
    id += `/${type}`;
  }

  return id;
}
export function extractTokenAddressFromHoldingId(holdingId: string): string | null {
  const parts = holdingId.split("/");

  if (parts.length < 3) {
    return null; // Invalid format, tokenAddress is missing
  }

  return parts[1]; // Second part corresponds to tokenAddress
}
export function useHolding(id: string): Holding | null {
  let holding = Holding.load(id);
  if (holding == null) {
    log.critical("Failed to load holding {}.", [id]);
  }

  return holding;
}

export function getHolding(block: ethereum.Block, tokenAddress: Address, pool_address: Address, balance: BigInt, userAddress: Address | null = null, type: string = ""): Holding {
  let id = generateHoldingId(block, tokenAddress, pool_address, userAddress, type);
  let holding = Holding.load(id);
  if (holding == null) {
    holding = new Holding(id);
    fetchTokenPriceFromContract(tokenAddress, block);
    let token = getToken(tokenAddress);
    holding.asset = token.id;
    holding.balance = balance;
    holding.price = token.price; //priceID at time of creation
  } else {
    holding.balance = holding.balance.plus(balance);
  }
  holding.save();

  return holding;
}
