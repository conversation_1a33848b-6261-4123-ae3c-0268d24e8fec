import { ethereum, Entity, Value } from "@graphprotocol/graph-ts";

import { getChangeSequenceNumber } from "./Global";
import { getTransaction } from "./Transaction";

export function beginChange(): i32 {
  return getChangeSequenceNumber();
}

export function initChange(change: Entity, event: ethereum.Event, sequenceNumber: i32): void {
  let transactionId = getTransaction(event).id;

  change.set("sequenceNumber", Value.fromI32(sequenceNumber));
  change.set("transaction", Value.fromString(transactionId));
}
