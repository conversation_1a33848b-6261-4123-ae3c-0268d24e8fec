import { Address, ethereum, log, BigInt } from "@graphprotocol/graph-ts";
import { Vault, VaultSnapshot, VaultSlot } from "../../generated/schema";
import { CollateralVault as VaultEntity } from "../../generated/templates";

import { BIGINT_SCALING_FACTOR, BIGINT_SCALING_FACTOR_BP, BIGINT_ZERO } from "../utils/bignumbers";
import { getLatestTokenPrice } from "./TokenPrice";
import { COLL_VAULT_ROUTER_ADDRESS, IBGT_ADDRESS } from "../utils/constants";
import { getHolding } from "./Holding";
import { CollVault } from "../../generated/templates/CollateralVault/CollVault";
import { CollVaultRouter } from "../../generated/templates/CollateralVault/CollVaultRouter";
import { getGlobal } from "./Global";
import { getVaultSnapshotType } from "../types/TypeOperation";
import { VaultSnapshotType } from "../types/enums";

export const getVault = (vaultAddr: Address): Vault => {
  let vault = Vault.load(vaultAddr.toHex());
  if (vault == null) {
    let vault_contract = CollVault.bind(vaultAddr);
    let global = getGlobal();
    const withdrawFee = vault_contract.try_getWithdrawFee();
    const asset = vault_contract.try_asset();
    VaultEntity.create(vaultAddr);
    vault = new Vault(vaultAddr.toHex());
    vault.token = vaultAddr.toHex();
    vault.asset = Address.zero().toHexString();
    vault.withdrawFee = BIGINT_ZERO;
    if (asset.reverted) {
      log.error("Asset reverted for vault ${}", [vaultAddr.toHexString()]);
    } else {
      vault.asset = asset.value.toHexString();
    }
    if (!withdrawFee.reverted) {
      vault.withdrawFee = BigInt.fromI32(withdrawFee.value);
    }
    vault.global = global.id; // Link to Global entity
    vault.save();
  }
  if (vault.asset === Address.zero().toHexString()) {
    let vault_contract = CollVault.bind(vaultAddr);
    const asset = vault_contract.try_asset();
    if (!asset.reverted) {
      vault.asset = asset.value.toHexString();
    }
    vault.save();
  }

  return vault as Vault;
};
export const updateVault = (vaultAddr: Address): void => {
  let vault = Vault.load(vaultAddr.toHex());
  if (vault == null) {
    getVault(vaultAddr);
  } else {
    let vault_contract = CollVault.bind(vaultAddr);
    const withdrawFee = vault_contract.try_getWithdrawFee();
    const asset = vault_contract.try_asset();
    if (!withdrawFee.reverted) {
      vault.withdrawFee = BigInt.fromI32(withdrawFee.value);
    }
    if (!asset.reverted) {
      vault.asset = asset.value.toHexString();
    }
    vault.save();
  }
};
export const generateVaultSnapshotId = (vault: Address, block: ethereum.Block): string => {
  return vault.toHex() + "/" + block.number.toString();
};

function updateVaultSlot(vaultAddr: Address, block: ethereum.Block, balance: BigInt, totalSupply: BigInt, price: BigInt, interval: BigInt, bucket: string): VaultSlot {
  if (interval.isZero()) {
    interval = BigInt.fromI32(604800);
    bucket = "WEEKLY";
  }
  let slotIndex = block.timestamp.div(interval);
  let slotId = vaultAddr.toHex() + "/" + bucket + "/" + slotIndex.toString();
  let slot = VaultSlot.load(slotId);
  if (slot == null) {
    slot = new VaultSlot(slotId);
    slot.vault = vaultAddr.toHex();
    slot.bucket = bucket;
    slot.slotIndex = slotIndex;
    slot.startTime = block.timestamp;
    slot.startTotalSupply = totalSupply;
    slot.startBalance = balance;
    slot.cumBalance = BIGINT_ZERO;
    slot.cumTotalSupply = BIGINT_ZERO;
    slot.cumPrice = BIGINT_ZERO;
    slot.cumTime = BIGINT_ZERO;
    slot.observations = 0;
  }

  slot.lastTime = block.timestamp;
  slot.lastBalance = balance;
  slot.lastTotalSupply = totalSupply;
  slot.cumBalance = slot.cumBalance.plus(balance);
  slot.cumTotalSupply = slot.cumTotalSupply.plus(totalSupply);
  slot.cumPrice = slot.cumPrice.plus(price);
  slot.cumTime = slot.cumTime.plus(block.timestamp);
  slot.observations = slot.observations + 1;

  slot.save();
  return slot;
}

export const getVaultSnapshot = (vaultAddr: Address, block: ethereum.Block, type: VaultSnapshotType): VaultSnapshot => {
  const vault = getVault(vaultAddr);
  let vault_contract = CollVault.bind(vaultAddr);
  const price = getLatestTokenPrice(vaultAddr, block);
  let id = generateVaultSnapshotId(vaultAddr, block);
  let snapshot = VaultSnapshot.load(id);
  const balanceCall = vault_contract.try_getBalanceOfWithFutureEmissions(Address.fromString(vault.asset));
  const totalSharesCall = vault_contract.try_totalSupply();
  let balance = BIGINT_ZERO;
  let totalSupply = BIGINT_ZERO;
  if (!balanceCall.reverted) {
    balance = balanceCall.value;
  }
  if (!totalSharesCall.reverted) {
    totalSupply = totalSharesCall.value;
  }
  if (snapshot == null) {
    if (totalSupply !== BIGINT_ZERO && balance !== BIGINT_ZERO) {
      updateVaultSlot(vaultAddr, block, balance, totalSupply, price, BigInt.fromI32(3600), "HOURLY");
      updateVaultSlot(vaultAddr, block, balance, totalSupply, price, BigInt.fromI32(86400), "DAILY");
      updateVaultSlot(vaultAddr, block, balance, totalSupply, price, BigInt.fromI32(604800), "WEEKLY");
    } else {
      log.error("totalSupply or getBalanceOfWithFutureEmissions reverted for vault {}", [vaultAddr.toHex()]);
    }

    snapshot = new VaultSnapshot(id);
    snapshot.vault = vaultAddr.toHex();
    snapshot.timestamp = block.timestamp;
    snapshot.price = price;
    snapshot.sharePrice = BIGINT_ZERO;
    snapshot.totalAssets = balance;
    snapshot.iBGT = BIGINT_ZERO;
    snapshot.totalSupply = totalSupply;
    snapshot.underlyingAssets = [];
    snapshot.snapshotEvent = getVaultSnapshotType(type);
    const iBGTCall = vault_contract.try_getBalance(Address.fromString(IBGT_ADDRESS));
    if (!iBGTCall.reverted) {
      snapshot.iBGT = iBGTCall.value;
    }

    let collVaultRouterAddress = Address.fromString(COLL_VAULT_ROUTER_ADDRESS);
    let holdings: string[] = [];
    const vault = getVault(vaultAddr);
    let withdrawFee = vault && vault.withdrawFee ? vault.withdrawFee : BIGINT_ZERO;
    const withdrawFeeFraction = withdrawFee.times(BIGINT_SCALING_FACTOR).div(BIGINT_SCALING_FACTOR_BP);
    const ratio = BIGINT_SCALING_FACTOR.minus(withdrawFeeFraction);
    let router_contract = CollVaultRouter.bind(collVaultRouterAddress);
    const previewRedeemUnderlying = router_contract.try_previewRedeemUnderlying(vaultAddr, BIGINT_SCALING_FACTOR);
    if (!previewRedeemUnderlying.reverted) {
      const tokens = previewRedeemUnderlying.value.getTokens();
      const tokenBalances = previewRedeemUnderlying.value.getAmounts();
      for (let i = 0; i < tokens.length; i++) {
        holdings.push(getHolding(block, tokens[i], vault_contract._address, tokenBalances[i].times(snapshot.totalSupply).div(ratio)).id);
      }
    }
    snapshot.underlyingAssets = holdings;
    snapshot.save();
  }
  return snapshot as VaultSnapshot;
};
