import { Address, ethereum, BigInt } from "@graphprotocol/graph-ts";
import { VaultPosition, VaultPositionChange } from "../../generated/schema";

import { BIGINT_SCALING_FACTOR, BIGINT_SCALING_FACTOR_BP, BIGINT_ZERO } from "../utils/bignumbers";

import { beginChange, initChange } from "./Change";
import { getVault } from "./Vault";
import { getSequenceNumber } from "./Global";
import { getUser } from "./User";
import { COLL_VAULT_ROUTER_ADDRESS } from "../utils/constants";
import { CollVaultRouter } from "../../generated/templates/CollateralVault/CollVaultRouter";
import { getHolding, extractTokenAddressFromHoldingId, useHolding } from "./Holding";
import { poolOperation } from "../types/enums";
import { getPoolOperation } from "../types/TypeOperation";
import { CollVault } from "../../generated/templates/CollateralVault/CollVault";

export const generateVaultPositionId = (vaultAddr: Address, user: Address): string => {
  return "vault" + "/" + vaultAddr.toHex() + "/" + user.toHex();
};

export const getVaultPosition = (vaultAddr: Address, user: Address): VaultPosition => {
  const id = generateVaultPositionId(vaultAddr, user);
  let vaultPosition = VaultPosition.load(id);
  if (vaultPosition === null) {
    vaultPosition = new VaultPosition(id);
    vaultPosition.vault = vaultAddr.toHex();
    vaultPosition.owner = getUser(user).id;
    vaultPosition.deposited = BIGINT_ZERO;
    vaultPosition.shares = BIGINT_ZERO;
    vaultPosition.assets = BIGINT_ZERO;
    vaultPosition.save();
  }
  return vaultPosition;
};

function createVaultPositionChange(event: ethereum.Event): VaultPositionChange {
  let sequenceNumber = beginChange();
  let vaultChange = new VaultPositionChange(sequenceNumber.toString());
  initChange(vaultChange, event, sequenceNumber);

  return vaultChange;
}
export function updateVaultPositionTransfer(event: ethereum.Event, vaultAddr: Address, operation: poolOperation, user: Address, shares: BigInt): void {
  let vault_contract = CollVault.bind(vaultAddr);
  const assets = vault_contract.try_convertToAssets(shares);
  if (assets.reverted) updateVaultPosition(event, vaultAddr, operation, user, shares, assets.reverted ? shares : assets.value);
}

export function updateVaultPosition(event: ethereum.Event, vaultAddr: Address, operation: poolOperation, user: Address, shares: BigInt, assets: BigInt): void {
  let vault = getVault(vaultAddr);
  let vaultPosition = getVaultPosition(vaultAddr, user);

  // update vault

  let vaultPositionChange = createVaultPositionChange(event);
  let collVaultRouterAddress = Address.fromString(COLL_VAULT_ROUTER_ADDRESS);
  let withdrawFee = vault && vault.withdrawFee ? vault.withdrawFee : BIGINT_ZERO;

  const withdrawFeeFraction = withdrawFee.times(BIGINT_SCALING_FACTOR).div(BIGINT_SCALING_FACTOR_BP);
  const ratio = BIGINT_SCALING_FACTOR.minus(withdrawFeeFraction);
  let router_contract = CollVaultRouter.bind(collVaultRouterAddress);
  const previewRedeemUnderlying = router_contract.try_previewRedeemUnderlying(vaultAddr, BIGINT_SCALING_FACTOR);

  vaultPositionChange.vault = vault.id;
  vaultPositionChange.vaultPosition = vaultPosition.id;
  vaultPositionChange.owner = vaultPosition.owner;
  vaultPositionChange.vaultOperation = getPoolOperation(operation);
  vaultPositionChange.deposited = BIGINT_ZERO;

  if (operation === poolOperation.WITHDRAW || (operation === poolOperation.TRANSFER && event.transaction.from === user)) {
    vaultPosition.shares = vaultPosition.shares.minus(shares);
    vaultPosition.assets = vaultPosition.assets.minus(assets);
    vaultPositionChange.shares = vaultPosition.shares;
    vaultPositionChange.assets = vaultPosition.assets;
    vaultPositionChange.shareChange = shares.neg();
    vaultPositionChange.assetsChange = assets.neg();
    let holdingsWithdrawn: string[] = [];
    let holdingsHistoricalWithdrawn: string[] = [];
    if (previewRedeemUnderlying.reverted === false) {
      const tokens = previewRedeemUnderlying.value.getTokens();
      const tokenBalances = previewRedeemUnderlying.value.getAmounts();
      for (let i = 0; i < tokens.length; i++) {
        let currBalance = BIGINT_ZERO;
        let holdingId: string | null = null;
        if (vaultPosition.withdrawn != null) {
          let withdrawnArray = vaultPosition.withdrawn as string[]; // Explicit cast

          for (let j = 0; j < withdrawnArray.length; j++) {
            if (extractTokenAddressFromHoldingId(withdrawnArray[j]) === tokens[i].toHex()) {
              holdingId = withdrawnArray[j];
              break;
            }
          }
        }
        if (holdingId) {
          const holding = useHolding(holdingId);
          if (holding) {
            currBalance = holding.balance;
          }
        }
        holdingsWithdrawn.push(getHolding(event.block, tokens[i], Address.fromString(vault.id), tokenBalances[i].times(shares).div(ratio), user, "withdrawn").id);
        holdingsHistoricalWithdrawn.push(
          getHolding(event.block, tokens[i], Address.fromString(vault.id), currBalance.plus(tokenBalances[i].times(shares).div(ratio)), user, "historicalWithdrawn").id
        );
      }
    }
    vaultPositionChange.withdrawn = holdingsWithdrawn;
    vaultPosition.withdrawn = holdingsHistoricalWithdrawn;
  } else if (operation === poolOperation.DEPOSIT || (operation === poolOperation.TRANSFER && event.transaction.from !== user)) {
    vaultPosition.shares = vaultPosition.shares.plus(shares);
    vaultPosition.assets = vaultPosition.assets.plus(assets);
    vaultPositionChange.shares = vaultPosition.shares;
    vaultPositionChange.assets = vaultPosition.assets;
    vaultPosition.deposited = vaultPosition.deposited.plus(assets);
    vaultPositionChange.shareChange = shares;
    vaultPositionChange.assetsChange = assets;
    vaultPositionChange.deposited = assets;
  }
  let holdings: string[] = [];

  if (previewRedeemUnderlying.reverted === false) {
    const tokens = previewRedeemUnderlying.value.getTokens();
    const tokenBalances = previewRedeemUnderlying.value.getAmounts();
    for (let i = 0; i < tokens.length; i++) {
      holdings.push(getHolding(event.block, tokens[i], Address.fromString(vault.id), tokenBalances[i].times(shares).div(ratio), user).id);
    }
  }
  vaultPositionChange.underlyingAssets = holdings;

  getSequenceNumber();
  vaultPositionChange.save();
  vaultPosition.save();

  // track deposit only

  // Update global state
}
