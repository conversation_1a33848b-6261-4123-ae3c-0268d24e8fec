import { Address, BigInt, Value, ethereum } from "@graphprotocol/graph-ts";
import { DenManager as DenManagerEntity } from "../../generated/templates";
import { BorrowingRateSnapshot, DenManager, DenManagerSnapshot } from "../../generated/schema";
import { ZERO_ADDRESS } from "../utils/constants";
import { BIGINT_ZERO } from "../utils/bignumbers";
import { getGlobal } from "./Global";
import { getLatestTokenPrice } from "./TokenPrice";
import { calculateCollateralRatio } from "../utils/collateralRatio";
import { getVaultSnapshot } from "./Vault";
import { VaultSnapshotType } from "../types/enums";

export const getDenManager = (_denManager: Address): DenManager => {
  let denManager = DenManager.load(_denManager.toHex());

  if (denManager == null) {
    let global = getGlobal();
    DenManagerEntity.create(_denManager);
    denManager = new DenManager(_denManager.toHex());
    denManager.global = global.id; // Link to Global entity
    denManager.collateral = ZERO_ADDRESS;
    denManager.totalCollateral = BIGINT_ZERO;
    denManager.totalDebt = BIGINT_ZERO;

    denManager.totalCollateralRatio = BIGINT_ZERO;

    denManager.totalBorrowingFeesPaid = BIGINT_ZERO;
    denManager.totalRedemptionFeesPaid = BIGINT_ZERO;
    denManager.rawTotalRedistributedCollateral = BIGINT_ZERO;
    denManager.rawTotalRedistributedDebt = BIGINT_ZERO;
    denManager.totalNumberOfDens = 0;
    denManager.liquidationCount = 0;
    denManager.redemptionCount = 0;

    denManager.numberOfOpenDens = 0;
    denManager.numberOfDensClosedByOwner = 0;
    denManager.numberOfLiquidatedDens = 0;
    denManager.numberOfRedeemedDens = 0;

    denManager.totalStakedAmount = BIGINT_ZERO;

    denManager.borrowingFeeFloor = BIGINT_ZERO;
    denManager.maxBorrowingFee = BIGINT_ZERO;
    denManager.borrowingFeeRate = BIGINT_ZERO;

    denManager.interestRate = BIGINT_ZERO;

    denManager.save();
  }

  return denManager as DenManager;
};

export const increaseNumberOfOpenDens = (_denManager: Address): void => {
  let denManager = DenManager.load(_denManager.toHex());
  if (denManager != null) {
    denManager.numberOfOpenDens = denManager.numberOfOpenDens + 1;
    denManager.save();
  }
  return;
};

export const increaseNumberOfTotalDens = (_denManager: Address): void => {
  let denManager = DenManager.load(_denManager.toHex());
  if (denManager != null) {
    denManager.totalNumberOfDens = denManager.totalNumberOfDens + 1;
    denManager.save();
  }
  return;
};

export const increaseNumberOfDensClosedByOwner = (_denManager: Address): void => {
  let denManager = DenManager.load(_denManager.toHex());
  if (denManager != null) {
    denManager.numberOfDensClosedByOwner = denManager.numberOfDensClosedByOwner + 1;
    denManager.save();
  }
  return;
};

export const decreaseNumberOfDensClosedByOwner = (_denManager: Address): void => {
  let denManager = DenManager.load(_denManager.toHex());
  if (denManager != null) {
    denManager.numberOfDensClosedByOwner = denManager.numberOfDensClosedByOwner - 1;
    denManager.save();
  }
  return;
};

export const increaseNumberOfLiquidatedDens = (_denManager: Address): void => {
  let denManager = DenManager.load(_denManager.toHex());
  if (denManager != null) {
    denManager.numberOfLiquidatedDens = denManager.numberOfLiquidatedDens + 1;
    denManager.save();
  }
  return;
};

export const decreaseNumberOfLiquidatedDens = (_denManager: Address): void => {
  let denManager = DenManager.load(_denManager.toHex());
  if (denManager != null) {
    denManager.numberOfLiquidatedDens = denManager.numberOfLiquidatedDens - 1;
    denManager.save();
  }
  return;
};

export const increaseNumberOfRedeemedDens = (_denManager: Address): void => {
  let denManager = DenManager.load(_denManager.toHex());
  if (denManager != null) {
    denManager.numberOfRedeemedDens = denManager.numberOfRedeemedDens + 1;
    denManager.save();
  }
  return;
};

export const decreaseNumberOfRedeemedDens = (_denManager: Address): void => {
  let denManager = DenManager.load(_denManager.toHex());
  if (denManager != null) {
    denManager.numberOfRedeemedDens = denManager.numberOfRedeemedDens - 1;
    denManager.save();
  }
  return;
};

export const increaseTotalBorrowingFeesPaid = (_denManager: Address, fee: BigInt): void => {
  let denManager = DenManager.load(_denManager.toHex());
  if (denManager != null) {
    denManager.totalBorrowingFeesPaid = denManager.totalBorrowingFeesPaid.plus(fee);
    denManager.save();
  }
  return;
};

// @ts-ignore
function increaseCounter(_denManager: Address, key: string): i32 {
  let denManager = getDenManager(_denManager);
  let count = denManager.get(key);
  if (count != null) {
    let _count = count.toI32();
    denManager.set(key, Value.fromI32(_count + 1));
    denManager.save();
    // @ts-ignore
    return _count as i32;
  } else {
    return 0;
  }
}

// @ts-ignore
export function getLiquidationSequenceNumber(_denManager: Address): i32 {
  return increaseCounter(_denManager, "liquidationCount");
}

// @ts-ignore
export function getRedemptionSequenceNumber(_denManager: Address): i32 {
  return increaseCounter(_denManager, "redemptionCount");
}

function getBorrowingFeeRateSnapshot(event: ethereum.Event): BorrowingRateSnapshot {
  let snapshot = BorrowingRateSnapshot.load(event.block.timestamp.toString());
  if (snapshot == null) {
    snapshot = new BorrowingRateSnapshot(event.block.timestamp.toString());
    snapshot.blockNumber = event.block.number;
    snapshot.timestamp = event.block.timestamp;
    snapshot.denManager = Address.zero().toHex();
    snapshot.feeRate = BIGINT_ZERO;
    snapshot.save();
  }

  return snapshot;
}

function calculateBorrowFee(_baseRate: BigInt, _borrowingFeeFloor: BigInt, _maxBorrowingFee: BigInt): BigInt {
  if (_borrowingFeeFloor.plus(_baseRate) > _maxBorrowingFee) {
    return _maxBorrowingFee;
  } else {
    return _borrowingFeeFloor.plus(_baseRate);
  }
}

export function takeBorrowingFeeRateSnapshot(event: ethereum.Event, _denManager: Address, _baseRate: BigInt): void {
  let denManager = getDenManager(event.address);

  let feeRate = calculateBorrowFee(_baseRate, denManager.borrowingFeeRate, denManager.maxBorrowingFee);
  // If feeRate is not changed, then ignore
  if (feeRate == denManager.borrowingFeeRate) return;

  denManager.borrowingFeeRate = feeRate;
  denManager.save();

  let snapshot = getBorrowingFeeRateSnapshot(event);

  snapshot.denManager = _denManager.toHex();
  snapshot.feeRate = feeRate;

  snapshot.save();
}

export function generateDenManagerSnapshotId(denManagerAddress: Address, block: ethereum.Block): string {
  return denManagerAddress.toHex() + "/" + block.timestamp.toString();
}
export function createDenManagerSnapshot(denManagerAddress: Address, block: ethereum.Block): DenManagerSnapshot {
  let id = generateDenManagerSnapshotId(denManagerAddress, block);

  let snapshot = DenManagerSnapshot.load(id);
  if (snapshot == null) {
    const denManager = getDenManager(denManagerAddress);
    snapshot = new DenManagerSnapshot(id);
    let price = getLatestTokenPrice(Address.fromString(denManager.collateral), block);

    denManager.totalCollateralRatio = calculateCollateralRatio(denManager.totalCollateral, denManager.totalDebt, price);
    denManager.save();
    snapshot.timestamp = block.timestamp;
    snapshot.denManager = denManager.id;

    snapshot.totalCollateral = denManager.totalCollateral;
    snapshot.totalDebt = denManager.totalDebt;
    snapshot.totalCollateralRatio = denManager.totalCollateralRatio;

    snapshot.totalBorrowingFeesPaid = denManager.totalBorrowingFeesPaid;
    snapshot.totalRedemptionFeesPaid = denManager.totalRedemptionFeesPaid;

    snapshot.rawTotalRedistributedCollateral = denManager.rawTotalRedistributedCollateral;
    snapshot.rawTotalRedistributedDebt = denManager.rawTotalRedistributedDebt;
    snapshot.totalStakedAmount = denManager.totalStakedAmount;

    snapshot.totalNumberOfDens = denManager.totalNumberOfDens;
    snapshot.liquidationCount = denManager.liquidationCount;
    snapshot.redemptionCount = denManager.redemptionCount;

    snapshot.borrowingFeeFloor = denManager.borrowingFeeFloor;
    snapshot.maxBorrowingFee = denManager.maxBorrowingFee;
    snapshot.borrowingFeeRate = denManager.borrowingFeeRate;

    snapshot.interestRate = denManager.interestRate;
    const vault = denManager.vault;
    if (vault !== null) {
      let vaultAddress = Address.fromString(vault);
      if (vaultAddress !== null) {
        getVaultSnapshot(vaultAddress, block, VaultSnapshotType.EVENT);
      }
    }
    snapshot.save();
  }

  return snapshot;
}
