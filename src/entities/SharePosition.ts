import { ethereum, Address, BigInt } from "@graphprotocol/graph-ts";

import { SharePosition, SharePositionChange } from "../../generated/schema";

import { BIGINT_ZERO } from "../utils/bignumbers";

import { beginChange, initChange } from "./Change";
import { getUser } from "./User";
import { poolOperation } from "../types/enums";
import { getPoolOperation } from "../types/TypeOperation";

export function generateSharePositionId(_user: Address): string {
  return "SharePosition" + "/" + _user.toHex();
}

export function getSharePosition(_user: Address): SharePosition {
  let id = generateSharePositionId(_user);
  let SharePositionOrNull = SharePosition.load(id);

  if (SharePositionOrNull != null) {
    return SharePositionOrNull as SharePosition;
  } else {
    let owner = getUser(_user);
    let newSharePosition = new SharePosition(id);

    newSharePosition.owner = owner.id;
    newSharePosition.shareAmount = BIGINT_ZERO;

    owner.save();

    return newSharePosition;
  }
}

function createSharePositionChange(event: ethereum.Event): SharePositionChange {
  let sequenceNumber = beginChange();
  let sharePositionChange = new SharePositionChange(sequenceNumber.toString());
  initChange(sharePositionChange, event, sequenceNumber);

  return sharePositionChange;
}

function updateSharePositionByOperation(event: ethereum.Event, sharePosition: SharePosition, operation: poolOperation, newShares: BigInt): void {
  let sharePositionChange = createSharePositionChange(event);

  sharePositionChange.sharePosition = sharePosition.id;
  sharePositionChange.shareOperation = getPoolOperation(operation);
  sharePositionChange.shareAmountBefore = sharePosition.shareAmount;

  if (operation == poolOperation.DEPOSIT) {
    sharePosition.shareAmount = sharePosition.shareAmount.plus(newShares);
  } else {
    sharePosition.shareAmount = sharePosition.shareAmount.minus(newShares);
  }

  sharePositionChange.shareAmountAfter = sharePosition.shareAmount;
  sharePositionChange.shareAmountChange = sharePositionChange.shareAmountAfter.minus(sharePositionChange.shareAmountBefore);

  sharePosition.save();
  sharePositionChange.save();
}

export function updateSharePosition(event: ethereum.Event, _user: Address, _amount: BigInt, _operation: poolOperation): void {
  let sharePosition = getSharePosition(_user);
  let user = getUser(_user);
  user.sharePosition = sharePosition.id;
  user.save();

  updateSharePositionByOperation(event, sharePosition, _operation, _amount);
}
