import { ethereum, Address, BigInt } from "@graphprotocol/graph-ts";

import { Den, DenChange } from "../../generated/schema";

import { BIGINT_SCALING_FACTOR, BIGINT_ZERO } from "../utils/bignumbers";
import { calculateCollateralRatio, calculateNominalCollateralRatio } from "../utils/collateralRatio";

import { isLiquidation, isRedemption } from "../types/TypeOperation";

import { getLastChangeSequenceNumber, getGlobal, getSequenceNumber } from "./Global";
import { beginChange, initChange } from "./Change";
import { getLiquidation } from "./Liquidation";
import { getRedemption } from "./Redemption";
import { generateDenId } from "../utils/helper";
import {
  decreaseNumberOfDensClosedByOwner,
  decreaseNumberOfLiquidatedDens,
  decreaseNumberOfRedeemedDens,
  getDenManager,
  increaseNumberOfDensClosedByOwner,
  increaseNumberOfLiquidatedDens,
  increaseNumberOfOpenDens,
  increaseNumberOfRedeemedDens,
  increaseNumberOfTotalDens,
} from "./DenManager";
import { getUser } from "./User";
import { getLatestTokenPrice, getDebtTokenPrice } from "./TokenPrice";
import { getMarketDailySnapshot } from "./DailySnapshot";

export function getDen(_denManager: Address, _user: Address): Den {
  let id = generateDenId(_denManager, _user);
  let DenOrNull = Den.load(id);

  if (DenOrNull != null) {
    return DenOrNull as Den;
  } else {
    let newDen = new Den(id);
    let user = getUser(_user);
    newDen.owner = user.id;
    newDen.collateral = BIGINT_ZERO;
    newDen.debt = BIGINT_ZERO;
    newDen.denManager = _denManager.toHex();
    // Avoid using setDenStatus, because newDen's status is not yet initialized
    newDen.status = "open";
    newDen.nominalCollateralRatio = BIGINT_ZERO;
    newDen.nominalCollateralRatioWithRedistribution = BIGINT_ZERO;
    newDen.rawCollateral = BIGINT_ZERO;
    newDen.rawDebt = BIGINT_ZERO;
    newDen.rawStake = BIGINT_ZERO;
    newDen.rawSnapshotOfTotalRedistributedCollateral = BIGINT_ZERO;
    newDen.rawSnapshotOfTotalRedistributedDebt = BIGINT_ZERO;
    newDen.accuredCollateralRewards = BIGINT_ZERO;
    newDen.accuredDebtRewards = BIGINT_ZERO;

    increaseNumberOfTotalDens(_denManager);
    increaseNumberOfOpenDens(_denManager);
    newDen.save();

    return newDen;
  }
}

function setDenStatus(den: Den, status: string): void {
  let statusBefore = den.status;

  if (status != statusBefore) {
    if (status == "open") {
      if (statusBefore == "closedByOwner") {
        decreaseNumberOfDensClosedByOwner(Address.fromString(den.denManager));
      } else if (statusBefore == "closedByLiquidation") {
        decreaseNumberOfLiquidatedDens(Address.fromString(den.denManager));
      } else if (statusBefore == "closedByRedemption") {
        decreaseNumberOfRedeemedDens(Address.fromString(den.denManager));
      }
    } else if (status == "closedByOwner") {
      increaseNumberOfDensClosedByOwner(Address.fromString(den.denManager));
    } else if (status == "closedByLiquidation") {
      increaseNumberOfLiquidatedDens(Address.fromString(den.denManager));
    } else if (status == "closedByRedemption") {
      increaseNumberOfRedeemedDens(Address.fromString(den.denManager));
    }

    den.status = status;
  }
}

function createDenChange(event: ethereum.Event): DenChange {
  let sequenceNumber = beginChange();
  let denChange = new DenChange(sequenceNumber.toString());
  initChange(denChange, event, sequenceNumber);

  return denChange;
}

export function updateDen(event: ethereum.Event, _denManager: Address, operation: string, _borrower: Address, _coll: BigInt, _debt: BigInt, stake: BigInt): void {
  let denManager = getDenManager(_denManager);

  let den = getDen(_denManager, _borrower);
  let newCollateral = _coll;
  let newDebt = _debt;

  if (newCollateral == den.collateral && newDebt == den.debt) {
    return;
  }

  // update den manager
  denManager.totalCollateral = denManager.totalCollateral.minus(den.collateral);
  denManager.totalDebt = denManager.totalDebt.minus(den.debt);
  denManager.totalStakedAmount = denManager.totalStakedAmount.minus(den.rawStake);

  denManager.totalCollateral = denManager.totalCollateral.plus(newCollateral);
  denManager.totalDebt = denManager.totalDebt.plus(newDebt);
  denManager.totalStakedAmount = denManager.totalStakedAmount.plus(stake);

  let denChange = createDenChange(event);

  let price = getLatestTokenPrice(Address.fromString(denManager.collateral), event.block);

  denChange.denManager = denManager.id;
  denChange.den = den.id;
  denChange.denOperation = operation;

  denChange.collateralBefore = den.collateral;
  denChange.debtBefore = den.debt;
  denChange.collateralRatioBefore = calculateCollateralRatio(den.collateral, den.debt, price);

  den.collateral = newCollateral;
  den.debt = newDebt;

  denChange.collateralAfter = den.collateral;
  denChange.debtAfter = den.debt;
  denChange.collateralRatioAfter = calculateCollateralRatio(den.collateral, den.debt, price);

  denChange.collateralChange = denChange.collateralAfter.minus(denChange.collateralBefore);
  denChange.debtChange = denChange.debtAfter.minus(denChange.debtBefore);

  if (isLiquidation(operation)) {
    let currentLiquidation = getLiquidation(_denManager, event);
    currentLiquidation.denManager = _denManager.toHex();
    currentLiquidation.save();

    denChange.liquidation = currentLiquidation.id;
  }

  if (isRedemption(operation)) {
    let currentRedemption = getRedemption(_denManager, event);
    currentRedemption.denManager = _denManager.toHex();
    currentRedemption.save();
    denChange.redemption = currentRedemption.id;
  }

  getSequenceNumber();
  denChange.save();

  den.rawCollateral = _coll;
  den.rawDebt = _debt;
  den.rawStake = stake;

  let redistributedCollateral = denManager.rawTotalRedistributedCollateral.minus(den.rawSnapshotOfTotalRedistributedCollateral).times(den.rawStake).div(BIGINT_SCALING_FACTOR);

  let redistributedDebt = denManager.rawTotalRedistributedDebt.minus(den.rawSnapshotOfTotalRedistributedDebt).times(den.rawStake).div(BIGINT_SCALING_FACTOR);

  den.accuredCollateralRewards = den.accuredCollateralRewards.plus(redistributedCollateral);
  den.accuredDebtRewards = den.accuredDebtRewards.plus(redistributedDebt);

  if (stake != BIGINT_ZERO) {
    den.rawSnapshotOfTotalRedistributedCollateral = denManager.rawTotalRedistributedCollateral;
    den.rawSnapshotOfTotalRedistributedDebt = denManager.rawTotalRedistributedDebt;

    den.collateralRatioSortKey = _debt.times(BIGINT_SCALING_FACTOR).div(stake).minus(denManager.rawTotalRedistributedDebt);
  } else {
    den.rawSnapshotOfTotalRedistributedCollateral = BIGINT_ZERO;
    den.rawSnapshotOfTotalRedistributedDebt = BIGINT_ZERO;
    den.collateralRatioSortKey = null;
  }

  if (_coll == BIGINT_ZERO) {
    if (isLiquidation(operation)) {
      setDenStatus(den, "closedByLiquidation");
    } else if (isRedemption(operation)) {
      setDenStatus(den, "closedByRedemption");
    } else {
      setDenStatus(den, "closedByOwner");
    }
  } else {
    setDenStatus(den, "open");
  }
  den.nominalCollateralRatio = calculateNominalCollateralRatio(den.collateral, den.debt);
  den.nominalCollateralRatioWithRedistribution = calculateNominalCollateralRatio(
    den.rawCollateral.plus(den.rawSnapshotOfTotalRedistributedCollateral),
    den.rawDebt.plus(den.rawSnapshotOfTotalRedistributedDebt)
  );

  den.save();

  // track deposit only
  let user = getUser(_borrower);
  if (denChange.collateralChange > BIGINT_ZERO) {
    user.totalDepositVolumeInDen = user.totalDepositVolumeInDen.plus(denChange.collateralChange.times(price));
    user.save();
  }

  // Update global state
  let debtPrice = getDebtTokenPrice(event.block);
  let global = getGlobal();
  global.debtTokenSupplies = global.debtTokenSupplies.plus(denChange.debtChange);
  global.save();

  denManager.save();

  //   //update financial daily snapshot
  //   let snapshot = getFinancialDailySnapshot(event);
  //   snapshot.dailyBorrowUSD = snapshot.dailyBorrowUSD.plus(denChange.debtChange.times(debtPrice));
  //   snapshot.dailyDepositUSD = snapshot.dailyDepositUSD.plus(denChange.collateralChange.times(price));

  //   snapshot.save();

  // update market daily snapshot
  let marketSnapshot = getMarketDailySnapshot(event, _denManager);
  marketSnapshot.dailyBorrowUSD = marketSnapshot.dailyBorrowUSD.plus(denChange.debtChange.times(debtPrice));
  marketSnapshot.dailyDepositUSD = marketSnapshot.dailyDepositUSD.plus(denChange.collateralChange.times(price));
  marketSnapshot.save();
}

export function setBorrowingFeeOfLastDenChange(_LUSDFee: BigInt): void {
  let lastChangeSequenceNumber = getLastChangeSequenceNumber();

  let lastDenChange = DenChange.load(lastChangeSequenceNumber.toString());
  if (lastDenChange) {
    lastDenChange.borrowingFee = _LUSDFee;
    lastDenChange.save();
  }
}

export function applyRedistributionToDenBeforeLiquidation(event: ethereum.Event, _denManager: Address, _borrower: Address): void {
  let den = getDen(_denManager, _borrower);
  let denManager = getDenManager(_denManager);
  let redistributedCollateral = denManager.rawTotalRedistributedCollateral.minus(den.rawSnapshotOfTotalRedistributedCollateral).times(den.rawStake).div(BIGINT_SCALING_FACTOR);

  let redistributedDebt = denManager.rawTotalRedistributedDebt.minus(den.rawSnapshotOfTotalRedistributedDebt).times(den.rawStake).div(BIGINT_SCALING_FACTOR);

  updateDen(
    event,
    _denManager,
    "accrueRewards",
    _borrower,
    den.rawCollateral.plus(redistributedCollateral),
    den.rawDebt.plus(redistributedDebt),
    BIGINT_ZERO // No need to calculate new stake, because we know the Den is being liquidated
  );
}
