import { useEffect } from "react";
import { accountAtom, profile<PERSON><PERSON><PERSON> } from "../Atoms/Account";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { createPublicClient, erc721Abi, http, parseAbiItem, PublicClient, zeroAddress } from "viem";
import { arbitrum, mainnet } from "viem/chains";
import { BFB_NFT_ADDRESS, HONEY_COMB_NFT_ADDRESS } from "../utils/constants";
import { HONEY_COMB_ABI } from "../abi";

export const useNFT = () => {
  const account = useAtomValue(accountAtom);
  const setProfileNFT = useSetAtom(profileNFTAtom);
  const publicClient = createPublicClient({
    chain: arbitrum,
    transport: http(),
  });
  const ethPublicClient = createPublicClient({
    chain: mainnet,
    transport: http(),
  });

  const getBFBTokenIdsByOwner = async (): Promise<bigint[]> => {
    const in_logs = await publicClient.getLogs({
      address: BFB_NFT_ADDRESS,
      event: parseAbiItem("event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)"),
      args: {
        from: null,
        to: account,
      },
      fromBlock: "earliest",
    });

    const out_logs = await publicClient.getLogs({
      address: BFB_NFT_ADDRESS,
      event: parseAbiItem("event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)"),
      args: {
        from: account,
        to: null,
      },
      fromBlock: "earliest",
    });

    const in_tokenIds = in_logs?.map((log) => log.args.tokenId as bigint);
    const out_tokenIds = out_logs?.map((log) => log.args.tokenId as bigint);

    const tokenIds = in_tokenIds?.filter((tokenId) => !out_tokenIds?.includes(tokenId));

    return tokenIds;
  };

  const getHoneyTokenIdsByOwner = async (): Promise<readonly bigint[]> => {
    if (!account) return [];

    const balance = await ethPublicClient.readContract({
      address: HONEY_COMB_NFT_ADDRESS,
      abi: HONEY_COMB_ABI,
      functionName: "balanceOf",
      args: [account],
    });

    if (balance == 0n) {
      return [];
    }

    const totalSupply = await ethPublicClient.readContract({
      address: HONEY_COMB_NFT_ADDRESS,
      abi: HONEY_COMB_ABI,
      functionName: "totalSupply",
      args: [],
    });

    const tokenIds: bigint[] = [];
    const chunkSize = 5000n;
    for (let i = 0n; i < totalSupply; i += chunkSize) {
      const _tokenIds = await ethPublicClient.readContract({
        address: HONEY_COMB_NFT_ADDRESS,
        abi: HONEY_COMB_ABI,
        functionName: "tokensOfOwnerIn",
        args: [account, i, i + chunkSize],
      });
      if (_tokenIds && _tokenIds.length > 0) {
        return [_tokenIds[0]];
      }
    }

    return tokenIds;
  };

  const getImageOfTokenId = async (publicClient: PublicClient, contractAddress: `0x${string}`, tokenId: bigint): Promise<string> => {
    const tokenURI = await publicClient.readContract({
      address: contractAddress,
      abi: erc721Abi,
      functionName: "tokenURI",
      args: [tokenId],
    });

    const ipfsGateway = "https://ipfs.io/ipfs/";
    const httpUrl = tokenURI.replace("ipfs://", ipfsGateway);
    const response = await fetch(httpUrl);
    if (!response.ok) {
      return "";
    }

    const metadata = await response.json();

    // Extract the image URL
    let imageUrl = metadata.image;

    // If the image URL is also an IPFS URI, convert it to an HTTP URL
    if (imageUrl.startsWith("ipfs://")) {
      imageUrl = imageUrl.replace("ipfs://", ipfsGateway);
    }

    return imageUrl;
  };

  useEffect(() => {
    if (!account || account == zeroAddress || !publicClient || !ethPublicClient) return;
    (async () => {
      try {
        // Check BFB firstly
        const tokenIds = await getBFBTokenIdsByOwner();

        const tokenId = tokenIds?.[0];
        if (tokenId !== undefined) {
          const imgUrl = await getImageOfTokenId(publicClient, BFB_NFT_ADDRESS, tokenId);
          setProfileNFT({
            url: imgUrl,
            type: "Image",
          });
          return;
        } else {
          // Check Honey Comb secondly
          const honeyTokenIds = await getHoneyTokenIdsByOwner();
          const honeyTokenId = honeyTokenIds?.[0];
          if (honeyTokenId !== undefined) {
            const honeyImgUrl = await getImageOfTokenId(ethPublicClient, HONEY_COMB_NFT_ADDRESS, honeyTokenId);
            setProfileNFT({
              url: honeyImgUrl,
              type: "Video",
            });
          } else {
            setProfileNFT({
              url: "",
              type: "Image",
            });
          }
        }
      } catch (err) {
        setProfileNFT({
          url: "",
          type: "Image",
        });
      }
    })();
  }, [account, publicClient, ethPublicClient]);
};
