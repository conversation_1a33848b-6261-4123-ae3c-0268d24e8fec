import { useAtomValue } from "jotai";
import { simpleModeAtom } from "../Atoms/Account";
import { useLocation } from "react-router-dom";
import { SxProps, Theme } from "@mui/material";
import { SystemStyleObject } from "@mui/system";

export const useBackgroundImage = () => {
  //   const borrowLevel = useAtomValue(borrowLevelAtom);

  //   const navigation = useAtomValue(navigationAtom);
  const simpleMode = useAtomValue(simpleModeAtom);
  const location = useLocation();
  let backgroundSxProps: SxProps<Theme> = {};
  let justifyContent: SystemStyleObject<Theme> = { xs: "flex-top", lg: "center" };
  let backgroundPadding:
    | {
        xs: number;
        sm: number;
        md: number;
        lg: number;
      }
    | undefined = { xs: 20, sm: 30, md: 8, lg: 0 };
  let backgroundImg: string | undefined = undefined;
  let backgroundColor: string | undefined = "var(--background-accent-full)";
  if (location.pathname.startsWith("/rewards")) {
    backgroundColor = "var(--background-accent-full)";
    backgroundImg = undefined;
    backgroundPadding = undefined;
    backgroundSxProps = {};
    justifyContent = { xs: "flex-top" };
  } else if (simpleMode) {
    backgroundColor = undefined;
    backgroundImg = undefined;
    backgroundPadding = undefined;
    backgroundSxProps = {};
  } else {
    if (location.pathname === "/") {
      justifyContent = { xs: "flex-end", lg: "center" };
      //   backgroundImg = `landing/${navigation}.webp`;
      backgroundImg = `landing/defaultv2.webp`;

      backgroundPadding = undefined;
      backgroundSxProps = {
        backgroundPosition: {
          xs: "center top 45px",
          sm: "right top 45px",
          md: "right top 45px",
          lg: "center",
        },
        backgroundSize: {
          xs: "150% auto",
          sm: "120% auto",
          lg: "cover",
        },
      };
    } else if (location.pathname.startsWith("/vault/")) {
      backgroundImg = `vaultv2.webp`;
      backgroundPadding = { xs: 26, sm: 30, md: 8, lg: 0 };
      backgroundSxProps = {
        backgroundPosition: {
          xs: "right top 45px",
          sm: "right top 45px",
          lg: "right bottom",
        },
        backgroundSize: {
          xs: "150% auto",
          sm: "cover",
          lg: "cover",
        },
      };
    } else if (location.pathname.startsWith("/vault")) {
      backgroundImg = `vaultv2.webp`;

      backgroundPadding = { xs: 26, sm: 30, md: 8, lg: 0 };
      backgroundSxProps = {
        backgroundAttachment: { md: "fixed" },
        backgroundPosition: {
          xs: "right top 45px",
          md: "center",
        },
        backgroundSize: {
          xs: "150% auto",
          sm: "cover",
          lg: "cover",
        },
      };
    } else if (location.pathname.startsWith("/boyco")) {
      backgroundImg = `boycov2.webp`;
      backgroundSxProps = {
        backgroundPosition: {
          xs: "center top 45px",
          sm: "right top 45px",
          md: "right top 45px",
          lg: "center",
        },
        backgroundSize: {
          xs: "150% auto",
          sm: "120% auto",
          lg: "cover",
        },
      };
    } else if (location.pathname.startsWith("/pool")) {
      backgroundImg = "poolv2.webp";
      backgroundSxProps = {
        backgroundPosition: {
          xs: "center top 45px",
          sm: "right top -60px",
          md: "right center",
          lg: "center center",
        },
        backgroundSize: {
          xs: "900px 300px, auto 300px",
          sm: "cover",
        },
      };
    } else if (location.pathname.startsWith("/redeem")) {
      backgroundImg = "redeemv2.webp";
      backgroundSxProps = {
        backgroundPosition: {
          xs: "center top 45px",
          sm: "right top -60px",
          md: "right top -60px",
          lg: "right center",
        },
        backgroundSize: {
          xs: "900px 300px, auto 300px",
          sm: "cover",
        },
      };
    } else if (location.pathname.startsWith("/dashboard")) {
      backgroundImg = "dashboardv2.webp";
      backgroundSxProps = {
        backgroundAttachment: { md: "fixed" },
        backgroundPosition: {
          xs: "center top 0px",
          sm: "right top ",
          md: "left center",
          lg: "left center",
        },
        backgroundSize: {
          xs: "00px 00px, auto 300px",
          sm: "120% auto",

          md: "cover",
        },
      };
    } else if (location.pathname.startsWith("/den")) {
      backgroundImg = `denv3.webp`;
      backgroundSxProps = {
        backgroundImage: {
          xs: `linear-gradient(to bottom, transparent 80%,var(--background-accent-full)), url(/background/${backgroundImg})`,
          md: ` url(/background/${backgroundImg})`,
        },
      };
      backgroundSxProps = {
        backgroundAttachment: location.pathname === "/den" ? { md: "fixed" } : undefined,
        backgroundPosition: {
          xs: "left top 45px",
          sm: "left top -60px",
          md: "left center",
          lg: "center center",
        },
        backgroundSize: {
          xs: "900px 300px, auto 300px",
          sm: "120% auto",

          md: "cover",
        },
      };

      //   if (borrowLevel === "Gold") {
      //     backgroundSxProps = {
      //       ...backgroundSxProps,
      //       backgroundPosition: {
      //         xs: "center top -50px",
      //         sm: "center top -250px",
      //         md: "center top 0px",
      //         lg: "center ",
      //       },
      //       backgroundSize: {
      //         xs: "auto 400px",
      //         sm: "auto 800px",
      //         md: "350% auto",
      //         lg: "cover",
      //       },
      //     };
      //   } else if (borrowLevel === "Silver") {
      //     backgroundSxProps = {
      //       ...backgroundSxProps,
      //       backgroundPosition: {
      //         xs: "center top -50px",
      //         sm: "center top -250px",
      //         md: "center",
      //       },
      //       backgroundSize: {
      //         xs: "auto 400px",
      //         sm: "auto 800px",
      //         md: "cover",
      //       },
      //     };
      //   } else if (borrowLevel === "Bronze") {
      //     backgroundSxProps = {
      //       ...backgroundSxProps,
      //       backgroundPosition: {
      //         xs: "center top -50px",
      //         sm: "center top -250px",
      //         md: "center",
      //       },
      //       backgroundSize: {
      //         xs: "auto 400px",
      //         sm: "auto 800px",
      //         md: "cover",
      //       },
      //     };
      //   } else {
      //     backgroundSxProps = {
      //       ...backgroundSxProps,
      //       backgroundPosition: {
      //         xs: "left -50px top 45px",
      //         sm: "left -50px top -250px",
      //         md: "right -150px center",
      //         lg: "center",
      //       },
      //       backgroundSize: {
      //         xs: "600px 300px, auto 300px",
      //         sm: "1150px 700px, auto 700px",
      //         md: "cover",
      //       },
      //     };
      //   }
    } else {
      backgroundImg = undefined;
      backgroundPadding = undefined;
      backgroundSxProps = {};
    }
    backgroundSxProps = {
      backgroundImage: {
        xs: `linear-gradient(to bottom, transparent 80%,var(--background-accent-full)), url(/background/${backgroundImg})`,
        sm: ` url(/background/${backgroundImg})`,
      },
      ...backgroundSxProps,
    };
  }

  return { backgroundImg, backgroundPadding, backgroundSxProps, justifyContent, backgroundColor };
};
