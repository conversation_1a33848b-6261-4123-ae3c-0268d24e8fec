import { usePostHog as usePostHogBase } from "posthog-js/react";
import { useLocation } from "react-router-dom";
import { useAtomValue } from "jotai";
import { simpleModeAtom, borrowLevelAtom, accountAtom } from "../Atoms/Account";
import { useAccountEffect } from "wagmi";

export const usePostHog = () => {
  const posthog = usePostHogBase();
  const location = useLocation();
  const simpleMode = useAtomValue(simpleModeAtom);
  const borrowLevel = useAtomValue(borrowLevelAtom);
  const account = useAtomValue(accountAtom);
  const walletConnected = ({ address }: { address?: string }) => {
    if (address) {
      posthog.identify(address.toString());
      posthog.capture("Wallet Connected", {
        wallet_id: address.toString(),
        membership_level: borrowLevel,
        page_rel_path: location.pathname,
        display_mode: simpleMode ? "simple" : "den",
      });
    }
  };
  const walletDisconnected = () => {
    if (account) {
      posthog.capture("Wallet Disconnected", {
        wallet_id: account.toString(),
        membership_level: borrowLevel,
        page_rel_path: location.pathname,
        display_mode: simpleMode ? "simple" : "den",
      });

      posthog.reset();
    }
  };
  useAccountEffect({
    onConnect(data) {
      walletConnected(data);
    },
    onDisconnect() {
      walletDisconnected();
    },
  });
};
