import { useAtomValue, useAtom } from "jotai";
import { denManagersAtom, denManagerAtom } from "../Atoms/Den";
import { useParams } from "react-router-dom";
import { useEffect } from "react";

export const useUrlTicker = () => {
  let { ticker: tickerParam } = useParams();
  const denManagers = useAtomValue(denManagersAtom);
  const [denManagerAddr, setDenManagerAddr] = useAtom(denManagerAtom);
  const newDenManager = denManagers.find((item) => item.collateralTicker.toLowerCase() === tickerParam?.toLowerCase())?.contractAddress;

  useEffect(() => {
    if (newDenManager && newDenManager !== denManagerAddr) {
      setDenManagerAddr(newDenManager);
    }
  }, [newDenManager, denManagerAddr]);
};
