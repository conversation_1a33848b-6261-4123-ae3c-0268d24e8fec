import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useAccount, useWriteContract } from "wagmi";
import { useSimulateBeraBorrow } from "./useSimulateBeraBorrow";
import { latestTxAtom, writeStateAtom } from "../Atoms/Transaction";
import { useEffect } from "react";
import { TransactionProps } from "../@type/Transactions";
import { PostHogEvent } from "../@type/Account";
import { useNotifications } from "./useNotifications";
import { useWriteContext } from "../providers/TransactionProvider";
import { invalidatorAtom } from "../Atoms/System";
import { pendingPostHogEventAtom, postHogEventAtom } from "../Atoms/Account";
import { useQueryClient } from "@tanstack/react-query";

export const useTransaction = (transactionDetails: TransactionProps, local?: boolean) => {
  const { connector } = useAccount();
  const { addToast } = useNotifications();
  const queryClient = useQueryClient();
  const { simulate, error: simulateError, status: simulateStatus } = useSimulateBeraBorrow(transactionDetails);
  const { writeContract: writeContractGlobal, data: writeDataGlobal, error: writeErrorGlobal, status: writeStatusGlobal, reset: resetWriteGlobal } = useWriteContext();
  const { writeContract: writeContractLocal, data: writeDataLocal, error: writeErrorLocal, status: writeStatusLocal, reset: resetWriteLocal } = useWriteContract();
  const { writeContract, writeData, writeError, writeStatus, resetWrite } = local
    ? { writeContract: writeContractLocal, writeData: writeDataLocal, writeError: writeErrorLocal, writeStatus: writeStatusLocal, resetWrite: resetWriteLocal }
    : { writeContract: writeContractGlobal, writeData: writeDataGlobal, writeError: writeErrorGlobal, writeStatus: writeStatusGlobal, resetWrite: resetWriteGlobal };
  const latestTx = useAtomValue(latestTxAtom);
  const setWriteState = useSetAtom(writeStateAtom);
  const setInvalidation = useSetAtom(invalidatorAtom);
  let isConfirmed = writeStatus === "success" && simulateStatus === "success" && latestTx?.blockNumber && latestTx?.hash === writeData;
  const setPendingPostHogEvent = useSetAtom(pendingPostHogEventAtom);
  const postHogEvent = useAtomValue(postHogEventAtom);
  useEffect(() => {
    //convert write state to Atom
    const setTransaction = async () => {
      if (writeData && writeStatus === "success") {
        if (connector?.id === "safe") {
          addToast({ id: "broadcast-safe-tx", message: "Complete signing of Transaction in Safe", severity: "info" });
          setTimeout(() => {
            setInvalidation("all");
            isConfirmed = true;
            setWriteState({ type: "idle" });
            resetWrite();
            queryClient.setQueryDefaults([], {
              refetchOnWindowFocus: true,
              refetchOnMount: true,
              refetchOnReconnect: true,
              refetchInterval: 1000 * 5, // 5 seconds,
              staleTime: 1000 * 10, // 10 seconds
              gcTime: 1000 * 60 * 10, // 10 minutes
            });
          }, 10000);
        }
      }
      setWriteState(
        writeStatus === "error"
          ? { type: writeStatus, payload: writeError }
          : writeStatus === "success"
            ? writeData
              ? { type: writeStatus, payload: writeData }
              : { type: "error", payload: "Transaction Hash not found" }
            : { type: writeStatus }
      );
    };
    setTransaction();
  }, [writeStatus]);

  const submit = async (type?: PostHogEvent["type"]) => {
    resetWrite();
    const simulateRes = await simulate();
    if (simulateRes && writeStatus !== "pending") {
      writeContract(simulateRes.request);
      if (postHogEvent) {
        setPendingPostHogEvent({ ...postHogEvent, type: type ?? postHogEvent.type });
      }
    }
  };
  const error: unknown | null = simulateError ?? writeError;
  return {
    submit,
    data: writeData ?? null,
    error,
    isIdle: simulateStatus === "idle",
    isPending: writeStatus === "pending" || simulateStatus === "pending",
    isBlocked: !latestTx?.blockNumber && latestTx?.hash !== writeData,
    isConfirming: writeStatus === "success" && simulateStatus === "success" && !latestTx?.blockNumber && latestTx?.hash === writeData,
    isConfirmed,
  };
};
