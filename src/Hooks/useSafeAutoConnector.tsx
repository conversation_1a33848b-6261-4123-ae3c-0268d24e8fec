import { useConnect } from "wagmi";
import { useEffect } from "react";
import { DEPLOYMENT_CHAIN_ID } from "../utils/constants";
import { useSet<PERSON>tom } from "jotai";
import { isSafeConnectorAtom } from "../Atoms/System";
import { useQueryClient } from "@tanstack/react-query";

const AUTOCONNECTED_CONNECTOR_IDS = ["safe"];

function useSafeAutoConnecter() {
  const { connect, connectors } = useConnect();
  const setSafeConnector = useSetAtom(isSafeConnectorAtom);
  const queryClient = useQueryClient();
  //   useModal({
  //     onDisconnect: () => {
  //       setSafeConnector(false);
  //     },
  //   });
  function inIframe() {
    try {
      return window.self !== window.top;
    } catch (e) {
      return true;
    }
  }
  useEffect(() => {
    AUTOCONNECTED_CONNECTOR_IDS.forEach((connector) => {
      const connectorInstance = connectors.find((c) => c.id === connector);
      if (connectorInstance && inIframe()) {
        connect({ connector: connectorInstance, chainId: DEPLOYMENT_CHAIN_ID });
        queryClient.setQueryDefaults([], {
          refetchOnWindowFocus: true,
          refetchOnMount: false,
          refetchOnReconnect: true,
          refetchInterval: 1000 * 10, // 10 seconds,
          staleTime: 1000 * 10, // 10 seconds
          gcTime: 1000 * 60 * 10, // 10 minutes
        });
        setSafeConnector(true);
      }
    });
  }, [connect, connectors]);
}

export default useSafeAutoConnecter;
