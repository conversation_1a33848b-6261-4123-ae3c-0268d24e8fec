import { useSet<PERSON>tom } from "jotai";
import { useWatchBlocks as useWatchBlocksWagmi } from "wagmi";
import { blockNumberAtom } from "../Atoms/System";

export const useWatchBlocks = () => {
  // Simple hook that will close the change chain modal after chain changed to Bera
  const setBlockNumber = useSetAtom(blockNumberAtom);
  useWatchBlocksWagmi({
    onBlock: (block) => {
      if (block?.number) {
        setBlockNumber(block.number);
      }
    },
  });
};
