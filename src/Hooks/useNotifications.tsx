import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { addModal<PERSON>tom, addToastAtom, isModalPresentAtom, removeModalAtom, removeToastAtom } from "../Atoms/Notifications";

export const useNotifications = () => {
  const addToast = useSetAtom(addToastAtom);
  const removeToast = useSetAtom(removeToastAtom);
  const isModalPresent = useAtomValue(isModalPresentAtom);
  const addModal = useSetAtom(addModalAtom);
  const removeModal = useSetAtom(removeModalAtom);

  return { addModal, isModalPresent, removeModal, addToast, removeToast };
};
