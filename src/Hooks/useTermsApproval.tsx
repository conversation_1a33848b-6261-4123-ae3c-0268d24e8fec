import { useEffect } from "react";
import { useNotifications } from "./useNotifications";
import { useAtomValue } from "jotai";
import { acceptedAtom } from "../Atoms/System";
import TermsApproval from "../components/TermsApproval";

export const useTermsApproval = () => {
  const { addModal } = useNotifications();
  const termsAccepted = useAtomValue(acceptedAtom);
  useEffect(() => {
    if (!termsAccepted) {
      addModal({ id: "tcAccept", dismissable: false, maxWidth: "800px", title: "Disclaimer", Component: <TermsApproval /> });
    }
  }, []);
};
