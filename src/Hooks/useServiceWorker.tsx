import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useAtom } from "jotai";
import { updateAppTimestampAtom } from "../Atoms/System";
import { useRegisterSW } from "virtual:pwa-register/react";
let swInterval: number | undefined;

export const useServiceWorker = () => {
  const [updateAppTimestamp, setUpdateAppTimestamp] = useAtom(updateAppTimestampAtom);
  const location = useLocation();
  const intervalMS = 5 * 60 * 1000; //5 minutes

  const { updateServiceWorker } = useRegisterSW({
    onNeedRefresh() {
      setUpdateAppTimestamp(Date.now());
    },
    onRegisteredSW(swUrl, r) {
      if (r && !swInterval) {
        swInterval = window.setInterval(async () => {
          if (r.installing || !navigator) return;
          if ("connection" in navigator && !navigator.onLine) return;

          try {
            const resp = await fetch(swUrl, {
              cache: "no-store",
              headers: {
                cache: "no-store",
                "cache-control": "no-cache",
              },
            });
            if (resp?.status === 200) {
              await r.update();
            }
          } catch (_) {}
        }, intervalMS);
      }
    },
  });
  useEffect(() => {
    if (updateAppTimestamp) {
      updateServiceWorker();
      window.location.reload();
      console.debug("update on page change");
    }
  }, [location.pathname]);
  useEffect(() => {
    if (updateAppTimestamp) {
      setTimeout(() => {
        updateServiceWorker();
        window.location.reload();
        console.debug("update on timeout");
      }, intervalMS);
    }
  }, [updateAppTimestamp]);
};
