import { useAtom, useAtomValue } from "jotai";
import { Hex, SimulateContractReturnType } from "viem";
import { accountAtom, beraborrowAtom } from "../Atoms/Account";
import { denManagerAtom, protocolAtom } from "../Atoms/Den";
import { getBeraBalanceAtom, collateralTokenAtom } from "../Atoms/Tokens";
import { simulateStateAtom } from "../Atoms/Transaction";
import { BERABORROW_ADDRESSES } from "../utils/constants";
import { useEffect } from "react";
import { TransactionProps } from "../@type/Transactions";
import { isSafeConnectorAtom } from "../Atoms/System";
import { _vaultAddrAtom } from "../Atoms/Vault";
import { _managedVaultAtom } from "../Atoms/Boyco";
import { simulateContract } from "viem/actions";
import { usePublicClient } from "wagmi";

export const useSimulateBeraBorrow = (transaction: TransactionProps) => {
  const { data: beraBalance } = useAtomValue(getBeraBalanceAtom);
  const beraborrow = useAtomValue(beraborrowAtom);
  const denManagerAddr = useAtomValue(denManagerAtom);
  const collVault = useAtomValue(_vaultAddrAtom);
  const managedVault = useAtomValue(_managedVaultAtom);
  const account = useAtomValue(accountAtom);
  const isSafe = useAtomValue(isSafeConnectorAtom);
  const collateralAddr = useAtomValue(collateralTokenAtom);
  const protocol = useAtomValue(protocolAtom);
  const [state, setState] = useAtom(simulateStateAtom);
  const client = usePublicClient();

  useEffect(() => {
    return () => {
      setState({ type: "idle" });
    };
  }, []);

  const simulate = async () => {
    const { type, variables } = transaction;
    try {
      setState({ type: "pending" });
      let simulateRes: unknown;
      switch (type) {
        case "depositDebt":
          simulateRes = await beraborrow.stabilityPool.depositDebtInStabilityPool(...variables);
          break;
        case "withdrawDebtOnly":
          simulateRes = await beraborrow.stabilityPool.withdrawDebtFromStabilityPool(...variables);
          break;
        case "redeemDebtOnly":
          simulateRes = await beraborrow.stabilityPool.redeemDebtFromStabilityPool(...variables);
          break;
        case "withdrawDebt":
          simulateRes = await beraborrow.lspRouter.redeem(...variables);
          break;
        case "approveCollateral": {
          if (!account) throw new Error("Account required");
          const contractAddress = variables[1] || beraborrow.protocols[protocol].borrowerOperationsHandler.getInternalBorrowOperationsAddress(denManagerAddr);
          simulateRes = await beraborrow.collateralTokens[collateralAddr].approve(variables[0], contractAddress, account);
          break;
        }
        case "approveLsp":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.stabilityPool.approve(...variables, beraborrow.lspRouter.contractAddress);
          break;
        case "openDen":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.protocols[protocol].borrowerOperationsHandler.openDen(denManagerAddr, account, ...variables);
          break;
        case "adjustDen":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.protocols[protocol].borrowerOperationsHandler.adjustDen(denManagerAddr, account, !!variables[0].borrowNECT, ...variables);
          break;
        // case "openLeverage":
        //   if (!account) throw new Error("Account required");
        //   simulateRes = await beraborrow.leverageRouter.automaticLoopingOpenDen(denManagerAddr, collVault, account, ...variables);
        //   break;
        // case "adjustLeverage":
        //   if (!account) throw new Error("Account required");
        //   simulateRes = await beraborrow.leverageRouter.automaticLoopingAddCollateral(denManagerAddr, collVault, account, ...variables);
        //   break;
        case "closeDen":
          if (!account) throw new Error("Account required");

          simulateRes = await beraborrow.protocols[protocol].borrowerOperationsHandler.closeDen(
            BERABORROW_ADDRESSES.denManagers[denManagerAddr].denManager ?? denManagerAddr,
            account
          );
          break;
        case "redeemDebt":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.denManagers[denManagerAddr].denManager.redeemDebt(...variables);
          break;
        case "redeemPsm":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.psmBond.redeem(...variables, account);
          break;
        case "approveDelegate":
          if (!account) throw new Error("Account required");

          simulateRes = await beraborrow.protocols[protocol].borrowerOperationsHandler.setDelegateApproval(...variables, true);
          break;
        case "claimCollateralSurplus":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.denManagers[denManagerAddr].denManager.claimCollateralSurplusBalances(...variables);
          break;
        case "approveVault":
          if (!account) throw new Error("Account required");

          simulateRes = await beraborrow.collateralTokens[BERABORROW_ADDRESSES.vaults[collVault].collateral].approve(...variables, collVault);
          break;
        case "depositVault":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.vaults[collVault].deposit(...variables, account);
          break;
        case "redeemVault":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.vaults[collVault].redeem(...variables, account);
          break;
        case "approveManagedVault":
          if (!account) throw new Error("Account required");

          simulateRes = await beraborrow.collateralTokens[BERABORROW_ADDRESSES.managedVaults[managedVault].collateral].approve(...variables, managedVault);
          break;
        case "depositManagedVault":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.managedVaults[managedVault].managedVault.deposit(...variables, account);
          break;
        case "redeemIntentManagedVault":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.managedVaults[managedVault].managedVault.redeemIntent(...variables, account);
          break;
        case "claimIntentManagedVault":
          if (!account) throw new Error("Account required");
          const swapper: Hex = "0xcE46cefd82eb0e9572E4618dD1E6E6233b1F6Cb6"; //TODO add swapper
          simulateRes = await beraborrow.managedVaults[managedVault].managedVault.withdrawFromEpoch(...variables, swapper, account);
          break;
        case "cancelIntentManagedVault":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.managedVaults[managedVault].managedVault.cancelWithdrawalIntent(...variables, account);
          break;
        case "withdrawBoyco":
          if (!account) throw new Error("Account required");
          // @ts-ignore
          simulateRes = await simulateContract(client, {
            address: variables[0],
            abi: [
              {
                name: "redeem",
                type: "function",
                stateMutability: "nonpayable",
                inputs: [
                  { name: "shares", type: "uint256" },
                  { name: "receiver", type: "address" },
                  { name: "owner", type: "address" },
                ],
                outputs: [],
              },
            ],
            functionName: "redeem",
            args: [variables[1], account, account],
            account: account,
          });

          break;

        // 1) I need to use wrap methods, instead of stake methods
        // 2) I need to call sPollen.depositFor instead of stake -> call beraborrow.wrapPollen -> call approve first from it

        // 3) unwrap to call sPollen.withdrawTo instead of unstake
        // 4) beraborrow.wrapPollen , and beraborrow.unwrapPollen

        case "wrapPollen":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.sPollenToken.deposit(variables[0], account);
          break;

        case "unwrapPollen":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.sPollenToken.withdraw(variables[0], account);
          break;

        case "stakePollen":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.sPollenStaking.stake(variables[0], account);
          break;

        case "unstakePollen":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.sPollenStaking.unstake(variables[0], account);
          break;

        case "claimPollenRewards":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.sPollenStaking.claimRewards(account);
          break;

        case "claimLpPollenRewards":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.lpPollenStaking.claimRewards(account);
          break;

        case "stakeLpPollen":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.lpPollenStaking.stake(variables[0], account);
          break;

        case "unstakeLpPollen":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.lpPollenStaking.unstake(variables[0], account);
          break;

        case "increaseLockPosition":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.sPollenVotingEscrow.increaseLockPosition(variables[0], variables[1], account);
          break;

        case "increaseLpLockPosition":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.lpVotingEscrow.increaseLockPosition(variables[0], variables[1], account);
          break;

        case "withdrawVotingEscrow":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.sPollenVotingEscrow.withdraw(account);
          break;

        case "withdrawLpVotingEscrow":
          if (!account) throw new Error("Account required");
          simulateRes = await beraborrow.lpVotingEscrow.withdraw(account);
          break;

        default:
          throw new Error("Invalid Transaction Type");
      }
      const gas = (simulateRes as SimulateContractReturnType).request?.gas;
      if (gas && beraBalance <= gas && !isSafe) {
        throw new Error("Not enough Bera to cover gas");
      }

      setState({ type: "success", payload: simulateRes as SimulateContractReturnType });
      return simulateRes as SimulateContractReturnType;
    } catch (error) {
      setState({ type: "error", payload: error });
    }
  };

  return { simulate, status: state.type, data: state.type === "success" ? state.payload : null, error: state.type === "error" ? state.payload : null };
};
