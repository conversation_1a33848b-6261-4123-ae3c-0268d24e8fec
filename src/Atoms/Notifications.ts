import { atom } from "jotai";
import { NotificationTypes, ModalProps, ToastProps } from "../@type/Notifications";

export const modalsAtom = atom<ModalProps[]>([]);
export const addModalAtom = atom<null, [value: ModalProps], void>(null, (get, set, value) => {
  set(modalsAtom, [...get(modalsAtom).filter((item) => item.id !== value.id), value]);
});
export const isModalPresentAtom = atom<(id: string) => boolean>((get) => {
  const isModalPresent = (id: string) => {
    return get(modalsAtom).some((item) => item.id === id);
  };
  return isModalPresent;
});
export const removeModalAtom = atom<null, [value: string], void>(null, (get, set, value) => {
  set(modalsAtom, [...get(modalsAtom).filter((item) => item.id !== value)]);
});
export const toastsAtom = atom<ToastProps[]>([]);
export const addToastAtom = atom<null, [value: ToastProps], void>(null, (get, set, value) => {
  set(toastsAtom, [...get(toastsAtom).filter((item) => item.id !== value.id), value]);
});
export const removeToastAtom = atom<null, [value: string], void>(null, (get, set, value) => {
  set(toastsAtom, [...get(toastsAtom).filter((item) => item.id !== value)]);
});
export const prevNotificationEventAtom = atom<NotificationTypes | undefined>(undefined);

export const notificationEventAtom = atom<NotificationTypes | undefined>(undefined);
export const setNotificationEventAtom = atom<null, [value: NotificationTypes], void>(null, (get, set, value) => {
  const prev = get(prevNotificationEventAtom);
  if (!prev || (prev && (prev.payload !== value.payload || (prev.payload === value.payload && prev.type !== value.type)))) {
    set(notificationEventAtom, value);
    if (value.type !== "error") set(prevNotificationEventAtom, value);
    setTimeout(() => {
      set(notificationEventAtom, undefined);
    }, 6000);
  }
});
