import { atomWithSuspenseQuery } from "jotai-tanstack-query";
import { accountAtom, beraborrow<PERSON>tom } from "./Account";
import { atom } from "jotai";
import { getBalance } from "@wagmi/core";
import { BERABORROW_ADDRESSES, WAGMI_ADAPTOR } from "../utils/constants";
import { Hex, maxUint256, zeroAddress } from "viem";
import { denManagerAtom, protocolAtom } from "./Den";
import { CollateralDetails } from "../@type/Token";
import { psmBondAtom } from "./PsmBond";
import { vaultAtom } from "./Vault";
import { managedVaultAtom } from "./Boyco";

export const collateralTypeSelectorAtom = atom<"den" | "psm" | "vault" | "managedVault" | "staking">("den");
export const stakingTokenAtom = atom<Hex | undefined>(undefined);

export const collateralTokenAtom = atom<Hex>((get) => {
  const psmTokenAddr = get(psmBondAtom);
  const vaultAddr = get(vaultAtom);
  const managedVaultAddr = get(managedVaultAtom);
  const denManagerAddr = get(denManagerAtom);
  const collateralTypeSelector = get(collateralTypeSelectorAtom);
  const stakingToken = get(stakingTokenAtom);

  if (collateralTypeSelector === "staking") {
    // Fallback to POLLEN token address if stakingTokenAtom is undefined
    return stakingToken as `0x${string}`;
  } else if (psmTokenAddr && collateralTypeSelector === "psm") {
    return psmTokenAddr;
  } else if (vaultAddr && collateralTypeSelector === "vault") {
    return BERABORROW_ADDRESSES?.vaults[vaultAddr].collateral;
  } else if (managedVaultAddr && collateralTypeSelector === "managedVault") {
    return BERABORROW_ADDRESSES?.managedVaults[managedVaultAddr].collateral;
  }
  return BERABORROW_ADDRESSES?.denManagers[denManagerAddr].collateral;
});
export const getBeraBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), "bera"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    if (!account || account === zeroAddress) {
      return 0n;
    }
    const balance = await getBalance(WAGMI_ADAPTOR.wagmiConfig, {
      address: account,
    });
    return balance.value;
  },

  throwOnError: true,
}));

export const getCollateralBalanceAtom = atom((get) => {
  const collateral = get(collateralTokenAtom);
  if (collateral === zeroAddress) {
    return get(getBeraBalanceAtom);
  }
  return get(_getCollateralBalanceAtom);
});

const _getCollateralBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), get(collateralTokenAtom), "getCollateralBalance"],
  queryFn: async ({ queryKey }) => {
    const [, collateralToken] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const balance = await beraborrow.collateralTokens[collateralToken].getBalance();
    return balance;
  },
  throwOnError: true,
}));
export const getCollateralPrice = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(collateralTokenAtom), "getCollateralPrice"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const psmTokenAddr = get(psmBondAtom);
    const denManagerAddr = get(denManagerAtom);
    let price = 0n;
    if (psmTokenAddr) {
      return BigInt(1e18);
      //prices are set at 1 for redemption
      //   return beraborrow.priceFeed.fetchPrice(psmTokenAddr);
    } else if (beraborrow.denManagers[denManagerAddr]?.vault) {
      price = await beraborrow.denManagers[denManagerAddr].vault.getPrice(
        BERABORROW_ADDRESSES.denManagers[denManagerAddr].wrappedCollateral || BERABORROW_ADDRESSES.denManagers[denManagerAddr].collateral
      );
    }
    return price ?? beraborrow.denManagers[denManagerAddr].denManager.fetchPrice();
  },
  throwOnError: true,
}));

export const getCollateralAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), get(collateralTokenAtom), "getCollateralAllowance"],
  queryFn: async ({ queryKey }) => {
    const [address, collateralToken] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const denManagerAddr = get(denManagerAtom);
    const protocol = get(protocolAtom);
    if (collateralToken == zeroAddress) {
      return maxUint256;
    } else {
      return beraborrow.collateralTokens[collateralToken].allowance(
        address,
        beraborrow.protocols[protocol].borrowerOperationsHandler.getInternalBorrowOperationsAddress(denManagerAddr)
      );
    }
  },
  throwOnError: true,
}));
export const getCollateralLeverageAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), get(collateralTokenAtom), "getCollateralLeverageAllowanceAtom"],
  queryFn: async ({ queryKey }) => {
    const [address, collateralToken] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (collateralToken == zeroAddress) {
      return maxUint256;
    } else {
      return beraborrow.collateralTokens[collateralToken].allowance(address, BERABORROW_ADDRESSES.leverageRouter);
    }
  },
  throwOnError: true,
}));
export const getCollateralDetailsAtom = atom<CollateralDetails>((get) => {
  const collateralToken = get(collateralTokenAtom);
  const denManagerAddr = get(denManagerAtom);

  // Regular collateral tokens
  if (!BERABORROW_ADDRESSES.collateralTokens[collateralToken]) {
    console.error(`Collateral token not found: ${collateralToken}`);
    throw new Error(`Collateral token not found: ${collateralToken}`);
  }

  const collateralDecimals = BERABORROW_ADDRESSES.collateralTokens[collateralToken].decimals;
  const vaultDecimals = BERABORROW_ADDRESSES.denManagers[denManagerAddr]?.vault
    ? BERABORROW_ADDRESSES.vaults[BERABORROW_ADDRESSES.denManagers[denManagerAddr]?.vault]?.decimals
    : collateralDecimals;

  return {
    contractAddress: collateralToken,
    decimals: collateralDecimals,
    vaultDecimals: vaultDecimals,
    ticker: BERABORROW_ADDRESSES.collateralTokens[collateralToken].ticker,
    lpToken: BERABORROW_ADDRESSES.collateralTokens[collateralToken]?.lpToken,
    category: BERABORROW_ADDRESSES.collateralTokens[collateralToken]?.category,
  };
});

export const getCollateralTokensAtom = atom<CollateralDetails[]>(() => {
  const collateralTokens = Object.entries(BERABORROW_ADDRESSES.collateralTokens).map((item) => {
    const vaultDecimals = Object.entries(BERABORROW_ADDRESSES.vaults).find((vault) => vault[1].collateral === item[0])?.[1].decimals;

    return {
      contractAddress: item[0] as Hex,
      decimals: item[1].decimals,
      vaultDecimals: vaultDecimals ?? item[1].decimals,
      ticker: item[1].ticker,
      lpToken: item[1]?.lpToken,
      category: item[1]?.category,
    };
  });
  return collateralTokens;
});

export const getPollenBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.pollenToken, "getPollenBalance"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);

    return beraborrow.pollenToken.getBalance();
  },
  throwOnError: true,
}));

export const getPollenPriceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.pollenToken.contractAddress, "getPollenPriceAtom"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    return beraborrow.stabilityPool.getPrice(BERABORROW_ADDRESSES.pollenToken.contractAddress);
  },
  throwOnError: true,
}));

export const getDebtBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.debtToken, "geDebtBalance"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    return beraborrow.debtToken.getBalance();
  },
  throwOnError: true,
}));

export const getDebtPriceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.debtToken.contractAddress, "getDebtPriceAtom"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    return beraborrow.stabilityPool.getPrice(BERABORROW_ADDRESSES.debtToken.contractAddress);
  },
  throwOnError: true,
}));

export const getDebtTotalSupplyAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [BERABORROW_ADDRESSES.debtToken, "getDebtTotalSupplyAtom"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    return beraborrow.debtToken.totalSupply();
  },
  throwOnError: true,
}));
