import { atomWithSuspenseQuery, atomWithQuery } from "jotai-tanstack-query";
import { accountAtom, beraborrowAtom, historyEndDateAtom, historyStartDateAtom } from "./Account";
import { Hex, maxUint256, zeroAddress } from "viem";
import { _denManagerAddrAtom } from "./Den";
import { blockNumberAtom } from "./System";
import { Holding, SCALING_FACTOR, VaultOperationsDto } from "@Beraborrowofficial/sdk";
import { atom } from "jotai";
import { axiosAPI, BERABORROW_ADDRESSES } from "../utils/constants";
import { unwrap } from "jotai/utils";
import { formatNoDecimal, formatToken } from "../utils/helpers";
import { transactionStateAtom } from "./Transaction";
import { FormError } from "../@type";
import { collateralTokenAtom, collateralTypeSelectorAtom, getCollateralBalanceAtom, getCollateralDetailsAtom } from "./Tokens";
import { managedVaultAtom } from "./Boyco";

export const vaultAtom = atom<Hex | undefined>(undefined);

export const publicVaultsAtom = atom(() => {
  return Object.entries(BERABORROW_ADDRESSES.vaults)
    .filter((item) => item[1].public)
    .map((item) => {
      return {
        contractAddress: item[0] as Hex,
        decimals: item[1].decimals,
        collateralAddress: item[1].collateral,
        ticker: item[1].ticker,
      };
    });
});
export const publicVaultsWithDetailAtom = atomWithQuery((get) => {
  const apiVaults = get(getVaultsDetailsNoWaitAtom);
  return {
    queryKey: [zeroAddress, apiVaults.data?.length ?? 0, "publicVaultsWithDetailAtom"],
    queryFn: async () => {
      const publicVaults = get(publicVaultsAtom);
      const { data: apiVaults } = await get(getVaultsDetailsNoWaitAtom);
      if (!apiVaults) {
        return publicVaults;
      }
      const apiVaultMap = new Map(apiVaults.map((v) => [v.vaultAddress, { tvl: v.tvl, apy: v.apy }])) ?? {};
      return publicVaults
        .map((vault) => {
          const apiDetails = apiVaultMap.get(vault.contractAddress);
          return {
            ...vault,
            ...(apiDetails ?? {}), // safely spread if available
          };
        })
        .sort((a, b) => {
          if (a.contractAddress === "0x58bbBf2721B6674eE8C6d86FC3F7aDd40dBC8A14") return -1;
          if (b.contractAddress === "0x58bbBf2721B6674eE8C6d86FC3F7aDd40dBC8A14") return 1;
          return Number((b.tvl ?? 0n) - (a.tvl ?? 0n));
        });
    },
    throwOnError: true,
  };
});

export const vaultDetailsAtom = atom((get) => {
  const vaultAddr = get(_vaultAddrAtom);
  const vaultDetails = BERABORROW_ADDRESSES.vaults[vaultAddr];
  return {
    contractAddress: vaultAddr,
    decimals: vaultDetails.decimals,
    collateralAddress: vaultDetails.collateral,
    ticker: vaultDetails.ticker,
  };
});

export const _vaultAddrAtom = atom<Hex>((get) => {
  const collateralTypeSelector = get(collateralTypeSelectorAtom);

  let _denManagerAddr = get(_denManagerAddrAtom);
  let _vaultAddr = get(vaultAtom);
  let _managedVaultAddr = get(managedVaultAtom);

  if (_vaultAddr && collateralTypeSelector === "vault") {
    return _vaultAddr;
  } else if (_managedVaultAddr && collateralTypeSelector === "managedVault") {
    return BERABORROW_ADDRESSES.denManagers[BERABORROW_ADDRESSES.managedVaults[_managedVaultAddr].denManager].vault;
  }
  return BERABORROW_ADDRESSES.denManagers[_denManagerAddr].vault;
});

export const getInfraredPricingAtom = atomWithSuspenseQuery<{ price: bigint; collPrice: bigint }>((get) => ({
  queryKey: [zeroAddress, get(_vaultAddrAtom), "getInfraredPricingAtom"],
  queryFn: async ({ queryKey }) => {
    const [, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;

    try {
      const price = await axiosAPI.get<{ price: string; collPrice: string }>(`/infrared/vault/price/${_vaultAddr}/${BERABORROW_ADDRESSES.vaults[_vaultAddr].collateral}`);
      return { price: BigInt(price.data.price), collPrice: BigInt(price.data.collPrice) };
    } catch (error) {
      console.error(error);
      return { price: 0n, collPrice: 0n };
    }
  },
  throwOnError: true,
}));

export const getInfraredVaultPriceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, get(_vaultAddrAtom), "getInfraredVaultPrice"],
  queryFn: async ({ queryKey }) => {
    const [, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;

    if (!_vaultAddr || !BERABORROW_ADDRESSES.vaults[_vaultAddr].oracleless) {
      return 0n;
    }
    try {
      const { data: vaults } = await get(getVaultsDetailsAtom);
      const vaultPrice = vaults.find((item) => item.vaultAddress === _vaultAddr)?.price;
      if (vaultPrice) {
        return vaultPrice;
      }
    } catch (error) {
      console.error(error);
    }
    try {
      const { data: pricing } = await get(getInfraredPricingAtom);
      return BigInt(pricing.price);
    } catch (error) {
      console.error(error);
      return 0n;
    }
  },
  throwOnError: true,
}));
export const getInfraredTokenPriceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, get(_vaultAddrAtom), "getInfraredTokenPriceAtom"],
  queryFn: async ({ queryKey }) => {
    const [, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;

    if (!_vaultAddr || !BERABORROW_ADDRESSES.vaults[_vaultAddr].oracleless) {
      return 0n;
    }
    try {
      const { data: vaults } = await get(getVaultsDetailsAtom);
      const collPrice = vaults.find((item) => item.vaultAddress === _vaultAddr)?.collPrice;
      if (collPrice) {
        return collPrice;
      }
    } catch (error) {
      console.error(error);
    }
    try {
      const { data: pricing } = await get(getInfraredPricingAtom);
      return BigInt(pricing.collPrice);
    } catch (error) {
      console.error(error);
      return 0n;
    }
  },
  throwOnError: true,
}));

export const previewVaultedSharesAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, get(_vaultAddrAtom), "previewVaultedSharesAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    const preview = await beraborrow.vaults[_vaultAddr].previewDeposit(SCALING_FACTOR);
    return preview ?? SCALING_FACTOR;
  },
  throwOnError: true,
}));

export const getVaultDepositAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(_vaultAddrAtom), "getVaultDepositAtom"],
  queryFn: async ({ queryKey }) => {
    get(vaultAtom);
    get(collateralTypeSelectorAtom);
    const beraborrow = get(beraborrowAtom);
    const [account, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;

    const assets = await beraborrow.vaults[_vaultAddr].getVaultDeposit(!BERABORROW_ADDRESSES.vaults[_vaultAddr].oracleless, account);
    return assets ?? [];
  },
  throwOnError: true,
}));

export const previewRedeemUnderlyingAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(_vaultAddrAtom), "previewRedeemUnderlyingAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    try {
      const assets = await beraborrow.vaults[_vaultAddr].previewRedeemUnderlying(SCALING_FACTOR, !BERABORROW_ADDRESSES.vaults[_vaultAddr].public);
      return assets ?? [];
    } catch (error) {
      return [];
    }
  },
  throwOnError: true,
}));

export const getCollateralVaultAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), get(collateralTokenAtom), get(_vaultAddrAtom), , "getCollateralAllowance"],
  queryFn: async ({ queryKey }) => {
    const [address, collateralToken, _vaultAddr] = queryKey as Readonly<[Hex, Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (collateralToken == zeroAddress) {
      return maxUint256;
    }
    return beraborrow.collateralTokens[collateralToken].allowance(address, _vaultAddr);
  },
  throwOnError: true,
}));

export const getVaultContractApyAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, get(_vaultAddrAtom), "getVaultContractApyAtom"],
  queryFn: async ({ queryKey }) => {
    try {
      const currentBlock = get(blockNumberAtom);
      const beraborrow = get(beraborrowAtom);
      const [, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;

      const lpToken = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.vaults[_vaultAddr].collateral].lpToken;
      if (lpToken) {
        const [apyResp, aprResp] = await Promise.allSettled([
          beraborrow.vaults[_vaultAddr].getContractAPY(currentBlock || undefined, 14400n, false),
          axiosAPI.get<{ apr: string }>(`/infrared/vault/apr/${BERABORROW_ADDRESSES.vaults[_vaultAddr].collateral}`),
        ]);
        const apr = BigInt((aprResp.status === "fulfilled" && aprResp?.value.data.apr) || 0);
        const apy = (apyResp.status === "fulfilled" && apyResp?.value) || 0n;
        return apy < apr ? apr : apy;
      }
      const apy = await beraborrow.vaults[_vaultAddr].getContractAPY(currentBlock || undefined);

      return apy < 0n ? 0n : apy;
    } catch (error) {
      console.error(error);
      return 0n;
    }
  },
  throwOnError: true,
}));

export const getVaultsDetailsAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, "getVaultsDetailsAtom"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const vaults = await beraborrow.api.getVaults();
    return vaults.map((item) => ({
      ...item,
      apy: formatNoDecimal(item.apy.toFixed(18), 16),
      tvl: formatNoDecimal(item.tvl.toFixed(18), 18),
      price: formatNoDecimal(item.price.toFixed(18), 18),
      collPrice: formatNoDecimal(item.collPrice.toFixed(18), 18),
    }));
  },
  retry: false,
}));
export const getVaultsDetailsNoWaitAtom = atomWithQuery((get) => ({
  queryKey: [zeroAddress, "getVaultsDetailsAtom"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const vaults = await beraborrow.api.getVaults();
    return vaults.map((item) => ({
      ...item,
      apy: formatNoDecimal(item.apy.toFixed(18), 16),
      tvl: formatNoDecimal(item.tvl.toFixed(18), 18),
      price: formatNoDecimal(item.price.toFixed(18), 18),
    }));
  },
  retry: false, // disable retries to speed up fail-fast
}));

export const getVaultApyAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, get(_vaultAddrAtom), "getVaultApyAtom"],
  queryFn: async ({ queryKey }) => {
    const [, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    const currentBlock = get(blockNumberAtom);
    const beraborrow = get(beraborrowAtom);
    if (beraborrow.vaults[_vaultAddr].apyType === "none") {
      return 0n;
    }
    try {
      const { data: vaultsApy } = await get(getVaultsDetailsAtom);
      const vaultApy = vaultsApy.find((item) => item.vaultAddress === _vaultAddr)?.apy;

      if (vaultApy) {
        return vaultApy;
      }
    } catch (error) {
      console.error(error);
    }

    try {
      const [apyResp, aprResp] = await Promise.allSettled([
        beraborrow.vaults[_vaultAddr].getSlotAPY(undefined, _vaultAddr === "0xdc8408870f77B0B99d70779f68Fb560b6FE39259" ? 2 : 1),
        axiosAPI.get<{ apr: string }>(`/infrared/vault/apr/${BERABORROW_ADDRESSES.vaults[_vaultAddr].collateral}`),
      ]);
      let apy = apyResp.status === "fulfilled" ? apyResp?.value : undefined;
      const apr = (aprResp.status === "fulfilled" && BigInt(aprResp?.value.data.apr)) || 0n;
      if (apy === undefined) {
        apy = await beraborrow.vaults[_vaultAddr].getContractAPY(currentBlock || undefined, 14400n, false);
      }
      const apyValue = apy < apr ? apr : apy;
      return apyValue > SCALING_FACTOR * 1000n ? BigInt(apyValue.toString().slice(0, 18 + 3)) : apyValue;
    } catch (error) {
      console.error(error);
    }
    const [apyResp, aprResp] = await Promise.allSettled([
      beraborrow.vaults[_vaultAddr].getContractAPY(currentBlock || undefined, 14400n, false),
      axiosAPI.get<{ apr: string }>(`/infrared/vault/apr/${BERABORROW_ADDRESSES.vaults[_vaultAddr].collateral}`),
    ]);
    const apr = BigInt((aprResp.status === "fulfilled" && aprResp?.value.data.apr) || 0);
    const apy = (apyResp.status === "fulfilled" && apyResp?.value) || 0n;
    const apyValue = apy < apr ? apr : apy;
    return apyValue > SCALING_FACTOR * 1000n ? BigInt(apyValue.toString().slice(0, 18 + 3)) : apyValue;
  },
  throwOnError: true,
  refetchOnWindowFocus: false,
  staleTime: 1000 * 60, // 60 seconds
}));
//withdraw
export const vaultWithdrawAmountAtom = atom<bigint>((get) => {
  try {
    const vaultDetails = get(vaultDetailsAtom);
    return formatNoDecimal(get(formValuesVaultWithdrawAtom), vaultDetails.decimals); //TODO GET VAULT DECIMALS
  } catch (error) {
    return 0n;
  }
});
export const formValuesVaultWithdrawAtom = atom<string>("0");

export const formValidationsVaultWithdrawAtom = unwrap(
  atom<Promise<FormError | undefined>>(async (get) => {
    const value = get(vaultWithdrawAmountAtom);
    const vaultDetails = get(vaultDetailsAtom);

    const txState = get(transactionStateAtom);
    if (txState.type === "pending") {
      return {
        type: "transaction_in_pending",
        shortMessage: "Pending transaction",
        description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
      };
    }
    const walletConnected = get(accountAtom) !== zeroAddress;
    if (!walletConnected) {
      return {
        type: "wallet_not_connected",
      };
    }
    const { data: currentDeposit } = await get(getVaultDepositAtom);
    const maxWithdraw = currentDeposit.shares;

    if (value === 0n) {
      return {
        type: "zero_value",
      };
    } else if (value > maxWithdraw) {
      return {
        type: "exceed_max_withdraw",
        shortMessage: `Exceed max withdrawal amount`,
        description: `Exceed max withdraw amount ${formatToken(maxWithdraw, vaultDetails.decimals, 0)}`,
      };
    }

    return undefined;
  })
);
//deposits
export const vaultDepositAmountAtom = atom<bigint>((get) => {
  const vaultDetails = get(vaultDetailsAtom);

  try {
    return formatNoDecimal(get(formValuesVaultDepositAtom), vaultDetails.decimals); //TODO VAULT DECIMALS
  } catch (error) {
    return 0n;
  }
});

export const formValuesVaultDepositAtom = atom<string>("0");

export const formValidationsVaultDepositAtom = unwrap(
  atom<Promise<FormError | undefined>>(async (get) => {
    const value = get(vaultDepositAmountAtom);
    const txState = get(transactionStateAtom);
    const collateralDetails = get(getCollateralDetailsAtom);
    if (txState.type === "pending") {
      return {
        type: "transaction_in_pending",
        shortMessage: "Pending transaction",
        description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
      };
    }
    const [{ data: available }] = await Promise.all([get(getCollateralBalanceAtom)]);
    const walletConnected = get(accountAtom) !== zeroAddress;
    if (!walletConnected) {
      return {
        type: "wallet_not_connected",
      };
    }
    if (value === 0n) {
      return {
        type: "zero_value",
      };
    } else if (available < value) {
      return {
        type: "insufficient_balance",
        shortMessage: `Insufficient ${collateralDetails.ticker} balance`,
        description: `Insufficient ${collateralDetails.ticker} balance.`,
      };
    }

    return undefined;
  })
);

export const getUserVaultChangesAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(historyStartDateAtom), get(historyEndDateAtom), "getUserVaultChangesAtom"],
  queryFn: async ({ queryKey }) => {
    const [user, _start, _end] = queryKey as Readonly<[Hex, number, number]>;
    const beraborrow = get(beraborrowAtom);
    const vaultPositions = await beraborrow.subgraphClient.getUserVaultOperations(user, Math.floor(_start / 1000), Math.floor(_end / 1000));

    return vaultPositions;
  },
  throwOnError: true,
}));

export const getVaultDepositedAmountAtom = unwrap(
  atom<Promise<bigint>>(async (get) => {
    const { data: vaultPositions } = await get(getUserVaultChangesAtom);

    return vaultPositions.map((vaultPosition: VaultOperationsDto) => {
      const totalDeposited = BigInt(vaultPosition.deposited);

      const totalWithdrawn = vaultPosition.withdrawn
        .filter((w: Holding) => w.asset.id === vaultPosition.vault.asset.id)
        .reduce((sum: bigint, w: Holding) => sum + BigInt(w.balance), BigInt(0));

      // Compute net current deposited
      return totalDeposited - totalWithdrawn;
    })[0];
  })
);
