import { atomWithSuspenseQuery, atomWithQuery } from "jotai-tanstack-query";
import { accountAtom, beraborrowAtom, historyEndDateAtom, historyStartDateAtom } from "./Account";
import { DEFAULT_SLIPPAGE_TOLERANCE, SCALING_FACTOR, SCALING_FACTOR_BP, StabilityDeposit, TokenBalance } from "@Beraborrowofficial/sdk";
import { atom } from "jotai";
import { formatNoDecimal, formatToken } from "../utils/helpers";
import { BERABORROW_ADDRESSES } from "../utils/constants";
import { getDebtBalanceAtom } from "./Tokens";
import { unwrap } from "jotai/utils";
import { Hex, zeroAddress } from "viem";
import { FormError } from "../@type";
import { transactionStateAtom } from "./Transaction";
import { blockNumberAtom } from "./System";
import atomWithDebounce from "./AtomWithDebounce";

export const getLspTvlAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, BERABORROW_ADDRESSES.stabilityPool, "getLspTvlAtom"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    return beraborrow.stabilityPool.getTvl();
  },
  throwOnError: true,
}));

export const getStabilityDepositAtom = atomWithSuspenseQuery<StabilityDeposit>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.stabilityPool, "getStabilityDeposit"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const stabilityDeposit = await beraborrow.stabilityPool.getStabilityDeposit();
    return stabilityDeposit;
  },
  throwOnError: true,
}));

export const getMaxWithdrawAmountAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.stabilityPool, "getMaxWithdrawAmountAtom"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);

    return beraborrow.stabilityPool.getMaxWithdraw(account);
  },
  throwOnError: true,
}));
export const convertSharesToAssetsAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.stabilityPool, "convertSharesToAssets"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);

    return beraborrow.stabilityPool.convertSharesToAssets(SCALING_FACTOR);
  },
  throwOnError: true,
}));
export const previewDepositAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.stabilityPool, "previewDeposit"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    return beraborrow.stabilityPool.previewDeposit(SCALING_FACTOR);
  },
  throwOnError: true,
}));

export const getPoolAPYAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, BERABORROW_ADDRESSES.stabilityPool, "getPoolAPY"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const currentBlock = get(blockNumberAtom);
    const now = Math.floor(Date.now() / 1000);
    const now30DaysAgo = now - 86400 * 30;
    try {
      const apy = await beraborrow.stabilityPool.getApy(now30DaysAgo, now);
      return apy < 0n ? 0n : apy;
    } catch (error) {
      const apy = await beraborrow.stabilityPool.getContractAPY(currentBlock || undefined);
      return apy < 0n ? 0n : apy;
    }
  },
  throwOnError: true,
}));
export const getLspAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.stabilityPool, "getLspAllowanceAtom"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const res = await beraborrow.stabilityPool.allowance(account, beraborrow.lspRouter.contractAddress);
    return res;
  },
  throwOnError: true,
}));

export const getPendingGainTimestampAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.stabilityPool, "getPendingGainTimestamp"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    return beraborrow.stabilityPool.getPendingGainTimestamp();
  },
  throwOnError: true,
}));
export const getUserLSPRewards = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.stabilityPool, "getUserLSPRewards"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    return beraborrow.subgraphClient.getUserLSPRewards(account);
  },
}));

export const getUserLspShareChangesAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(historyStartDateAtom), get(historyEndDateAtom), BERABORROW_ADDRESSES.stabilityPool, "getUserShareChangesAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [user, _start, _end] = queryKey as Readonly<[Hex, number, number]>;
    return beraborrow.subgraphClient.getUserLSPShareChanges(user, Math.floor(_start / 1000), Math.floor(_end / 1000));
  },
}));

export const stabilityPoolWithdrawAmountAtom = atom<bigint>(0n);
//withdrawals
export const { debouncedValueAtom: debouncedStabilityPoolWithdrawAmountAtom } = atomWithDebounce(0n);
export const formValuesStabilityPoolWithdrawAtom = atom<string, [string], void>("0", (_get, set, update) => {
  set(formValuesStabilityPoolWithdrawAtom, update);
  try {
    const value = formatNoDecimal(update, BERABORROW_ADDRESSES.debtToken.decimals);
    set(stabilityPoolWithdrawAmountAtom, value);
    set(debouncedStabilityPoolWithdrawAmountAtom, value);
  } catch (error) {
    set(stabilityPoolWithdrawAmountAtom, 0n);
    set(debouncedStabilityPoolWithdrawAmountAtom, 0n);
  }
});

export const poolAssetSelectedAtom = atom<Hex | undefined>(undefined);
export const enableZapAtom = atom<boolean>(false); //disabled

const _preferredUnderlyingTokensAtom = atom(async (get) => {
  const address = get(poolAssetSelectedAtom);
  const { data: currentDeposit } = await get(getStabilityDepositAtom);
  if (address === undefined) {
    return [];
  } else if (address === BERABORROW_ADDRESSES.debtToken.contractAddress) {
    return [...currentDeposit.assets.slice(1), currentDeposit.assets[0]];
  } else {
    //find all the selected Tokens that are wrapped and have positive balance
    const wrappedTokens = currentDeposit.assets
      .filter(
        (item) =>
          (item.contractAddress === address && !!item.balance) || (!!item.balance && item.underlyingAssets?.some((item) => item.contractAddress === address && !!item.balance))
      )
      .sort((a, b) => {
        const balanceA = a.contractAddress === address ? a.balance : (a?.underlyingAssets?.find((item) => item.contractAddress === address)?.balance ?? 0n);
        const balanceB = b.contractAddress === address ? b.balance : (b?.underlyingAssets?.find((item) => item.contractAddress === address)?.balance ?? 0n);
        if (balanceA > balanceB) return -1;
        if (balanceA < balanceB) return 1;
        return a.contractAddress.localeCompare(b.contractAddress);
      });
    const wrappedTokenAddresses = new Set(wrappedTokens.map((token) => token.contractAddress));
    const remainingItems = currentDeposit.assets.filter((item) => !wrappedTokenAddresses.has(item.contractAddress));
    return [...wrappedTokens, ...remainingItems.slice(1), remainingItems[0]];
  }
});
export const preferredUnderlyingTokensAtom = unwrap(_preferredUnderlyingTokensAtom);

export const getRedeemPreferredUnderlyingToAnyAtom = atomWithQuery<(Omit<TokenBalance, "underlyingAssets"> & { userBalance?: bigint })[]>((get) => ({
  queryKey: [
    get(accountAtom),
    get(poolAssetSelectedAtom) ?? "all",
    get(enableZapAtom).toString(),
    get(debouncedStabilityPoolWithdrawAmountAtom).toString(),
    BERABORROW_ADDRESSES.stabilityPool,
    "getRedeemPreferredUnderlyingToAny",
  ],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const preferredUnderlyingTokens = await get(_preferredUnderlyingTokensAtom);
    const [, targetToken, _zap] = queryKey as Readonly<[Hex, Hex | "all", string]>;
    const { data: currentDeposit } = await get(getStabilityDepositAtom);
    const withdrawAmount = get(debouncedStabilityPoolWithdrawAmountAtom);
    const { data: shareToAssetRatio } = await get(convertSharesToAssetsAtom);
    const myAssets = currentDeposit.convertSharesToAssets(withdrawAmount, shareToAssetRatio);
    const zap = _zap === "true";
    const debtPrice = currentDeposit.assets.find((item) => item.contractAddress === BERABORROW_ADDRESSES.debtToken.contractAddress)?.price;
    if (debtPrice === undefined) throw Error("debt Price not found");
    const withdrawalDebtValue = (myAssets * SCALING_FACTOR) / debtPrice;
    if (targetToken === "all" || withdrawAmount === 0n || (!zap && targetToken === BERABORROW_ADDRESSES.debtToken.contractAddress)) {
      return currentDeposit
        .getUnderlyingAssets()
        .map((item) => ({ ...item, userBalance: currentDeposit.getMyShare(item.balance, withdrawAmount - (withdrawAmount * currentDeposit.exitFee) / SCALING_FACTOR_BP) }));
    }
    const expectedTokens = await beraborrow.lspRouter.previewRedeemPreferredUnderlyingToAny(
      targetToken,
      withdrawAmount,
      withdrawalDebtValue,
      preferredUnderlyingTokens,
      zap,
      currentDeposit.getUnderlyingAssets(preferredUnderlyingTokens).length,
      DEFAULT_SLIPPAGE_TOLERANCE * 2n
    );

    return currentDeposit.getUnderlyingAssets().map((item) => {
      const userBalance = expectedTokens.find((expectedToken) => expectedToken.contractAddress === item.contractAddress)?.userBalance ?? 0n;
      return { ...item, userBalance: userBalance };
    });
  },
}));

export const formValidationsStabilityPoolWithdrawAtom = unwrap(
  atom<Promise<FormError | undefined>>(async (get) => {
    const value = get(stabilityPoolWithdrawAmountAtom);
    const walletConnected = get(accountAtom) !== zeroAddress;
    if (!walletConnected) {
      return {
        type: "wallet_not_connected",
      };
    }
    const [{ data: deposit }] = await Promise.all([get(getStabilityDepositAtom)]);
    const maxWithdraw = deposit.shares;

    if (value === 0n) {
      return {
        type: "zero_value",
      };
    } else if (value > maxWithdraw) {
      return {
        type: "exceed_max_withdraw",
        shortMessage: `Exceed max withdrawal amount`,
        description: `Exceed max withdraw amount ${formatToken(maxWithdraw, BERABORROW_ADDRESSES.debtToken.decimals, 0)}`,
      };
    }

    return undefined;
  })
);
//deposits
export const stabilityPoolDepositAmountAtom = atom<bigint>((get) => {
  try {
    return formatNoDecimal(get(formValuesStabilityPoolDepositAtom), BERABORROW_ADDRESSES.debtToken.decimals);
  } catch (error) {
    return 0n;
  }
});

export const formValuesStabilityPoolDepositAtom = atom<string>("0");

export const formValidationsStabilityPoolDepositAtom = unwrap(
  atom<Promise<FormError | undefined>>(async (get) => {
    const value = get(stabilityPoolDepositAmountAtom);
    const txState = get(transactionStateAtom);
    if (txState.type === "pending") {
      return {
        type: "transaction_in_pending",
        shortMessage: "Pending transaction",
        description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
      };
    }
    const [{ data: available }] = await Promise.all([get(getDebtBalanceAtom)]);
    const walletConnected = get(accountAtom) !== zeroAddress;
    if (!walletConnected) {
      return {
        type: "wallet_not_connected",
      };
    }
    if (value === 0n) {
      return {
        type: "zero_value",
      };
    } else if (available < value) {
      return {
        type: "insufficient_balance",
        shortMessage: `Insufficient ${BERABORROW_ADDRESSES.debtToken.ticker} balance`,
        description: `Insufficient ${BERABORROW_ADDRESSES.debtToken.ticker} balance. You can mint more or buy on Ooga Booga `,
        link: "https://app.oogabooga.io/?fromToken=HONEY&toToken=NECT",
      };
    }

    return undefined;
  })
);
