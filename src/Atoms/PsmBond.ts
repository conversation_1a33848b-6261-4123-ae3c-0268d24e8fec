import { atom } from "jotai";
import { atomWithSuspenseQuery } from "jotai-tanstack-query";
import { Hex, zeroAddress } from "viem";
import { accountAtom, beraborrowAtom } from "./Account";
import { BERABORROW_ADDRESSES } from "../utils/constants";
import { SCALING_FACTOR, SCALING_FACTOR_BP } from "@Beraborrowofficial/sdk";
import { denRedeemAmountsAtom } from "./Redemptions";
import { transactionStateAtom } from "./Transaction";
import { getCollateralDetailsAtom, getDebtBalanceAtom } from "./Tokens";
import { formatToken } from "../utils/helpers";
import { FormError } from "../@type";
import { unwrap } from "jotai/utils";

export const psmBondAtom = atom<Hex | undefined>(undefined);
export const psmBondCollateralsAtom = atom(BERABORROW_ADDRESSES.psmBondCollaterals);
export const getMaxPsmRedeemAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(psmBondAtom) ?? "undefined", "getMaxPsmRedeemAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, psmBondAddr] = queryKey as Readonly<[Hex, Hex | "undefined"]>;
    if (psmBondAddr == "undefined") {
      return 0n;
    }
    return beraborrow.psmBond.maxRedeem(psmBondAddr);
  },
  throwOnError: true,
}));

export const isPausedPsmAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(psmBondAtom) ?? "undefined", "getMaxPsmRedeemAtom"],
  queryFn: async ({ queryKey }) => {
    const [, psmBondAddr] = queryKey as Readonly<[Hex, Hex | "undefined"]>;

    if (psmBondAddr == "undefined") {
      return true;
    }
    const beraborrow = get(beraborrowAtom);
    return beraborrow.psmBond.isPaused();
  },
  throwOnError: true,
}));
export const getFeePsmAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, , "getMaxPsmRedeemAtom"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const fee = await beraborrow.psmBond.getFee();
    return (BigInt(fee) * SCALING_FACTOR) / SCALING_FACTOR_BP;
  },
  throwOnError: true,
}));
export const formValidationsPsmRedeemAtom = unwrap(
  atom<Promise<FormError | undefined>>(async (get) => {
    const value = get(denRedeemAmountsAtom);
    const txState = get(transactionStateAtom);
    if (txState.type === "pending") {
      return {
        type: "transaction_in_pending",
        shortMessage: "Pending transaction",
        description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
      };
    }
    if (!value.debt || !value.collateral) {
      return {
        type: "zero_value",
      };
    }
    const collateralDetails = get(getCollateralDetailsAtom);

    const [{ data: nectBalance }, { data: psmTotal }] = await Promise.all([get(getDebtBalanceAtom), get(getMaxPsmRedeemAtom)]);
    const walletConnected = get(accountAtom) !== zeroAddress;
    if (!walletConnected) {
      return {
        type: "wallet_not_connected",
      };
    }
    if (value.collateral > psmTotal) {
      return {
        type: "insufficient_collateral_balance",
        shortMessage: `Insufficient ${collateralDetails.ticker} to Redeem`,
        description: `there is not enough ${collateralDetails.ticker} in the respective den manager, only ${formatToken(psmTotal, collateralDetails.decimals, 0)} is available to be redeemed.`,
      };
    } else if (value.debt > nectBalance) {
      return {
        type: "insufficient_nect_balance",
        shortMessage: `Insufficient ${BERABORROW_ADDRESSES.debtToken.ticker} balance`,
        description: `Insufficient ${BERABORROW_ADDRESSES.debtToken.ticker} balance. You can mint more or buy on Ooga Booga `,
        link: "https://app.oogabooga.io/?fromToken=HONEY&toToken=NECT",
      };
    }

    return undefined;
  })
);
