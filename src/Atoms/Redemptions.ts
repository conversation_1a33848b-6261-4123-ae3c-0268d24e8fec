import { atomWithSuspenseQuery } from "jotai-tanstack-query";
import { accountAtom, beraborrowAtom, historyEndDateAtom, historyStartDateAtom } from "./Account";
import { unwrap } from "jotai/utils";
import { Hex, zeroAddress } from "viem";
import { formatNoDecimal, formatToken } from "../utils/helpers";
import { atom } from "jotai";
import { getCollateralDetailsAtom, getDebtBalanceAtom } from "./Tokens";
import { FormError } from "../@type";
import { BERABORROW_ADDRESSES } from "../utils/constants";
import { Den } from "@Beraborrowofficial/sdk";
import { transactionStateAtom } from "./Transaction";
import { getDensTotalsAtom, _denManagerAddrAtom } from "./Den";

export const getRedemptionsByRedeemerAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(historyStartDateAtom), get(historyEndDateAtom), "getRedemptionsByRedeemerAtom"],
  queryFn: async ({ queryKey }) => {
    const [user, _start, _end] = queryKey as Readonly<[Hex, number, number]>;

    const beraborrow = get(beraborrowAtom);
    const liquidations = await beraborrow.subgraphClient.getRedemptionsByRedeemer(user, Math.floor(_start / 1000), Math.floor(_end / 1000));
    return liquidations;
  },
  throwOnError: true,
}));
export const getRedeemFeeRateAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(_denManagerAddrAtom), "getRedeemFeeRateAtom"],
  queryFn: async ({ queryKey }) => {
    const [, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const redemptionFeeRate = await beraborrow.denManagers[_denManagerAddr].denManager.getRedemptionFeeRate();
    return redemptionFeeRate;
  },
  throwOnError: true,
}));

export const getRedeemFeeRateWithDecayAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(_denManagerAddrAtom), "getRedeemFeeRateWithDecayAtom"],
  queryFn: async ({ queryKey }) => {
    const [_denManagerAddr] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    const redemptionFeeRate = await beraborrow.denManagers[_denManagerAddr].denManager.getRedemptionFeeRateWithDecay();
    return redemptionFeeRate;
  },
  throwOnError: true,
}));
export const formValuesDenRedeemAtom = atom<{ collateral: string; nect: string }>({ collateral: "", nect: "" });

export const denRedeemAmountsAtom = atom<Den>((get) => {
  const collateralDetails = get(getCollateralDetailsAtom);
  const update = get(formValuesDenRedeemAtom);
  let den = new Den(0n, 0n);
  try {
    den = new Den(formatNoDecimal(update.collateral, collateralDetails.vaultDecimals), formatNoDecimal(update.nect, BERABORROW_ADDRESSES.debtToken.decimals));
  } catch (error) {
    console.error(error);
  }
  return den;
});

export const formValidationsDenRedeemAtom = unwrap(
  atom<Promise<FormError | undefined>>(async (get) => {
    const value = get(denRedeemAmountsAtom);
    const txState = get(transactionStateAtom);
    if (txState.type === "pending") {
      return {
        type: "transaction_in_pending",
        shortMessage: "Pending transaction",
        description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
      };
    }
    if (!value.debt || !value.collateral) {
      return {
        type: "zero_value",
      };
    }
    const collateralDetails = get(getCollateralDetailsAtom);

    const [{ data: nectBalance }, { data: densTotal }] = await Promise.all([get(getDebtBalanceAtom), get(getDensTotalsAtom)]);
    const walletConnected = get(accountAtom) !== zeroAddress;
    if (!walletConnected) {
      return {
        type: "wallet_not_connected",
      };
    }
    if (value.collateral > densTotal.collateral) {
      return {
        type: "insufficient_collateral_balance",
        shortMessage: `Insufficient ${collateralDetails.ticker} to Redeem`,
        description: `there is not enough ${collateralDetails.ticker} in the respective den manager, only ${formatToken(densTotal.collateral, collateralDetails.vaultDecimals, 0)} is available to be redeemed.`,
      };
    } else if (value.debt > nectBalance) {
      return {
        type: "insufficient_nect_balance",
        shortMessage: `Insufficient ${BERABORROW_ADDRESSES.debtToken.ticker} balance`,
        description: `Insufficient ${BERABORROW_ADDRESSES.debtToken.ticker} balance. You can mint more or buy on Ooga Booga `,
        link: "https://app.oogabooga.io/?fromToken=HONEY&toToken=NECT",
      };
    }

    return undefined;
  })
);
