import { atomWithSuspenseQuery, atomWithQuery } from "jotai-tanstack-query";
import { accountAtom, beraborrow<PERSON>tom, historyEndDateAtom, historyStartDateAtom, beraborrowConstantsAtom } from "./Account";
import { atom } from "jotai";
import { formatBigIntPercentage, formatNoDecimal, formatToken } from "../utils/helpers";
import { atomWithStorage, unwrap } from "jotai/utils";
import { getBeraBalanceAtom, getCollateralBalanceAtom, getCollateralDetailsAtom, getDebtPriceAtom } from "./Tokens";
import { DEFAULT_SLIPPAGE_TOLERANCE, Den, MIN_GAS_FEE, SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { Hex, zeroAddress } from "viem";
import { BERABORROW_ADDRESSES } from "../utils/constants";
import { transactionStateAtom } from "./Transaction";
import { FormError } from "../@type";
import { isSafeConnectorAtom } from "./System";
import { DenManagerDetails, DenMangersFullDetails } from "../@type/Dens";
import { previewVaultedSharesAtom } from "./Vault";

export const denPageSwitchTypeAtom = atomWithStorage<"grid" | "list">("denPageSwitchTypeAtom", "list", undefined, { getOnInit: true });
export const denPageFilterAtom = atomWithStorage<"all" | "positions">("denPageFilterAtom", "all", undefined, { getOnInit: true });

export const denManagerAtom = atom<Hex>(
  Object.entries(BERABORROW_ADDRESSES?.denManagers).find((item) => BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].ticker.toLowerCase() === "wbera")?.[1]?.denManager ??
    zeroAddress
);
export const protocolAtom = atom<Hex>((get) => {
  const denManagerAddr = get(_denManagerAddrAtom);
  return BERABORROW_ADDRESSES.denManagers[denManagerAddr].protocol ?? BERABORROW_ADDRESSES.defaultProtocol;
});

export const _denManagerAddrAtom = atom<Hex>((get) => {
  let _denManagerAddr = get(denManagerAtom);
  const underlyingDenManager = BERABORROW_ADDRESSES.wrappedDenManagers[_denManagerAddr] ?? _denManagerAddr;
  return underlyingDenManager;
});
export const showCurrentBorrowAtom = atom(true);
export const leverageSlippageAtom = atom(DEFAULT_SLIPPAGE_TOLERANCE * 2n);

export const denManagersAtom = atom<DenManagerDetails[]>(
  Object.entries(BERABORROW_ADDRESSES.denManagers)
    .filter((item) => !item[1]?.permissioned)
    .map((item) => ({
      contractAddress: item[0] as Hex,
      collateralTicker: BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].ticker,
      collateralDecimals: BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].decimals,
      vaultDecimals: item[1]?.vault ? BERABORROW_ADDRESSES.vaults[item[1]?.vault]?.decimals : BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].decimals,
      collateralAddress: item[1].collateral,
      vaultAddress: item[1]?.vault,
      wrappedCollateral: item[1]?.wrappedCollateral,
      wrappedCollateralTicker: item[1].wrappedCollateral ? BERABORROW_ADDRESSES.collateralTokens[item[1].wrappedCollateral]?.ticker : undefined,
      wrappedCollateralDecimals: item[1].wrappedCollateral ? BERABORROW_ADDRESSES.collateralTokens[item[1].wrappedCollateral]?.decimals : undefined,
    }))
    .sort((a, b) => {
      // Compare wrappedCollateralTicker or fallback to collateralTicker
      const tickerA = a.wrappedCollateralTicker || a.collateralTicker;
      const tickerB = b.wrappedCollateralTicker || b.collateralTicker;
      if (tickerA !== tickerB) {
        return tickerA.toLocaleLowerCase().localeCompare(tickerB.toLocaleLowerCase());
      } else if (a.wrappedCollateral && !b.wrappedCollateral) {
        return -1;
      } else if (!a.wrappedCollateral && b.wrappedCollateral) {
        return 1;
      } else if (!a.vaultAddress) {
        return 1;
      } else if (!b.vaultAddress) {
        return 1;
      } else {
        return a.vaultAddress.localeCompare(b.vaultAddress);
      }
    })
);

export const getDensDetailsAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), "getDensDetailsAtom"],
  queryFn: async ({ queryKey }) => {
    const [address] = queryKey as Readonly<[Hex]>;
    return Promise.all(
      get(denManagersAtom).map(async ({ contractAddress }) => ({
        ...(await get(beraborrowAtom).denManagers[contractAddress].denManager.getDenDetails(address)),
        contractAddress,
      }))
    );
  },
}));
export const getDensDetailsNoWaitAtom = atomWithQuery((get) => ({
  queryKey: [get(accountAtom), "getDensDetailsAtom"],
  queryFn: async ({ queryKey }) => {
    const [address] = queryKey as Readonly<[Hex]>;
    return Promise.all(
      get(denManagersAtom).map(async ({ contractAddress }) => ({
        ...(await get(beraborrowAtom).denManagers[contractAddress].denManager.getDenDetails(address)),
        contractAddress,
      }))
    );
  },
  retry: false,
}));

export const getDenManagersDetailsAtom = atomWithQuery<DenMangersFullDetails[]>((get) => {
  const account = get(accountAtom);
  const densDetails = get(getDensDetailsNoWaitAtom);
  return {
    queryKey: [account, densDetails.data?.length ?? 0, "getDenManagersDetailsAtom"],
    queryFn: async () => {
      const denManagers = get(denManagersAtom);
      const { data: densDetails } = await get(getDensDetailsNoWaitAtom);

      if (densDetails === undefined) return denManagers;

      const densDetailsMap = new Map(densDetails.map((v) => [v.contractAddress, v]));
      return denManagers
        .map((denManager) => {
          const apiDetails = densDetailsMap.get(denManager.contractAddress);
          return { ...denManager, ...(apiDetails ?? {}) };
        })
        .sort((a, b) => Number((b?.densTotal?.collateral ?? 0n) * (b?.price ?? 0n) - (a?.densTotal?.collateral ?? 0n) * (a?.price ?? 0n)));
    },
    throwOnError: true,
  };
});

export const denBorrowTypeAtom = atom<"open" | "deposit" | "withdraw">("open");

export const getDenFullDetailsAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(_denManagerAddrAtom), "getDenFullDetailsAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [address, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    const { data: denManagers } = await get(getDensDetailsAtom);
    const denManager = denManagers.find((item) => item.contractAddress === _denManagerAddr);
    if (denManager) {
      return denManager;
    }
    return beraborrow.denManagers[_denManagerAddr].denManager.getDenDetails(address);
  },
  throwOnError: true,
}));
export const getDensTotalsAtom = atomWithSuspenseQuery<Den>((get) => ({
  queryKey: [zeroAddress, get(_denManagerAddrAtom), "getDensTotalsAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    return beraborrow.denManagers[_denManagerAddr].denManager.getTotal();
  },
  throwOnError: true,
}));

export const getDensDefaultedDebtAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, get(_denManagerAddrAtom), "getDensDefaultedDebt"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    return beraborrow.denManagers[_denManagerAddr].denManager.getDefaultedDebt();
  },
  throwOnError: true,
}));

export const getDensMaxDebtAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, get(_denManagerAddrAtom), "getDensMaxDebt"],
  queryFn: async ({ queryKey }) => {
    const [, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;

    const beraborrow = get(beraborrowAtom);
    return beraborrow.denManagers[_denManagerAddr].denManager.getMaxDebt();
  },
  throwOnError: true,
}));
export const getDenDetailsAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(_denManagerAddrAtom), "getDenDetailsAtom"],
  queryFn: async ({ queryKey }) => {
    const [account, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;

    const beraborrow = get(beraborrowAtom);

    return beraborrow.subgraphClient.getDen(`Den/${_denManagerAddr.toLowerCase()}/${account.toLowerCase()}`);
  },
}));
export const getCollateralSurplusBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), get(_denManagerAddrAtom), "getSurplusCollateralAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    return beraborrow.denManagers[_denManagerAddr].denManager.getCollateralSurplusBalances();
  },
  throwOnError: true,
}));
export const getDenInterestRatesAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, "getDenInterestRatesAtom"],
  queryFn: async ({ queryKey }) => {
    const [, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);

    return beraborrow.subgraphClient.getInterestRates();
  },
}));

export const getDenInterestRateAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(_denManagerAddrAtom), "getDenInterestRateAtom"],
  queryFn: async ({ queryKey }) => {
    const [, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);

    const { data: interestRates } = await get(getDenInterestRatesAtom);
    const denManagerAddr = _denManagerAddr.toLocaleLowerCase();
    const interestRate = interestRates?.find((item) => item.id === denManagerAddr);
    if (interestRate) return Number(interestRate.interestRate);
    return beraborrow.subgraphClient.getInterestRate(_denManagerAddr);
  },
}));
export const getDenManagerCollateralPrice = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(_denManagerAddrAtom), "getDenManagerCollateralPrice"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    const price = await beraborrow.denManagers[_denManagerAddr].denManager.fetchPrice();
    if (price === 0n) {
      const collPrice = await beraborrow.priceFeed.fetchPrice(BERABORROW_ADDRESSES?.denManagers[_denManagerAddr].collateral);
      return collPrice;
    }
    return price;
  },
  throwOnError: true,
}));
export const getIsApprovedDelegateCollVaultRouterAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), "getIsApprovedDelegateCollVaultRouterAtom"],
  queryFn: async () => {
    return true;
  },
  throwOnError: true,
}));
export const getIsApprovedDelegateLeverageRouterAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), "getIsApprovedDelegateLeverageRouterAtom"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const protocol = get(protocolAtom);
    const denManagerAddr = get(denManagerAtom);
    return beraborrow.protocols[protocol].borrowerOperationsHandler.isApprovedDelegate(denManagerAddr, BERABORROW_ADDRESSES.leverageRouter, account);
  },
  throwOnError: true,
}));
export const getMCRAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(_denManagerAddrAtom), "getMCRAtom"],
  queryFn: async ({ queryKey }) => {
    const [, denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const mcr = await beraborrow.denManagers[denManagerAddr].denManager.getMCR();

    return mcr;
  },
  throwOnError: true,
}));

export const formValuesDenBorrowAtom = atom<{ collateral: string; nect: string; ratio: string; leverage?: number }>({ collateral: "", nect: "", ratio: "" });

export const denBorrowAmountsAtom = atom<Den>((get) => {
  const value = get(formValuesDenBorrowAtom);
  const collateralDetails = get(getCollateralDetailsAtom);
  const borrowType = get(denBorrowTypeAtom);
  try {
    const leverage = value.leverage === undefined || value.leverage === 1 ? undefined : BigInt(Math.round(value.leverage)) * SCALING_FACTOR;
    return new Den(
      formatNoDecimal(value.collateral, borrowType === "withdraw" ? collateralDetails.vaultDecimals : collateralDetails.decimals),
      formatNoDecimal(value.nect, BERABORROW_ADDRESSES.debtToken.decimals),
      leverage
    );
  } catch (error) {
    return new Den(0n, 0n);
  }
});

export const getBorrowingMintFeeRateAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(_denManagerAddrAtom), "getBorrowingMintFeeRateAtom"],
  queryFn: async ({ queryKey }) => {
    const [_denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);

    const borrowingFee = await beraborrow.denManagers[_denManagerAddr].denManager.getBorrowingMintFeeRate();
    return borrowingFee;
  },
  throwOnError: true,
}));
/**
 * @notice Atom to get sunsetting
 */
export const isDenManagerSunsettingAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(_denManagerAddrAtom), "isDenManagerSunsettingAtom"],
  queryFn: async ({ queryKey }) => {
    const [_denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);

    const is_sunsetting = await beraborrow.denManagers[_denManagerAddr].denManager.isSunsetting();

    return is_sunsetting;
  },
  throwOnError: true,
}));

/**
 * @notice Atom to get Den Status
 */
export const getDenStatusAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(_denManagerAddrAtom), get(accountAtom), "getDenStatusAtom"],
  queryFn: async ({ queryKey }) => {
    const [_denManagerAddr, user] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);

    const denStatus = await beraborrow.denManagers[_denManagerAddr].denManager.getDenStatus(user);
    return denStatus;
  },
  throwOnError: true,
}));

/**
 * @notice Atom to get if denManager is paused or not
 */
export const isDenManagerPausedAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(_denManagerAddrAtom), "isDenManagerPausedAtom"],
  queryFn: async ({ queryKey }) => {
    const [_denManagerAddr] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);

    const isPaused = await beraborrow.denManagers[_denManagerAddr].denManager.isPaused();
    return isPaused;
  },
  throwOnError: true,
}));

/**
 * Get GTCR
 */
export const getGTCRAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(protocolAtom), "getGTCR"],
  queryFn: async ({ queryKey }) => {
    const [, protocol] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const tcr = beraborrow.protocols[protocol].borrowerOperationsHandler.getTCR();
    return tcr;
  },
  throwOnError: true,
}));

export const isRecoveryModeAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, "isRecoveryMode"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const recoveryModes = await Promise.all(
      Object.entries(beraborrow.protocols).map(async (item) => {
        const [tcr, ccr] = await Promise.all([item[1].borrowerOperationsHandler.getTCR(), item[1].borrowerOperationsHandler.getCCR()]);
        return tcr < ccr;
      })
    );
    return !!recoveryModes.find((item) => item);
  },
  throwOnError: true,
}));

export const getGlobalSystemBalancesAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, "getGlobalSystemBalances"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const protocol = get(protocolAtom);
    return beraborrow.protocols[protocol].borrowerOperationsHandler.getGlobalSystemBalances();
  },
  throwOnError: true,
}));
/**
 * Get tvl of user
 */
export const getUserDensTVLAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), "getUserTVL"],
  queryFn: async ({ queryKey }) => {
    const [user] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);

    const tvls = await Promise.all(Object.entries(beraborrow.protocols).map((item) => item[1].denManagerGetters.getTVLOfUserDens(user)));
    return tvls.reduce((prev, curr) => prev + curr, 0n);
  },
  throwOnError: true,
}));
export const getUserTotalDepositVolumeAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), "getUserTotalDepositVolumeAtom"],
  queryFn: async ({ queryKey }) => {
    const [user] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const volume = await beraborrow.subgraphClient.getUserDepositVolumeInDen(user || zeroAddress);
    return volume || 0n;
  },
}));
export const getDenChangesOfUserDens = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(historyStartDateAtom), get(historyEndDateAtom), "getDenChangesOfUserDens"],
  queryFn: async ({ queryKey }) => {
    const [user, _start, _end] = queryKey as Readonly<[Hex, number, number]>;
    const beraborrow = get(beraborrowAtom);
    const densWithChanges = await beraborrow.subgraphClient.getDenChangesOfUserDens(user, undefined, Math.floor(_start / 1000), Math.floor(_end / 1000));
    return densWithChanges;
  },
  throwOnError: true,
}));

export const getLiquidationsByLiquidator = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(historyStartDateAtom), get(historyEndDateAtom), , "getLiquidationsByLiquidator"],
  queryFn: async ({ queryKey }) => {
    const [user, _start, _end] = queryKey as Readonly<[Hex, number, number]>;
    const beraborrow = get(beraborrowAtom);
    const densWithChanges = await beraborrow.subgraphClient.getLiquidationsByLiquidator(user, Math.floor(_start / 1000), Math.floor(_end / 1000));
    return densWithChanges;
  },
}));
// export const getLeverageFlashLoanFeeAtom = atomWithSuspenseQuery((get) => ({
//   queryKey: [zeroAddress, "getLeverageFlashLoanFee"],
//   queryFn: async () => {
//     const beraborrow = get(beraborrowAtom);
//     const denManagers = beraborrow.leverageRouter.flashLoanFee();
//     return denManagers;
//   },
//   throwOnError: true,
// }));
export const formValidationsDenBorrowAtom = unwrap(
  atom<Promise<FormError | undefined>>(async (get) => {
    const borrowType = get(denBorrowTypeAtom);
    const txState = get(transactionStateAtom);
    const formAmounts = get(denBorrowAmountsAtom);
    const formValues = get(formValuesDenBorrowAtom);
    const walletConnected = get(accountAtom) !== zeroAddress;
    if (!walletConnected) {
      return {
        type: "wallet_not_connected",
      };
    }
    if (!!formAmounts.leverage && !formAmounts.collateral) {
      return {
        type: "zero_value",
      };
    } else if (borrowType == "open" && !formAmounts.leverage) {
      if (!formAmounts.debt || !formAmounts.collateral) {
        return {
          type: "zero_value",
        };
      }
    } else {
      if (!formAmounts.debt && !formAmounts.collateral && !formAmounts.leverage) {
        return {
          type: "zero_value",
        };
      }
    }
    if (txState.type === "pending") {
      return {
        type: "transaction_in_pending",
        shortMessage: "Pending transaction",
        description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
      };
    }

    const [
      { data: debtPrice },
      { data: previewVaultedShares },
      { data: denDetails },
      { data: collateralBalance },
      { data: densMaxDebt },
      { data: densDefaultedDebt },
      { data: nativeBalance },
      { data: isSunsetting },
      //   { data: denStatus },
      { data: isPaused },
      { data: mcr },
      { data: _currentBorrowingRate },
      { data: GTCR },
      {
        data: [totalPricedCollateral, totalDebt],
      },
      { data: collateralPrice },
    ] = await Promise.all([
      get(getDebtPriceAtom),
      get(previewVaultedSharesAtom),
      get(getDenFullDetailsAtom),
      get(getCollateralBalanceAtom),
      get(getDensMaxDebtAtom),
      get(getDensDefaultedDebtAtom),
      get(getBeraBalanceAtom),
      get(isDenManagerSunsettingAtom),
      //   get(getDenStatusAtom),
      get(isDenManagerPausedAtom),
      get(getMCRAtom),
      get(getBorrowingMintFeeRateAtom),
      get(getGTCRAtom),
      get(getGlobalSystemBalancesAtom),
      get(getDenManagerCollateralPrice),
    ]);
    const { den: userDen, densTotal, ccr } = denDetails; // price: collateralPrice
    const collateralDetails = get(getCollateralDetailsAtom);
    const beraborrowConstants = get(beraborrowConstantsAtom);
    const NECT_MINIMUM_NET_DEBT = beraborrowConstants.minimumDebt + beraborrowConstants.liquidationReserve;
    const isRecoveryMode = GTCR <= ccr;
    const currentBorrowingRate = isRecoveryMode ? 0n : _currentBorrowingRate;

    const newDen = formAmounts;
    const leveragedDen = formAmounts.applyLeverage(formAmounts.getCollateralShares(previewVaultedShares), currentBorrowingRate, collateralPrice, debtPrice);
    const updatedDen = !!newDen.leverage
      ? userDen
          .addCollateral(leveragedDen.getCollateralShares(previewVaultedShares))
          .addDebt(
            leveragedDen.debt +
              ((borrowType === "open" ? beraborrowConstants.liquidationReserve : 0n) +
                (borrowType !== "withdraw" ? (leveragedDen.debt * currentBorrowingRate) / SCALING_FACTOR : 0n))
          )
      : borrowType !== "withdraw"
        ? userDen
            .addCollateral(newDen.getCollateralShares(previewVaultedShares))
            .addDebt(newDen.debt)
            .addDebt((borrowType === "open" ? beraborrowConstants.liquidationReserve : 0n) + (formAmounts.debt * currentBorrowingRate) / SCALING_FACTOR) //fees
        : userDen.subtractCollateral(newDen.collateral).subtractDebt(newDen.debt);
    const newGTCR =
      (borrowType !== "withdraw"
        ? totalPricedCollateral + formAmounts.getCollateralShares(previewVaultedShares) * collateralPrice
        : totalPricedCollateral - formAmounts.collateral * collateralPrice) / (totalDebt + updatedDen.debt - userDen.debt === 0n ? 1n : totalDebt + updatedDen.debt - userDen.debt);
    const crBefore = userDen.collateralRatio(collateralPrice);
    const crAfter = updatedDen.collateralRatio(collateralPrice);
    const crIncreased = borrowType == "open" ? false : crAfter > crBefore;
    const remainingDebt = densMaxDebt - densTotal.debt - densDefaultedDebt - userDen.debt;
    const debtAmount = newDen.debt + (borrowType === "open" ? beraborrowConstants.liquidationReserve : 0n) + (newDen.debt * currentBorrowingRate) / SCALING_FACTOR;
    const collateralAmount = userDen.status === "open" ? (userDen.collateral < updatedDen.collateral ? newDen.collateral - userDen.collateral : 0n) : newDen.collateral;
    if (collateralAmount > collateralBalance) {
      return {
        type: "insufficient_collateral_balance",
        shortMessage: `Insufficient ${collateralDetails.ticker} Balance`,
        description: `Your available ${collateralDetails.ticker} balance is too low to complete this action.`,
      };
    } else if (updatedDen.debt <= NECT_MINIMUM_NET_DEBT) {
      return {
        type: "minimum_nect",
        shortMessage: `Minimum Debt of ${formatToken(borrowType === "open" ? beraborrowConstants.minimumDebt : NECT_MINIMUM_NET_DEBT, BERABORROW_ADDRESSES.debtToken.decimals)} required`,
        description: `A minimum debt of ${formatToken(borrowType === "open" ? beraborrowConstants.minimumDebt : NECT_MINIMUM_NET_DEBT, BERABORROW_ADDRESSES.debtToken.decimals)} is required to proceed with this action. Please increase the amount of ${formatToken(NECT_MINIMUM_NET_DEBT, BERABORROW_ADDRESSES.debtToken.decimals)} you are minting or Close your Position`,
      };
    } else if (updatedDen.collateralRatioIsBelowMinimum(collateralPrice, mcr)) {
      return {
        type: "below_MCR",
        shortMessage: `Ratio must be at least ${formatBigIntPercentage(mcr)}%`,
        description: `A Minimum Collateral Ratio of ${formatBigIntPercentage(mcr)}% is required to proceed with this action. Please increase the amount of collateral or decrease the amount of NECT you are minting`,
      };
    } else if (isRecoveryMode && updatedDen.collateralRatioIsBelowCritical(collateralPrice, ccr) && formAmounts.debt > 0 && borrowType !== "withdraw") {
      return {
        type: "below_CCR",
        shortMessage: `Ratio must be at least ${formatBigIntPercentage(ccr)}%`,
        description: `Your current Total Collateral Ratio is below the required threshold of ${formatBigIntPercentage(ccr)}%. To proceed, you must increase your Total Collateral Ratio to exceed this critical percentage. Please add more collateral or adjust your holdings to meet this requirement.`,
      };
    } else if (isRecoveryMode && formAmounts.collateral > 0n && borrowType === "withdraw") {
      return {
        type: "recovery_mode_collateral_withdraw",
        shortMessage: `No Collateral withdrawal during Recovery Mode`,
        description: `Borrower transactions withdraw collateral  not permitted during Recovery Mode. Only transactions that improve your collateral ratio can be processed at this time.`,
      };
    } else if (isRecoveryMode && !crIncreased) {
      return {
        type: "recovery_mode_CR_decrease",
        shortMessage: `Ratio must increase during Recovery Mode`,
        description: `Borrower transactions that lower your collateral ratio are not permitted during Recovery Mode. Only transactions that improve your collateral ratio can be processed at this time.`,
      };
    } else if (
      !formAmounts.leverage &&
      borrowType !== "withdraw" &&
      (formValues.nect === "0" || formValues.nect === "") &&
      Number(formValues.ratio) > Number(formatBigIntPercentage(crAfter, 2, true))
    ) {
      return {
        type: "cr_above_nect_deposit",
        shortMessage: ` Ratio too High`,
        description: `Deposit more Collateral to achieve this Collateral Ratio`,
      };
    } else if (newGTCR <= ccr && GTCR > ccr) {
      return {
        type: "cr_bring_TCR_below_CCR",
        shortMessage: `Collateral Ratio too low`,
        description: `Your current Ratio will cause the system to go into Recovery Mode, please increase your Ratio`,
      };
    } else if (debtAmount > remainingDebt) {
      return {
        type: "max_nect_exceeded",
        shortMessage: "Debt above maximum minted Debt",
        description:
          "The amount of NECT you are trying to mint exceeds the maximum debt for this collateral asset. Please reduce the amount of debt to" +
          formatToken(remainingDebt, BERABORROW_ADDRESSES.debtToken.decimals),
      };
    } else if (!get(isSafeConnectorAtom) && nativeBalance < MIN_GAS_FEE) {
      return {
        type: "insufficient_gas",
        shortMessage: "Insufficient gas fee",
        description: "You don't have sufficient Bera in your wallet to cover gas costs, Please top up your wallet and try again.",
      };
    } else if (isSunsetting) {
      return {
        type: "den_is_sunsetting",
        shortMessage: "Den is in sunsetting",
        description: "The Den is currently in the process of sunsetting, which means it is being phased",
        link: "https://beraborrow.gitbook.io/docs/borrowing/sunsetting-dens",
      };
      // } else if (denStatus == BackendDenStatus.active) {
      //   return "Den is active"
    } else if (isPaused) {
      return {
        type: "den_paused",
        shortMessage: "Den is paused",
        description: "The Den is currently paused and is not accepting new transactions or actions.",
      };
    }
    return undefined;
  })
);
