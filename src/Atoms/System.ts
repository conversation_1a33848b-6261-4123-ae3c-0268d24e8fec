import { atom } from "jotai";
import { atomEffect } from "jotai-effect";
import { atomWithStorage } from "jotai/utils";
import { getBeraBalanceAtom } from "./Tokens";
import { QUERY_CLIENT, WAGMI_ADAPTOR } from "../utils/constants";
import { accountAtom } from "./Account";
import { getBalance } from "@wagmi/core";
import { zeroAddress } from "viem";
import { atomWithSuspenseQuery } from "jotai-tanstack-query";

export const blockNumberAtom = atom<bigint>(0n);
export const lockedAtom = atomWithStorage<boolean>("lock", true, undefined, { getOnInit: true });
export const acceptedAtom = atomWithStorage<boolean>("t&c", false, undefined, { getOnInit: true });
export const consentAtom = atomWithStorage<boolean>("consentMode", false, undefined, { getOnInit: true });
export const isSafeConnectorAtom = atom(false);
export const updateAppTimestampAtom = atom<number | undefined>(undefined);
export const updateAppFnAtom = atom<((reloadPage?: boolean) => Promise<void>) | undefined>(undefined);
const _lastInvalidationTimeAtom = atom(0);
const DEBOUNCE_INTERVAL = 5000; // 5 seconds
export const invalidatorAtom = atom<null, [type: "all" | "system" | "user"], void>(null, (get, set, type) => {
  const now = Date.now();
  // Check if the last invalidation happened within the debounce interval
  if (now - get(_lastInvalidationTimeAtom) > DEBOUNCE_INTERVAL) {
    console.log("clear cache");
    set(_lastInvalidationTimeAtom, now);
    switch (type) {
      case "all":
        QUERY_CLIENT.invalidateQueries();
        break;
      case "user":
        if (accountAtom) {
          QUERY_CLIENT.invalidateQueries({ queryKey: [get(accountAtom)] });
        } else {
          QUERY_CLIENT.invalidateQueries();
        }
        break;
      case "system":
        QUERY_CLIENT.invalidateQueries({ queryKey: [zeroAddress] });
        break;
      default:
        QUERY_CLIENT.invalidateQueries();
        break;
    }
  }
});
export const preloadCompAtom = atom(false);
export const preloadCompSetterAtom = atom<null, [delay: number], void>(null, (get, set, delay) => {
  const preload = get(preloadCompAtom);
  if (!preload) {
    setTimeout(() => {
      set(preloadCompAtom, true);
      setTimeout(() => {
        set(preloadCompAtom, false);
      }, 5000);
    }, delay);
  }
});

export const systemChangeAtom: ReturnType<typeof atomEffect> = atomEffect((get) => {
  const blockNumber = get(blockNumberAtom);
  //listen to SP change and listen to BorrowOps and finally listen to account balance change
  if (blockNumber % 3n === 0n) {
    get(checkerAtom);
  }
});
export const checkerAtom = atom(null, async (get, set) => {
  const account = get(accountAtom);
  const [{ data: beraBalance }, newBeraBalance] = await Promise.all([
    get(getBeraBalanceAtom),
    account
      ? getBalance(WAGMI_ADAPTOR.wagmiConfig, {
          address: account,
        })
      : undefined,
  ]);
  if (beraBalance && newBeraBalance && beraBalance !== newBeraBalance?.value) {
    set(invalidatorAtom, "user");
  }
});

export const defillamaTVLAtom = atomWithSuspenseQuery(() => ({
  queryKey: [zeroAddress, "oogaboogaHealthAtom"],
  queryFn: async () => {
    try {
      const response = await fetch("https://api.llama.fi/tvl/beraborrow");
      if (!response.ok) throw new Error(`Fetch failed: ${response.status}`);
      return response.json() as Promise<number>; // <-- Convert response body to JSON
    } catch (error) {
      console.error(error);
      return 0;
    }
  },
}));
