import { atomWithSuspenseQuery } from "jotai-tanstack-query";
import { accountAtom, beraborrowAtom } from "./Account";
import { Hex } from "viem";

export const getLiquidationsByLiquidatorAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), "getLiquidationsByLiquidatorAtom"],
  queryFn: async ({ queryKey }) => {
    const [user] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const liquidations = await beraborrow.subgraphClient.getLiquidationsByLiquidator(user);
    return liquidations;
  },
  throwOnError: true,
}));
