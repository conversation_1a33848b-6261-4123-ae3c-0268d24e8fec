import { atomWithQuery, atomWithSuspenseQuery } from "jotai-tanstack-query";
import { accountAtom, beraborrow<PERSON>tom } from "./Account";
import { Hex, zeroAddress } from "viem";
import { _denManagerAddrAtom } from "./Den";
import { axiosAPI } from "../utils/constants";

// export const rewardsPartnersAtom = atomWithSuspenseQuery((get) => ({
//   queryKey: [zeroAddress, "partnersAtom"],
//   queryFn: async () => {
//     const beraborrow = get(beraborrowAtom);
//     const assets = await beraborrow.api.rewards.getPartners();
//     const temp = assets.filter((item) => item.data !== null).map((item) => item.data);
//     return temp;
//   },
//   throwOnError: true,
// }));
// export const oogaboogaHealthAtom = atomWithQuery((get) => ({
//   queryKey: [zeroAddress, "oogaboogaHealthAtom"],
//   queryFn: async () => {
//     const beraborrow = get(beraborrowAtom);
//     return beraborrow.oogaboogaHealth();
//   },
//   staleTime: 1000 * 10, // 10 seconds
// }));

export const ensoHealth = atomWithSuspenseQuery<boolean>((get) => ({
  queryKey: [zeroAddress, "ensoHealth"],
  queryFn: async ({}) => {
    try {
      const beraborrow = get(beraborrowAtom);
      return beraborrow.api.getEnsoHealth();
    } catch (error) {
      console.error(error);
      return false;
    }
  },
  staleTime: 1000 * 10, // 10 seconds
}));
export const getEnsoBalances = atomWithQuery<TokenBalanceWithDetails[]>((get) => ({
  queryKey: [get(accountAtom), "getEnsoBalances"],
  queryFn: async ({ queryKey }) => {
    const [user] = queryKey as Readonly<[Hex]>;

    // const beraborrow = get(beraborrowAtom);
    // return beraborrow.api.getEnsoTokenBalances(user);
    const balance = await axiosAPI.get<{
      data: TokenBalanceWithDetails[];
    }>(`/enso/balances/${user}`);
    return balance.data.data;
  },
  staleTime: 1000 * 10, // 10 seconds
}));

export type TokenBalanceResponse = {
  token: string;
  amount: string;
  decimals: number;
  price: number;
};

export type TokenBalanceWithDetails = TokenBalanceResponse & TokenDetails;

export type TokenDetailsResponse = {
  meta: {
    total: number;
    lastPage: number;
    currentPage: number;
    perPage: number;
    prev: number;
    next: number;
    cursor: number;
  };
  data: [TokenDetails];
};
export type TokenDetails = {
  address: string;
  chainId: number;
  type: string;
  decimals: number;
  symbol: string;
  name: string;
  logosUri: [string];
  underlyingTokens: [
    {
      address: string;
      chainId: number;
      type: string;
      decimals: number;
      symbol: string;
      name: string;
      logosUri: [string];
    },
  ];
  project: string;
  protocolSlug: string;
  apy: number;
  apyBase: number;
  apyReward: number;
  tvl: number;
  primaryAddress: string;
};
