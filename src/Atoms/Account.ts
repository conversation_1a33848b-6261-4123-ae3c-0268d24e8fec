import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@Beraborrowofficial/sdk";
import { atom } from "jotai";
import { Client, Hex, zeroAddress } from "viem";
import { ANON_BERABORROW, ANON_CLIENT, ANON_ADDRESS, CLUB_LEVELS } from "../utils/constants";
import { atomWithStorage } from "jotai/utils";
import { atomEffect } from "jotai-effect";
import { getUserTotalDepositVolumeAtom } from "./Den";
import posthog from "posthog-js";
import { PostHogEvent, BorrowLevel, NavigationOptions, ProfileAsset } from "../@type/Account";
export const accountAtom = atom<Hex>(ANON_ADDRESS);
export const clientAtom = atom<Client>(ANON_CLIENT);
export const beraborrowAtom = atom<BeraBorrow>(ANON_BERABORROW);
export const beraborrowConstantsAtom = atom((get) => get(beraborrowAtom).constants);

const today = new Date();
export const historyStartDateAtom = atom(new Date(today.getFullYear(), today.getMonth(), 1).getTime());
export const historyEndDateAtom = atom((get) => {
  const firstMonth = new Date(get(historyStartDateAtom));
  return new Date(firstMonth.getFullYear(), firstMonth.getMonth() + 1, 1).getTime() - 1;
});
export const historyMonthsAtom = atom((get) => {
  const firstMonth = new Date(get(beraborrowAtom).constants.deploymentTimestamp * 1000);
  const today = new Date(); // Current date
  const lastMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0); // Last day of the current month
  const dateArray: { date: string; label: string }[] = [];
  const options = { year: "numeric", month: "long" } as const;
  while (firstMonth <= lastMonth) {
    const formattedDate = firstMonth.toISOString().split("T")[0]; // format YYYY-MM-DD
    const label = new Intl.DateTimeFormat("en-US", options).format(firstMonth); // format Month Year
    dateArray.push({ date: formattedDate, label });
    firstMonth.setMonth(firstMonth.getMonth() + 1); // Move to the next month
  }
  return dateArray;
});
export const pendingPostHogEventAtom = atom<PostHogEvent | undefined>(undefined);
export const postHogEventAtom = atom<PostHogEvent | undefined>(undefined);
export const resetPostHogEventAtom = atom<null, [{ type: PostHogEvent["type"]; page_rel_path: string } | undefined], void>(null, (_get, set, value) => {
  if (value) {
    set(postHogEventAtom, value);
  } else {
    set(postHogEventAtom, undefined);
  }
});
export const updatePostHogEventAtom = atom<null, [value: { [key: string]: string }], void>(null, (get, set, value) => {
  const oldEvent = get(postHogEventAtom);
  if (oldEvent?.type) {
    set(postHogEventAtom, { ...oldEvent, ...value });
  }
});
export const DrawerOpenAtom = atom(false);
export const navigationAtom = atom<NavigationOptions>("default");
export const simpleModeAtom = atomWithStorage("mode", true, undefined, {
  getOnInit: true,
});
export const borrowLevelAtom = atomWithStorage<BorrowLevel>("borrowLevel", "Basic", undefined, {
  getOnInit: true,
});
export const borrowLevelEffectAtom: ReturnType<typeof atomEffect> = atomEffect((get, set) => {
  const level = get(borrowLevelAtom);
  const account = get(accountAtom);
  const getLevel = async () => {
    const { data: userTotalVolume } = await get(getUserTotalDepositVolumeAtom);
    const userLvl = userTotalVolume > CLUB_LEVELS.gold ? "Gold" : userTotalVolume > CLUB_LEVELS.silver ? "Silver" : userTotalVolume > CLUB_LEVELS.bronze ? "Bronze" : "Basic";
    if (userTotalVolume !== undefined && level !== userLvl) {
      set(borrowLevelAtom, userLvl);
      if (account !== zeroAddress) {
        const display_mode = get(simpleModeAtom) ? "simple" : "den";
        posthog.capture("MembershipLevelUpdated", {
          prev_membership_level: level,
          membership_level: userLvl,
          display_mode,
        });
      }
    }
  };
  getLevel();
});

export const profileNFTAtom = atomWithStorage<ProfileAsset>(
  "profileNFT",
  {
    url: "",
    type: "Image",
  },
  undefined,
  {
    getOnInit: true,
  }
);
