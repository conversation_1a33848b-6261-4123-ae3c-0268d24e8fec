import { Getter, Setter, W<PERSON><PERSON>tom, atom } from "jotai";
import { atomFamily, atomWithStorage } from "jotai/utils";
import { Hex, SimulateContractReturnType, Transaction } from "viem";
import { atomEffect } from "jotai-effect";
import { getTransaction } from "viem/actions";
import { clientAtom, pendingPostHogEventAtom, simpleModeAtom, accountAtom } from "./Account";
import { invalidatorAtom } from "./System";
import { bigintJSONReplacer, bigintJSONReviver } from "../utils/helpers";
import { setNotificationEventAtom } from "./Notifications";
import posthog from "posthog-js";
import { BaseTransactionStates, TransactionStates } from "../@type/Transactions";

export const simulateStateAtom = atom<BaseTransactionStates<SimulateContractReturnType>>({
  type: "idle",
});
export const writeStateAtom = atom<BaseTransactionStates<Hex>>({
  type: "idle",
});

export const transactionStateAtom = atom<TransactionStates>({
  type: "idle",
});

const transactionStateEvents = (transaction: TransactionStates, get: Getter, set: Setter, latestTx?: Transaction | undefined) => {
  if (transaction.type === "success") {
    set(setNotificationEventAtom, { type: "broadcast", payload: transaction.payload });
    set(latestTxIdAtom, transaction.payload);
    const postHogEvent = get(pendingPostHogEventAtom);
    if (postHogEvent) {
      const { type: postHogEventType, ...postHogEventDetails } = postHogEvent;
      const display_mode = get(simpleModeAtom) ? "simple" : "den";
      const wallet_id = get(accountAtom)?.toString();
      posthog.capture(postHogEventType, { txId: transaction.payload, wallet_id, display_mode, ...postHogEventDetails });
      set(pendingPostHogEventAtom, undefined);
    }
  } else if (transaction.type === "error") {
    console.error(transaction.payload);
    const error = transaction.payload as { message?: string; shortMessage?: string };
    const errorMessage = error?.shortMessage || error?.message || "An unexpected error occurred";
    set(setNotificationEventAtom, { type: "error", payload: errorMessage });
    const postHogEvent = get(pendingPostHogEventAtom);
    if (postHogEvent) {
      const { type: postHogEventType, ...postHogEventDetails } = postHogEvent;
      const display_mode = get(simpleModeAtom) ? "simple" : "den";
      const wallet_id = get(accountAtom)?.toString();
      posthog.capture(postHogEventType + "Error", { errorMessage: errorMessage, wallet_id, display_mode, ...postHogEventDetails });
    }
    setTimeout(() => {
      set(transactionStateAtom, { type: "idle" });
      set(writeStateAtom, { type: "idle" });
      set(simulateStateAtom, { type: "idle" });
    }, 1000);
  } else if (transaction.type === "confirmed") {
    set(invalidatorAtom, "all");
    if (latestTx) {
      set(setNotificationEventAtom, { type: "confirmed", payload: latestTx.hash });
    }
  }
};

export const transactionStateListenerAtom: ReturnType<typeof atomEffect> = atomEffect((get, set) => {
  const latestTx = get(latestTxAtom);
  const writeState = get(writeStateAtom);
  const simulateState = get(simulateStateAtom);
  const transactionState = get(transactionStateAtom);
  let transaction: TransactionStates;
  if (writeState.type === "error") {
    transaction = { type: "error", payload: writeState?.payload };
  } else if (simulateState.type === "error") {
    transaction = { type: "error", payload: simulateState?.payload };
  } else if (writeState.type === "pending" || simulateState.type === "pending" || (simulateState.type === "success" && writeState.type === "idle")) {
    transaction = { type: "pending" };
  } else if (writeState.type === "success" && latestTx?.blockNumber && latestTx?.hash === writeState.payload) {
    transaction = { type: "confirmed", payload: latestTx?.hash };
  } else if (writeState.type === "success") {
    transaction = { type: "success", payload: writeState.payload };
  } else {
    transaction = { type: "idle" };
  }
  if (transactionState.type !== transaction.type) {
    transactionStateEvents(transaction, get, set, latestTx);
  }
  set(transactionStateAtom, transaction);
});

export const txIdsAtom = atomWithStorage<Hex[]>("Transactions", []);

export const latestTxAtom = atom((get) => {
  const txId = get(latestTxIdAtom);
  if (!txId) return undefined;
  return get(_txFamilyAtom(txId));
});
export const clearLatestTxAtom = atom(null, (_get, set) => {
  set(_latestTxIdAtom, undefined);
});
export const latestTxStatusAtom = atom((get) => {
  const LatestTx = get(latestTxAtom);

  return !LatestTx ? "idle" : LatestTx.blockNumber ? "confirmed" : "pending";
});
const _txFamilyAtom = atomFamily<Hex, WritableAtom<Transaction | undefined, [Transaction | undefined], void>>(
  (txId) => createTxAtom(txId),
  (txA, txB) => txA === txB
);
const _latestTxIdAtom = atom<Hex | undefined>(undefined);

const latestTxIdAtom = atom<Hex | undefined, [txId: Hex], void>(
  (get) => get(_latestTxIdAtom),
  (get, set, txId) => {
    const _txIds = get(txIdsAtom);
    if (!_txIds.includes(txId)) {
      _txIds.push(txId);
      set(txIdsAtom, _txIds);
    }
    set(_latestTxIdAtom, txId);
    const txAtom = _txFamilyAtom(txId);
    const tx = get(txAtom);
    //start polling if not confirmed
    if (!tx || !tx.blockNumber) {
      set(txAtom, undefined);
    }
  }
);

function createTxAtom(txId: Hex, pollInterval = 1000) {
  const storedTxStr = localStorage.getItem(txId);
  const storedTx: Transaction | undefined = storedTxStr ? JSON.parse(storedTxStr, bigintJSONReviver) : undefined;
  let confirmed = false;
  const intervalHandleAtom = atom<NodeJS.Timeout | undefined>(undefined);
  const txAtom = atom<Transaction | undefined, [Transaction | undefined], void>(storedTx, (get, set, tx) => {
    const isComplete = tx !== undefined && tx.blockNumber;
    const intervalHandle = get(intervalHandleAtom);
    let isInitialized = tx !== undefined;
    const setTransaction = async () => {
      try {
        const updatedTx = await getTransaction(get(clientAtom), { hash: txId });
        if (updatedTx.blockNumber || !isInitialized) {
          isInitialized = true;
          localStorage.setItem(txId, JSON.stringify(tx, bigintJSONReplacer));
          set(txAtom, updatedTx);
        }
        const intervalHandle = get(intervalHandleAtom);
        if (intervalHandle && updatedTx.blockNumber) {
          clearInterval(intervalHandle);
          set(intervalHandleAtom, undefined);
          confirmed = true;
        }
      } catch (e) {
        console.error(e);
      }
    };
    if (isComplete && intervalHandle) {
      clearInterval(intervalHandle);
      set(intervalHandleAtom, undefined);
    } else if (!intervalHandle) {
      if (!isInitialized) {
        setTransaction();
      }
      const intervalHandle = setInterval(setTransaction, pollInterval);
      set(intervalHandleAtom, intervalHandle);
      //handle Timeout
      setTimeout(() => {
        if (!confirmed) {
          clearInterval(intervalHandle);
          set(intervalHandleAtom, undefined);
          set(transactionStateAtom, { type: "idle" });
          set(invalidatorAtom, "user");
        }
      }, pollInterval * 15);
    }
  });
  return txAtom;
}
