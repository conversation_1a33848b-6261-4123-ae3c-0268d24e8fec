import { atom, Atom } from "jotai";
import { atomWithSuspenseQuery } from "jotai-tanstack-query";
import { unwrap } from "jotai/utils";
import { Hex, zeroAddress } from "viem";
import { FormError } from "../@type";
import { BERABORROW_ADDRESSES } from "../utils/constants";
import { formatNoDecimal } from "../utils/helpers";
import { accountAtom, beraborrowAtom } from "./Account";
import { getPollenBalanceAtom } from "./Tokens";
import { transactionStateAtom } from "./Transaction";
export interface ProtocolStaked {
  totalStakingBalance: bigint;
  stakingPct: bigint;
  stakingApr: bigint;
  weeklyStakingRewards: bigint;
  totalStakingRewards: bigint;
  totalStakingRewardsClaimed: bigint;
}

export interface SPollenProtocolData {
  totalStakingBalance: bigint;
  totalWrapped: bigint;
  totalLocked: bigint;
  stakingPct: bigint;
  lockingPct: bigint;
  stakingAprDaily: bigint;
  stakingAprWeekly: bigint;
  calculatedLockingAprWeekly: bigint;
  weeklyStakingRewards: bigint;
  weeklyLockingRewards: bigint;
  allTimeStakingRewards: bigint;
  allTimeLockingRewards: bigint;
  allTimeStakingClaimed: bigint;
  allTimeLockingClaimed: bigint;
}

export interface LpProtocolData {
  totalStakingBalance: bigint;
  totalLocked: bigint;
  stakingPct: bigint;
  lockingPct: bigint;
  stakingAprDaily: bigint;
  stakingAprWeekly: bigint;
  calculatedLockingAprWeekly: bigint;
  weeklyStakingRewards: bigint;
  weeklyLockingRewards: bigint;
  allTimeStakingRewards: bigint;
  allTimeLockingRewards: bigint;
  allTimeStakingClaimed: bigint;
  allTimeLockingClaimed: bigint;
  allTimeBuybackClaimed: bigint;
  iBGTClaimed: bigint;
  iREDClaimed: bigint;
}

// Unified atom to fetch all protocols data once
export const getAllProtocolsAtom = atomWithSuspenseQuery<any[]>((get) => ({
  queryKey: [BERABORROW_ADDRESSES.defaultProtocol, "getAllProtocols"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    try {
      return await beraborrow.subgraphPollenClient.getAllProtocols();
    } catch (error) {
      console.error("Failed to fetch all protocols data:", error);
      return [];
    }
  },
  throwOnError: true,
}));

export const getSPollenProtocolDataAtom = atomWithSuspenseQuery<SPollenProtocolData | null>((get) => ({
  queryKey: [BERABORROW_ADDRESSES.defaultProtocol, "getSPollenProtocolData"],
  queryFn: async () => {
    try {
      const protocols = (await get(getAllProtocolsAtom)).data;
      const [protocolData] = protocols;

      if (!protocolData?.sPollen) {
        return null;
      }

      const { sPollen } = protocolData;

      return {
        totalStakingBalance: BigInt(sPollen.totals.staked ?? "0"),
        totalWrapped: BigInt(sPollen.totals.wrapped ?? "0"),
        totalLocked: BigInt(sPollen.totals.locked ?? "0"),
        stakingPct: BigInt(sPollen.rates.stakingPct ?? "0"),
        lockingPct: BigInt(sPollen.rates.lockingPct ?? "0"),
        stakingAprDaily: BigInt(sPollen.rates.buybackStakingAprDaily ?? "0"),
        stakingAprWeekly: BigInt(sPollen.rates.buybackStakingAprWeekly ?? "0"),
        calculatedLockingAprWeekly: BigInt(sPollen.rates.calculatedLockingAprWeekly ?? "0"),
        weeklyStakingRewards: BigInt(sPollen.rewards.weeklyStaking ?? "0"),
        weeklyLockingRewards: BigInt(sPollen.rewards.weeklyLocking ?? "0"),
        allTimeStakingRewards: BigInt(sPollen.rewards.allTimeStaking ?? "0"),
        allTimeLockingRewards: BigInt(sPollen.rewards.allTimeLocking ?? "0"),
        allTimeStakingClaimed: BigInt(sPollen.claims.allTimeStaking ?? "0"),
        allTimeLockingClaimed: BigInt(sPollen.claims.allTimeLocking ?? "0"),
      };
    } catch (error) {
      console.error("Failed to fetch sPollen protocol data:", error);
      return null;
    }
  },
  throwOnError: true,
}));

export const getLpProtocolDataAtom = atomWithSuspenseQuery<LpProtocolData | null>((get) => ({
  queryKey: [BERABORROW_ADDRESSES.defaultProtocol, "getLpProtocolData"],
  queryFn: async () => {
    try {
      const protocols = (await get(getAllProtocolsAtom)).data;
      const [protocolData] = protocols;

      if (!protocolData?.lp) {
        return null;
      }

      const { lp } = protocolData;

      return {
        totalStakingBalance: BigInt(lp.totals.staked ?? "0"),
        totalLocked: BigInt(lp.totals.locked ?? "0"),
        stakingPct: BigInt(lp.rates.stakingPct ?? "0"),
        lockingPct: BigInt(lp.rates.lockingPct ?? "0"),
        stakingAprDaily: BigInt(lp.rates.buybackStakingAprDaily ?? "0"),
        stakingAprWeekly: BigInt(lp.rates.buybackStakingAprWeekly ?? "0"),
        calculatedLockingAprWeekly: BigInt(lp.rates.calculatedLockingAprWeekly ?? "0"),
        weeklyStakingRewards: BigInt(lp.rewards.weeklyStaking ?? "0"),
        weeklyLockingRewards: BigInt(lp.rewards.weeklyLocking ?? "0"),
        allTimeStakingRewards: BigInt(lp.rewards.allTimeStaking ?? "0"),
        allTimeLockingRewards: BigInt(lp.rewards.allTimeLocking ?? "0"),
        allTimeStakingClaimed: BigInt(lp.claims.allTimeStaking ?? "0"),
        allTimeLockingClaimed: BigInt(lp.claims.allTimeLocking ?? "0"),
        allTimeBuybackClaimed: BigInt(lp.claims.allTimeBuyback ?? "0"),
        iBGTClaimed: BigInt(lp.claims.iBGT ?? "0"),
        iREDClaimed: BigInt(lp.claims.iRED ?? "0"),
      };
    } catch (error) {
      console.error("Failed to fetch LP protocol data:", error);
      return null;
    }
  },
  throwOnError: true,
}));

// Legacy atom for backward compatibility - now uses sPollen data
export const getProtocolStakingDataAtom = atomWithSuspenseQuery<ProtocolStaked[]>((get) => ({
  queryKey: [BERABORROW_ADDRESSES.defaultProtocol, "getProtocolStakingData"],
  queryFn: async () => {
    try {
      const protocols = (await get(getAllProtocolsAtom)).data;
      const [protocolData] = protocols;

      if (!protocolData?.sPollen) {
        return [];
      }
      const { sPollen } = protocolData;

      return [
        {
          totalStakingBalance: BigInt(sPollen.totals.staked ?? "0"),
          stakingPct: BigInt(sPollen.rates.stakingPct ?? "0"),
          stakingApr: BigInt(sPollen.rates.buybackStakingAprWeekly ?? "0"),
          weeklyStakingRewards: BigInt(sPollen.rewards.weeklyStaking ?? "0"),
          totalStakingRewards: BigInt(sPollen.rewards.allTimeStaking ?? "0"),
          totalStakingRewardsClaimed: BigInt(sPollen.claims.allTimeStaking ?? "0"),
        },
      ];
    } catch (error) {
      console.error("Failed to fetch protocol staking data:", error);
      return [];
    }
  },
  throwOnError: true,
}));

export const getSPollenBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), "getSPollenBalanceAtom"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;

    const b = await beraborrow.sPollenToken.getBalance(account);

    return b;
  },
  throwOnError: true,
}));

export const getStakedSPollenBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), "getStakedSPollenBalance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;

    try {
      return await beraborrow.sPollenStaking.getBalance(account);
    } catch (error) {
      console.warn("Failed to fetch staked sPOLLEN balance:", error);
      return 0n;
    }
  },
  throwOnError: true,
}));

export const getLpPollenBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.pollenLpToken.contractAddress, "getLpPollenBalance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;
    return await beraborrow.collateralTokens[BERABORROW_ADDRESSES.pollenLpToken.contractAddress].getBalance();
  },
  throwOnError: true,
}));

export const getStakedLpPollenBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), "getStakedLpPollenBalance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;

    try {
      return await beraborrow.lpPollenStaking.getBalance(account);
    } catch (error) {
      console.warn("Failed to fetch staked LP POLLEN balance:", error);
      return 0n;
    }
  },
  throwOnError: true,
}));

export const getPollenWrapAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.pollenToken.contractAddress, "getPollenWrapAllowance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;
    const collateralToken = beraborrow.collateralTokens[BERABORROW_ADDRESSES.pollenToken.contractAddress];
    return await collateralToken.allowance(account, beraborrow.sPollenToken.contractAddress);
  },
  throwOnError: true,
}));

export const getSPollenStakingAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.sPollenToken.contractAddress, "getSPollenStakingAllowance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;
    const collateralToken = beraborrow.collateralTokens[BERABORROW_ADDRESSES.sPollenToken.contractAddress];
    return await collateralToken.allowance(account, beraborrow.sPollenStaking.contractAddress);
  },
  throwOnError: true,
}));

export const getLpPollenStakingAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.pollenLpToken.contractAddress, "getLpPollenStakingAllowance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;
    const collateralToken = beraborrow.collateralTokens[BERABORROW_ADDRESSES.pollenLpToken.contractAddress];
    return await collateralToken.allowance(account, beraborrow.lpPollenStaking.contractAddress);
  },
  throwOnError: true,
}));

export const getLpPollenPendingRewardsAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), "getLpPollenPendingRewards"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    if (!account || account === zeroAddress) return 0n;

    try {
      const beraborrow = get(beraborrowAtom);
      const rewards = await beraborrow.lpPollenStaking.getRewardsForUser(account);

      // Handle case where rewards might be an array of reward objects
      if (Array.isArray(rewards) && rewards.length > 0) {
        return rewards.reduce((total, reward) => {
          if (typeof reward === "object" && "amount" in reward) {
            return total + (reward.amount as bigint);
          }
          return total;
        }, 0n);
      }

      // Handle case where rewards is already a bigint
      if (typeof rewards === "bigint") {
        return rewards;
      }

      return 0n;
    } catch (error) {
      console.warn("Failed to fetch LP pending rewards:", error);
      return 0n;
    }
  },
  throwOnError: true,
}));

export const getVePollenBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), "getVePollenBalance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;

    try {
      return await beraborrow.sPollenVotingEscrow.getUserVeBalance(account);
    } catch (error) {
      console.warn("Failed to fetch vePOLLEN balance:", error);
      return 0n;
    }
  },
  throwOnError: true,
}));

export const getLpVePollenBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), "getLpVePollenBalance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;

    try {
      return await beraborrow.lpVotingEscrow.getUserVeBalance(account);
    } catch (error) {
      console.warn("Failed to fetch LP vePOLLEN balance:", error);
      return 0n;
    }
  },
  throwOnError: true,
}));

export const getSPollenLockAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.sPollenToken.contractAddress, "getSPollenLockAllowance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;
    const collateralToken = beraborrow.collateralTokens[BERABORROW_ADDRESSES.sPollenToken.contractAddress];
    return await collateralToken.allowance(account, beraborrow.sPollenVotingEscrow.contractAddress);
  },
  throwOnError: true,
}));

export const getLpPollenLockAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), BERABORROW_ADDRESSES.pollenLpToken.contractAddress, "getLpPollenLockAllowance"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;
    const collateralToken = beraborrow.collateralTokens[BERABORROW_ADDRESSES.pollenLpToken.contractAddress];
    return await collateralToken.allowance(account, beraborrow.lpVotingEscrow.contractAddress);
  },
  throwOnError: true,
}));

export const getSPollenPendingRewardsAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), "getSPollenPendingRewards"],
  queryFn: async ({ queryKey }) => {
    const [account] = queryKey as Readonly<[Hex]>;
    const beraborrow = get(beraborrowAtom);
    if (!account || account === zeroAddress) return 0n;

    try {
      const rewards = await beraborrow.sPollenStaking.getRewardsForUser(account);

      if (Array.isArray(rewards) && rewards.length > 0) {
        return rewards.reduce((total, reward) => {
          if (typeof reward === "object" && "amount" in reward) {
            return total + (reward.amount as bigint);
          }
          return total;
        }, 0n);
      }
      return 0n;
    } catch {
      return 0n;
    }
  },
  throwOnError: true,
}));

export const formValuesWrapAtom = atom<string>("0");
export const formValuesLiquidStakeAtom = atom<string>("0");
export const formValuesLpLiquidStakeAtom = atom<string>("0");
export const formValuesLockAtom = atom<string>("0");
export const formValuesUnlockAtom = atom<string>("0");

export const formValuesStakeDepositAtom = formValuesWrapAtom;
export const formValuesStakeWithdrawAtom = formValuesWrapAtom;
export const formValuesLiquidUnstakeAtom = formValuesLiquidStakeAtom;
export const formValuesLpLiquidUnstakeAtom = formValuesLpLiquidStakeAtom;

export const wrapAmountAtom = atom<bigint>((get) => {
  try {
    return formatNoDecimal(get(formValuesWrapAtom), BERABORROW_ADDRESSES.pollenToken.decimals);
  } catch {
    return 0n;
  }
});

export const liquidStakeAmountAtom = atom<bigint>((get) => {
  try {
    return formatNoDecimal(get(formValuesLiquidStakeAtom), BERABORROW_ADDRESSES.sPollenToken.decimals);
  } catch {
    return 0n;
  }
});

export const lpLiquidStakeAmountAtom = atom<bigint>((get) => {
  try {
    return formatNoDecimal(get(formValuesLpLiquidStakeAtom), BERABORROW_ADDRESSES.pollenLpToken.decimals);
  } catch {
    return 0n;
  }
});

export const lockAmountAtom = atom<bigint>((get) => {
  try {
    return formatNoDecimal(get(formValuesLockAtom), BERABORROW_ADDRESSES.sPollenToken.decimals);
  } catch {
    return 0n;
  }
});

export const unlockAmountAtom = atom<bigint>((get) => {
  try {
    return formatNoDecimal(get(formValuesUnlockAtom), BERABORROW_ADDRESSES.sPollenToken.decimals);
  } catch {
    return 0n;
  }
});

const createValidationAtom = (amountAtom: Atom<bigint>, balanceAtom: Atom<unknown>, tokenName: string, isStakeMode?: boolean) => {
  return unwrap(
    atom<Promise<FormError | undefined>>(async (get) => {
      const value = get(amountAtom);
      const txState = get(transactionStateAtom);

      if (txState.type === "pending") {
        return {
          type: "transaction_in_pending",
          shortMessage: "Pending transaction",
          description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
        };
      }

      const walletConnected = get(accountAtom) !== zeroAddress;
      if (!walletConnected) {
        return {
          type: "wallet_not_connected",
        };
      }

      if (value === 0n) {
        return {
          type: "zero_value",
        };
      }

      const [{ data: available }] = (await Promise.all([get(balanceAtom)])) as [{ data: bigint }];

      if (value > available) {
        return {
          type: "insufficient_balance",
          shortMessage: `Insufficient ${isStakeMode === false ? "staked " : ""}${tokenName} balance`,
          description: `You don't have enough ${isStakeMode === false ? "staked " : ""}${tokenName} to ${isStakeMode === false ? "unstake" : "stake"} this amount.`,
        };
      }

      return undefined;
    })
  );
};

const createUnlockValidationAtom = (
  amountAtom: Atom<bigint>,
  balanceAtom: Atom<unknown>,
  tokenName: string,
  canUnlockAtomParam: Atom<unknown>,
  timeUntilUnlockAtomParam: Atom<unknown>
) => {
  return unwrap(
    atom<Promise<FormError | undefined>>(async (get) => {
      const value = get(amountAtom);
      const txState = get(transactionStateAtom);

      if (txState.type === "pending") {
        return {
          type: "transaction_in_pending",
          shortMessage: "Pending transaction",
          description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
        };
      }

      const walletConnected = get(accountAtom) !== zeroAddress;
      if (!walletConnected) {
        return {
          type: "wallet_not_connected",
        };
      }

      if (value === 0n) {
        return {
          type: "zero_value",
        };
      }

      const { data: canUnlock } = (await get(canUnlockAtomParam)) as { data: boolean };
      if (!canUnlock) {
        const { data: timeUntilUnlock } = (await get(timeUntilUnlockAtomParam)) as {
          data: { timeString: string; remainingSeconds: number; canUnlock: boolean; hasPosition: boolean };
        };
        const timeMessage = timeUntilUnlock.timeString === "No lock" ? "You don't have an active lock position." : `Time remaining: ${timeUntilUnlock.timeString}`;
        return {
          type: "position_not_expired",
          shortMessage: "Position not expired",
          description: `Your locked position has not expired yet. ${timeMessage}`,
        };
      }

      const [{ data: available }] = (await Promise.all([get(balanceAtom)])) as [{ data: bigint }];

      if (value > available) {
        return {
          type: "insufficient_balance",
          shortMessage: `Insufficient ${tokenName} balance`,
          description: `You don't have enough ${tokenName} to unlock this amount.`,
        };
      }

      return undefined;
    })
  );
};

export const formValidationsWrapAtom = createValidationAtom(wrapAmountAtom, getPollenBalanceAtom, "POLLEN", true);
export const formValidationsUnwrapAtom = createValidationAtom(wrapAmountAtom, getSPollenBalanceAtom, "sPOLLEN", false);
export const formValidationsLiquidUnstakeAtom = createValidationAtom(liquidStakeAmountAtom, getStakedSPollenBalanceAtom, "sPOLLEN", false);
export const formValidationsLpLiquidUnstakeAtom = createValidationAtom(lpLiquidStakeAmountAtom, getStakedLpPollenBalanceAtom, "BERA-POLLEN", false);

export const formValidationsLiquidStakeAtom = createValidationAtom(liquidStakeAmountAtom, getSPollenBalanceAtom, "sPOLLEN", true);

export const formValidationsLpLiquidStakeAtom = createValidationAtom(lpLiquidStakeAmountAtom, getLpPollenBalanceAtom, "BERA-POLLEN", true);

export const lockDurationAtom = atom<number>(30);

export const getLockingAprDataAtom = atomWithSuspenseQuery<Record<number, bigint>>((get) => ({
  queryKey: ["getLockingAprData"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);

    try {
      const aprBuckets = await beraborrow.subgraphPollenClient.getAllLockingAprBuckets();

      const aprData: Record<number, bigint> = {};

      const weeklyBuckets = aprBuckets.filter((bucket) => bucket.bucketType === "WEEKLY");
      const sortedWeeklyBuckets = [...weeklyBuckets].sort((a, b) => Number(b.lastTime) - Number(a.lastTime));
      const latestWeeklyBucket = sortedWeeklyBuckets[0];

      if (latestWeeklyBucket && latestWeeklyBucket.cumulativeRewardDistributed > 0n && latestWeeklyBucket.cumulativeTotalSupplyStored > 0n) {
        const baseApr = (latestWeeklyBucket.cumulativeRewardDistributed * 5200n * 100n) / latestWeeklyBucket.cumulativeTotalSupplyStored;

        aprData[30] = (baseApr * 125n) / 100n; // 1.25x multiplier
        aprData[90] = (baseApr * 200n) / 100n; // 2x multiplier
        aprData[180] = (baseApr * 300n) / 100n; // 3x multiplier
        aprData[360] = (baseApr * 400n) / 100n; // 4x multiplier
      } else {
        const protocolData = await beraborrow.subgraphPollenClient.getAllSPollenProtocols();
        const [protocol] = protocolData;

        if (protocol?.rates?.calculatedLockingAprWeekly) {
          const baseApr = BigInt(protocol.rates.calculatedLockingAprWeekly);
          aprData[30] = (baseApr * 125n) / 100n;
          aprData[90] = (baseApr * 200n) / 100n;
          aprData[180] = (baseApr * 300n) / 100n;
          aprData[360] = (baseApr * 400n) / 100n;
        } else {
          aprData[30] = 123n;
          aprData[90] = 246n;
          aprData[180] = 369n;
          aprData[360] = 492n;
        }
      }

      return aprData;
    } catch (error) {
      console.error("Failed to fetch locking APR data:", error);
      return {
        30: 0n,
        90: 0n,
        180: 0n,
        360: 0n,
      };
    }
  },
  throwOnError: true,
  staleTime: 1000 * 60 * 5,
}));

export const getUserLockPositionAtom = atomWithSuspenseQuery<{
  amount: bigint;
  expiry: bigint;
  hasPosition: boolean;
} | null>((get) => ({
  queryKey: [get(accountAtom), "getUserLockPosition"],
  queryFn: async ({ queryKey }) => {
    const [userAddress] = queryKey as Readonly<[Hex]>;

    if (!userAddress || userAddress === zeroAddress) {
      return null;
    }

    const beraborrow = get(beraborrowAtom);

    try {
      const userData = await beraborrow.subgraphPollenClient.getUserLockPosition(userAddress);

      // @ts-expect-error type
      if (userData?.sPollen?.lockingDetails) {
        // @ts-expect-error type
        const lockingDetails = userData.sPollen.lockingDetails;
        return {
          amount: BigInt(lockingDetails.amount),
          expiry: BigInt(lockingDetails.expiry),
          hasPosition: BigInt(lockingDetails.amount) > 0n,
        };
      }

      return {
        amount: 0n,
        expiry: 0n,
        hasPosition: false,
      };
    } catch (error) {
      console.error("Failed to fetch user lock position:", error);
      return {
        amount: 0n,
        expiry: 0n,
        hasPosition: false,
      };
    }
  },
  throwOnError: true,
  staleTime: 1000 * 30,
}));

export const getUserLpLockPositionAtom = atomWithSuspenseQuery<{
  amount: bigint;
  expiry: bigint;
  hasPosition: boolean;
} | null>((get) => ({
  queryKey: [get(accountAtom), "getUserLpLockPosition"],
  queryFn: async ({ queryKey }) => {
    const [userAddress] = queryKey as Readonly<[Hex]>;

    if (!userAddress || userAddress === zeroAddress) {
      return null;
    }

    const beraborrow = get(beraborrowAtom);

    try {
      const userData = await beraborrow.subgraphPollenClient.getUserLockPosition(userAddress);

      if (userData?.lp?.lockingDetails) {
        const lockingDetails = userData.lp.lockingDetails;
        return {
          amount: BigInt(lockingDetails.amount),
          expiry: BigInt(lockingDetails.expiry),
          hasPosition: BigInt(lockingDetails.amount) > 0n,
        };
      }

      return {
        amount: 0n,
        expiry: 0n,
        hasPosition: false,
      };
    } catch (error) {
      console.error("Failed to fetch user LP lock position:", error);
      return {
        amount: 0n,
        expiry: 0n,
        hasPosition: false,
      };
    }
  },
  throwOnError: true,
  staleTime: 1000 * 30,
}));

const createTimeUntilUnlockAtom = (lockPositionAtom: Atom<unknown>, queryKeySuffix: string) => {
  return atomWithSuspenseQuery<{
    timeString: string;
    remainingSeconds: number;
    canUnlock: boolean;
    hasPosition: boolean;
  }>((get) => ({
    queryKey: [get(accountAtom), queryKeySuffix, Math.floor(Date.now() / 1000)],
    queryFn: async () => {
      const { data: lockPosition } = (await get(lockPositionAtom)) as { data: { amount: bigint; expiry: bigint; hasPosition: boolean } | null };
      if (!lockPosition?.hasPosition) {
        return {
          timeString: "No lock",
          remainingSeconds: 0,
          canUnlock: false,
          hasPosition: false,
        };
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const expiryTime = Number(lockPosition.expiry);

      if (currentTime >= expiryTime) {
        return {
          timeString: "Ready to unlock",
          remainingSeconds: 0,
          canUnlock: true,
          hasPosition: true,
        };
      }

      const timeDiff = expiryTime - currentTime;
      const days = Math.floor(timeDiff / (24 * 60 * 60));
      const hours = Math.floor((timeDiff % (24 * 60 * 60)) / (60 * 60));
      const minutes = Math.floor((timeDiff % (60 * 60)) / 60);

      const timeString = `${days} days and ${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;

      return {
        timeString,
        remainingSeconds: timeDiff,
        canUnlock: false,
        hasPosition: true,
      };
    },
    throwOnError: true,
    staleTime: 1000 * 1,
  }));
};

const createCanUnlockAtom = (lockPositionAtom: Atom<unknown>, queryKeySuffix: string) => {
  return atomWithSuspenseQuery<boolean>((get) => ({
    queryKey: [get(accountAtom), queryKeySuffix],
    queryFn: async () => {
      const { data: lockPosition } = (await get(lockPositionAtom)) as { data: { amount: bigint; expiry: bigint; hasPosition: boolean } | null };
      if (!lockPosition?.hasPosition) {
        return false;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const expiryTime = Number(lockPosition.expiry);

      return currentTime >= expiryTime;
    },
    throwOnError: true,
    staleTime: 1000 * 10,
  }));
};

export const canUnlockAtom = createCanUnlockAtom(getUserLockPositionAtom, "canUnlock");
export const timeUntilUnlockAtom = createTimeUntilUnlockAtom(getUserLockPositionAtom, "timeUntilUnlock");
export const canUnlockLpAtom = createCanUnlockAtom(getUserLpLockPositionAtom, "canUnlockLp");
export const timeUntilUnlockLpAtom = createTimeUntilUnlockAtom(getUserLpLockPositionAtom, "timeUntilUnlockLp");

export const lockExpiryAtom = atom<bigint>((get) => {
  const duration = get(lockDurationAtom);

  const durationMap: Record<number, number> = {
    30: 30 * 24 * 60 * 60, // 30 days in seconds (2592000)
    90: 90 * 24 * 60 * 60, // 90 days in seconds (7776000)
    180: 180 * 24 * 60 * 60, // 180 days in seconds (15552000)
    360: 52 * 7 * 24 * 60 * 60,
  };

  const WEEK = 7 * 24 * 60 * 60; // 604800 seconds

  const currentTime = Math.floor(Date.now() / 1000);

  const SUNDAY_OFFSET = 4 * 24 * 60 * 60;
  const currentWeekStart = Math.floor((currentTime - SUNDAY_OFFSET) / WEEK) * WEEK + SUNDAY_OFFSET;

  return BigInt(currentWeekStart + durationMap[duration] + WEEK);
});

export const formValidationsLockAtom = createValidationAtom(lockAmountAtom, getSPollenBalanceAtom, "sPOLLEN", true);
export const formValidationsUnlockAtom = createUnlockValidationAtom(unlockAmountAtom, getVePollenBalanceAtom, "vePOLLEN", canUnlockAtom, timeUntilUnlockAtom);

export const formValidationsLpLockAtom = createValidationAtom(lockAmountAtom, getLpPollenBalanceAtom, "BERA-POLLEN", true);
export const formValidationsLpUnlockAtom = createUnlockValidationAtom(unlockAmountAtom, getLpVePollenBalanceAtom, "LP vePOLLEN", canUnlockLpAtom, timeUntilUnlockLpAtom);

export const formValidationsStakeDepositAtom = formValidationsWrapAtom;
export const formValidationsStakeWithdrawAtom = formValidationsUnwrapAtom;
