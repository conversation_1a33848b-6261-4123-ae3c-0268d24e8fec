import { atomWithQuery, atomWithSuspenseQuery } from "jotai-tanstack-query";
import { beraborrowAtom, accountAtom } from "./Account";
import { atom } from "jotai";
import { Hex, zeroAddress } from "viem";
import { BERABORROW_ADDRESSES, ANON_CLIENT, axiosAPI } from "../utils/constants";
import { DenManagerDetails } from "../@type/Dens";
import { _denManagerAddrAtom, denManagerAtom, getDenInterestRateAtom } from "./Den";
import { formatNoDecimal, formatToken } from "../utils/helpers";
import { transactionStateAtom } from "./Transaction";
import { FormError } from "../@type";

import { readContract } from "viem/actions";
import { collateralTokenAtom, collateralTypeSelectorAtom, getCollateralBalanceAtom, getCollateralDetailsAtom } from "./Tokens";
import { ManagedVaultDetails } from "../@type/Boyco";
import { _vaultAddr<PERSON>tom, getVault<PERSON>py<PERSON>tom, getVaultsDetailsAtom } from "./Vault";
import { unwrap } from "jotai/utils";
import { getPoolAPYAtom } from "./StabilityPool";
import { DEFAULT_SLIPPAGE_TOLERANCE, SCALING_FACTOR, SCALING_FACTOR_BP } from "@Beraborrowofficial/sdk";
import { blockNumberAtom } from "./System";

export const managedVaultAtom = atom<Hex | undefined>(undefined);
export const _managedVaultAtom = atom<Hex>((get) => {
  const collateralTypeSelector = get(collateralTypeSelectorAtom);
  let _managedVaultAddr = get(managedVaultAtom);
  if (_managedVaultAddr && collateralTypeSelector === "managedVault") {
    return _managedVaultAddr;
  }
  return get(managedVaultsAtom)[0].contractAddress;
});
export const managedVaultsAtom = atom<ManagedVaultDetails[]>(
  Object.entries(BERABORROW_ADDRESSES.managedVaults).map((item) => {
    const denManagerAddresses = BERABORROW_ADDRESSES.denManagers[item[1].denManager];
    return {
      contractAddress: item[0] as Hex,
      denManagerAddress: item[1].denManager,
      collateralTicker: BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].ticker,
      collateralDecimals: BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].decimals,
      vaultDecimals: denManagerAddresses?.vault
        ? BERABORROW_ADDRESSES.vaults[denManagerAddresses.vault]?.decimals
        : BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].decimals,
      collateralAddress: item[1].collateral,
      vaultAddress: denManagerAddresses.vault,
    };
  })
);

export const boycoDenManagersAtom = atom<DenManagerDetails[]>(
  Object.entries(BERABORROW_ADDRESSES.denManagers)
    .filter((item) => !!item[1].permissioned)
    .map((item) => ({
      contractAddress: item[0] as Hex,
      collateralTicker: BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].ticker,
      collateralDecimals: BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].decimals,
      vaultDecimals: item[1]?.vault ? BERABORROW_ADDRESSES.vaults[item[1]?.vault]?.decimals : BERABORROW_ADDRESSES.collateralTokens[item[1].collateral].decimals,
      collateralAddress: item[1].collateral,
      vaultAddress: item[1]?.vault,
      wrappedCollateral: item[1]?.wrappedCollateral,
      wrappedCollateralTicker: item[1].wrappedCollateral ? BERABORROW_ADDRESSES.collateralTokens[item[1].wrappedCollateral]?.ticker : undefined,
      wrappedCollateralDecimals: item[1].wrappedCollateral ? BERABORROW_ADDRESSES.collateralTokens[item[1].wrappedCollateral]?.decimals : undefined,
      index: item[1].index,
    }))
    .sort((a, b) => {
      return a.index - b.index;
    })
);

export const getManagedVaultsDetailsAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, "getManagedVaultsDetailsAtom"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const managedVaults = await beraborrow.api.getManagedVaults();
    return managedVaults.map((item) => ({
      ...item,
      apy: formatNoDecimal(item.apy.toFixed(18), 16),
      tvl: formatNoDecimal(item.tvl.toFixed(18), 18),
      price: formatNoDecimal(item.price.toFixed(18), 18),
    }));
  },
  retry: false, // disable retries to speed up fail-fast
}));
export const getManagedVaultsDetailsNoWaitAtom = atomWithQuery((get) => ({
  queryKey: [zeroAddress, "getManagedVaultsDetailsAtom"],
  queryFn: async () => {
    const beraborrow = get(beraborrowAtom);
    const managedVaults = await beraborrow.api.getManagedVaults();
    return managedVaults.map((item) => ({
      ...item,
      apy: formatNoDecimal(item.apy.toFixed(18), 16),
      tvl: formatNoDecimal(item.tvl.toFixed(18), 18),
      price: formatNoDecimal(item.price.toFixed(18), 18),
    }));
  },
  retry: false, // disable retries to speed up fail-fast
}));
export const managedVaultsDetailsAtom = atomWithQuery((get) => {
  const apiManagedVaults = get(getManagedVaultsDetailsNoWaitAtom);
  return {
    queryKey: [zeroAddress, apiManagedVaults.data?.length ?? 0, "publicVaultsWithDetailAtom"],
    queryFn: async () => {
      const publicVaults = get(managedVaultsAtom);
      const { data: apiManagedVaults } = await get(getManagedVaultsDetailsNoWaitAtom);
      if (!apiManagedVaults) {
        return publicVaults;
      }
      const apiVaultMap = new Map(apiManagedVaults.map((v) => [v.contractAddress, { tvl: v.tvl, apy: v.apy }])) ?? {};
      return publicVaults
        .map((vault) => {
          const apiDetails = apiVaultMap.get(vault.contractAddress);
          return {
            ...vault,
            ...(apiDetails ?? {}), // safely spread if available
          };
        })
        .sort((a, b) => {
          return Number((b.tvl ?? 0n) - (a.tvl ?? 0n));
        });
    },
    throwOnError: true,
  };
});
export const getBoycoDenFullDetailsAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(_managedVaultAtom), "getBoycoDenFullDetailsAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _managedVaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    const denManagerAddress = BERABORROW_ADDRESSES.managedVaults[_managedVaultAddr].denManager;
    return beraborrow.denManagers[denManagerAddress].denManager.getDenDetails(_managedVaultAddr);
  },
  throwOnError: true,
}));

export const getManagedVaultDepositDetailsAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(_managedVaultAtom), "getManagedVaultDepositDetailsAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [address, _managedVaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    return beraborrow.managedVaults[_managedVaultAddr].managedVault.getVaultDeposit(address);
  },
  throwOnError: true,
}));
export const getsNectVaultDetailsAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(_managedVaultAtom), "getsNectVaultDetailsAtom"],
  queryFn: async ({ queryKey }) => {
    const sNectVaultAddress = Object.entries(BERABORROW_ADDRESSES.vaults).find((item) => item[1].ticker.toLowerCase() === "bb.snect")?.[0] as Hex;
    const beraborrow = get(beraborrowAtom);
    const [, _managedVaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    return beraborrow.vaults[sNectVaultAddress].getVaultDeposit(false, _managedVaultAddr);
  },
  throwOnError: true,
}));

export const EpochDetailsAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(_managedVaultAtom), "EpochDetailsAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _managedVaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    const details = await beraborrow.managedVaults[_managedVaultAddr].managedVault.getEpochDetails();
    return details;
  },
  throwOnError: true,
}));

export const hasRedeemIntentAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(_managedVaultAtom), "hasRedeemIntentAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _managedVaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    const {
      data: { epoch },
    } = await get(EpochDetailsAtom);
    try {
      await beraborrow.managedVaults[_managedVaultAddr].managedVault.cancelWithdrawalIntent(epoch, 1n);
      return { epoch, intent: true };
    } catch (_error) {
      console.error(_error);
      return { epoch, intent: false };
    }
  },
  throwOnError: true,
}));
export const hasRedeemClaimAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [get(accountAtom), get(_managedVaultAtom), "hasRedeemClaimAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _managedVaultAddr] = queryKey as Readonly<[Hex, Hex]>;

    const claimable = async (epoch: bigint) => {
      const amount = await beraborrow.managedVaults[_managedVaultAddr].managedVault.getReportBalanceOf(epoch);
      const minAmount = amount - (amount * DEFAULT_SLIPPAGE_TOLERANCE) / SCALING_FACTOR;
      try {
        const swapper: Hex = "0xcE46cefd82eb0e9572E4618dD1E6E6233b1F6Cb6"; // TODO: add swapper
        await beraborrow.managedVaults[_managedVaultAddr].managedVault.withdrawFromEpoch(epoch, minAmount, swapper);
        return { claimable: !!amount, epoch, amount, minAmount };
      } catch (_error) {
        return { claimable: false, epoch, amount, minAmount };
      }
    };

    const {
      data: { epoch },
    } = await get(EpochDetailsAtom);

    const EPOCH_STEP = 48n;
    const MAX_LOOKBACKS = 40n;
    const FIRST_EPOCH = 1940639n;

    const epochsToCheck: bigint[] = [];

    for (let i = 0n; i < MAX_LOOKBACKS; i++) {
      const currentEpoch = epoch - EPOCH_STEP * i;
      if (currentEpoch < FIRST_EPOCH) break;

      epochsToCheck.push(currentEpoch);
    }

    const claimables = await Promise.all(epochsToCheck.map(claimable));
    return claimables;
  },
  throwOnError: true,
}));

export const getCollateralManagedVaultAllowanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), get(collateralTokenAtom), get(_managedVaultAtom), , "getCollateralManagedVaultAllowanceAtom"],
  queryFn: async ({ queryKey }) => {
    const [address, collateralToken, _managedVaultAddr] = queryKey as Readonly<[Hex, Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    return beraborrow.collateralTokens[collateralToken].allowance(address, _managedVaultAddr);
  },
  throwOnError: true,
}));

export const getManagedVaultsApysAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(_managedVaultAtom), get(denManagerAtom), "getManagedVaultsApyAtom"],
  queryFn: async ({ queryKey }) => {
    const [, managedVaultAddr, _denManagerAddr] = queryKey as Readonly<[Hex, Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    const [
      { data: interestRate },
      { data: denDetails },
      { data: currentDeposit },
      { data: sNectDeposit },
      { data: sNectApy },
      { data: apy },
      { data: bbsNectApy },
      { data: underlyingAssets },
    ] = await Promise.all([
      get(getDenInterestRateAtom),
      get(getBoycoDenFullDetailsAtom),
      get(getManagedVaultDepositDetailsAtom),
      get(getsNectVaultDetailsAtom),
      get(getPoolAPYAtom),
      get(getVaultApyAtom),
      get(getBBsNectVaultApyAtom),
      get(getBBsNectPreviewRedeemUnderlyingAtom),
    ]);
    const sNectInVault = underlyingAssets.find((item) => item.contractAddress === BERABORROW_ADDRESSES.stabilityPool) as { price: bigint; contractAddress: Hex; balance: bigint };
    const sNectAmount = currentDeposit.sNect + (sNectDeposit.shares * sNectInVault.balance) / SCALING_FACTOR;
    return {
      sNectAmount,
      ...beraborrow.managedVaults[managedVaultAddr].managedVault.getApys({
        collApy: apy,
        sNectApy,
        bbsNectApy,
        debtInterest: (BigInt(interestRate || 0) * SCALING_FACTOR) / SCALING_FACTOR_BP,
        collAmount: denDetails.den.collateral,
        debtAmount: denDetails.den.debt,
        sNectAmount,
        bbsNectAmount: sNectDeposit.shares,
        collPrice: denDetails.price,
        debtPrice: currentDeposit.nectPrice,
        sNectPrice: currentDeposit.sNectPrice,
        bbsNectPrice: currentDeposit.bbsNectPrice,
      }),
    };
  },
  throwOnError: true,
}));
export const getBoycoDebtBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, get(_managedVaultAtom), BERABORROW_ADDRESSES.debtToken, "getBoycoDebtBalanceAtom"],
  queryFn: async ({ queryKey }) => {
    const [, managedVaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    const beraborrow = get(beraborrowAtom);
    return beraborrow.debtToken.getBalance(managedVaultAddr);
  },
  throwOnError: true,
}));
export const getBBsNectPreviewRedeemUnderlyingAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, Object.entries(BERABORROW_ADDRESSES.vaults).find((item) => item[1].ticker.toLowerCase() === "bb.snect")?.[0] as Hex, "previewRedeemUnderlyingAtom"],
  queryFn: async ({ queryKey }) => {
    const beraborrow = get(beraborrowAtom);
    const [, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    try {
      const assets = await beraborrow.vaults[_vaultAddr].previewRedeemUnderlying(SCALING_FACTOR, !BERABORROW_ADDRESSES.vaults[_vaultAddr].public);
      return assets ?? [];
    } catch (error) {
      return [];
    }
  },
  throwOnError: true,
}));
export const getBBsNectVaultApyAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, Object.entries(BERABORROW_ADDRESSES.vaults).find((item) => item[1].ticker.toLowerCase() === "bb.snect")?.[0] as Hex, "getVaultApyAtom"],
  queryFn: async ({ queryKey }) => {
    const [, _vaultAddr] = queryKey as Readonly<[Hex, Hex]>;
    const currentBlock = get(blockNumberAtom);
    const beraborrow = get(beraborrowAtom);
    if (beraborrow.vaults[_vaultAddr].apyType === "none") {
      return 0n;
    }
    try {
      const { data: vaultsApy } = await get(getVaultsDetailsAtom);
      const vaultApy = vaultsApy.find((item) => item.vaultAddress === _vaultAddr)?.apy;

      if (vaultApy) {
        return vaultApy;
      }
    } catch (error) {
      console.error(error);
    }

    try {
      const [apyResp, aprResp] = await Promise.allSettled([
        beraborrow.vaults[_vaultAddr].getSlotAPY(),
        axiosAPI.get<{ apr: string }>(`/infrared/vault/apr/${BERABORROW_ADDRESSES.vaults[_vaultAddr].collateral}`),
      ]);
      let apy = apyResp.status === "fulfilled" ? apyResp?.value : undefined;
      const apr = (aprResp.status === "fulfilled" && BigInt(aprResp?.value.data.apr)) || 0n;
      if (apy === undefined) {
        apy = await beraborrow.vaults[_vaultAddr].getContractAPY(currentBlock || undefined, 14400n, false);
      }
      const apyValue = apy < apr ? apr : apy;
      return apyValue > SCALING_FACTOR * 1000n ? BigInt(apyValue.toString().slice(0, 18 + 3)) : apyValue;
    } catch (error) {
      console.error(error);
    }
    const [apyResp, aprResp] = await Promise.allSettled([
      beraborrow.vaults[_vaultAddr].getContractAPY(currentBlock || undefined, 14400n, false),
      axiosAPI.get<{ apr: string }>(`/infrared/vault/apr/${BERABORROW_ADDRESSES.vaults[_vaultAddr].collateral}`),
    ]);
    const apr = BigInt((aprResp.status === "fulfilled" && aprResp?.value.data.apr) || 0);
    const apy = (apyResp.status === "fulfilled" && apyResp?.value) || 0n;
    const apyValue = apy < apr ? apr : apy;
    return apyValue > SCALING_FACTOR * 1000n ? BigInt(apyValue.toString().slice(0, 18 + 3)) : apyValue;
  },
  throwOnError: true,
  refetchOnWindowFocus: false,
  staleTime: 1000 * 60, // 60 seconds
}));

//withdraw
export const managedVaultWithdrawAmountAtom = atom<bigint>((get) => {
  try {
    const collateralDetails = get(getCollateralDetailsAtom);
    return formatNoDecimal(get(formValuesManagedVaultWithdrawAtom), collateralDetails.decimals); //TODO GET VAULT DECIMALS
  } catch (error) {
    return 0n;
  }
});
export const formValuesManagedVaultWithdrawAtom = atom<string>("0");

export const formValidationsManagedVaultWithdrawAtom = unwrap(
  atom<Promise<FormError | undefined>>(async (get) => {
    const value = get(managedVaultWithdrawAmountAtom);
    const collateralDetails = get(getCollateralDetailsAtom);

    const txState = get(transactionStateAtom);
    if (txState.type === "pending") {
      return {
        type: "transaction_in_pending",
        shortMessage: "Pending transaction",
        description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
      };
    }
    const walletConnected = get(accountAtom) !== zeroAddress;
    if (!walletConnected) {
      return {
        type: "wallet_not_connected",
      };
    }
    const { data: currentDeposit } = await get(getManagedVaultDepositDetailsAtom);
    const maxWithdraw = currentDeposit.shares;

    if (value === 0n) {
      return {
        type: "zero_value",
      };
    } else if (value > maxWithdraw) {
      return {
        type: "exceed_max_withdraw",
        shortMessage: `Exceed max withdrawal amount`,
        description: `Exceed max withdraw amount ${formatToken(maxWithdraw, collateralDetails.decimals, 0)}`,
      };
    }

    return undefined;
  })
);
//deposits
export const managedVaultDepositAmountAtom = atom<bigint>((get) => {
  const collateralDetails = get(getCollateralDetailsAtom);

  try {
    return formatNoDecimal(get(formValuesManagedVaultDepositAtom), collateralDetails.decimals); //TODO VAULT DECIMALS
  } catch (error) {
    return 0n;
  }
});

export const formValuesManagedVaultDepositAtom = atom<string>("0");

export const formValidationsManagedVaultDepositAtom = unwrap(
  atom<Promise<FormError | undefined>>(async (get) => {
    const value = get(managedVaultDepositAmountAtom);
    const txState = get(transactionStateAtom);
    const collateralDetails = get(getCollateralDetailsAtom);
    if (txState.type === "pending") {
      return {
        type: "transaction_in_pending",
        shortMessage: "Pending transaction",
        description: "Another transaction is currently being processed. Please wait for the current transaction to complete before initiating a new one.",
      };
    }
    const [{ data: available }] = await Promise.all([get(getCollateralBalanceAtom)]);
    const walletConnected = get(accountAtom) !== zeroAddress;
    if (!walletConnected) {
      return {
        type: "wallet_not_connected",
      };
    }
    if (value === 0n) {
      return {
        type: "zero_value",
      };
    } else if (available < value) {
      return {
        type: "insufficient_balance",
        shortMessage: `Insufficient ${collateralDetails.ticker} balance`,
        description: `Insufficient ${collateralDetails.ticker} balance.`,
      };
    }

    return undefined;
  })
);

//OLD BOYCO
export const boycoVaultAtom = atom<Hex | undefined>(undefined);
export const boycoVaultCollateralAtom = atom<Hex>((get) => {
  const address = get(boycoVaultAtom);
  switch (address) {
    case "******************************************":
      return "******************************************";
    case "******************************************":
      return "******************************************";
    case "******************************************":
      return "******************************************";
    case "******************************************":
      return "0xdCB3D91555385DaE23e6B966b5626aa7A75Be940";
    case "0x583Cc8a82B55A96a9dED97f5353397c85ee8b60E":
      return "0x93F4d0ab6a8B4271f4a28Db399b5E30612D21116";
    default:
      return zeroAddress;
  }
});
export const getBoycoCollateralBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [zeroAddress, get(boycoVaultAtom), "getBoycoCollateralBalanceAtom"],
  queryFn: async ({ queryKey }) => {
    const [, address] = queryKey as Readonly<[Hex, Hex]>;
    const collateralAddress = get(boycoVaultCollateralAtom);
    const beraborrow = get(beraborrowAtom);
    return beraborrow.collateralTokens[collateralAddress].getBalance(address);
  },
  throwOnError: true,
}));
export const getBoycoCollateralPriceAtom = atomWithSuspenseQuery((get) => ({
  queryKey: [zeroAddress, get(boycoVaultAtom), "getBoycoCollateralPriceAtom"],
  queryFn: async () => {
    const collateralAddress = get(boycoVaultCollateralAtom);
    const { data: prices } = await get(getDefiLlamaPriceAtom);
    const price = prices?.["berachain:" + collateralAddress] ? BigInt(prices?.["berachain:" + collateralAddress].price * Number(SCALING_FACTOR)) : 0n;
    return price;
  },
  throwOnError: true,
}));

export const getBoycoUserBalanceAtom = atomWithSuspenseQuery<bigint>((get) => ({
  queryKey: [get(accountAtom), get(boycoVaultAtom), "getBoycoUserBalanceAtom"],
  queryFn: async ({ queryKey }) => {
    const [user, address] = queryKey as Readonly<[Hex, Hex]>;
    const balance = await readContract(ANON_CLIENT, {
      address: address,
      abi: [
        {
          name: "balanceOf",
          type: "function",
          stateMutability: "view",
          inputs: [{ name: "account", type: "address" }],
          outputs: [{ name: "", type: "uint256" }],
        },
      ],
      functionName: "balanceOf",
      args: [user],
    });

    return balance;
  },

  throwOnError: true,
}));
type DefiLlamaCoins = {
  coins: {
    [key: string]: {
      decimals: number;
      price: number;
      symbol: string;
      timestamp: number;
      confidence: number;
    };
  };
};

export const getDefiLlamaPriceAtom = atomWithSuspenseQuery(() => ({
  queryKey: [zeroAddress, "getDefiLlamaPriceAtom"],
  queryFn: async () => {
    const addresses = [
      "******************************************",
      "******************************************",
      "******************************************",
      "0xdCB3D91555385DaE23e6B966b5626aa7A75Be940",
      "0x93F4d0ab6a8B4271f4a28Db399b5E30612D21116",
    ]
      .map((item) => "berachain:" + item)
      .join(",");
    const response = await fetch("https://coins.llama.fi/prices/current/" + addresses);
    if (!response.ok) throw new Error(`Fetch failed: ${response.status}`);
    const coins = (await response.json()) as DefiLlamaCoins; // Explicitly cast the parsed JSON to DefiLlamaCoins
    return coins.coins;
  },
  throwOnError: true,
}));
