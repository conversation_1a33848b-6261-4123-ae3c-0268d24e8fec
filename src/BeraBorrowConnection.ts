import { PublicClient } from "viem";
import { BeraBorrowDeploymentJSON, BeraBorrowConnection } from "./types";
import { SCALING_FACTOR } from "./constants";

export const connectionFrom = (
  publicClient: PublicClient,
  userAddress: `0x${string}`,
  { liquidationReserve, minimumDebt, ...deployment }: BeraBorrowDeploymentJSON
): BeraBorrowConnection => {
  return {
    publicClient,
    userAddress,
    liquidationReserve: BigInt(liquidationReserve) * SCALING_FACTOR,
    minimumDebt: BigInt(minimumDebt) * SCALING_FACTOR,
    ...deployment,
  };
};
