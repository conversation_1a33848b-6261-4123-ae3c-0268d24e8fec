import React from "react";
import Toggler from "../../components/Toggler";
import { Box, Button, Container, Grid, Paper, useTheme } from "@mui/material";
import DenInfo from "../../components/DenInfo/component";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import CollateralSelector from "../../components/CollateralSelector/wrapper";
import ArrowBackRoundedIcon from "@mui/icons-material/ArrowBackRounded";
import { useUrlTicker } from "../../Hooks/useUrlTicker";

const Component: React.FC = () => {
  const theme = useTheme();
  const location = useLocation();
  useUrlTicker();
  const navigate = useNavigate();
  const isDenPage = location.pathname === "/den" || location.pathname === "/den/";

  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Container
        maxWidth={isDenPage ? "xl" : "md"}
        sx={{
          py: 2,

          [theme.breakpoints.down("md")]: {
            paddingX: "8px",
          },
        }}
      >
        {isDenPage ? (
          <CollateralSelector urlRedirect={"/den/borrow"} isModal={false} />
        ) : (
          <Box display="flex" flexDirection="column" alignItems="flex-start" gap="8px" width={"100%"}>
            <Button
              onClick={() => {
                navigate("/den");
              }}
              startIcon={<ArrowBackRoundedIcon />}
              sx={{ backgroundColor: "var(--border-dark)", padding: "8px 16px", borderRadius: "48px", textDecoration: "none" }}
            >
              Back
            </Button>
            <Paper
              sx={{
                backgroundColor: "var(--background-tertiary)",
                backdropFilter: {
                  xs: "none", // No blur on mobile
                  md: "blur(5px)", // Apply blur for medium and larger screens
                },
                width: "100%",
                minHeight: "547px",
              }}
            >
              <Box>
                <Grid container spacing={1}>
                  <Grid item xs={12} md={6}>
                    <Paper sx={{ minHeight: "540px" }}>
                      <Toggler
                        togglers={[
                          { label: "Borrow", link: "/den/borrow" },
                          { label: "Manage", link: "/den/manage" },
                        ]}
                      />
                      <Outlet />
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <DenInfo />
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          </Box>
        )}
      </Container>
    </Box>
  );
};

const Den = Component; //withScopedProvider(Component, [...getDenRedeemScopedAtoms(), ...getDenBorrowScopedAtoms()]);
export default Den;
