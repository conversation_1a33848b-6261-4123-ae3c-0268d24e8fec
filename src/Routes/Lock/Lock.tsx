import { ReactNode, useEffect, useState } from "react";
import { Box, Button, Grid, IconButton, InputAdornment, Paper, TextField, Typography } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { useAtom, useAtomValue } from "jotai";
import { lockedAtom } from "../../Atoms/System";
import { profileNFTAtom } from "../../Atoms/Account";

function Lock({ children }: { children: ReactNode }) {
  const [form, setForm] = useState("");
  const [formError, setFormError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [lock, setLock] = useAtom(lockedAtom);
  const profileAsset = useAtomValue(profileNFTAtom);

  useEffect(() => {
    if (profileAsset.url) {
      setLock(false);
    }
  }, [profileAsset]);

  const handleInputChange = (value: string) => {
    setForm(value);
    setFormError("");
  };

  const handleSubmit = async () => {
    if (form === "bera420") {
      setLock(false);
    } else {
      setFormError("Incorrect");
    }
  };
  if (!lock) {
    return <>{children}</>;
  }

  return (
    <Box>
      <Paper elevation={0} variant="outlined" sx={{ textAlign: "center", width: "100%", p: 2.5 }}>
        <Typography variant="h1" mb={2}>
          UNLOCK
        </Typography>
        <Typography variant="body2" mb={2.5}>
          Enter the secret password to experience BeraBorrow.
        </Typography>
        <Box
          component="form"
          noValidate
          autoComplete="off"
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
        >
          <Grid container direction="column" spacing={2.5}>
            <Grid item>
              <TextField
                autoFocus
                margin="none"
                required
                fullWidth
                id="password-input"
                label="Password"
                placeholder="Enter the secret password"
                name="password"
                type={showPassword ? "text" : "password"}
                value={form}
                error={!!formError}
                helperText={formError}
                onChange={(e) => {
                  handleInputChange(e.target.value);
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        disabled={form.length < 1}
                        color={formError ? "error" : "default"}
                        aria-label="toggle password visibility"
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <Visibility /> : <VisibilityOff />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item>
              <Button variant="contained" color="primary" fullWidth size="medium" sx={{ px: 5 }} disabled={!form} type="submit">
                Unlock
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Box>
  );
}

export default Lock;
