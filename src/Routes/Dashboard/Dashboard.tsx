import React from "react";
import Toggler from "../../components/Toggler";
import { Box, Container, Grid, Paper, useTheme } from "@mui/material";

import { Outlet } from "react-router-dom";
import AccountOverview from "../../components/AccountOverview/Component";
import { useAccountHistoryRedirect } from "../../components/AccountHistory/Redirect";

const Component: React.FC = () => {
  const theme = useTheme();
  useAccountHistoryRedirect();
  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Container
        maxWidth="md"
        sx={{
          py: 2,

          [theme.breakpoints.down("md")]: {
            paddingX: "8px",
          },
        }}
      >
        <Paper
          sx={{
            backgroundColor: "var(--background-tertiary)",
            backdropFilter: {
              xs: "none", // No blur on mobile
              md: "blur(20px)", // Apply blur for medium and larger screens
            },
          }}
        >
          <Box>
            <Grid container spacing={1}>
              <Grid item xs={12}>
                <Box pb={1}>
                  <AccountOverview />
                </Box>
                <Paper>
                  <Toggler
                    togglers={[
                      { label: "Portfolio", link: "/dashboard/portfolio" },
                      { label: "Positions", link: "/dashboard/positions" },
                      { label: "History", link: "/dashboard/history" },
                    ]}
                  />
                  <Outlet />
                </Paper>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

const Dashboard = Component; //withScopedProvider(Component, [...getDenRedeemScopedAtoms(), ...getDenBorrowScopedAtoms()]);
export default Dashboard;
