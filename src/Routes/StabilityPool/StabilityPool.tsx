import React from "react";
import Toggler from "../../components/Toggler";
import { Outlet } from "react-router-dom";
import { Box, Container, Paper, useTheme, Grid } from "@mui/material";
import PoolInfo from "../../components/PoolInfo/component";

const Component: React.FC = () => {
  const theme = useTheme();
  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Container
        maxWidth="md"
        sx={{
          py: 2,

          [theme.breakpoints.down("md")]: {
            paddingX: "8px",
          },
        }}
      >
        <Paper
          sx={{
            backgroundColor: "var(--background-tertiary)",
            padding: "10px",
            backdropFilter: {
              xs: "none", // No blur on mobile
              md: "blur(5px)", // Apply blur for medium and larger screens
            },
          }}
        >
          <Box>
            <Grid container spacing={1}>
              <Grid item xs={12} md={6}>
                <Paper>
                  <Toggler
                    togglers={[
                      { label: "Deposit", link: "/pool/deposit" },
                      { label: "Withdraw", link: "/pool/withdraw" },
                    ]}
                  />
                  <Outlet />
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <PoolInfo />
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};
const StabilityPool = Component; //withScopedProvider(Component, [...getPoolWithdrawScopedAtoms(), ...getPoolDepositScopedAtoms()]);
export default StabilityPool;
