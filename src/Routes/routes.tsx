import { wrapCreateBrowserRouterV6 } from "@sentry/react";
import { useAtomValue } from "jotai";
import { createBrowserRouter, Navigate, RouterProvider } from "react-router-dom";
import { NavigationOptions } from "../@type/Account";
import { simpleModeAtom } from "../Atoms/Account";
import AccountHistory from "../components/AccountHistory/Component";
import AppError from "../components/AppError";
import DenBorrow from "../components/DenBorrow/component";
import ManagedVaultDeposit from "../components/ManagedVault/VaultDeposit/component";
import ManagedVaultWithdraw from "../components/ManagedVault/VaultWithdraw/component";
import PoolDeposit from "../components/PoolDeposit/component";
import PoolWithdraw from "../components/PoolWithdraw/component";
import Portfolio from "../components/Portfolio/Component";
import Positions from "../components/Positions/Component";
import GenericLockForm from "../components/Stake/LockForm/GenericLockForm";
import WrapForm from "../components/Stake/WrapForm";
import LiquidStakeForm from "../components/Stake/LiquidStakeForm";
import LpLiquidStakeForm from "../components/Stake/LpLiquidStakeForm";
import VaultDeposit from "../components/VaultDeposit/component";
import VaultWithdraw from "../components/VaultWithdraw/component";
import OutletProvider from "../Layout/OutletProvider";
import Dashboard from "./Dashboard/Dashboard";
import Den from "./Den/Den";
import Home from "./Home/component";
import ManagedVault from "./ManagedVault/ManagedVault";
import Redeem from "./Redeem/Redeem";
import StabilityPool from "./StabilityPool/StabilityPool";
import Stake from "./Stake/Stake";
import StakeWrap from "./Stake/StakeWrap";
import StakeLocking from "./Stake/StakeLocking";
import StakeLpLocking from "./Stake/StakeLpLocking";
import StakeLiquid from "./Stake/StakeLiquid";
import StakeLpLiquid from "./Stake/StakeLpLiquid";
import Vault from "./Vault/Vault";
// import Rewards from "./Rewards/Component";

function ConnectedRoutes() {
  const simpleMode = useAtomValue(simpleModeAtom);

  const routes = wrapCreateBrowserRouterV6(createBrowserRouter)([
    {
      errorElement: <AppError />,
      path: "/",
      element: <OutletProvider />,
      children: [
        {
          path: "/",
          element: simpleMode ? <Navigate to={"/den"} /> : <Home />,
        },
        {
          path: "/stake",
          children: [
            { path: "", element: <Stake /> },
            {
              path: "locking",
              element: <StakeLocking />,
              children: [
                { path: "", element: <Navigate to="lock" replace={true} /> },
                { path: "lock", element: <GenericLockForm tokenType="spollen" /> },
                { path: "unlock", element: <GenericLockForm tokenType="spollen" /> },
              ],
            },
            {
              path: "lp-locking",
              element: <StakeLpLocking />,
              children: [
                { path: "", element: <Navigate to="lock" replace={true} /> },
                { path: "lock", element: <GenericLockForm tokenType="lp" /> },
                { path: "unlock", element: <GenericLockForm tokenType="lp" /> },
              ],
            },
            {
              path: "wrapping",
              element: <StakeWrap />,
              children: [
                { path: "", element: <Navigate to="wrap" replace={true} /> },
                { path: "wrap", element: <WrapForm /> },
                { path: "unwrap", element: <WrapForm /> },
              ],
            },
            {
              path: "liquid",
              element: <StakeLiquid />,
              children: [
                { path: "", element: <Navigate to="stake" replace={true} /> },
                { path: "stake", element: <LiquidStakeForm /> },
                { path: "unstake", element: <LiquidStakeForm /> },
              ],
            },
            {
              path: "lp-liquid",
              element: <StakeLpLiquid />,
              children: [
                { path: "", element: <Navigate to="stake" replace={true} /> },
                { path: "stake", element: <LpLiquidStakeForm /> },
                { path: "unstake", element: <LpLiquidStakeForm /> },
              ],
            },
          ],
        },
        {
          path: "/boyco",
          element: <Navigate to={"/managed-vaults"} replace={true} />,
        },

        {
          path: "/managed-vault",
          element: <ManagedVault />,
          children: [
            {
              path: "deposit",
              children: [
                { path: "", element: <ManagedVaultDeposit /> },
                {
                  path: ":ticker",
                  element: <ManagedVaultDeposit />,
                },
              ],
            },
            {
              path: "withdraw",
              children: [
                { path: "", element: <ManagedVaultWithdraw /> },
                {
                  path: ":ticker",
                  element: <ManagedVaultWithdraw />,
                },
              ],
            },
          ],
        },
        {
          path: "/den",
          element: <Den />,
          children: [
            {
              path: "borrow",
              children: [
                { path: "", element: <DenBorrow borrowAction={"borrow"} /> },
                {
                  path: ":ticker",
                  element: <DenBorrow borrowAction={"borrow"} />,
                },
              ],
            },
            {
              path: "manage",
              children: [
                { path: "", element: <DenBorrow borrowAction={"withdraw"} /> },
                {
                  path: ":ticker",
                  element: <DenBorrow borrowAction={"withdraw"} />,
                },
              ],
            },
          ],
        },
        {
          path: "/swap",
          element: <Navigate to={"/redeem"} replace={true} />,
        },
        {
          path: "/redeem",
          children: [
            { path: "", element: <Redeem /> },
            {
              path: ":ticker",
              element: <Redeem />,
            },
          ],
        },
        {
          path: "/pool",
          element: <StabilityPool />,
          children: [
            { path: "", element: <Navigate to="deposit" replace={true} /> }, //deposit redirect when activated
            { path: "deposit", element: <PoolDeposit /> },
            { path: "withdraw", element: <PoolWithdraw /> },
          ],
        },
        {
          path: "/vault",
          element: <Vault />,
          children: [
            {
              path: "deposit",
              children: [
                { path: "", element: <VaultDeposit /> },
                {
                  path: ":ticker",
                  element: <VaultDeposit />,
                },
              ],
            },
            {
              path: "withdraw",
              children: [
                { path: "", element: <VaultWithdraw /> },
                {
                  path: ":ticker",
                  element: <VaultWithdraw />,
                },
              ],
            },
          ],
        },
        {
          path: "/dashboard",
          element: <Dashboard />,
          children: [
            { path: "", element: <Navigate to="positions" replace /> },
            {
              path: "portfolio",
              element: <Portfolio />,
            },
            {
              path: "positions",
              element: <Positions />,
            },
            {
              path: "history",
              children: [
                { path: "", element: <AccountHistory /> },
                {
                  path: ":filter",
                  element: <AccountHistory />,
                },
                {
                  path: ":filter/:date",
                  element: <AccountHistory />,
                },
              ],
              element: <AccountHistory />,
            },
          ],
        },
        // {
        //   path: "/rewards",
        //   element: <Rewards />,
        // },

        {
          path: "*",
          element: <Navigate to="/" replace />,
        },
      ],
    },
  ]);

  return <RouterProvider router={routes} />;
}

/**
 * sets all routes for App
 */
export function Routes() {
  return <>{<ConnectedRoutes />}</>;
}

export const routerPaths: {
  id: NavigationOptions;
  label: string;
  path: string;
  parentPath?: string;
  ticker?: boolean;
  icon: string;
  description: string;
}[] = [
  {
    id: "borrow",
    label: "Borrow",
    path: "/den",
    ticker: false,
    parentPath: "/den",
    icon: "/imgs/borrow.png",
    description: "Deposit Collateral to borrow NECT",
  },
  {
    id: "stake",
    label: "Stake",
    path: "/stake",
    icon: "/imgs/boyco.png",
    description: "Stake Pollen",
  },

  {
    id: "pool",
    label: "Pool",
    path: "/pool/deposit",
    parentPath: "/pool",
    icon: "/imgs/pool.png",
    description: "Deposit NECT to earn",
  },
  {
    id: "redeem",
    label: "Redeem",
    path: "/redeem/honey",
    parentPath: "/redeem",
    icon: "/imgs/swap.png",
    description: "Swap NECT to Collaterals",
  },
  {
    id: "default",
    label: "Managed Vaults",
    path: "/managed-vault",
    icon: "/imgs/managed-vaults.png",
    description: "Coming Thoon",
  },
  {
    id: "vault",
    label: "Vaults",
    path: "/vault",
    ticker: false,
    parentPath: "/vault",
    icon: "/imgs/vault.png",
    description: "Auto compounding Vault",
  },
  {
    id: "dashboard",
    label: "Dashboard",
    path: "/dashboard/positions",
    parentPath: "/dashboard",
    icon: "/imgs/dashboard.png",
    description: "Manage Your Positions",
  },
];
