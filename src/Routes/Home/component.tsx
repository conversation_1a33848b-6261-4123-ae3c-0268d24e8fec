import React, { useEffect } from "react";
import { Box, Paper, Divider, Typography, Grid } from "@mui/material";

import Drawer from "../../Layout/Drawer";
import { useSetAtom } from "jotai";
import { navigationAtom } from "../../Atoms/Account";
import { preloadImage } from "../../utils/helpers";

const Component: React.FC = () => {
  const setNavigation = useSetAtom(navigationAtom);

  useEffect(() => {
    ["/background/landing/default.webp", "/background/landing/redeem.webp", "/background/landing/borrow.webp", "/background/landing/pool.webp"].map((item) => preloadImage(item));
    return () => {
      setNavigation("default");
    };
  }, []);
  return (
    <Box
      sx={{
        width: "100%",
        flex: 1,
        display: "flex",
        pb: 2,
        px: { xs: 1, lg: 5, xl: 10 },
      }}
    >
      <Grid container spacing={1} justifyContent="flex-start">
        <Grid item xs={12} lg={4} xl={3}>
          <Paper
            sx={{
              backdropFilter: {
                xs: "blur(5px)", // Apply blur for medium and larger screens
              },
            }}
          >
            <Box>
              <Typography variant="h1" textAlign={"center"}>
                Welcome!
              </Typography>
              <Divider sx={{ backgroundColor: "var(--border-color)", my: { xs: 1, sm: 1, xl: 1.5 } }} />

              <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 1 }}>
                <Drawer />
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

const Home = Component;
export default Home;
