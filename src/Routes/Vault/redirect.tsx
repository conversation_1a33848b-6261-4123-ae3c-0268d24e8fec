import { useAtomValue, useAtom, useSetAtom } from "jotai";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { getCollateralTokensAtom, collateralTypeSelectorAtom } from "../../Atoms/Tokens";
import { useEffect } from "react";
import { zeroAddress } from "viem";
import { vaultAtom } from "../../Atoms/Vault";
import { publicVaultsAtom } from "../../Atoms/Vault";
import { extractTickerProvider } from "../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../utils/constants";

export const useVaultRedirect = (vaultType: string) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { ticker: _tickerParam } = useParams();
  const { ticker: tickerParam, provider: providerParam } = extractTickerProvider(_tickerParam);
  const collaterals = useAtomValue(getCollateralTokensAtom);
  const publicVaults = useAtomValue(publicVaultsAtom);
  const setCollateralTypeSelector = useSetAtom(collateralTypeSelectorAtom);
  const [vaultAddr, setVaultAddr] = useAtom(vaultAtom);
  const collateral = collaterals.find(
    (item) => (providerParam ? providerParam.toLowerCase() === item?.lpToken?.provider.toLowerCase() : true) && item.ticker.toLowerCase() === tickerParam?.toLowerCase()
  )?.contractAddress;
  const newVault = publicVaults.find((item) => item.collateralAddress === (collateral || zeroAddress))?.contractAddress;

  const currentVault = publicVaults.find((item) => item.contractAddress === vaultAddr);
  const isVaultPage = location.pathname === "/vault" || location.pathname === "/vault/";

  const _collateral = vaultAddr ? BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.vaults[vaultAddr].collateral] : undefined;
  const newTicker = _collateral ? (_collateral?.lpToken ? _collateral.lpToken.provider + "-" : "") + _collateral?.ticker : undefined;
  useEffect(() => {
    if (newVault) {
      setVaultAddr(newVault);
    } else if ((vaultAddr !== undefined && tickerParam === undefined) || currentVault?.ticker?.toLowerCase() !== tickerParam?.toLowerCase()) {
      currentVault && setVaultAddr(currentVault?.contractAddress);

      isVaultPage
        ? navigate("/vault", { replace: true })
        : navigate(`/vault/${vaultType}/${(newTicker ? newTicker : publicVaults[0].ticker).replace("BB.", "")}`, { replace: true });
    }
    setCollateralTypeSelector("vault");
    return () => {
      if (vaultAddr && !location.pathname.startsWith("/vault")) {
        setVaultAddr(undefined);
        setCollateralTypeSelector("den");
      }
    };
  }, [vaultAddr, publicVaults, tickerParam, newVault, location.pathname]);
};
