import React from "react";
import Toggler from "../../components/Toggler";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { Box, Container, Paper, useTheme, Grid, Typography, IconButton, useMediaQuery } from "@mui/material";
import VaultInfo from "../../components/VaultInfo/component";
import { useVaultRedirect } from "../Vault/redirect";
import CollateralSelector from "../../components/CollateralSelector/wrapper";
import ArrowBackRoundedIcon from "@mui/icons-material/ArrowBackRounded";
import { useAtomValue } from "jotai";
import { vaultAtom } from "../../Atoms/Vault";

const Component: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  useVaultRedirect(location.pathname.includes("withdraw") ? "withdraw" : "deposit");
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  const vaultAddr = useAtomValue(vaultAtom);
  const isVaultPage = location.pathname === "/vault" || location.pathname === "/vault/";
  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Container
        maxWidth="md"
        sx={{
          py: 2,

          [theme.breakpoints.down("md")]: {
            paddingX: "8px",
          },
        }}
      >
        {isVaultPage ? (
          <CollateralSelector urlRedirect={"/vault/deposit"} excludeWrappedCollateral excludeDenManagers includePublicVaults isModal={false} />
        ) : (
          <Box>
            <Paper
              sx={{
                position: "relative",
                mb: 2,
                backgroundColor: "var(--background-tertiary)",
                backdropFilter: {
                  xs: "none", // No blur on mobile
                  md: "blur(5px)", // Apply blur for medium and larger screens
                  alignContent: "center",
                },
                p: 2,
                px: 3,
                [theme.breakpoints.down("md")]: {
                  paddingX: "12px",
                },
                borderRadius: "20px",
                display: "flex", // Use flexbox for centering
                justifyContent: "center", // Center content horizontally
                alignItems: "center", // Center content vertically
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <img src="/icons/vaulted.png" height={"16px"} />
                <Typography variant="h4" textAlign={{ xs: "center", sm: "left" }}>
                  Auto-Compounding Infrared Vaults,
                  <Box component={"br"} sx={{ display: { md: "none" } }} /> Now Earning{" "}
                  {(vaultAddr === "0x933bD39CeDb5947523110c33E98bEa8480978adB" || vaultAddr === "0x33aD23316A0C3dd999d4AeDe4fe99074DE42324b") && "1.375x"} Infrared Points
                </Typography>
                <IconButton onClick={() => navigate("/vault")} sx={{ color: "white", display: "flex", alignItems: "center", position: "absolute", left: isMdUp ? "16px" : 0 }}>
                  <ArrowBackRoundedIcon />
                </IconButton>
                {/* <Link href="" target="_blank">
              <Typography variant="h4">Read More</Typography>
            </Link> */}
              </Box>
            </Paper>
            <Paper
              sx={{
                backgroundColor: "var(--background-tertiary)",
                padding: "10px",
                backdropFilter: {
                  xs: "none", // No blur on mobile
                  md: "blur(5px)", // Apply blur for medium and larger screens
                },
              }}
            >
              <Box>
                <Grid container spacing={1}>
                  <Grid item xs={12} md={6}>
                    <Paper>
                      <Toggler
                        togglers={[
                          { label: "Deposit", link: "/vault/deposit" },
                          { label: "Withdraw", link: "/vault/withdraw" },
                        ]}
                      />
                      <Outlet />
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <VaultInfo />
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          </Box>
        )}
      </Container>
    </Box>
  );
};
const Vault = Component; //withScopedProvider(Component, [...getVaultWithdrawScopedAtoms(), ...getVaultDepositScopedAtoms()]);
export default Vault;
