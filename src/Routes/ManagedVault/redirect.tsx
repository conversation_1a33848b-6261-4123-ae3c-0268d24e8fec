import { useAtomValue, useAtom, useSetAtom } from "jotai";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { getCollateralTokensAtom, collateralTypeSelectorAtom } from "../../Atoms/Tokens";
import { useEffect } from "react";
import { zeroAddress } from "viem";
import { extractTickerProvider } from "../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { managedVaultsAtom, managedVaultAtom } from "../../Atoms/Boyco";
import { denManagerAtom } from "../../Atoms/Den";

export const useManagedVaultRedirect = (vaultType: string) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { ticker: _tickerParam } = useParams();
  const { ticker: tickerParam, provider: providerParam } = extractTickerProvider(_tickerParam);
  const collaterals = useAtomValue(getCollateralTokensAtom);
  const managedVaults = useAtomValue(managedVaultsAtom);
  const setDenManager = useSetAtom(denManagerAtom);
  const setCollateralTypeSelector = useSetAtom(collateralTypeSelectorAtom);
  const [vaultAddr, setVaultAddr] = useAtom(managedVaultAtom);
  const collateral = collaterals.find(
    (item) => (providerParam ? providerParam.toLowerCase() === item?.lpToken?.provider.toLowerCase() : true) && item.ticker.toLowerCase() === tickerParam?.toLowerCase()
  )?.contractAddress;
  const newVault = managedVaults.find((item) => item.collateralAddress === (collateral || zeroAddress))?.contractAddress;
  const currentVault = managedVaults.find((item) => item.contractAddress === vaultAddr);

  const isVaultPage = location.pathname === "/managed-vault" || location.pathname === "/managed-vault/";

  const _collateral = vaultAddr ? BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.managedVaults[vaultAddr].collateral] : undefined;
  const newTicker = _collateral ? (_collateral?.lpToken ? _collateral.lpToken.provider + "-" : "") + _collateral?.ticker : undefined;
  useEffect(() => {
    if (newVault) {
      setDenManager(BERABORROW_ADDRESSES.managedVaults[newVault].denManager);
      setCollateralTypeSelector("managedVault");
      setVaultAddr(newVault);
    } else if (
      (vaultAddr !== undefined && tickerParam === undefined) ||
      (currentVault !== undefined && BERABORROW_ADDRESSES.collateralTokens[currentVault.collateralAddress]?.ticker?.toLowerCase()) !== tickerParam?.toLowerCase()
    ) {
      if (currentVault) {
        setVaultAddr(currentVault.contractAddress);
        setDenManager(currentVault.denManagerAddress);
        setCollateralTypeSelector("managedVault");
      }

      isVaultPage
        ? navigate("/managed-vault", { replace: true })
        : navigate(`/managed-vault/${vaultType}/${newTicker ? newTicker : BERABORROW_ADDRESSES.collateralTokens[managedVaults[0].collateralAddress]?.ticker}`, { replace: true });
    }
    setCollateralTypeSelector("managedVault");
    return () => {
      if (vaultAddr && !location.pathname.startsWith("/managed-vault")) {
        setVaultAddr(undefined);
        setCollateralTypeSelector("den");
      }
    };
  }, [vaultAddr, managedVaults, tickerParam, newVault, location.pathname]);
};
