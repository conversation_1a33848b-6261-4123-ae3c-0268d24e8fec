import React, { useRef } from "react";
import Toggler from "../../components/Toggler";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { Box, Container, Paper, useTheme, Grid, Typography, Button } from "@mui/material";
import CollateralSelector from "../../components/CollateralSelector/wrapper";
import ArrowBackRoundedIcon from "@mui/icons-material/ArrowBackRounded";
import { useManagedVaultRedirect } from "./redirect";
// import HelpOutlineRoundedIcon from "@mui/icons-material/HelpOutlineRounded";
import ManagedVaultInfo from "../../components/ManagedVault/VaultInfo/component";

const Component: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  useManagedVaultRedirect(location.pathname.includes("withdraw") ? "withdraw" : "deposit");
  const infoRef = useRef<HTMLDivElement>(null);
  const isVaultPage = location.pathname === "/managed-vault" || location.pathname === "/managed-vault/";
  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Container
        maxWidth={isVaultPage ? "xl" : "md"}
        sx={{
          py: 2,

          [theme.breakpoints.down("md")]: {
            paddingX: "8px",
          },
        }}
      >
        {isVaultPage ? (
          <CollateralSelector urlRedirect={"/managed-vault/deposit"} excludeWrappedCollateral excludeDenManagers includeManagedVaults isModal={false} />
        ) : (
          <Box>
            <Box display="flex" mb="16px" justifyContent="space-between" alignItems="center">
              <Button
                onClick={() => {
                  navigate("/managed-vault");
                }}
                startIcon={<ArrowBackRoundedIcon />}
                sx={{ backgroundColor: "var(--border-dark)", padding: "8px 16px", borderRadius: "48px", textDecoration: "none" }}
              >
                Back
              </Button>
              <Typography variant="h1" fontWeight={600} fontSize={28}>
                Managed Vaults
              </Typography>
              <Box></Box>
              {/* <Button
                // onClick={() => {}}
                startIcon={<HelpOutlineRoundedIcon />}
                sx={{ backgroundColor: "var(--border-dark)", padding: "8px 16px", borderRadius: "48px", textDecoration: "none" }}
              >
                Help
              </Button> */}
            </Box>
            <Paper
              sx={{
                backgroundColor: "var(--background-tertiary)",
                padding: "10px",
                backdropFilter: {
                  xs: "none", // No blur on mobile
                  md: "blur(5px)", // Apply blur for medium and larger screens
                },
              }}
            >
              <Box>
                <Grid container spacing={1}>
                  <Grid item xs={12} md={6}>
                    <Paper
                      sx={{
                        height: "100%",
                      }}
                    >
                      <Toggler
                        togglers={[
                          { label: "Deposit", link: "/managed-vault/deposit" },
                          { label: "Withdraw", link: "/managed-vault/withdraw" },
                        ]}
                      />
                      <Outlet />
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={6} ref={infoRef}>
                    <ManagedVaultInfo />
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          </Box>
        )}
      </Container>
    </Box>
  );
};
const ManagedVault = Component; //withScopedProvider(Component, [...getVaultWithdrawScopedAtoms(), ...getVaultDepositScopedAtoms()]);
export default ManagedVault;
