import { Box, Container, Grid, Typography } from "@mui/material";
import StakeItem from "../../components/Stake/StakeItem/Component";
import { AprMultiplierSection } from "./StakeItemSections/AprMultiplierSection";
import { ExchangeRateSection } from "./StakeItemSections/ExchangeRateSection";
import { TvlAprSection } from "./StakeItemSections/TvlAprSection";

const Component = () => {
  return (
    <Box sx={{ flex: 1, marginBottom: "100px" }}>
      <Container maxWidth="lg" sx={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
        <Typography
          color={"var(--text-primary)"}
          sx={{
            marginTop: "80px",
            marginBottom: "80px",
            fontWeight: 700,
            fontSize: 36,
          }}
        >
          Staking
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6} lg={4}>
            <StakeItem type="wrap" statsSection={<ExchangeRateSection rate="1:1" />} />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <StakeItem type="stake-spollen" statsSection={<TvlAprSection tvl="$9.82M" apr="1.23%" />} />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <StakeItem type="stake-bera-pollen" statsSection={<TvlAprSection tvl="$9.82M" apr="1.23%" />} />
          </Grid>
        </Grid>

        <Typography
          color={"var(--text-primary)"}
          sx={{
            marginTop: "80px",
            marginBottom: "80px",
            fontWeight: 700,
            fontSize: 36,
            textAlign: "center",
          }}
        >
          Locking
        </Typography>

        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6} lg={4}>
              <StakeItem type="lock-spollen" statsSection={<AprMultiplierSection apr="1.23-4.56%" multiplier="1.25-4X" />} />
            </Grid>
            <Grid item xs={12} md={6} lg={4}>
              <StakeItem type="lock-bera-pollen" statsSection={<AprMultiplierSection apr="1.23-4.56%" multiplier="1.25-4X" />} />
            </Grid>
          </Grid>
        </Container>
      </Container>
    </Box>
  );
};

const StakeRoute = Component;
export default StakeRoute;
