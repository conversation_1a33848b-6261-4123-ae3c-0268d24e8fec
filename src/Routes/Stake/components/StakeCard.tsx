import { Box, Typography, SxProps, Theme } from "@mui/material";
import { ReactNode } from "react";

interface StakeCardProps {
  title: string;
  value: string | ReactNode;
  valueColor?: string;
  height?: string | number;
  sx?: SxProps<Theme>;
}

export const StakeCard = ({ title, value, valueColor = "#ffedd4", height = "60px", sx = {} }: StakeCardProps) => {
  return (
    <Box
      sx={{
        backgroundColor: "#241E1B80",
        borderRadius: "12px",
        py: "10px",
        height,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        ...sx,
      }}
    >
      <Typography sx={{ color: "#8D7366", fontSize: "12px", mb: "6px" }}>{title}</Typography>
      <Typography sx={{ color: valueColor, fontSize: "16px", fontWeight: 600 }}>{value}</Typography>
    </Box>
  );
};
