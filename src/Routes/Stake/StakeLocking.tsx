import React from "react";
import { StakeLayout } from "../../components/Stake/StakeLayout";
import GenericLockInfo from "../../components/Stake/shared/GenericLockInfo";
import { useStakeRedirect } from "./redirect";

const LockInfoComponent = () => <GenericLockInfo tokenType="spollen" />;

const Component: React.FC = () => {
  useStakeRedirect();

  return (
    <StakeLayout
      title="Lock sPOLLEN"
      description="Lock your sPOLLEN to receive vePOLLEN and protocol benefits"
      togglers={[
        { label: "Lock", link: "/stake/locking/lock" },
        { label: "UnLock", link: "/stake/locking/unlock" },
      ]}
      InfoComponent={LockInfoComponent}
    />
  );
};

const StakeLocking = Component;
export default StakeLocking;
