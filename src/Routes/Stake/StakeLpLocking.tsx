import React from "react";
import { StakeLayout } from "../../components/Stake/StakeLayout";
import GenericLockInfo from "../../components/Stake/shared/GenericLockInfo";
import { useStakeRedirect } from "./redirect";

const LpLockInfoComponent = () => <GenericLockInfo tokenType="lp" />;

const Component: React.FC = () => {
  useStakeRedirect();

  return (
    <StakeLayout
      title="Lock BERA-POLLEN"
      description="Lock your BERA-POLLEN to receive veBERA-POLLEN and protocol benefits"
      togglers={[
        { label: "Lock", link: "/stake/lp-locking/lock" },
        { label: "UnLock", link: "/stake/lp-locking/unlock" },
      ]}
      InfoComponent={LpLockInfoComponent}
    />
  );
};

const StakeLpLocking = Component;
export default StakeLpLocking;
