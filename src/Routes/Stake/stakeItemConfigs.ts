export type StakeItemType = "wrap" | "stake-spollen" | "stake-bera-pollen" | "lock-spollen" | "lock-bera-pollen";

interface IconConfig {
  path: string;
  alt: string;
  backgroundColor: string;
}

interface BadgeConfig {
  label: string;
  backgroundColor: string;
  borderColor: string;
  secondary?: string;
}

interface BottomLinkConfig {
  text: string;
  href: string;
  icons: string[];
}

interface StakeItemConfig {
  badge: BadgeConfig;
  icons: IconConfig[];
  showArrow?: boolean;
  title: string;
  description: string;
  buttonLabel: string;
  route: string;
  bottomLink?: BottomLinkConfig;
}

export const stakeItemConfigs: Record<StakeItemType, StakeItemConfig> = {
  wrap: {
    badge: {
      label: "Wrap",
      backgroundColor: "#c1a100",
      borderColor: "#ECE81533",
    },
    icons: [
      {
        path: "/icons/pollen.png",
        alt: "POLLEN",
        backgroundColor: "#ece815",
      },
      {
        path: "/icons/spollen.png",
        alt: "sPOLLEN",
        backgroundColor: "#ec6f15",
      },
    ],
    showArrow: true,
    title: "Wrap POLLEN",
    description: "Wrap your POLLEN to sPOLLEN to use single-sided staking and locking",
    buttonLabel: "Wrap",
    route: "/stake/wrapping/wrap",

    bottomLink: {
      text: "Get POLLEN",
      href: "#",
      icons: ["/icons/pollen.png"],
    },
  },
  "stake-spollen": {
    badge: {
      label: "Stake",
      backgroundColor: "#3db250",
      borderColor: "#15EC2A33",
      secondary: "Auto-compounding",
    },
    icons: [
      {
        path: "/icons/spollen.png",
        alt: "sPOLLEN",
        backgroundColor: "#ec6f15",
      },
    ],
    title: "Stake sPOLLEN",
    description: "Stake your sPOLLEN to earn POLLEN rewards",
    buttonLabel: "Stake",
    route: "/stake/liquid/stake",
    bottomLink: {
      text: "Get sPOLLEN",
      href: "#",
      icons: ["/icons/spollen.png"],
    },
  },
  "stake-bera-pollen": {
    badge: {
      label: "Stake",
      backgroundColor: "#3db250",
      borderColor: "#15EC2A33",
      secondary: "Auto-compounding",
    },
    icons: [
      {
        path: "/icons/wbera.png",
        alt: "BERA",
        backgroundColor: "#fff",
      },
      {
        path: "/icons/pollen.png",
        alt: "POLLEN",
        backgroundColor: "#ece815",
      },
    ],
    title: "Stake BERA-POLLEN",
    description: "Stake your BERA-POLLEN to earn POLLEN rewards",
    buttonLabel: "Stake",
    route: "/stake/lp-liquid/stake",
    bottomLink: {
      text: "Get BERA-POLLEN",
      href: "#",
      icons: ["/icons/wbera.png", "/icons/pollen.png"],
    },
  },
  "lock-spollen": {
    badge: {
      label: "Lock",
      backgroundColor: "#ec6f15",
      borderColor: "#EC6F1533",
    },
    icons: [
      {
        path: "/icons/spollen.png",
        alt: "sPOLLEN",
        backgroundColor: "#ec6f15",
      },
    ],
    title: "Lock sPOLLEN",
    description: "Lock your sPOLLEN up to a year receive a share of buybacks and protocol fees in POLLEN, and accrue voting power",
    buttonLabel: "Lock",
    route: "/stake/locking/lock",
  },
  "lock-bera-pollen": {
    badge: {
      label: "Lock",
      backgroundColor: "#ec6f15",
      borderColor: "#EC6F1533",
    },
    icons: [
      {
        path: "/icons/wbera.png",
        alt: "BERA",
        backgroundColor: "#fff",
      },
      {
        path: "/icons/pollen.png",
        alt: "POLLEN",
        backgroundColor: "#ece815",
      },
    ],
    title: "Lock BERA-POLLEN",
    description: "Lock your BERA-POLLEN up to receive a share of buybacks and protocol fees in BERA-POLLEN, and accrue voting power",
    buttonLabel: "Lock",
    route: "/stake/lp-locking/lock",
  },
};
