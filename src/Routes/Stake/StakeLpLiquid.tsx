import { StakeLayout } from "../../components/Stake/StakeLayout";
import LpStakeLiquidInfo from "../../components/Stake/LpStakeLiquidInfo";
import { useStakeRedirect } from "./redirect";

const Component = () => {
  useStakeRedirect();

  return (
    <StakeLayout
      title="LP Liquid Staking"
      description="Stake or unstake your LP tokens for liquid staking rewards"
      togglers={[
        { label: "Stake", link: "/stake/lp-liquid/stake" },
        { label: "Unstake", link: "/stake/lp-liquid/unstake" },
      ]}
      InfoComponent={LpStakeLiquidInfo}
    />
  );
};

const StakeLpLiquid = Component;
export default StakeLpLiquid;
