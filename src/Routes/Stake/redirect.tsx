import { useHydrateAtoms } from "jotai/utils";
import { useLocation } from "react-router-dom";
import { collateralTypeSelectorAtom, stakingTokenAtom } from "../../Atoms/Tokens";
import { BERABORROW_ADDRESSES } from "../../utils/constants";

export const useStakeRedirect = () => {
  const location = useLocation();

  const getStakingTokenAddress = () => {
    if (location.pathname.includes("/wrap")) {
      return BERABORROW_ADDRESSES.pollenToken.contractAddress;
    } else if (location.pathname.includes("/unwrap")) {
      return BERABORROW_ADDRESSES.sPollenToken.contractAddress;
    } else if (location.pathname.includes("/liquid")) {
      return BERABORROW_ADDRESSES.sPollenToken.contractAddress;
    } else if (location.pathname.includes("/locking")) {
      return BERABORROW_ADDRESSES.sPollenToken.contractAddress;
    } else if (location.pathname.includes("/bera-pollen") || location.pathname.includes("/lp-")) {
      return BERABORROW_ADDRESSES.pollenLpToken.contractAddress;
    }
    return BERABORROW_ADDRESSES.pollenToken.contractAddress;
  };

  if (location.pathname.startsWith("/stake")) {
    useHydrateAtoms([
      [stakingTokenAtom, getStakingTokenAddress()],
      [collateralTypeSelectorAtom, "staking"],
    ]);
  }
};
