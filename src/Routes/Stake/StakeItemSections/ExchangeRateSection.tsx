import { Box, Typography } from "@mui/material";

interface ExchangeRateSectionProps {
  rate: string;
}

export const ExchangeRateSection = ({ rate }: ExchangeRateSectionProps) => {
  return (
    <Box
      sx={{
        borderRadius: "12px",
        pt: "10px",
        pb: "20px",
        textAlign: "center",
        backgroundColor: "#241E1B80",
        width: "100%",
        height: "60px",
      }}
    >
      <Typography
        sx={{
          color: "#8D7366",
          fontSize: "12px",
          mb: "6px",
        }}
      >
        Exchange Rate:
      </Typography>
      <Typography
        sx={{
          color: "#FFEDD4",
          fontSize: "16px",
          fontWeight: 600,
        }}
      >
        {rate}
      </Typography>
    </Box>
  );
};
