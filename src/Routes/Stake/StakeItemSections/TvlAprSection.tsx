import { Grid } from "@mui/material";
import { StakeCard } from "../components/StakeCard";

interface TvlAprSectionProps {
  tvl: string;
  apr: string;
}

export const TvlAprSection = ({ tvl, apr }: TvlAprSectionProps) => {
  return (
    <Grid container spacing={2}>
      <Grid item xs={6}>
        <StakeCard title="TVL" value={tvl} />
      </Grid>
      <Grid item xs={6}>
        <StakeCard title="Current APR" value={apr} valueColor="#3db250" height="100%" />
      </Grid>
    </Grid>
  );
};
