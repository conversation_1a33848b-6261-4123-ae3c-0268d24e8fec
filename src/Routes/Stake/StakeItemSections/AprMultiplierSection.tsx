import { Box, Grid, Typography } from "@mui/material";

interface AprMultiplierSectionProps {
  apr: string;
  multiplier: string;
}

export const AprMultiplierSection = ({ apr, multiplier }: AprMultiplierSectionProps) => {
  return (
    <Grid container spacing={2}>
      <Grid item xs={6}>
        <Box
          sx={{
            backgroundColor: "#241E1B80",
            borderRadius: "12px",
            py: "10px",
            height: "60px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Typography sx={{ color: "#8D7366", fontSize: "12px", mb: "6px" }}>APR</Typography>
          <Typography sx={{ color: "#3db250", fontSize: "16px", fontWeight: 600 }}>{apr}</Typography>
        </Box>
      </Grid>

      <Grid item xs={6}>
        <Box
          sx={{
            backgroundColor: "#241E1B80",
            borderRadius: "12px",
            py: "10px",
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Typography sx={{ color: "#8D7366", fontSize: "12px", mb: "6px" }}>Multiplier</Typography>
          <Typography sx={{ color: "#ec6f15", fontSize: "16px", fontWeight: 600 }}>{multiplier}</Typography>
        </Box>
      </Grid>
    </Grid>
  );
};
