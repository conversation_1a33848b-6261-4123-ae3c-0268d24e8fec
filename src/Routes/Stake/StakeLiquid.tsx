import { StakeLayout } from "../../components/Stake/StakeLayout";
import StakeLiquidInfo from "../../components/Stake/StakeLiquidInfo/Component";
import { useStakeRedirect } from "./redirect";

const Component = () => {
  useStakeRedirect();

  return (
    <StakeLayout
      title="Stake sPOLLEN"
      description="Stake your sPOLLEN to earn POLLEN rewards with auto-compounding"
      togglers={[
        { label: "Stake", link: "/stake/liquid/stake" },
        { label: "Unstake", link: "/stake/liquid/unstake" },
      ]}
      InfoComponent={StakeLiquidInfo}
    />
  );
};

const StakeLiquid = Component;
export default StakeLiquid;
