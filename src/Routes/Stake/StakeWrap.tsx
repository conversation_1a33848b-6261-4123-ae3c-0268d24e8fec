import React from "react";
import { StakeLayout } from "../../components/Stake/StakeLayout";
import WrapInfo from "../../components/Stake/WrapInfo/Component";
import { useStakeRedirect } from "./redirect";

const Component: React.FC = () => {
  useStakeRedirect();

  return (
    <StakeLayout
      title="Wrap POLLEN"
      description="Wrap your POLLEN to sPOLLEN for single-sided staking and locking"
      togglers={[
        { label: "Wrap", link: "/stake/wrapping/wrap" },
        { label: "Unwrap", link: "/stake/wrapping/unwrap" },
      ]}
      InfoComponent={WrapInfo}
    />
  );
};

const StakeWrap = Component;
export default StakeWrap;
