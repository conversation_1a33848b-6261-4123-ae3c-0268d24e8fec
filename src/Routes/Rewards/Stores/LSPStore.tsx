import { useAtomValue } from "jotai";
import { convertSharesToAssetsAtom, getLspTvlAtom, getPoolAPYAtom, getStabilityDepositAtom } from "../../../Atoms/StabilityPool";
import { Reward } from "../Component";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";

export const useLSPStore = (): Reward => {
  const { data: apy } = useAtomValue(getPoolAPYAtom);
  const { data: tvl } = useAtomValue(getLspTvlAtom);
  const { data: currentDeposit } = useAtomValue(getStabilityDepositAtom);
  const { data: shareToAssetRatio } = useAtomValue(convertSharesToAssetsAtom);

  const myTotalAssets = currentDeposit.convertSharesToAssets(currentDeposit.shares, shareToAssetRatio);
  return {
    id: "beraborrowLSP",
    action: "Deposit NECT",
    contractAddress1: BERABORROW_ADDRESSES.debtToken.contractAddress,
    provider: "Beraborrow",
    link: "https://bartio.beradrome.com/farms/0x27502F04872F5b7e82e33D362edAfFcFdB7fC840",
    rewardRate: apy,
    rewardRateLabel: "APY",
    tvl,
    balance: myTotalAssets,
  };
};
