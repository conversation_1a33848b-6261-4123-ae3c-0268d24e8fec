// import { useAtomValue } from "jotai";
import { Reward } from "../Component";
// import { rewardsPartnersAtom } from "../../../Atoms/Api";
// import { SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
// import { formatNoDecimal } from "../../../utils/helpers";
// import { getAddress } from "viem";

export const useRewardsPartnersStore = (): Reward[] => {
  //   const { data: partnersJSON } = useAtomValue(rewardsPartnersAtom);

  //   const partners = partnersJSON.map((item) => ({
  //     ...item,
  //     rewardRate: formatNoDecimal(item.rewardRate.toString(), SCALING_FACTOR_DECIMALS - 2),
  //     tvl: formatNoDecimal(item.tvl.toString(), SCALING_FACTOR_DECIMALS),
  //     contractAddress1: getAddress(item.contractAddress1),
  //     contractAddress2: item.contractAddress2 ? getAddress(item.contractAddress2) : undefined,
  //   }));
  return [];
  //   return partners ?? [];
};
