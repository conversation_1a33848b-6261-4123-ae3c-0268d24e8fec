import React from "react";
import { Box, Container, Paper, useTheme, Typography, Chip, Grid, Divider, Button } from "@mui/material";
import { Hex } from "viem";
import TokenIcon from "../../components/TokenIcons";
import { Favorite } from "@mui/icons-material";
import TokenPair from "../../components/TokenPair";
import { findToken, formatBigIntPercentage } from "../../utils/helpers";
import { useRewardStore } from "./store";
import WithSuspense from "../../providers/WithSuspense";
export type Reward = {
  id: string;
  action: string;
  rewardRate: bigint;
  rewardRateLabel: string;
  contractAddress1: Hex;
  contractAddress2?: Hex;
  tvl: bigint;
  balance?: bigint;
  provider: string;
  link: string;
};
const Component: React.FC = () => {
  const theme = useTheme();
  const { rewards } = useRewardStore();
  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Container
        maxWidth="lg"
        sx={{
          py: 2,

          [theme.breakpoints.down("md")]: {
            paddingX: "8px",
          },
        }}
      >
        <Box>
          <Paper
            sx={{
              backdropFilter: {
                xs: "none", // No blur on mobile
                md: "blur(5px)", // Apply blur for medium and larger screens
              },
              marginTop: 3,
              backgroundImage: `url(/imgs/bgDots.png),var(--modal-gradient)`,
              backgroundRepeat: "no-repeat",
              backgroundSize: "cover", // Ensures the image covers the entire area
              backgroundClip: "padding-box", // Extends the background to include padding
            }}
          >
            <Box display={"flex"} justifyContent={"space-between"}>
              <Box padding={{ xs: "20px", md: "30px" }}>
                <Typography variant="h1" fontSize={36} lineHeight={1.2}>
                  Rewards
                </Typography>
                <Typography variant="h3" color={"var(--text-secondary)"}>
                  Use your assets <Box component="br" sx={{ display: { sm: "none" } }} /> to earn
                </Typography>
              </Box>
              <Box position="relative">
                <Box
                  component="img"
                  src="/imgs/farmerBera.png"
                  sx={{
                    height: { xs: "80%", sm: "140%" },
                    position: "absolute",
                    right: "20px",
                    bottom: { xs: "-8px", sm: "-10px" },
                  }}
                />
              </Box>
            </Box>
          </Paper>

          <>
            <Grid container spacing={1} py={2}>
              {rewards.map((item) => {
                const token1 = findToken(item.contractAddress1);
                const token2 = item.contractAddress2 ? findToken(item.contractAddress2) : undefined;
                if (!!token1) {
                  return (
                    <Grid item xs={6} md={3}>
                      <Paper sx={{ backgroundColor: "var(--background-tertiary)" }}>
                        <Box display={"flex"} justifyContent={"space-between"} py={1}>
                          <Box display="flex" alignItems="center">
                            <TokenIcon contractAddress={item.contractAddress1} height={"30px"} />
                            <Typography variant="h1" pl={1}>
                              {item.provider}
                            </Typography>
                          </Box>
                          <Box display="flex" alignItems="center">
                            <Chip label={"best"} variant="filled" color={"success"} size="small" icon={<Favorite fontSize={"small"} />} />
                          </Box>
                        </Box>
                        <Paper sx={{ padding: { xs: "12px", md: "20px" }, backgroundColor: "var(--background-accent-full)", backgroundImage: "unset" }}>
                          <TokenPair
                            heading={item.action}
                            token1={{
                              ticker: token1.ticker,
                              contractAddress: "contractAddress" in token1 ? token1.contractAddress : token1.collateral,
                              decimals: token1.decimals,
                            }}
                            token2={
                              token2
                                ? {
                                    contractAddress: "contractAddress" in token2 ? token2.contractAddress : token2.collateral,
                                    ticker: token2.ticker,
                                    decimals: token2.decimals,
                                  }
                                : undefined
                            }
                          />

                          <Typography variant="h1" fontSize={32} color="var(--text-success)" pt={2}>
                            {formatBigIntPercentage(item.rewardRate || 0n, 2)}% {item.rewardRateLabel}
                          </Typography>

                          <Divider sx={{ my: 2, backgroundColor: "var(--border-color)" }} />
                          <Button variant={"contained"} fullWidth>
                            Earn
                          </Button>
                        </Paper>
                      </Paper>
                    </Grid>
                  );
                } else return <></>;
              })}
            </Grid>
          </>
        </Box>
      </Container>
    </Box>
  );
};

const Rewards = WithSuspense(Component, "paper");
export default Rewards;
