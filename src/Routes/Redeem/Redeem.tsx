import React, { useEffect } from "react";
import Toggler from "../../components/Toggler";
import { Box, Container, Grid, Paper, useTheme } from "@mui/material";
import DenRedeem from "../../components/DenRedeem/component";
import { useNotifications } from "../../Hooks/useNotifications";
import RedemptionModal from "../../components/RedemptionModal";

const Component: React.FC = () => {
  const { addModal } = useNotifications();
  const theme = useTheme();
  useEffect(() => {
    addModal({
      id: "redemption-warning",
      title: "Redemptions",
      Component: <RedemptionModal />,
      maxWidth: "600px",
    });
  }, []);
  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Container
        maxWidth="sm"
        sx={{
          py: 2,

          [theme.breakpoints.down("md")]: {
            paddingX: "8px",
          },
        }}
      >
        <Paper
          sx={{
            backgroundColor: "var(--background-tertiary)",
            backdropFilter: {
              xs: "none", // No blur on mobile
              md: "blur(5px)", // Apply blur for medium and larger screens
            },
          }}
        >
          <Box>
            <Grid container spacing={1}>
              <Grid item xs={12}>
                <Paper>
                  <Toggler togglers={[{ label: "Redeem", link: "/redeem" }]} />

                  <DenRedeem />
                </Paper>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

const Redeem = Component;
export default Redeem;
