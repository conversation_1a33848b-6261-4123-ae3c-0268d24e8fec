import { parseEther } from "viem";

/**
 * Scaling Factor of 100% for percentage to avoid decimal
 *
 * @public
 */
export const SCALING_FACTOR = 1000000000000000000n; //1e18
export const SCALING_FACTOR_DECIMALS = 18;

/**
 * Scaling Factor Basic Point of 100% for percentage to avoid decimal
 *
 * @public
 */
export const SCALING_FACTOR_BP = 10000n; //1e4
export const SCALING_FACTOR_BP_DECIMALS = 4;

/**
 * Value that the {@link Fees.borrowingMintRate | borrowing rate} will never decay below.
 *
 * @remarks
 * Note that the borrowing Mint rate can still be lower than this during recovery mode, when it's
 * overridden by zero.
 *
 * @public
 */
export const MINIMUM_BORROWING_MINT_RATE = (SCALING_FACTOR / 1000n) * 5n; // 0.5%

/**
 * Value that the {@link Fees.borrowingFEERate | borrowing fee rate} will never exceed.
 *
 * @public
 */
export const MAXIMUM_BORROWING_MINT_RATE = (SCALING_FACTOR / 100n) * 5n; // 5%

/**
 * Value that the {@link Fees.redemptionRate | redemption rate} will never decay below.
 *
 * @public
 */
export const MINIMUM_REDEMPTION_RATE = (SCALING_FACTOR / 1000n) * 5n; // 0.5%
/**
 * Value that the {@link Fees.redemptionRate | redemption rate} will never exceed.
 *
 * @public
 */
export const MAXIMUM_REDEMPTION_RATE = (SCALING_FACTOR / 100n) * 5n; // 5%

// With 70 iterations redemption costs about ~10M gas, and each iteration accounts for ~138k more
export const MAX_REDEEM_ITERATIONS = 70n;
/**
 * DEFAULT_SLIPPAGE_TOLERANCE set to 0.5%
 */
export const DEFAULT_SLIPPAGE_TOLERANCE = (SCALING_FACTOR / 1000n) * 5n; // 0.5%

export const MIN_GAS_FEE = parseEther("0.0001");

export const SECONDS_IN_YEAR = ********;
