import { DocumentNode, gql } from "@apollo/client/core";

export const getPausedByIdQuery = (): DocumentNode => {
  return gql`
    query GetPausedById($id: Bytes!) {
      paused(id: $id) {
        id
        account
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllPausedQuery = (): DocumentNode => {
  return gql`
    query GetAllPaused {
      pauseds {
        id
        account
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRecoveredByIdQuery = (): DocumentNode => {
  return gql`
    query GetRecoveredById($id: Bytes!) {
      recovered(id: $id) {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRecoveredByTokenQuery = (): DocumentNode => {
  return gql`
    query GetRecoveredByToken($token: Bytes!) {
      recovereds(where: { token: $token }) {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllRecoveredEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllRecoveredEvents {
      recovereds {
        id
        blockNumber
        blockTimestamp
        transactionHash
        token
        amount
      }
    }
  `;
};

export const getRewardAddedByIdQuery = (): DocumentNode => {
  return gql`
    query GetRewardAddedById($id: Bytes!) {
      rewardAdded(id: $id) {
        id
        rewardsToken
        reward
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRewardAddedByTokenQuery = (): DocumentNode => {
  return gql`
    query GetRewardAddedByToken($rewardsToken: Bytes!) {
      rewardAddeds(where: { rewardsToken: $rewardsToken }) {
        id
        rewardsToken
        reward
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllRewardAddedEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllRewardAddedEvents {
      rewardAddeds {
        id
        rewardsToken
        reward
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRewardPaidByIdQuery = (): DocumentNode => {
  return gql`
    query GetRewardPaidById($id: Bytes!) {
      rewardPaid(id: $id) {
        id
        user
        rewardsToken
        reward
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRewardsPaidByUserQuery = (): DocumentNode => {
  return gql`
    query GetRewardsPaidByUser($user: Bytes!) {
      rewardPaids(where: { user: $user }) {
        id
        user
        rewardsToken
        reward
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRewardPaidsByTokenQuery = (): DocumentNode => {
  return gql`
    query GetRewardPaidsByToken($rewardsToken: Bytes!) {
      rewardPaids(where: { rewardsToken: $rewardsToken }) {
        id
        user
        rewardsToken
        reward
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllRewardPaidEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllRewardPaidEvents {
      rewardPaids {
        id
        blockNumber
        blockTimestamp
        transactionHash
        user
        rewardsToken
        reward
      }
    }
  `;
};

export const getRewardRemovedByIdQuery = (): DocumentNode => {
  return gql`
    query GetRewardRemovedById($id: Bytes!) {
      rewardRemoved(id: $id) {
        id
        rewardsToken
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRewardRemovedByTokenQuery = (): DocumentNode => {
  return gql`
    query GetRewardRemovedByToken($rewardsToken: Bytes!) {
      rewardRemoveds(where: { rewardsToken: $rewardsToken }) {
        id
        rewardsToken
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllRewardRemovedEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllRewardRemovedEvents {
      rewardRemoveds {
        id
        blockNumber
        blockTimestamp
        transactionHash
        rewardsToken
      }
    }
  `;
};

export const getRewardStoredByIdQuery = (): DocumentNode => {
  return gql`
    query GetRewardStoredById($id: Bytes!) {
      rewardStored(id: $id) {
        id
        rewardsToken
        rewardsDuration
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRewardStoredByTokenQuery = (): DocumentNode => {
  return gql`
    query GetRewardStoredByToken($rewardsToken: Bytes!) {
      rewardStoreds(where: { rewardsToken: $rewardsToken }) {
        id
        rewardsToken
        rewardsDuration
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllRewardStoredEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllRewardStoredEvents {
      rewardStoreds {
        id
        blockNumber
        blockTimestamp
        transactionHash
        rewardsToken
        rewardsDuration
      }
    }
  `;
};

export const getRewardsDurationUpdatedByIdQuery = (): DocumentNode => {
  return gql`
    query GetRewardsDurationUpdatedById($id: Bytes!) {
      rewardsDurationUpdated(id: $id) {
        id
        token
        newDuration
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRewardsDurationUpdatedByTokenQuery = (): DocumentNode => {
  return gql`
    query GetRewardsDurationUpdatedByToken($token: Bytes!) {
      rewardsDurationUpdateds(where: { token: $token }) {
        id
        token
        newDuration
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllRewardsDurationUpdatedEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllRewardsDurationUpdatedEvents {
      rewardsDurationUpdateds {
        id
        blockNumber
        blockTimestamp
        transactionHash
        token
        newDuration
      }
    }
  `;
};

export const getStakedByIdQuery = (): DocumentNode => {
  return gql`
    query GetStakedById($id: Bytes!) {
      staked(id: $id) {
        id
        user
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getStakedEventsByUserQuery = (): DocumentNode => {
  return gql`
    query GetStakedEventsByUser($user: Bytes!) {
      stakeds(where: { user: $user }) {
        id
        user
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllStakedEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllStakedEvents {
      stakeds {
        id
        blockNumber
        blockTimestamp
        transactionHash
        user
        amount
      }
    }
  `;
};

export const getUnpausedByIdQuery = (): DocumentNode => {
  return gql`
    query GetUnpausedById($id: Bytes!) {
      unpaused(id: $id) {
        id
        account
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllUnpausedQuery = (): DocumentNode => {
  return gql`
    query GetAllUnpaused {
      unpauseds {
        id
        account
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getUnpausedEventsByAccountQuery = (): DocumentNode => {
  return gql`
    query GetUnpausedEventsByAccount($account: Bytes!) {
      unpauseds(where: { account: $account }) {
        id
        account
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getWithdrawnByIdQuery = (): DocumentNode => {
  return gql`
    query GetWithdrawnById($id: Bytes!) {
      withdrawn(id: $id) {
        id
        user
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getWithdrawnEventsByUserQuery = (): DocumentNode => {
  return gql`
    query GetWithdrawnEventsByUser($user: Bytes!) {
      withdrawns(where: { user: $user }) {
        id
        user
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllWithdrawnEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllWithdrawnEvents {
      withdrawns {
        id
        user
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getClaimRewardByIdQuery = (): DocumentNode => {
  return gql`
    query GetClaimRewardById($id: Bytes!) {
      claimReward(id: $id) {
        id
        source
        user
        wTime
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getClaimRewardsByUserQuery = (): DocumentNode => {
  return gql`
    query GetClaimRewardsByUser($user: Bytes!) {
      claimRewards(where: { user: $user }) {
        id
        source
        user
        wTime
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllClaimRewardEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllClaimRewardEvents {
      claimRewards {
        id
        source
        user
        wTime
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getClaimRewardsBySourceQuery = (): DocumentNode => {
  return gql`
    query GetClaimRewardsBySource($source: Bytes!) {
      claimRewards(where: { source: $source }) {
        id
        source
        user
        wTime
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getSourceAddedByIdQuery = (): DocumentNode => {
  return gql`
    query GetSourceAddedById($id: Bytes!) {
      sourceAdded(id: $id) {
        id
        source
        startWeek
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getSourceAddedBySourceQuery = (): DocumentNode => {
  return gql`
    query GetSourceAddedBySource($source: Bytes!) {
      sourceAddeds(where: { source: $source }) {
        id
        source
        startWeek
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllSourceAddedEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllSourceAddedEvents {
      sourceAddeds {
        id
        blockNumber
        blockTimestamp
        transactionHash
        source
        startWeek
      }
    }
  `;
};

export const getUpdateFeeByIdQuery = (): DocumentNode => {
  return gql`
    query GetUpdateFeeById($id: Bytes!) {
      updateFee(id: $id) {
        id
        source
        wTime
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getUpdateFeesBySourceQuery = (): DocumentNode => {
  return gql`
    query GetUpdateFeesBySource($source: Bytes!) {
      updateFees(where: { source: $source }) {
        id
        source
        wTime
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllUpdateFeeEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllUpdateFeeEvents {
      updateFees {
        id
        blockNumber
        blockTimestamp
        transactionHash
        source
        wTime
        amount
      }
    }
  `;
};

export const getNewLockPositionByIdQuery = (): DocumentNode => {
  return gql`
    query GetNewLockPositionById($id: Bytes!) {
      newLockPosition(id: $id) {
        id
        user
        amount
        expiry
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getNewLockPositionsByUserQuery = (): DocumentNode => {
  return gql`
    query GetNewLockPositionsByUser($user: Bytes!) {
      newLockPositions(where: { user: $user }) {
        id
        user
        amount
        expiry
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getNewLockPositionsByTimeRangeQuery = (): DocumentNode => {
  return gql`
    query GetNewLockPositionsByTimeRange($start: BigInt!, $end: BigInt!) {
      newLockPositions(where: { blockTimestamp_gte: $start, blockTimestamp_lte: $end }) {
        id
        user
        amount
        expiry
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllNewLockPositionEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllNewLockPositionEvents {
      newLockPositions {
        id
        blockNumber
        blockTimestamp
        transactionHash
        user
        amount
        expiry
      }
    }
  `;
};

export const getDepositByIdQuery = (): DocumentNode => {
  return gql`
    query GetDepositById($id: Bytes!) {
      deposit(id: $id) {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getDepositsByTokenQuery = (): DocumentNode => {
  return gql`
    query GetDepositsByToken($token: Bytes!) {
      deposits(where: { token: $token }) {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllDepositEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllDepositEvents {
      deposits {
        id
        blockNumber
        blockTimestamp
        transactionHash
        token
        amount
      }
    }
  `;
};

export const getWithdrawEventByIdQuery = (): DocumentNode => {
  return gql`
    query GetWithdrawEventById($id: Bytes!) {
      withdraw(id: $id) {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getWithdrawEventsByTokenQuery = (): DocumentNode => {
  return gql`
    query GetWithdrawEventsByToken($token: Bytes!) {
      withdraws(where: { token: $token }) {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllGenericWithdrawEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllGenericWithdrawEvents {
      withdraws {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getLockWithdrawByIdQuery = (): DocumentNode => {
  return gql`
    query GetLockWithdrawById($id: Bytes!) {
      lockWithdraw(id: $id) {
        id
        user
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getLockWithdrawsByUserQuery = (): DocumentNode => {
  return gql`
    query GetLockWithdrawsByUser($user: Bytes!) {
      lockWithdraws(where: { user: $user }) {
        id
        user
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllLockWithdrawEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllLockWithdrawEvents {
      lockWithdraws {
        id
        user
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRewardClaimedEventByIdQuery = (): DocumentNode => {
  return gql`
    query GetRewardClaimedEventById($id: Bytes!) {
      rewardClaimed(id: $id) {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getRewardClaimedEventsByTokenQuery = (): DocumentNode => {
  return gql`
    query GetRewardClaimedEventsByToken($token: Bytes!) {
      rewardClaimeds(where: { token: $token }) {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllRewardClaimedEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllRewardClaimedEvents {
      rewardClaimeds {
        id
        token
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getBuybackByIdQuery = (): DocumentNode => {
  return gql`
    query GetBuybackById($id: Bytes!) {
      buyback(id: $id) {
        id
        amountIn
        amountOut
        slippageThreshold
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllBuybacksQuery = (): DocumentNode => {
  return gql`
    query GetAllBuybacks {
      buybacks {
        id
        amountIn
        amountOut
        slippageThreshold
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getSPollenWrapByIdQuery = (): DocumentNode => {
  return gql`
    query GetSPollenWrapById($id: Bytes!) {
      spollenWrap(id: $id) {
        id
        account
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getSPollenWrapsByAccountQuery = (): DocumentNode => {
  return gql`
    query GetSPollenWrapsByAccount($account: Bytes!) {
      spollenWraps(where: { account: $account }) {
        id
        account
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllSPollenWrapEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllSPollenWrapEvents {
      spollenWraps {
        id
        blockNumber
        blockTimestamp
        transactionHash
        account
        amount
      }
    }
  `;
};

export const getSPollenUnwrapByIdQuery = (): DocumentNode => {
  return gql`
    query GetSPollenUnwrapById($id: Bytes!) {
      spollenUnwrap(id: $id) {
        id
        account
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getSPollenUnwrapsByAccountQuery = (): DocumentNode => {
  return gql`
    query GetSPollenUnwrapsByAccount($account: Bytes!) {
      spollenUnwraps(where: { account: $account }) {
        id
        account
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllSPollenUnwrapEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllSPollenUnwrapEvents {
      spollenUnwraps {
        id
        account
        amount
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getUserDetailsQuery = (): DocumentNode => {
  return gql`
    query GetUserDetails($userId: Bytes!) {
      user(id: $userId) {
        id
        lp {
          id
          balances {
            id
            staked
            locked
          }
          lockingDetails {
            id
            amount
            expiry
          }
          rewards {
            id
            staking {
              id
              lastClaimed
              totalClaimed
            }
            locking {
              id
              lastClaimed
              lastClaimedTimestamp
              lastClaimedWeek
              totalClaimed
            }
            totals {
              id
              staking
              locking
            }
          }
        }
        sPollen {
          id
          balances {
            id
            wrapped
            staked
            locked
          }
          lockingDetails {
            id
            amount
            expiry
          }
          rewards {
            id
            staking {
              id
              lastClaimed
              totalClaimed
            }
            locking {
              id
              lastClaimed
              lastClaimedTimestamp
              lastClaimedWeek
              totalClaimed
            }
            totals {
              id
              staking
              locking
            }
          }
        }
      }
    }
  `;
};

export const getLpUserQuery = (): DocumentNode => {
  return gql`
    query GetLpUser($lpUserId: Bytes!) {
      lpUser(id: $lpUserId) {
        id
        user {
          id
        }
        balances {
          id
          staked
          locked
        }
        lockingDetails {
          id
          amount
          expiry
        }
        rewards {
          id
          staking {
            id
            lastClaimed
            totalClaimed
          }
          locking {
            id
            lastClaimed
            lastClaimedTimestamp
            lastClaimedWeek
            totalClaimed
          }
          totals {
            id
            staking
            locking
          }
        }
      }
    }
  `;
};

export const getSPollenUserQuery = (): DocumentNode => {
  return gql`
    query GetSPollenUser($spollenUserId: Bytes!) {
      spollenUser(id: $spollenUserId) {
        id
        user {
          id
        }
        balances {
          id
          wrapped
          staked
          locked
        }
        lockingDetails {
          id
          amount
          expiry
        }
        rewards {
          id
          staking {
            id
            lastClaimed
            totalClaimed
          }
          locking {
            id
            lastClaimed
            lastClaimedTimestamp
            lastClaimedWeek
            totalClaimed
          }
          totals {
            id
            staking
            locking
          }
        }
      }
    }
  `;
};

export const getProtocolDetailsQuery = (): DocumentNode => {
  return gql`
    query GetProtocolDetails($protocolId: Bytes!) {
      protocol(id: $protocolId) {
        id
        lp {
          id
          rates {
            id
            stakingPct
            lockingPct
            stakingAprDaily
            stakingAprWeekly
            calculatedLockingAprWeekly
          }
          totals {
            id
            staked
            locked
          }
          rewards {
            id
            weeklyStaking
            weeklyLocking
            lastLockingWeek
            allTimeStaking
            allTimeLocking
          }
          claims {
            id
            allTimeStaking
            allTimeLocking
            allTimeBuyback
            iBGT
            iRED
          }
        }
        sPollen {
          id
          rates {
            id
            stakingPct
            lockingPct
            stakingAprDaily
            stakingAprWeekly
            calculatedLockingAprWeekly
          }
          totals {
            id
            wrapped
            staked
            locked
          }
          rewards {
            id
            weeklyStaking
            weeklyLocking
            lastLockingWeek
            allTimeStaking
            allTimeLocking
          }
          claims {
            id
            allTimeStaking
            allTimeLocking
          }
        }
      }
    }
  `;
};

export const getStakingAprBucketsQuery = (): DocumentNode => {
  return gql`
    query GetStakingAprBuckets($bucketType: String, $slotIndex: BigInt) {
      stakingAprBuckets(where: { bucketType: $bucketType, slotIndex: $slotIndex }) {
        id
        bucketType
        slotIndex
        startTime
        lastTime
        cumulativePollenDistributed
        cumulativeSPollenSupply
        cumulativeStakedLpValueInPollen
        updateCount
      }
    }
  `;
};

export const getLockingAprBucketsQuery = (): DocumentNode => {
  return gql`
    query GetLockingAprBuckets($bucketType: String, $slotIndex: BigInt) {
      lockingAprBuckets(where: { bucketType: $bucketType, slotIndex: $slotIndex }) {
        id
        bucketType
        slotIndex
        startTime
        lastTime
        cumulativeRewardDistributed
        cumulativeTotalSupplyStored
        updateCount
      }
    }
  `;
};

export const getClaimForVeLockByIdQuery = (): DocumentNode => {
  return gql`
    query GetClaimForVeLockById($id: Bytes!) {
      claimForVeLock(id: $id) {
        id
        token
        user
        rewards
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getClaimForVeLocksByUserQuery = (): DocumentNode => {
  return gql`
    query GetClaimForVeLocksByUser($user: Bytes!) {
      claimForVeLocks(where: { user: $user }) {
        id
        token
        user
        rewards
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getAllClaimForVeLockEventsQuery = (): DocumentNode => {
  return gql`
    query GetAllClaimForVeLockEvents {
      claimForVeLocks {
        id
        blockNumber
        blockTimestamp
        transactionHash
        token
        user
        rewards
      }
    }
  `;
};

export const getClaimForVeLockEventsByTokenQuery = (): DocumentNode => {
  return gql`
    query GetClaimForVeLockEventsByToken($token: Bytes!) {
      claimForVeLocks(where: { token: $token }) {
        id
        token
        user
        rewards
        blockNumber
        blockTimestamp
        transactionHash
      }
    }
  `;
};

export const getUserLpLockedAmountQuery = (): DocumentNode => {
  return gql`
    query GetUserLpLockedAmount($userId: Bytes!) {
      user(id: $userId) {
        id
        lp {
          balances {
            locked
          }
        }
      }
    }
  `;
};

export const getProtocolLpTotalStakedQuery = (): DocumentNode => {
  return gql`
    query GetProtocolLpTotalStaked($protocolId: Bytes!) {
      protocol(id: $protocolId) {
        id
        lp {
          totals {
            staked
          }
        }
      }
    }
  `;
};

export const getProtocolBuybackTotalsQuery = (): DocumentNode => {
  return gql`
    query GetProtocolBuybackTotals($protocolId: Bytes!) {
      protocol(id: $protocolId) {
        id
        lp {
          claims {
            allTimeBuyback
            iBGT
            iRED
          }
        }
      }
    }
  `;
};

export const getAllStakingAprBucketsQuery = (): DocumentNode => {
  return gql`
    query GetAllStakingAprBuckets {
      stakingAprBuckets {
        id
        bucketType
        slotIndex
        startTime
        lastTime
        cumulativePollenDistributed
        cumulativeSPollenSupply
        cumulativeStakedLpValueInPollen
        updateCount
      }
    }
  `;
};

export const getStakingAprBucketByIdQuery = (): DocumentNode => {
  return gql`
    query GetStakingAprBucketById($id: ID!) {
      stakingAprBucket(id: $id) {
        id
        bucketType
        slotIndex
        startTime
        lastTime
        cumulativePollenDistributed
        cumulativeSPollenSupply
        cumulativeStakedLpValueInPollen
        updateCount
      }
    }
  `;
};

export const getAllLockingAprBucketsQuery = (): DocumentNode => {
  return gql`
    query GetAllLockingAprBuckets {
      lockingAprBuckets {
        id
        bucketType
        slotIndex
        startTime
        lastTime
        cumulativeRewardDistributed
        cumulativeTotalSupplyStored
        updateCount
      }
    }
  `;
};

export const getLockingAprBucketByIdQuery = (): DocumentNode => {
  return gql`
    query GetLockingAprBucketsById($id: ID!) {
      lockingAprBucket(where: { id: $id }) {
        id
        bucketType
        slotIndex
        startTime
        lastTime
        cumulativeRewardDistributed
        cumulativeTotalSupplyStored
        updateCount
      }
    }
  `;
};

export const getAllLpProtocolsQuery = (): DocumentNode => {
  return gql`
    query GetAllLpProtocols {
      lpProtocols {
        id
        protocol {
          id
        }
        rates {
          id
          stakingPct
          lockingPct
          stakingAprDaily
          stakingAprWeekly
          calculatedLockingAprWeekly
        }
        totals {
          id
          staked
          locked
        }
        rewards {
          id
          weeklyStaking
          weeklyLocking
          lastLockingWeek
          allTimeStaking
          allTimeLocking
        }
        claims {
          id
          allTimeStaking
          allTimeLocking
          allTimeBuyback
          iBGT
          iRED
        }
      }
    }
  `;
};

export const getAllSPollenProtocolsQuery = (): DocumentNode => {
  return gql`
    query GetAllSPollenProtocols {
      spollenProtocols {
        id
        protocol {
          id
        }
        rates {
          id
          stakingPct
          lockingPct
          stakingAprDaily
          stakingAprWeekly
          calculatedLockingAprWeekly
        }
        totals {
          id
          wrapped
          staked
          locked
        }
        rewards {
          id
          weeklyStaking
          weeklyLocking
          lastLockingWeek
          allTimeStaking
          allTimeLocking
        }
        claims {
          id
          allTimeStaking
          allTimeLocking
        }
      }
    }
  `;
};

export const getAllUsersQuery = (): DocumentNode => {
  return gql`
    query GetAllUsers {
      users {
        id
        lp {
          id
          balances {
            id
            staked
            locked
          }
          lockingDetails {
            id
            amount
            expiry
          }
          rewards {
            id
            staking {
              id
              lastClaimed
              totalClaimed
            }
            locking {
              id
              lastClaimed
              lastClaimedTimestamp
              lastClaimedWeek
              totalClaimed
            }
            totals {
              id
              staking
              locking
            }
          }
        }
        sPollen {
          id
          balances {
            id
            staked
            locked
            wrapped
          }
          lockingDetails {
            id
            amount
            expiry
          }
          rewards {
            id
            staking {
              id
              lastClaimed
              totalClaimed
            }
            locking {
              id
              lastClaimed
              lastClaimedTimestamp
              lastClaimedWeek
              totalClaimed
            }
            totals {
              id
              staking
              locking
            }
          }
        }
      }
    }
  `;
};

export const getAllLpUsersQuery = (): DocumentNode => {
  return gql`
    query GetAllLpUsers {
      lpUsers {
        id
        user {
          id
        }
        balances {
          id
          staked
          locked
        }
        lockingDetails {
          id
          amount
          expiry
        }
        rewards {
          id
          staking {
            id
            lastClaimed
            totalClaimed
          }
          locking {
            id
            lastClaimed
            lastClaimedTimestamp
            lastClaimedWeek
            totalClaimed
          }
          totals {
            id
            staking
            locking
          }
        }
      }
    }
  `;
};

export const getAllSPollenUsersQuery = (): DocumentNode => {
  return gql`
    query GetAllSPollenUsers {
      spollenUsers {
        id
        user {
          id
        }
        balances {
          id
          staked
          locked
          wrapped
        }
        lockingDetails {
          id
          amount
          expiry
        }
        rewards {
          id
          staking {
            id
            lastClaimed
            totalClaimed
          }
          locking {
            id
            lastClaimed
            lastClaimedTimestamp
            lastClaimedWeek
            totalClaimed
          }
          totals {
            id
            staking
            locking
          }
        }
      }
    }
  `;
};

export const getAllProtocolsQuery = (): DocumentNode => {
  return gql`
    query GetAllProtocols {
      protocols {
        id
        lp {
          id
          rates {
            id
            stakingPct
            lockingPct
            stakingAprDaily
            stakingAprWeekly
            calculatedLockingAprWeekly
          }
          totals {
            id
            staked
            locked
          }
          rewards {
            id
            weeklyStaking
            weeklyLocking
            lastLockingWeek
            allTimeStaking
            allTimeLocking
          }
          claims {
            id
            allTimeStaking
            allTimeLocking
            allTimeBuyback
            iBGT
            iRED
          }
        }
        sPollen {
          id
          rates {
            id
            stakingPct
            lockingPct
            stakingAprDaily
            stakingAprWeekly
            calculatedLockingAprWeekly
          }
          totals {
            id
            staked
            locked
            wrapped
          }
          rewards {
            id
            weeklyStaking
            weeklyLocking
            lastLockingWeek
            allTimeStaking
            allTimeLocking
          }
          claims {
            id
            allTimeStaking
            allTimeLocking
          }
        }
      }
    }
  `;
};

// LpBalances Queries
export const getLpBalancesByIdQuery = (): DocumentNode => {
  return gql`
    query GetLpBalancesById($id: Bytes!) {
      lpBalances(id: $id) {
        id
        staked
        locked
        lpUser {
          id
        }
      }
    }
  `;
};

export const getAllLpBalancesQuery = (): DocumentNode => {
  return gql`
    query GetAllLpBalances {
      lpBalances_collection {
        id
        staked
        locked
        lpUser {
          id
        }
      }
    }
  `;
};

// SPollenBalances Queries
export const getSPollenBalancesByIdQuery = (): DocumentNode => {
  return gql`
    query GetSPollenBalancesById($id: Bytes!) {
      spollenBalances(id: $id) {
        id
        wrapped
        staked
        locked
        spollenUser {
          id
        }
      }
    }
  `;
};

export const getAllSPollenBalancesQuery = (): DocumentNode => {
  return gql`
    query GetAllSPollenBalances {
      spollenBalances_collection {
        id
        wrapped
        staked
        locked
        spollenUser {
          id
        }
      }
    }
  `;
};

// LockingDetails Queries
export const getLockingDetailsByIdQuery = (): DocumentNode => {
  return gql`
    query GetLockingDetailsById($id: Bytes!) {
      lockingDetails(id: $id) {
        id
        amount
        expiry
        lpUser {
          id
        }
        spollenUser {
          id
        }
      }
    }
  `;
};

export const getAllLockingDetailsQuery = (): DocumentNode => {
  return gql`
    query GetAllLockingDetails {
      lockingDetails_collection {
        id
        amount
        expiry
        lpUser {
          id
        }
        spollenUser {
          id
        }
      }
    }
  `;
};

// UserRewards Queries
export const getUserRewardsByIdQuery = (): DocumentNode => {
  return gql`
    query GetUserRewardsById($id: Bytes!) {
      userRewards(id: $id) {
        id
        lpUser {
          id
        }
        spollenUser {
          id
        }
        staking {
          id
          lastClaimed
          totalClaimed
        }
        locking {
          id
          lastClaimed
          lastClaimedTimestamp
          lastClaimedWeek
          totalClaimed
        }
        totals {
          id
          staking
          locking
        }
      }
    }
  `;
};

export const getAllUserRewardsQuery = (): DocumentNode => {
  return gql`
    query GetAllUserRewards {
      userRewards_collection {
        id
        lpUser {
          id
        }
        spollenUser {
          id
        }
        staking {
          id
          lastClaimed
          totalClaimed
        }
        locking {
          id
          lastClaimed
          lastClaimedTimestamp
          lastClaimedWeek
          totalClaimed
        }
        totals {
          id
          staking
          locking
        }
      }
    }
  `;
};

// StakingRewards Queries
export const getStakingRewardsByIdQuery = (): DocumentNode => {
  return gql`
    query GetStakingRewardsById($id: Bytes!) {
      stakingRewards(id: $id) {
        id
        lastClaimed
        totalClaimed
        userRewards {
          id
        }
      }
    }
  `;
};

export const getAllStakingRewardsQuery = (): DocumentNode => {
  return gql`
    query GetAllStakingRewards {
      stakingRewards_collection {
        id
        lastClaimed
        totalClaimed
        userRewards {
          id
        }
      }
    }
  `;
};

// LockingRewards Queries
export const getLockingRewardsByIdQuery = (): DocumentNode => {
  return gql`
    query GetLockingRewardsById($id: Bytes!) {
      lockingRewards(id: $id) {
        id
        lastClaimed
        lastClaimedTimestamp
        lastClaimedWeek
        totalClaimed
        userRewards {
          id
        }
      }
    }
  `;
};

export const getAllLockingRewardsQuery = (): DocumentNode => {
  return gql`
    query GetAllLockingRewards {
      lockingRewards_collection {
        id
        lastClaimed
        lastClaimedTimestamp
        lastClaimedWeek
        totalClaimed
        userRewards {
          id
        }
      }
    }
  `;
};

// TotalRewards Queries
export const getTotalRewardsByIdQuery = (): DocumentNode => {
  return gql`
    query GetTotalRewardsById($id: Bytes!) {
      totalRewards(id: $id) {
        id
        staking
        locking
        userRewards {
          id
        }
      }
    }
  `;
};

export const getAllTotalRewardsQuery = (): DocumentNode => {
  return gql`
    query GetAllTotalRewards {
      totalRewards_collection {
        id
        staking
        locking
        userRewards {
          id
        }
      }
    }
  `;
};

// ProtocolRates Queries
export const getProtocolRatesByIdQuery = (): DocumentNode => {
  return gql`
    query GetProtocolRatesById($id: Bytes!) {
      protocolRates(id: $id) {
        id
        stakingPct
        lockingPct
        stakingAprDaily
        stakingAprWeekly
        calculatedLockingAprWeekly
        lpProtocol {
          id
        }
        spollenProtocol {
          id
        }
      }
    }
  `;
};

export const getAllProtocolRatesQuery = (): DocumentNode => {
  return gql`
    query GetAllProtocolRates {
      protocolRates_collection {
        id
        stakingPct
        lockingPct
        stakingAprDaily
        stakingAprWeekly
        calculatedLockingAprWeekly
        lpProtocol {
          id
        }
        spollenProtocol {
          id
        }
      }
    }
  `;
};

export const getAllLpProtocolTotalsQuery = (): DocumentNode => {
  return gql`
    query GetAllLpProtocolTotals {
      lpProtocolTotals_collection {
        id
        staked
        locked
        lpProtocol {
          id
        }
      }
    }
  `;
};

export const getAllSPollenProtocolTotalsQuery = (): DocumentNode => {
  return gql`
    query GetAllSPollenProtocolTotals {
      spollenProtocolTotals_collection {
        id
        wrapped
        staked
        locked
        spollenProtocol {
          id
        }
      }
    }
  `;
};

export const getAllProtocolRewardsQuery = (): DocumentNode => {
  return gql`
    query GetAllProtocolRewards {
      protocolRewards_collection {
        id
        weeklyStaking
        weeklyLocking
        lastLockingWeek
        allTimeStaking
        allTimeLocking
        lpProtocol {
          id
        }
        spollenProtocol {
          id
        }
      }
    }
  `;
};

export const getAllLpClaimsQuery = (): DocumentNode => {
  return gql`
    query GetAllLpClaims {
      lpClaims_collection {
        id
        allTimeStaking
        allTimeLocking
        allTimeBuyback
        iBGT
        iRED
        lpProtocol {
          id
        }
      }
    }
  `;
};

export const getAllSPollenClaimsQuery = (): DocumentNode => {
  return gql`
    query GetAllSPollenClaims {
      spollenClaims_collection {
        id
        allTimeStaking
        allTimeLocking
        spollenProtocol {
          id
        }
      }
    }
  `;
};
