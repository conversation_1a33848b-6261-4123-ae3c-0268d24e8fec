import { Hex } from "viem";

// Base Entity Structure for events
export type BaseEvent = {
  id: Hex;
  blockNumber: bigint;
  blockTimestamp: bigint;
  transactionHash: Hex;
};

// Individual Event/Entity Types
export type PausedEvent = BaseEvent & {
  account: Hex;
};

export type RecoveredEvent = BaseEvent & {
  token: Hex;
  amount: bigint;
};

export type RewardAddedEvent = BaseEvent & {
  rewardsToken: Hex;
  reward: bigint;
};

export type RewardPaidEvent = BaseEvent & {
  user: Hex;
  rewardsToken: Hex;
  reward: bigint;
};

export type RewardRemovedEvent = BaseEvent & {
  rewardsToken: Hex;
};

export type RewardStoredEvent = BaseEvent & {
  rewardsToken: Hex;
  rewardsDuration: bigint;
};

export type RewardsDurationUpdatedEvent = BaseEvent & {
  token: Hex;
  newDuration: bigint;
};

export type StakedEvent = BaseEvent & {
  user: Hex;
  amount: bigint;
};

export type UnpausedEvent = BaseEvent & {
  account: Hex;
};

export type WithdrawnEvent = BaseEvent & {
  user: Hex;
  amount: bigint;
};

export type ClaimRewardEvent = BaseEvent & {
  source: Hex;
  user: Hex;
  wTime: bigint;
  amount: bigint;
};

export type SourceAddedEvent = BaseEvent & {
  source: Hex;
  startWeek: bigint;
};

export type UpdateFeeEvent = BaseEvent & {
  source: Hex;
  wTime: bigint;
  amount: bigint;
};

export type NewLockPositionEvent = BaseEvent & {
  user: Hex;
  amount: bigint;
  expiry: bigint;
};

export type DepositEvent = BaseEvent & {
  token: Hex;
  amount: bigint;
};

export type GenericWithdrawEvent = BaseEvent & {
  token: Hex;
  amount: bigint;
};

export type LockWithdrawEvent = BaseEvent & {
  user: Hex;
  amount: bigint;
};

export type RewardClaimedEvent = BaseEvent & {
  token: Hex;
  amount: bigint;
};

export type BuybackEvent = BaseEvent & {
  amountIn: bigint;
  amountOut: bigint;
  slippageThreshold: bigint;
};

export type SPollenWrapEvent = BaseEvent & {
  account: Hex;
  amount: bigint;
};

export type SPollenUnwrapEvent = BaseEvent & {
  account: Hex;
  amount: bigint;
};

export type ClaimForVeLockEvent = BaseEvent & {
  token: Hex;
  user: Hex;
  rewards: bigint;
};

// User Details Types
export type UserBalances = {
  id: Hex;
  staked?: bigint | null;
  locked?: bigint | null;
  wrapped?: bigint | null;
};

export type UserLockingDetails = {
  id: Hex;
  amount: bigint;
  expiry: bigint;
};

export type UserStakingRewardsDetails = {
  id: Hex;
  lastClaimed: bigint;
  totalClaimed: bigint;
};

export type UserLockingRewardsDetails = {
  id: Hex;
  lastClaimed: bigint;
  lastClaimedTimestamp: bigint;
  lastClaimedWeek: bigint;
  totalClaimed: bigint;
};

export type UserTotalRewards = {
  id: Hex;
  staking: bigint;
  locking: bigint;
};

export type UserRewardsSubEntity = {
  id: Hex;
  staking: UserStakingRewardsDetails;
  locking: UserLockingRewardsDetails;
  totals: UserTotalRewards;
};

export type LpUserDetails = {
  id: Hex;
  balances: UserBalances;
  lockingDetails: UserLockingDetails;
  rewards: UserRewardsSubEntity;
};

export type SPollenUserDetails = {
  id: Hex;
  balances: UserBalances;
  lockingDetails: UserLockingDetails;
  rewards: UserRewardsSubEntity;
};

export type User = {
  id: Hex;
  lp?: LpUserDetails | null;
  spollen?: SPollenUserDetails | null;
};

// Direct LpUser and SPollenUser types (if queried directly)
// These link back to the main User entity
export type LpUser = {
  id: Hex;
  user: { id: Hex }; // Link to User entity
  balances: UserBalances; // Uses the existing UserBalances for nested structure
  lockingDetails: UserLockingDetails; // Uses existing UserLockingDetails
  rewards: UserRewardsSubEntity; // Uses existing UserRewardsSubEntity
};

export type SPollenUser = {
  id: Hex;
  user: { id: Hex }; // Link to User entity
  balances: UserBalances; // Uses the existing UserBalances for nested structure
  lockingDetails: UserLockingDetails; // Uses existing UserLockingDetails
  rewards: UserRewardsSubEntity; // Uses existing UserRewardsSubEntity
};

// Protocol Details Types
export type ProtocolRates = {
  id: Hex;
  stakingPct: bigint;
  lockingPct: bigint;
  buybackStakingAprDaily: bigint;
  buybackStakingAprWeekly: bigint;
  calculatedLockingAprWeekly: bigint;
  lpProtocol?: { id: Hex } | null; // Link for direct query
  spollenProtocol?: { id: Hex } | null; // Link for direct query
};

export type ProtocolTotals = {
  id: Hex;
  staked?: bigint | null;
  locked?: bigint | null;
  wrapped?: bigint | null;
};

export type ProtocolRewardsSubEntity = {
  id: Hex;
  weeklyStaking: bigint;
  weeklyLocking: bigint;
  lastLockingWeek: bigint;
  allTimeStaking: bigint;
  allTimeLocking: bigint;
};

export type LpClaims = {
  id: Hex;
  allTimeStaking: bigint;
  allTimeLocking: bigint;
  allTimeBuyback: bigint;
  iBGT: bigint;
  iRED: bigint;
  lpProtocol: { id: Hex }; // Link for direct query
};

export type SPollenClaims = {
  id: Hex;
  allTimeStaking: bigint;
  allTimeLocking: bigint;
  allTimeBuyback: bigint;
  oBERO: bigint;
  spollenProtocol: { id: Hex }; // Link for direct query
};

export type LpProtocolDetails = {
  id: Hex;
  rates: ProtocolRates;
  totals: ProtocolTotals;
  rewards: ProtocolRewardsSubEntity;
  claims: LpClaims;
};

export type SPollenProtocolDetails = {
  id: Hex;
  rates: ProtocolRates;
  totals: ProtocolTotals;
  rewards: ProtocolRewardsSubEntity;
  claims: SPollenClaims;
};

export type ProtocolPollen = {
  id: Hex;
  lp?: LpProtocolDetails | null;
  spollen?: SPollenProtocolDetails | null;
};

// Specific Protocol Totals Types to match schema entities
export type LpProtocolTotalsType = {
  id: Hex;
  staked: bigint;
  locked: bigint;
};

export type SPollenProtocolTotalsType = {
  id: Hex;
  wrapped: bigint;
  staked: bigint;
  locked: bigint;
};

// For direct querying of LpProtocol entities, matching schema
export type LpProtocol = {
  id: Hex;
  protocol: { id: Hex };
  rates: ProtocolRates;
  totals: LpProtocolTotalsType;
  rewards: ProtocolRewardsSubEntity;
  claims: LpClaims;
};

// For direct querying of SPollenProtocol entities, matching schema
export type SPollenProtocol = {
  id: Hex;
  protocol: { id: Hex };
  rates: ProtocolRates; // Assumes ProtocolRates is fetched as part of this
  totals: SPollenProtocolTotalsType; // Uses existing SPollenProtocolTotalsType for nested structure
  rewards: ProtocolRewardsSubEntity; // Uses existing ProtocolRewardsSubEntity
  claims: SPollenClaims; // Updated reference
};

// APR Bucket Types
export type StakingAprBucket = {
  id: string;
  bucketType: string;
  slotIndex: bigint;
  startTime: bigint;
  lastTime: bigint;
  cumulativePollenDistributed: bigint;
  cumulativeSPollenSupply: bigint;
  cumulativeStakedLpValueInPollen: bigint;
  updateCount: bigint;
};

export type LockingAprBucket = {
  id: string;
  bucketType: string;
  slotIndex: bigint;
  startTime: bigint;
  lastTime: bigint;
  cumulativeRewardDistributed: bigint;
  cumulativeTotalSupplyStored: bigint;
  updateCount: bigint;
};

// --- START OF NEWLY ADDED/MODIFIED TYPES FOR DIRECT ENTITY QUERIES ---

// LpBalances Entity (Direct Query Type)
export type LpBalances = {
  id: Hex;
  staked: bigint;
  locked: bigint;
  lpUser: { id: Hex }; // Link to LpUser
};

// SPollenBalances Entity (Direct Query Type)
export type SPollenBalances = {
  id: Hex;
  wrapped: bigint;
  staked: bigint;
  locked: bigint;
  spollenUser: { id: Hex }; // Link to SPollenUser
};

// LockingDetails Entity (Direct Query Type)
// Note: UserLockingDetails is a subset and can remain for specific nested use cases.
export type LockingDetails = {
  id: Hex;
  amount: bigint;
  expiry: bigint;
  lpUser?: { id: Hex } | null; // Link to LpUser
  spollenUser?: { id: Hex } | null; // Link to SPollenUser
};

// StakingRewards Entity (Direct Query Type)
// Note: UserStakingRewardsDetails is a subset.
export type StakingRewards = {
  id: Hex;
  lastClaimed: bigint;
  totalClaimed: bigint;
  userRewards: { id: Hex }; // Link to UserRewards
};

// LockingRewards Entity (Direct Query Type)
// Note: UserLockingRewardsDetails is a subset.
export type LockingRewards = {
  id: Hex;
  lastClaimed: bigint;
  lastClaimedTimestamp: bigint;
  lastClaimedWeek: bigint;
  totalClaimed: bigint;
  userRewards: { id: Hex }; // Link to UserRewards
};

// TotalRewards Entity (Direct Query Type)
// Note: UserTotalRewards is a subset.
export type TotalRewards = {
  id: Hex;
  staking: bigint;
  locking: bigint;
  userRewards: { id: Hex }; // Link to UserRewards
};

// UserRewards Entity (Direct Query Type)
// Note: UserRewardsSubEntity is a subset.
export type UserRewards = {
  id: Hex;
  staking: StakingRewards; // Uses the new full StakingRewards type
  locking: LockingRewards; // Uses the new full LockingRewards type
  totals: TotalRewards; // Uses the new full TotalRewards type
  lpUser?: { id: Hex } | null; // Link to LpUser
  spollenUser?: { id: Hex } | null; // Link to SPollenUser
};

// LpProtocolTotals Entity (Direct Query Type)
// Note: LpProtocolTotalsType is a subset.
export type LpProtocolTotals = {
  id: Hex;
  staked: bigint;
  locked: bigint;
  lpProtocol: { id: Hex }; // Link to LpProtocol
};

// SPollenProtocolTotals Entity (Direct Query Type)
// Note: SPollenProtocolTotalsType is a subset.
export type SPollenProtocolTotals = {
  id: Hex;
  wrapped: bigint;
  staked: bigint;
  locked: bigint;
  spollenProtocol: { id: Hex }; // Link to SPollenProtocol
};

// ProtocolRewards Entity (Direct Query Type)
// Note: ProtocolRewardsSubEntity is a subset.
export type ProtocolRewards = {
  id: Hex;
  weeklyStaking: bigint;
  weeklyLocking: bigint;
  lastLockingWeek: bigint;
  allTimeStaking: bigint;
  allTimeLocking: bigint;
  lpProtocol?: { id: Hex } | null; // Link to LpProtocol
  spollenProtocol?: { id: Hex } | null; // Link to SPollenProtocol
};
