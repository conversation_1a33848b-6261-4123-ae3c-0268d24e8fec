import { Apollo<PERSON><PERSON>, HttpLink, InMemoryCache, NormalizedCacheObject, ApolloQueryResult, DefaultOptions, DocumentNode, OperationVariables } from "@apollo/client/core";
import { Hex } from "viem";
import { BeraBorrowConnection } from "../types";
import * as Queries from "./queries/pollen";
import * as Types from "./types";

export class SubgraphPollenClient {
  private apolloClient: ApolloClient<NormalizedCacheObject>;
  private connection: BeraBorrowConnection;

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    const defaultOptions: DefaultOptions = {
      watchQuery: {
        fetchPolicy: "no-cache",
        errorPolicy: "ignore",
      },
      query: {
        fetchPolicy: "no-cache",
        errorPolicy: "all",
      },
    };

    this.apolloClient = new ApolloClient({
      link: new HttpLink({
        uri: this.connection.SUBGRAPH_POLLEN_API_URL,
        fetch,
        headers: this.connection.SUBGRAPH_POLLEN_API_KEY ? { Authorization: `Bearer ${this.connection.SUBGRAPH_POLLEN_API_KEY}` } : undefined,
      }),
      cache: new InMemoryCache(),
      defaultOptions,
    });
  }

  /**
   * @notice Executes any GraphQL query.
   * @param query GraphQL Query to be executed.
   * @param variables Variables to be passed to construct the query.
   * @returns ApolloQueryResult<T>
   */
  async execute<T>(query: DocumentNode, variables?: OperationVariables): Promise<ApolloQueryResult<T>> {
    return this.apolloClient.query({
      query,
      variables,
    });
  }

  /**
   * @dev Returns all user staking position and reward data
   * @param userAddress User address
   * @returns Types.User | null data, or null if not found
   */
  async getUserStakingPosition(userAddress: Hex): Promise<Types.User | null> {
    const res = await this.execute<{
      user: Types.User;
    }>(Queries.getUserDetailsQuery(), { userId: userAddress });

    return res.data.user;
  }

  /**
   * @dev Returns all user locking position and reward data
   * @param userAddress User address
   * @returns Types.User | null data, or null if not found
   */
  async getUserLockPosition(userAddress: Hex): Promise<Types.User | null> {
    const res = await this.execute<{
      user: Types.User;
    }>(Queries.getUserDetailsQuery(), { userId: userAddress });

    return res.data.user;
  }

  /**
   * @dev Returns all protocol staking data for a given protocol ID
   * @param protocolId The ID of the protocol
   * @returns Types.ProtocolPollen | null data, or null if not found
   */
  async getProtocolStaking(protocolId: Hex): Promise<Types.ProtocolPollen | null> {
    const res = await this.execute<{
      protocol: Types.ProtocolPollen;
    }>(Queries.getProtocolDetailsQuery(), { protocolId: protocolId });

    return res.data.protocol;
  }

  /**
   * @dev Returns all protocol locking data for a given protocol ID
   * @param protocolId The ID of the protocol
   * @returns Types.ProtocolPollen | null data, or null if not found
   */
  async getProtocolLocked(protocolId: Hex): Promise<Types.ProtocolPollen | null> {
    const res = await this.execute<{
      protocol: Types.ProtocolPollen;
    }>(Queries.getProtocolDetailsQuery(), { protocolId: protocolId });

    return res.data.protocol;
  }

  /**
   * @dev Returns all protocol wrapped data for a given protocol ID
   * @param protocolId The ID of the protocol
   * @returns Types.ProtocolPollen | null data, or null if not found
   */
  async getProtocolWrapped(protocolId: Hex): Promise<Types.ProtocolPollen | null> {
    const res = await this.execute<{
      protocol: Types.ProtocolPollen;
    }>(Queries.getProtocolDetailsQuery(), { protocolId: protocolId });

    return res.data.protocol;
  }

  /**
   * @dev Returns all protocol buyback data for a given protocol ID
   * @param protocolId The ID of the protocol
   * @returns Types.ProtocolPollen | null data, or null if not found
   */
  async getProtocolBuyback(protocolId: Hex): Promise<Types.ProtocolPollen | null> {
    const res = await this.execute<{
      protocol: Types.ProtocolPollen;
    }>(Queries.getProtocolDetailsQuery(), { protocolId: protocolId });

    return res.data.protocol;
  }

  // Paused Entity Queries
  /**
   * @dev Retrieves a Paused event by its ID.
   * @param id The ID of the Paused event.
   * @returns Types.PausedEvent data or null if not found.
   */
  async getPausedById(id: Hex): Promise<Types.PausedEvent | null> {
    const res = await this.execute<{ paused: Types.PausedEvent | null }>(Queries.getPausedByIdQuery(), { id });
    return res.data.paused;
  }

  /**
   * @dev Retrieves all Paused events.
   * @returns Types.PausedEvent[] data.
   */
  async getAllPausedEvents(): Promise<Types.PausedEvent[]> {
    const res = await this.execute<{ pauseds: Types.PausedEvent[] }>(Queries.getAllPausedQuery());
    return res.data.pauseds;
  }

  // Recovered Entity Queries
  /**
   * @dev Retrieves a Recovered event by its ID.
   * @param id The ID of the Recovered event.
   * @returns Types.RecoveredEvent data or null if not found.
   */
  async getRecoveredById(id: Hex): Promise<Types.RecoveredEvent | null> {
    const res = await this.execute<{ recovered: Types.RecoveredEvent | null }>(Queries.getRecoveredByIdQuery(), { id });
    return res.data.recovered;
  }

  /**
   * @dev Retrieves all Recovered events for a specific token.
   * @param token The token address.
   * @returns Types.RecoveredEvent[] data.
   */
  async getRecoveredByToken(token: Hex): Promise<Types.RecoveredEvent[]> {
    const res = await this.execute<{ recovereds: Types.RecoveredEvent[] }>(Queries.getRecoveredByTokenQuery(), { token });
    return res.data.recovereds;
  }

  /**
   * @dev Retrieves all Recovered events.
   * @returns Types.RecoveredEvent[] data.
   */
  async getAllRecoveredEvents(): Promise<Types.RecoveredEvent[]> {
    const res = await this.execute<{ recovereds: Types.RecoveredEvent[] }>(Queries.getAllRecoveredEventsQuery());
    return res.data.recovereds;
  }

  // RewardAdded Entity Queries
  /**
   * @dev Retrieves a RewardAdded event by its ID.
   * @param id The ID of the RewardAdded event.
   * @returns Types.RewardAddedEvent data or null if not found.
   */
  async getRewardAddedById(id: Hex): Promise<Types.RewardAddedEvent | null> {
    const res = await this.execute<{ rewardAdded: Types.RewardAddedEvent | null }>(Queries.getRewardAddedByIdQuery(), { id });
    return res.data.rewardAdded;
  }

  /**
   * @dev Retrieves all RewardAdded events for a specific rewards token.
   * @param rewardsToken The rewards token address.
   * @returns Types.RewardAddedEvent[] data.
   */
  async getRewardAddedByToken(rewardsToken: Hex): Promise<Types.RewardAddedEvent[]> {
    const res = await this.execute<{ rewardAddeds: Types.RewardAddedEvent[] }>(Queries.getRewardAddedByTokenQuery(), { rewardsToken });
    return res.data.rewardAddeds;
  }

  /**
   * @dev Retrieves all RewardAdded events.
   * @returns Types.RewardAddedEvent[] data.
   */
  async getAllRewardAddedEvents(): Promise<Types.RewardAddedEvent[]> {
    const res = await this.execute<{ rewardAddeds: Types.RewardAddedEvent[] }>(Queries.getAllRewardAddedEventsQuery());
    return res.data.rewardAddeds;
  }

  // RewardPaid Entity Queries
  /**
   * @dev Retrieves a RewardPaid event by its ID.
   * @param id The ID of the RewardPaid event.
   * @returns Types.RewardPaidEvent data or null if not found.
   */
  async getRewardPaidById(id: Hex): Promise<Types.RewardPaidEvent | null> {
    const res = await this.execute<{ rewardPaid: Types.RewardPaidEvent | null }>(Queries.getRewardPaidByIdQuery(), { id });
    return res.data.rewardPaid;
  }

  /**
   * @dev Retrieves all RewardPaid events for a specific user.
   * @param userAddress The user's address.
   * @returns Types.RewardPaidEvent[] data.
   */
  async getRewardsPaidByUser(userAddress: Hex): Promise<Types.RewardPaidEvent[]> {
    const res = await this.execute<{ rewardPaids: Types.RewardPaidEvent[] }>(Queries.getRewardsPaidByUserQuery(), { user: userAddress });
    return res.data.rewardPaids;
  }

  /**
   * @dev Retrieves all RewardPaid events.
   * @returns Types.RewardPaidEvent[] data.
   */
  async getAllRewardPaidEvents(): Promise<Types.RewardPaidEvent[]> {
    const res = await this.execute<{ rewardPaids: Types.RewardPaidEvent[] }>(Queries.getAllRewardPaidEventsQuery());
    return res.data.rewardPaids;
  }

  // RewardRemoved Entity Queries
  /**
   * @dev Retrieves a RewardRemoved event by its ID.
   * @param id The ID of the RewardRemoved event.
   * @returns Types.RewardRemovedEvent data or null if not found.
   */
  async getRewardRemovedById(id: Hex): Promise<Types.RewardRemovedEvent | null> {
    const res = await this.execute<{ rewardRemoved: Types.RewardRemovedEvent | null }>(Queries.getRewardRemovedByIdQuery(), { id });
    return res.data.rewardRemoved;
  }

  /**
   * @dev Retrieves all RewardRemoved events for a specific rewards token.
   * @param rewardsToken The rewards token address.
   * @returns Types.RewardRemovedEvent[] data.
   */
  async getRewardRemovedByToken(rewardsToken: Hex): Promise<Types.RewardRemovedEvent[]> {
    const res = await this.execute<{ rewardRemoveds: Types.RewardRemovedEvent[] }>(Queries.getRewardRemovedByTokenQuery(), { rewardsToken });
    return res.data.rewardRemoveds;
  }

  /**
   * @dev Retrieves all RewardRemoved events.
   * @returns Types.RewardRemovedEvent[] data.
   */
  async getAllRewardRemovedEvents(): Promise<Types.RewardRemovedEvent[]> {
    const res = await this.execute<{ rewardRemoveds: Types.RewardRemovedEvent[] }>(Queries.getAllRewardRemovedEventsQuery());
    return res.data.rewardRemoveds;
  }

  // RewardStored Entity Queries
  /**
   * @dev Retrieves a RewardStored event by its ID.
   * @param id The ID of the RewardStored event.
   * @returns Types.RewardStoredEvent data or null if not found.
   */
  async getRewardStoredById(id: Hex): Promise<Types.RewardStoredEvent | null> {
    const res = await this.execute<{ rewardStored: Types.RewardStoredEvent | null }>(Queries.getRewardStoredByIdQuery(), { id });
    return res.data.rewardStored;
  }

  /**
   * @dev Retrieves all RewardStored events for a specific rewards token.
   * @param rewardsToken The rewards token address.
   * @returns Types.RewardStoredEvent[] data.
   */
  async getRewardStoredByToken(rewardsToken: Hex): Promise<Types.RewardStoredEvent[]> {
    const res = await this.execute<{ rewardStoreds: Types.RewardStoredEvent[] }>(Queries.getRewardStoredByTokenQuery(), { rewardsToken });
    return res.data.rewardStoreds;
  }

  /**
   * @dev Retrieves all RewardStored events.
   * @returns Types.RewardStoredEvent[] data.
   */
  async getAllRewardStoredEvents(): Promise<Types.RewardStoredEvent[]> {
    const res = await this.execute<{ rewardStoreds: Types.RewardStoredEvent[] }>(Queries.getAllRewardStoredEventsQuery());
    return res.data.rewardStoreds;
  }

  // RewardsDurationUpdated Entity Queries
  /**
   * @dev Retrieves a RewardsDurationUpdated event by its ID.
   * @param id The ID of the RewardsDurationUpdated event.
   * @returns Types.RewardsDurationUpdatedEvent data or null if not found.
   */
  async getRewardsDurationUpdatedById(id: Hex): Promise<Types.RewardsDurationUpdatedEvent | null> {
    const res = await this.execute<{ rewardsDurationUpdated: Types.RewardsDurationUpdatedEvent | null }>(Queries.getRewardsDurationUpdatedByIdQuery(), { id });
    return res.data.rewardsDurationUpdated;
  }

  /**
   * @dev Retrieves all RewardsDurationUpdated events for a specific token.
   * @param token The token address.
   * @returns Types.RewardsDurationUpdatedEvent[] data.
   */
  async getRewardsDurationUpdatedByToken(token: Hex): Promise<Types.RewardsDurationUpdatedEvent[]> {
    const res = await this.execute<{ rewardsDurationUpdateds: Types.RewardsDurationUpdatedEvent[] }>(Queries.getRewardsDurationUpdatedByTokenQuery(), { token });
    return res.data.rewardsDurationUpdateds;
  }

  /**
   * @dev Retrieves all RewardsDurationUpdated events.
   * @returns Types.RewardsDurationUpdatedEvent[] data.
   */
  async getAllRewardsDurationUpdatedEvents(): Promise<Types.RewardsDurationUpdatedEvent[]> {
    const res = await this.execute<{ rewardsDurationUpdateds: Types.RewardsDurationUpdatedEvent[] }>(Queries.getAllRewardsDurationUpdatedEventsQuery());
    return res.data.rewardsDurationUpdateds;
  }

  // Staked Entity Queries
  /**
   * @dev Retrieves a Staked event by its ID.
   * @param id The ID of the Staked event.
   * @returns Types.StakedEvent data or null if not found.
   */
  async getStakedById(id: Hex): Promise<Types.StakedEvent | null> {
    const res = await this.execute<{ staked: Types.StakedEvent | null }>(Queries.getStakedByIdQuery(), { id });
    return res.data.staked;
  }

  /**
   * @dev Retrieves all Staked events for a specific user.
   * @param userAddress The user's address.
   * @returns Types.StakedEvent[] data.
   */
  async getStakedEventsByUser(userAddress: Hex): Promise<Types.StakedEvent[]> {
    const res = await this.execute<{ stakeds: Types.StakedEvent[] }>(Queries.getStakedEventsByUserQuery(), { user: userAddress });
    return res.data.stakeds;
  }

  /**
   * @dev Retrieves all Staked events.
   * @returns Types.StakedEvent[] data.
   */
  async getAllStakedEvents(): Promise<Types.StakedEvent[]> {
    const res = await this.execute<{ stakeds: Types.StakedEvent[] }>(Queries.getAllStakedEventsQuery());
    return res.data.stakeds;
  }

  // Unpaused Entity Queries
  /**
   * @dev Retrieves an Unpaused event by its ID.
   * @param id The ID of the Unpaused event.
   * @returns Types.UnpausedEvent data or null if not found.
   */
  async getUnpausedById(id: Hex): Promise<Types.UnpausedEvent | null> {
    const res = await this.execute<{ unpaused: Types.UnpausedEvent | null }>(Queries.getUnpausedByIdQuery(), { id });
    return res.data.unpaused;
  }

  /**
   * @dev Retrieves all Unpaused events.
   * @returns Types.UnpausedEvent[] data.
   */
  async getAllUnpausedEvents(): Promise<Types.UnpausedEvent[]> {
    const res = await this.execute<{ unpauseds: Types.UnpausedEvent[] }>(Queries.getAllUnpausedQuery());
    return res.data.unpauseds;
  }

  /**
   * @dev Retrieves all Unpaused events for a specific account.
   * @param account The account address.
   * @returns Types.UnpausedEvent[] data.
   */
  async getUnpausedEventsByAccount(account: Hex): Promise<Types.UnpausedEvent[]> {
    const res = await this.execute<{ unpauseds: Types.UnpausedEvent[] }>(Queries.getUnpausedEventsByAccountQuery(), { account });
    return res.data.unpauseds;
  }

  // Withdrawn Entity Queries
  /**
   * @dev Retrieves a Withdrawn event by its ID.
   * @param id The ID of the Withdrawn event.
   * @returns Types.WithdrawnEvent data or null if not found.
   */
  async getWithdrawnById(id: Hex): Promise<Types.WithdrawnEvent | null> {
    const res = await this.execute<{ withdrawn: Types.WithdrawnEvent | null }>(Queries.getWithdrawnByIdQuery(), { id });
    return res.data.withdrawn;
  }

  /**
   * @dev Retrieves all Withdrawn events for a specific user.
   * @param userAddress The user's address.
   * @returns Types.WithdrawnEvent[] data.
   */
  async getWithdrawnEventsByUser(userAddress: Hex): Promise<Types.WithdrawnEvent[]> {
    const res = await this.execute<{ withdrawns: Types.WithdrawnEvent[] }>(Queries.getWithdrawnEventsByUserQuery(), { user: userAddress });
    return res.data.withdrawns;
  }

  /**
   * @dev Retrieves all Withdrawn events.
   * @returns Types.WithdrawnEvent[] data.
   */
  async getAllWithdrawnEvents(): Promise<Types.WithdrawnEvent[]> {
    const res = await this.execute<{ withdrawns: Types.WithdrawnEvent[] }>(Queries.getAllWithdrawnEventsQuery());
    return res.data.withdrawns;
  }

  // ClaimReward Entity Queries
  /**
   * @dev Retrieves a ClaimReward event by its ID.
   * @param id The ID of the ClaimReward event.
   * @returns Types.ClaimRewardEvent data or null if not found.
   */
  async getClaimRewardById(id: Hex): Promise<Types.ClaimRewardEvent | null> {
    const res = await this.execute<{ claimReward: Types.ClaimRewardEvent | null }>(Queries.getClaimRewardByIdQuery(), { id });
    return res.data.claimReward;
  }

  /**
   * @dev Retrieves all ClaimReward events for a specific user.
   * @param userAddress The user's address.
   * @returns Types.ClaimRewardEvent[] data.
   */
  async getClaimRewardsByUser(userAddress: Hex): Promise<Types.ClaimRewardEvent[]> {
    const res = await this.execute<{ claimRewards: Types.ClaimRewardEvent[] }>(Queries.getClaimRewardsByUserQuery(), { user: userAddress });
    return res.data.claimRewards;
  }

  /**
   * @dev Retrieves all ClaimReward events for a specific source.
   * @param source The source address.
   * @returns Types.ClaimRewardEvent[] data.
   */
  async getClaimRewardsBySource(source: Hex): Promise<Types.ClaimRewardEvent[]> {
    const res = await this.execute<{ claimRewards: Types.ClaimRewardEvent[] }>(Queries.getClaimRewardsBySourceQuery(), { source });
    return res.data.claimRewards;
  }

  /**
   * @dev Retrieves all ClaimReward events.
   * @returns Types.ClaimRewardEvent[] data.
   */
  async getAllClaimRewardEvents(): Promise<Types.ClaimRewardEvent[]> {
    const res = await this.execute<{ claimRewards: Types.ClaimRewardEvent[] }>(Queries.getAllClaimRewardEventsQuery());
    return res.data.claimRewards;
  }

  // SourceAdded Entity Queries
  /**
   * @dev Retrieves a SourceAdded event by its ID.
   * @param id The ID of the SourceAdded event.
   * @returns Types.SourceAddedEvent data or null if not found.
   */
  async getSourceAddedById(id: Hex): Promise<Types.SourceAddedEvent | null> {
    const res = await this.execute<{ sourceAdded: Types.SourceAddedEvent | null }>(Queries.getSourceAddedByIdQuery(), { id });
    return res.data.sourceAdded;
  }

  /**
   * @dev Retrieves all SourceAdded events for a specific source.
   * @param source The source address.
   * @returns Types.SourceAddedEvent[] data.
   */
  async getSourceAddedBySource(source: Hex): Promise<Types.SourceAddedEvent[]> {
    const res = await this.execute<{ sourceAddeds: Types.SourceAddedEvent[] }>(Queries.getSourceAddedBySourceQuery(), { source });
    return res.data.sourceAddeds;
  }

  /**
   * @dev Retrieves all SourceAdded events.
   * @returns Types.SourceAddedEvent[] data.
   */
  async getAllSourceAddedEvents(): Promise<Types.SourceAddedEvent[]> {
    const res = await this.execute<{ sourceAddeds: Types.SourceAddedEvent[] }>(Queries.getAllSourceAddedEventsQuery());
    return res.data.sourceAddeds;
  }

  // UpdateFee Entity Queries
  /**
   * @dev Retrieves an UpdateFee event by its ID.
   * @param id The ID of the UpdateFee event.
   * @returns Types.UpdateFeeEvent data or null if not found.
   */
  async getUpdateFeeById(id: Hex): Promise<Types.UpdateFeeEvent | null> {
    const res = await this.execute<{ updateFee: Types.UpdateFeeEvent | null }>(Queries.getUpdateFeeByIdQuery(), { id });
    return res.data.updateFee;
  }

  /**
   * @dev Retrieves all UpdateFee events for a specific source.
   * @param source The source address.
   * @returns Types.UpdateFeeEvent[] data.
   */
  async getUpdateFeesBySource(source: Hex): Promise<Types.UpdateFeeEvent[]> {
    const res = await this.execute<{ updateFees: Types.UpdateFeeEvent[] }>(Queries.getUpdateFeesBySourceQuery(), { source });
    return res.data.updateFees;
  }

  /**
   * @dev Retrieves all UpdateFee events.
   * @returns Types.UpdateFeeEvent[] data.
   */
  async getAllUpdateFeeEvents(): Promise<Types.UpdateFeeEvent[]> {
    const res = await this.execute<{ updateFees: Types.UpdateFeeEvent[] }>(Queries.getAllUpdateFeeEventsQuery());
    return res.data.updateFees;
  }

  // NewLockPosition Entity Queries
  /**
   * @dev Retrieves a NewLockPosition event by its ID.
   * @param id The ID of the NewLockPosition event.
   * @returns Types.NewLockPositionEvent data or null if not found.
   */
  async getNewLockPositionById(id: Hex): Promise<Types.NewLockPositionEvent | null> {
    const res = await this.execute<{ newLockPosition: Types.NewLockPositionEvent | null }>(Queries.getNewLockPositionByIdQuery(), { id });
    return res.data.newLockPosition;
  }

  /**
   * @dev Retrieves all NewLockPosition events for a specific user.
   * @param userAddress The user's address.
   * @returns Types.NewLockPositionEvent[] data.
   */
  async getNewLockPositionsByUser(userAddress: Hex): Promise<Types.NewLockPositionEvent[]> {
    const res = await this.execute<{ newLockPositions: Types.NewLockPositionEvent[] }>(Queries.getNewLockPositionsByUserQuery(), { user: userAddress });
    return res.data.newLockPositions;
  }

  /**
   * @dev Retrieves all NewLockPosition events within a given time range.
   * @param startTimestamp Start timestamp (Unix seconds).
   * @param endTimestamp End timestamp (Unix seconds).
   * @returns Types.NewLockPositionEvent[] data.
   */
  async getNewLockPositionsByTimeRange(startTimestamp: number, endTimestamp: number): Promise<Types.NewLockPositionEvent[]> {
    const res = await this.execute<{ newLockPositions: Types.NewLockPositionEvent[] }>(Queries.getNewLockPositionsByTimeRangeQuery(), { start: startTimestamp, end: endTimestamp });
    return res.data.newLockPositions;
  }

  /**
   * @dev Retrieves all NewLockPosition events.
   * @returns Types.NewLockPositionEvent[] data.
   */
  async getAllNewLockPositionEvents(): Promise<Types.NewLockPositionEvent[]> {
    const res = await this.execute<{ newLockPositions: Types.NewLockPositionEvent[] }>(Queries.getAllNewLockPositionEventsQuery());
    return res.data.newLockPositions;
  }

  // Deposit Entity Queries
  /**
   * @dev Retrieves a Deposit event by its ID.
   * @param id The ID of the Deposit event.
   * @returns Types.DepositEvent data or null if not found.
   */
  async getDepositById(id: Hex): Promise<Types.DepositEvent | null> {
    const res = await this.execute<{ deposit: Types.DepositEvent | null }>(Queries.getDepositByIdQuery(), { id });
    return res.data.deposit;
  }

  /**
   * @dev Retrieves all Deposit events for a specific token.
   * @param token The token address.
   * @returns Types.DepositEvent[] data.
   */
  async getDepositsByToken(token: Hex): Promise<Types.DepositEvent[]> {
    const res = await this.execute<{ deposits: Types.DepositEvent[] }>(Queries.getDepositsByTokenQuery(), { token });
    return res.data.deposits;
  }

  /**
   * @dev Retrieves all Deposit events.
   * @returns Types.DepositEvent[] data.
   */
  async getAllDepositEvents(): Promise<Types.DepositEvent[]> {
    const res = await this.execute<{ deposits: Types.DepositEvent[] }>(Queries.getAllDepositEventsQuery());
    return res.data.deposits;
  }

  // Withdraw Entity Queries (GenericWithdrawEvent)
  /**
   * @dev Retrieves a generic Withdraw event by its ID.
   * @param id The ID of the Withdraw event.
   * @returns Types.GenericWithdrawEvent data or null if not found.
   */
  async getWithdrawEventById(id: Hex): Promise<Types.GenericWithdrawEvent | null> {
    const res = await this.execute<{ withdraw: Types.GenericWithdrawEvent | null }>(Queries.getWithdrawEventByIdQuery(), { id });
    return res.data.withdraw;
  }

  /**
   * @dev Retrieves all generic Withdraw events for a specific token.
   * @param token The token address.
   * @returns Types.GenericWithdrawEvent[] data.
   */
  async getWithdrawEventsByToken(token: Hex): Promise<Types.GenericWithdrawEvent[]> {
    const res = await this.execute<{ withdraws: Types.GenericWithdrawEvent[] }>(Queries.getWithdrawEventsByTokenQuery(), { token });
    return res.data.withdraws;
  }

  /**
   * @dev Retrieves all generic Withdraw events.
   * @returns Types.GenericWithdrawEvent[] data.
   */
  async getAllWithdrawEvents(): Promise<Types.GenericWithdrawEvent[]> {
    const res = await this.execute<{ withdraws: Types.GenericWithdrawEvent[] }>(Queries.getAllGenericWithdrawEventsQuery());
    return res.data.withdraws;
  }

  // LockWithdraw Entity Queries
  /**
   * @dev Retrieves a LockWithdraw event by its ID.
   * @param id The ID of the LockWithdraw event.
   * @returns Types.LockWithdrawEvent data or null if not found.
   */
  async getLockWithdrawById(id: Hex): Promise<Types.LockWithdrawEvent | null> {
    const res = await this.execute<{ lockWithdraw: Types.LockWithdrawEvent | null }>(Queries.getLockWithdrawByIdQuery(), { id });
    return res.data.lockWithdraw;
  }

  /**
   * @dev Retrieves all LockWithdraw events for a specific user.
   * @param userAddress The user's address.
   * @returns Types.LockWithdrawEvent[] data.
   */
  async getLockWithdrawsByUser(userAddress: Hex): Promise<Types.LockWithdrawEvent[]> {
    const res = await this.execute<{ lockWithdraws: Types.LockWithdrawEvent[] }>(Queries.getLockWithdrawsByUserQuery(), { user: userAddress });
    return res.data.lockWithdraws;
  }

  /**
   * @dev Retrieves all LockWithdraw events.
   * @returns Types.LockWithdrawEvent[] data.
   */
  async getAllLockWithdrawEvents(): Promise<Types.LockWithdrawEvent[]> {
    const res = await this.execute<{ lockWithdraws: Types.LockWithdrawEvent[] }>(Queries.getAllLockWithdrawEventsQuery());
    return res.data.lockWithdraws;
  }

  // RewardClaimed Entity Queries
  /**
   * @dev Retrieves a RewardClaimed event by its ID.
   * @param id The ID of the RewardClaimed event.
   * @returns Types.RewardClaimedEvent data or null if not found.
   */
  async getRewardClaimedEventById(id: Hex): Promise<Types.RewardClaimedEvent | null> {
    const res = await this.execute<{ rewardClaimed: Types.RewardClaimedEvent | null }>(Queries.getRewardClaimedEventByIdQuery(), { id });
    return res.data.rewardClaimed;
  }

  /**
   * @dev Retrieves all RewardClaimed events for a specific token.
   * @param token The token address.
   * @returns Types.RewardClaimedEvent[] data.
   */
  async getRewardClaimedEventsByToken(token: Hex): Promise<Types.RewardClaimedEvent[]> {
    const res = await this.execute<{ rewardClaimeds: Types.RewardClaimedEvent[] }>(Queries.getRewardClaimedEventsByTokenQuery(), { token });
    return res.data.rewardClaimeds;
  }

  /**
   * @dev Retrieves all RewardClaimed events.
   * @returns Types.RewardClaimedEvent[] data.
   */
  async getAllRewardClaimedEvents(): Promise<Types.RewardClaimedEvent[]> {
    const res = await this.execute<{ rewardClaimeds: Types.RewardClaimedEvent[] }>(Queries.getAllRewardClaimedEventsQuery());
    return res.data.rewardClaimeds;
  }

  // Buyback Entity Queries
  /**
   * @dev Retrieves a Buyback event by its ID.
   * @param id The ID of the Buyback event.
   * @returns Types.BuybackEvent data or null if not found.
   */
  async getBuybackById(id: Hex): Promise<Types.BuybackEvent | null> {
    const res = await this.execute<{ buyback: Types.BuybackEvent | null }>(Queries.getBuybackByIdQuery(), { id });
    return res.data.buyback;
  }

  /**
   * @dev Retrieves all Buyback events.
   * @returns Types.BuybackEvent[] data.
   */
  async getAllBuybacks(): Promise<Types.BuybackEvent[]> {
    const res = await this.execute<{ buybacks: Types.BuybackEvent[] }>(Queries.getAllBuybacksQuery());
    return res.data.buybacks;
  }

  // SPollenWrap Entity Queries
  /**
   * @dev Retrieves an SPollenWrap event by its ID.
   * @param id The ID of the SPollenWrap event.
   * @returns Types.SPollenWrapEvent data or null if not found.
   */
  async getSPollenWrapById(id: Hex): Promise<Types.SPollenWrapEvent | null> {
    const res = await this.execute<{ spollenWrap: Types.SPollenWrapEvent | null }>(Queries.getSPollenWrapByIdQuery(), { id });
    return res.data.spollenWrap;
  }

  /**
   * @dev Retrieves all SPollenWrap events for a specific account.
   * @param account The account address.
   * @returns Types.SPollenWrapEvent[] data.
   */
  async getSPollenWrapsByAccount(account: Hex): Promise<Types.SPollenWrapEvent[]> {
    const res = await this.execute<{ spollenWraps: Types.SPollenWrapEvent[] }>(Queries.getSPollenWrapsByAccountQuery(), { account });
    return res.data.spollenWraps;
  }

  /**
   * @dev Retrieves all SPollenWrap events.
   * @returns Types.SPollenWrapEvent[] data.
   */
  async getAllSPollenWrapEvents(): Promise<Types.SPollenWrapEvent[]> {
    const res = await this.execute<{ spollenWraps: Types.SPollenWrapEvent[] }>(Queries.getAllSPollenWrapEventsQuery());
    return res.data.spollenWraps;
  }

  // SPollenUnwrap Entity Queries
  /**
   * @dev Retrieves an SPollenUnwrap event by its ID.
   * @param id The ID of the SPollenUnwrap event.
   * @returns Types.SPollenUnwrapEvent data or null if not found.
   */
  async getSPollenUnwrapById(id: Hex): Promise<Types.SPollenUnwrapEvent | null> {
    const res = await this.execute<{ spollenUnwrap: Types.SPollenUnwrapEvent | null }>(Queries.getSPollenUnwrapByIdQuery(), { id });
    return res.data.spollenUnwrap;
  }

  /**
   * @dev Retrieves all SPollenUnwrap events for a specific account.
   * @param account The account address.
   * @returns Types.SPollenUnwrapEvent[] data.
   */
  async getSPollenUnwrapsByAccount(account: Hex): Promise<Types.SPollenUnwrapEvent[]> {
    const res = await this.execute<{ spollenUnwraps: Types.SPollenUnwrapEvent[] }>(Queries.getSPollenUnwrapsByAccountQuery(), { account });
    return res.data.spollenUnwraps;
  }

  /**
   * @dev Retrieves all SPollenUnwrap events.
   * @returns Types.SPollenUnwrapEvent[] data.
   */
  async getAllSPollenUnwrapEvents(): Promise<Types.SPollenUnwrapEvent[]> {
    const res = await this.execute<{ spollenUnwraps: Types.SPollenUnwrapEvent[] }>(Queries.getAllSPollenUnwrapEventsQuery());
    return res.data.spollenUnwraps;
  }

  // ClaimForVeLock Entity Queries
  /**
   * @dev Retrieves a ClaimForVeLock event by its ID.
   * @param id The ID of the ClaimForVeLock event.
   * @returns Types.ClaimForVeLockEvent data or null if not found.
   */
  async getClaimForVeLockById(id: Hex): Promise<Types.ClaimForVeLockEvent | null> {
    const res = await this.execute<{ claimForVeLock: Types.ClaimForVeLockEvent | null }>(Queries.getClaimForVeLockByIdQuery(), { id });
    return res.data.claimForVeLock;
  }

  /**
   * @dev Retrieves all ClaimForVeLock events for a specific token.
   * @param token The token address.
   * @returns Types.ClaimForVeLockEvent[] data.
   */
  async getClaimForVeLocksByToken(token: Hex): Promise<Types.ClaimForVeLockEvent[]> {
    const res = await this.execute<{ claimForVeLocks: Types.ClaimForVeLockEvent[] }>(Queries.getClaimForVeLockEventsByTokenQuery(), { token });
    return res.data.claimForVeLocks;
  }

  /**
   * @dev Retrieves all ClaimForVeLock events for a specific user.
   * @param userAddress The user's address.
   * @returns Types.ClaimForVeLockEvent[] data.
   */
  async getClaimForVeLocksByUser(userAddress: Hex): Promise<Types.ClaimForVeLockEvent[]> {
    const res = await this.execute<{ claimForVeLocks: Types.ClaimForVeLockEvent[] }>(Queries.getClaimForVeLocksByUserQuery(), { user: userAddress });
    return res.data.claimForVeLocks;
  }

  /**
   * @dev Retrieves all ClaimForVeLock events.
   * @returns Types.ClaimForVeLockEvent[] data.
   */
  async getAllClaimForVeLockEvents(): Promise<Types.ClaimForVeLockEvent[]> {
    const res = await this.execute<{ claimForVeLocks: Types.ClaimForVeLockEvent[] }>(Queries.getAllClaimForVeLockEventsQuery());
    return res.data.claimForVeLocks;
  }

  // User Entity Query
  /**
   * @dev Retrieves detailed information for a specific user.
   * @param userId The ID of the user.
   * @returns Types.User data or null if not found.
   */
  async getUserDetails(userId: Hex): Promise<Types.User | null> {
    const res = await this.execute<{ user: Types.User | null }>(Queries.getUserDetailsQuery(), { userId });
    return res.data.user;
  }

  // LpUser Entity Query
  /**
   * @dev Retrieves detailed information for a specific Lp user.
   * @param lpUserId The ID of the Lp user.
   * @returns Types.LpUser data or null if not found.
   */
  async getLpUser(lpUserId: Hex): Promise<Types.LpUser | null> {
    const res = await this.execute<{ lpUser: Types.LpUser | null }>(Queries.getLpUserQuery(), { lpUserId });
    return res.data.lpUser;
  }

  // SPollenUser Entity Query
  /**
   * @dev Retrieves detailed information for a specific SPollen user.
   * @param spollenUserId The ID of the SPollen user.
   * @returns Types.SPollenUser data or null if not found.
   */
  async getSPollenUser(spollenUserId: Hex): Promise<Types.SPollenUser | null> {
    const res = await this.execute<{ spollenUser: Types.SPollenUser | null }>(Queries.getSPollenUserQuery(), { spollenUserId });
    return res.data.spollenUser;
  }

  // Protocol Entity Query
  /**
   * @dev Retrieves detailed information for a specific protocol.
   * @param protocolId The ID of the protocol.
   * @returns Types.ProtocolPollen data or null if not found.
   */
  async getProtocolDetails(protocolId: Hex): Promise<Types.ProtocolPollen | null> {
    const res = await this.execute<{ protocol: Types.ProtocolPollen | null }>(Queries.getProtocolDetailsQuery(), { protocolId });
    return res.data.protocol;
  }

  // StakingAprBucket Entity Queries
  /**
   * @dev Retrieves a StakingAprBucket by its ID.
   * @param id The ID of the StakingAprBucket.
   * @returns Types.StakingAprBucket data or null if not found.
   */
  async getStakingAprBucketById(id: string): Promise<Types.StakingAprBucket | null> {
    const res = await this.execute<{ stakingAprBucket: Types.StakingAprBucket | null }>(Queries.getStakingAprBucketByIdQuery(), { id });
    return res.data.stakingAprBucket;
  }

  /**
   * @dev Retrieves StakingAprBuckets, optionally filtered by bucketType and slotIndex.
   * @param bucketType The type of bucket (e.g., "DAILY", "WEEKLY").
   * @param slotIndex The slot index of the bucket.
   * @returns Types.StakingAprBucket[] data.
   */
  async getStakingAprBuckets(bucketType?: string, slotIndex?: bigint): Promise<Types.StakingAprBucket[]> {
    const variables: { bucketType?: string; slotIndex?: bigint } = {};
    if (bucketType) variables.bucketType = bucketType;
    if (slotIndex) variables.slotIndex = slotIndex;
    const res = await this.execute<{ stakingAprBuckets: Types.StakingAprBucket[] }>(Queries.getStakingAprBucketsQuery(), variables);
    return res.data.stakingAprBuckets;
  }

  /**
   * @dev Retrieves all StakingAprBuckets.
   * @returns Types.StakingAprBucket[] data.
   */
  async getAllStakingAprBuckets(): Promise<Types.StakingAprBucket[]> {
    const res = await this.execute<{ stakingAprBuckets: Types.StakingAprBucket[] }>(Queries.getAllStakingAprBucketsQuery());
    return res.data.stakingAprBuckets;
  }

  // LockingAprBucket Entity Queries
  /**
   * @dev Retrieves a LockingAprBucket by its ID.
   * @param id The ID of the LockingAprBucket.
   * @returns Types.LockingAprBucket data or null if not found.
   */
  async getLockingAprBucketById(id: string): Promise<Types.LockingAprBucket | null> {
    const res = await this.execute<{ lockingAprBucket: Types.LockingAprBucket | null }>(Queries.getLockingAprBucketByIdQuery(), { id });
    return res.data.lockingAprBucket;
  }

  /**
   * @dev Retrieves LockingAprBuckets, optionally filtered by bucketType and slotIndex.
   * @param bucketType The type of bucket (e.g., "HOURLY", "DAILY").
   * @param slotIndex The slot index of the bucket.
   * @returns Types.LockingAprBucket[] data.
   */
  async getLockingAprBuckets(bucketType?: string, slotIndex?: bigint): Promise<Types.LockingAprBucket[]> {
    const variables: { bucketType?: string; slotIndex?: bigint } = {};
    if (bucketType) variables.bucketType = bucketType;
    if (slotIndex) variables.slotIndex = slotIndex;
    const res = await this.execute<{ lockingAprBuckets: Types.LockingAprBucket[] }>(Queries.getLockingAprBucketsQuery(), variables);
    return res.data.lockingAprBuckets;
  }

  /**
   * @dev Retrieves all LockingAprBuckets.
   * @returns Types.LockingAprBucket[] data.
   */
  async getAllLockingAprBuckets(): Promise<Types.LockingAprBucket[]> {
    const res = await this.execute<{ lockingAprBuckets: Types.LockingAprBucket[] }>(Queries.getAllLockingAprBucketsQuery());
    return res.data.lockingAprBuckets;
  }

  // Example User-specific data retrieval (using the comprehensive User query)
  /**
   * @dev Returns a user's Lp locked amount.
   * @param userId User's address.
   * @returns bigint locked amount or null.
   */
  async getUserLpLockedAmount(userId: Hex): Promise<bigint | null | undefined> {
    const res = await this.execute<{ user: { lp?: { balances: { locked: bigint } } } | null }>(Queries.getUserLpLockedAmountQuery(), { userId });
    return res.data.user?.lp?.balances?.locked;
  }

  // Example Protocol-specific data retrieval (using the comprehensive Protocol query)
  /**
   * @dev Returns total Lp staked amount for the protocol.
   * @param protocolId Protocol ID.
   * @returns bigint total staked amount or null.
   */
  async getProtocolLpTotalStaked(protocolId: Hex): Promise<bigint | null | undefined> {
    const res = await this.execute<{ protocol: { lp?: { totals: { staked: bigint } } } | null }>(Queries.getProtocolLpTotalStakedQuery(), { protocolId });
    return res.data.protocol?.lp?.totals?.staked;
  }

  /**
   * @dev Returns protocol buyback totals.
   * @param protocolId Protocol ID.
   * @returns Object containing buyback totals for Lp and SPollen or null.
   */
  async getProtocolBuybackTotals(protocolId: Hex): Promise<{
    lp?: Types.LpClaims | null; // Updated from Types.LpProtocolClaims
    spollen?: Types.SPollenClaims | null; // Updated from Types.SPollenProtocolClaims
  } | null> {
    const res = await this.execute<{
      protocol: {
        lp?: { claims: Types.LpClaims } | null; // Updated from Types.LpProtocolClaims
        spollen?: { claims: Types.SPollenClaims } | null; // Updated from Types.SPollenProtocolClaims
      } | null;
    }>(Queries.getProtocolBuybackTotalsQuery(), { protocolId });

    if (!res.data.protocol) return null;
    return {
      lp: res.data.protocol.lp?.claims,
      spollen: res.data.protocol.spollen?.claims,
    };
  }

  // User Entity "GetAll" Query
  /**
   * @dev Retrieves all Users.
   * @returns Types.User[] data.
   */
  async getAllUsers(): Promise<Types.User[]> {
    const res = await this.execute<{ users: Types.User[] }>(Queries.getAllUsersQuery());
    return res.data.users;
  }

  // LpUser Entity "GetAll" Query
  /**
   * @dev Retrieves all LpUsers.
   * @returns Types.LpUser[] data.
   */
  async getAllLpUsers(): Promise<Types.LpUser[]> {
    const res = await this.execute<{ lpUsers: Types.LpUser[] }>(Queries.getAllLpUsersQuery());
    return res.data.lpUsers;
  }

  // SPollenUser Entity "GetAll" Query
  /**
   * @dev Retrieves all SPollenUsers.
   * @returns Types.SPollenUser[] data.
   */
  async getAllSPollenUsers(): Promise<Types.SPollenUser[]> {
    const res = await this.execute<{ spollenUsers: Types.SPollenUser[] }>(Queries.getAllSPollenUsersQuery());
    return res.data.spollenUsers;
  }

  // LpProtocol Entity Queries

  /**
   * @dev Retrieves all LpProtocols.
   * @returns Types.LpProtocol[] data.
   */
  async getAllLpProtocols(): Promise<Types.LpProtocol[]> {
    const res = await this.execute<{ lpProtocols: Types.LpProtocol[] }>(Queries.getAllLpProtocolsQuery());
    return res.data.lpProtocols;
  }

  // SPollenProtocol Entity Queries
  /**
   * @dev Retrieves all SPollenProtocols.
   * @returns Types.SPollenProtocol[] data.
   */
  async getAllSPollenProtocols(): Promise<Types.SPollenProtocol[]> {
    const res = await this.execute<{ spollenProtocols: Types.SPollenProtocol[] }>(Queries.getAllSPollenProtocolsQuery());
    return res.data.spollenProtocols;
  }

  // Protocol Entity "GetAll" Query
  /**
   * @dev Retrieves all Protocols.
   * @returns Types.ProtocolPollen[] data.
   */
  async getAllProtocols(): Promise<Types.ProtocolPollen[]> {
    const res = await this.execute<{ protocols: Types.ProtocolPollen[] }>(Queries.getAllProtocolsQuery());
    return res.data.protocols;
  }

  // Additional specific query wrappers that were missing

  /**
   * @dev Retrieves all RewardPaid events for a specific rewards token.
   * @param rewardsToken The rewards token address.
   * @returns Types.RewardPaidEvent[] data.
   */
  async getRewardPaidsByToken(rewardsToken: Hex): Promise<Types.RewardPaidEvent[]> {
    const res = await this.execute<{ rewardPaids: Types.RewardPaidEvent[] }>(Queries.getRewardPaidsByTokenQuery(), { rewardsToken });
    return res.data.rewardPaids;
  }

  // LpBalances Entity Queries
  /**
   * @dev Retrieves LpBalances by its ID.
   * @param id The ID of the LpBalances entity.
   * @returns Types.LpBalances data or null if not found.
   */
  async getLpBalancesById(id: Hex): Promise<Types.LpBalances | null> {
    const res = await this.execute<{ lpBalances: Types.LpBalances | null }>(Queries.getLpBalancesByIdQuery(), { id });
    return res.data.lpBalances;
  }

  /**
   * @dev Retrieves all LpBalances entities.
   * @returns Types.LpBalances[] data.
   */
  async getAllLpBalances(): Promise<Types.LpBalances[]> {
    const res = await this.execute<{ lpBalanceses: Types.LpBalances[] }>(Queries.getAllLpBalancesQuery()); // Changed from lpBalances
    return res.data.lpBalanceses; // Changed from lpBalances
  }

  // SPollenBalances Entity Queries
  /**
   * @dev Retrieves SPollenBalances by its ID.
   * @param id The ID of the SPollenBalances entity.
   * @returns Types.SPollenBalances data or null if not found.
   */
  async getSPollenBalancesById(id: Hex): Promise<Types.SPollenBalances | null> {
    const res = await this.execute<{ spollenBalances: Types.SPollenBalances | null }>(Queries.getSPollenBalancesByIdQuery(), { id });
    return res.data.spollenBalances;
  }

  /**
   * @dev Retrieves all SPollenBalances entities.
   * @returns Types.SPollenBalances[] data.
   */
  async getAllSPollenBalances(): Promise<Types.SPollenBalances[]> {
    const res = await this.execute<{ spollenBalanceses: Types.SPollenBalances[] }>(Queries.getAllSPollenBalancesQuery());
    return res.data.spollenBalanceses;
  }

  // LockingDetails Entity Queries
  /**
   * @dev Retrieves LockingDetails by its ID.
   * @param id The ID of the LockingDetails entity.
   * @returns Types.LockingDetails data or null if not found.
   */
  async getLockingDetailsById(id: Hex): Promise<Types.LockingDetails | null> {
    const res = await this.execute<{ lockingDetails: Types.LockingDetails | null }>(Queries.getLockingDetailsByIdQuery(), { id });
    return res.data.lockingDetails;
  }

  /**
   * @dev Retrieves all LockingDetails entities.
   * @returns Types.LockingDetails[] data.
   */
  async getAllLockingDetails(): Promise<Types.LockingDetails[]> {
    const res = await this.execute<{ lockingDetailses: Types.LockingDetails[] }>(Queries.getAllLockingDetailsQuery());
    return res.data.lockingDetailses;
  }

  // UserRewards Entity Queries
  /**
   * @dev Retrieves UserRewards by its ID.
   * @param id The ID of the UserRewards entity.
   * @returns Types.UserRewards data or null if not found.
   */
  async getUserRewardsById(id: Hex): Promise<Types.UserRewards | null> {
    const res = await this.execute<{ userRewards: Types.UserRewards | null }>(Queries.getUserRewardsByIdQuery(), { id });
    return res.data.userRewards;
  }

  /**
   * @dev Retrieves all UserRewards entities.
   * @returns Types.UserRewards[] data.
   */
  async getAllUserRewards(): Promise<Types.UserRewards[]> {
    const res = await this.execute<{ userRewards: Types.UserRewards[] }>(Queries.getAllUserRewardsQuery());
    return res.data.userRewards;
  }

  // StakingRewards Entity Queries
  /**
   * @dev Retrieves StakingRewards by its ID.
   * @param id The ID of the StakingRewards entity.
   * @returns Types.StakingRewards data or null if not found.
   */
  async getStakingRewardsById(id: Hex): Promise<Types.StakingRewards | null> {
    const res = await this.execute<{ stakingRewards: Types.StakingRewards | null }>(Queries.getStakingRewardsByIdQuery(), { id });
    return res.data.stakingRewards;
  }

  /**
   * @dev Retrieves all StakingRewards entities.
   * @returns Types.StakingRewards[] data.
   */
  async getAllStakingRewards(): Promise<Types.StakingRewards[]> {
    const res = await this.execute<{ stakingRewards: Types.StakingRewards[] }>(Queries.getAllStakingRewardsQuery());
    return res.data.stakingRewards;
  }

  // LockingRewards Entity Queries
  /**
   * @dev Retrieves LockingRewards by its ID.
   * @param id The ID of the LockingRewards entity.
   * @returns Types.LockingRewards data or null if not found.
   */
  async getLockingRewardsById(id: Hex): Promise<Types.LockingRewards | null> {
    const res = await this.execute<{ lockingRewards: Types.LockingRewards | null }>(Queries.getLockingRewardsByIdQuery(), { id });
    return res.data.lockingRewards;
  }

  /**
   * @dev Retrieves all LockingRewards entities.
   * @returns Types.LockingRewards[] data.
   */
  async getAllLockingRewards(): Promise<Types.LockingRewards[]> {
    const res = await this.execute<{ lockingRewards: Types.LockingRewards[] }>(Queries.getAllLockingRewardsQuery());
    return res.data.lockingRewards;
  }

  // TotalRewards Entity Queries
  /**
   * @dev Retrieves TotalRewards by its ID.
   * @param id The ID of the TotalRewards entity.
   * @returns Types.TotalRewards data or null if not found.
   */
  async getTotalRewardsById(id: Hex): Promise<Types.TotalRewards | null> {
    const res = await this.execute<{ totalRewards: Types.TotalRewards | null }>(Queries.getTotalRewardsByIdQuery(), { id });
    return res.data.totalRewards;
  }

  /**
   * @dev Retrieves all TotalRewards entities.
   * @returns Types.TotalRewards[] data.
   */
  async getAllTotalRewards(): Promise<Types.TotalRewards[]> {
    const res = await this.execute<{ totalRewards: Types.TotalRewards[] }>(Queries.getAllTotalRewardsQuery());
    return res.data.totalRewards;
  }

  // ProtocolRates Entity Queries
  /**
   * @dev Retrieves ProtocolRates by its ID.
   * @param id The ID of the ProtocolRates entity.
   * @returns Types.ProtocolRates data or null if not found.
   */
  async getProtocolRatesById(id: Hex): Promise<Types.ProtocolRates | null> {
    const res = await this.execute<{ protocolRates: Types.ProtocolRates | null }>(Queries.getProtocolRatesByIdQuery(), { id });
    return res.data.protocolRates;
  }

  /**
   * @dev Retrieves all ProtocolRates entities.
   * @returns Types.ProtocolRates[] data.
   */
  async getAllProtocolRates(): Promise<Types.ProtocolRates[]> {
    const res = await this.execute<{ protocolRates: Types.ProtocolRates[] }>(Queries.getAllProtocolRatesQuery()); // Ensure this matches if query was protocolRateses
    return res.data.protocolRates;
  }

  // LpProtocolTotals Entity Queries

  /**
   * @dev Retrieves all LpProtocolTotals entities.
   * @returns Types.LpProtocolTotals[] data.
   */
  async getAllLpProtocolTotals(): Promise<Types.LpProtocolTotals[]> {
    const res = await this.execute<{ lpProtocolTotals: Types.LpProtocolTotals[] }>(Queries.getAllLpProtocolTotalsQuery()); // Ensure this matches
    return res.data.lpProtocolTotals;
  }

  // SPollenProtocolTotals Entity Queries

  /**
   * @dev Retrieves all SPollenProtocolTotals entities.
   * @returns Types.SPollenProtocolTotals[] data.
   */
  async getAllSPollenProtocolTotals(): Promise<Types.SPollenProtocolTotals[]> {
    const res = await this.execute<{ spollenProtocolTotals: Types.SPollenProtocolTotals[] }>(Queries.getAllSPollenProtocolTotalsQuery()); // Ensure this matches
    return res.data.spollenProtocolTotals;
  }

  // ProtocolRewards Entity Queries

  /**
   * @dev Retrieves all ProtocolRewards entities.
   * @returns Types.ProtocolRewards[] data.
   */
  async getAllProtocolRewards(): Promise<Types.ProtocolRewards[]> {
    const res = await this.execute<{ protocolRewards: Types.ProtocolRewards[] }>(Queries.getAllProtocolRewardsQuery());
    return res.data.protocolRewards;
  }

  // LpClaims Entity Queries

  /**
   * @dev Retrieves all LpClaims entities.
   * @returns Types.LpClaims[] data.
   */
  async getAllLpClaims(): Promise<Types.LpClaims[]> {
    const res = await this.execute<{ lpClaims: Types.LpClaims[] }>(Queries.getAllLpClaimsQuery());
    return res.data.lpClaims;
  }

  // SPollenClaims Entity Queries

  /**
   * @dev Retrieves all SPollenClaims entities.
   * @returns Types.SPollenClaims[] data.
   */
  async getAllSPollenClaims(): Promise<Types.SPollenClaims[]> {
    const res = await this.execute<{ spollenClaims: Types.SPollenClaims[] }>(Queries.getAllSPollenClaimsQuery()); // Changed from spollenClaims
    return res.data.spollenClaims;
  }
}
