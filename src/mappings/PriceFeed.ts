import { Address, ethereum } from "@graphprotocol/graph-ts";
import { NewCollVaultRegistered, NewOracleRegistered, NewSpotOracleRegistered, PriceFeed } from "../../generated/Factory/PriceFeed";
import { getToken } from "../entities/Token";
import { fetchTokenPriceFromContract, getTokenPrice } from "../entities/TokenPrice";
import { getPriceFeed } from "../entities/PriceFeed";
import { ZERO_ADDRESS } from "../utils/constants";
import { takeFinancialDailySnapshot } from "../entities/DailySnapshot";
import { updatePoolPortfolio } from "../entities/SharePool";
import { getGlobal } from "../entities/Global";
import { createDenManagerSnapshot } from "../entities/DenManager";
import { getVaultSnapshot } from "../entities/Vault";
import { VaultSnapshotType } from "../types/enums";
export function handleNewOracleRegistered(event: NewOracleRegistered): void {
  _handleNewOracleRegistered(event.params.token, event.address, event.block);
}
export function handleNewCollVaultRegistered(event: NewCollVaultRegistered): void {
  _handleNewOracleRegistered(event.params.collVault, event.address, event.block);
}
export function handleNewSpotOracleRegistered(event: NewSpotOracleRegistered): void {
  _handleNewOracleRegistered(event.params.token, event.address, event.block);
}

function _handleNewOracleRegistered(tokenAddress: Address, contractAddress: Address, block: ethereum.Block): void {
  let feed_address = contractAddress;
  let token_address = tokenAddress;
  let feed = getPriceFeed();
  if (feed.address == ZERO_ADDRESS) {
    feed.address = feed_address.toHex();
  }
  let token = getToken(token_address);
  // Fetch the token price from price feed contract
  let feedContract = PriceFeed.bind(contractAddress);
  token.oracle = feed_address.toHex();
  let _price = feedContract.try_fetchPrice(Address.fromString(token.id));

  if (_price.reverted == false) {
    let tokenPrice = getTokenPrice(token_address, block);
    tokenPrice.price = _price.value;
    tokenPrice.save();
    token.price = tokenPrice.id;
  }
  token.save();

  // save token ids
  let tokenIds = feed.tokenIds;
  tokenIds.push(token_address);
  feed.tokenIds = tokenIds;
  feed.save();
  updatePoolPortfolio(block);
  takeFinancialDailySnapshot(block);
}

export function handleBlockWithCallToContract(block: ethereum.Block): void {
  let feed = getPriceFeed();
  let tokenAddresses = feed.tokenIds;
  for (let i = 0; i < tokenAddresses.length; i++) {
    fetchTokenPriceFromContract(Address.fromBytes(tokenAddresses[i]), block);
  }
  const global = getGlobal();
  updatePoolPortfolio(block);
  let denManagers = global.denManagers.load();
  for (let i = 0; i < denManagers.length; i++) {
    let denManagerId = Address.fromString(denManagers[i].id);
    createDenManagerSnapshot(denManagerId, block);
  }
  let vaults = global.vaults.load();
  for (let i = 0; i < vaults.length; i++) {
    let vaultId = Address.fromString(vaults[i].id);
    getVaultSnapshot(vaultId, block, VaultSnapshotType.TIMED);
  }

  takeFinancialDailySnapshot(block);
}
