import { Deposit, Rebalance, Offset, Withdraw } from "../../generated/StabilityPool/StabilityPool";
import { updatePoolPortfolio } from "../entities/SharePool";
import { updateSharePosition } from "../entities/SharePosition";
import { poolOperation } from "../types/enums";

export function handleUserDeposit(event: Deposit): void {
  updateSharePosition(event, event.params.owner, event.params.shares, poolOperation.DEPOSIT);

  // Take snapshot of pool composition
  updatePoolPortfolio(event.block);
}

export function handleUserWithdraw(event: Withdraw): void {
  updateSharePosition(event, event.params.owner, event.params.shares, poolOperation.WITHDRAW);
  // Take snapshot of pool composition
  updatePoolPortfolio(event.block);
}

export function handleRebalance(event: Rebalance): void {
  updatePoolPortfolio(event.block);
}
export function handleOffset(event: Offset): void {
  updatePoolPortfolio(event.block);
}
