import { NewDeployment } from "../../generated/Factory/Factory";
import { getDenManager } from "../entities/DenManager";
import { getToken } from "../entities/Token";
import { getLatestTokenPrice, getTokenPrice } from "../entities/TokenPrice";
import { getVault, getVaultSnapshot } from "../entities/Vault";
import { VaultSnapshotType } from "../types/enums";

export function handleDenManagerDeployed(event: NewDeployment): void {
  let denManager = getDenManager(event.params.denManager);

  let collateral = event.params.collateral;
  denManager.collateral = collateral.toHex();
  const vault = getVault(collateral);
  if (vault !== null) {
    getVaultSnapshot(collateral, event.block, VaultSnapshotType.EVENT);
    denManager.vault = vault.id;
  }
  denManager.save();

  let token = getToken(event.params.collateral);
  let price = getLatestTokenPrice(event.params.collateral, event.block);

  // create token price
  let tokenPrice = getTokenPrice(event.params.collateral, event.block);
  tokenPrice.price = price;
  tokenPrice.save();

  token.price = tokenPrice.id;
  token.oracle = event.params.priceFeed.toHex();
  token.save();
}
