import { DenLiquidated, DenUpdated, Liquidation } from "../../generated/LiquidationManager/LiquidationManager";
import { applyRedistributionToDenBeforeLiquidation, updateDen } from "../entities/Den";
import { createDenManagerSnapshot } from "../entities/DenManager";
import { finishCurrentLiquidation } from "../entities/Liquidation";
import { updatePoolPortfolio } from "../entities/SharePool";
import { getDenOperationFromBorrowerOperation } from "../types/TypeOperation";

export function handleDenLiquidated(event: DenLiquidated): void {
  applyRedistributionToDenBeforeLiquidation(event, event.params._denManager, event.params._borrower);
}

export function handleLiquidation(event: Liquidation): void {
  finishCurrentLiquidation(
    event,
    event.params._denManager,
    event.params._liquidatedColl,
    event.params._liquidatedDebt,
    event.params._collGasCompensation,
    event.params._debtGasCompensation
  );

  // Update pool portfolio
  updatePoolPortfolio(event.block);
}

export function handleDenUpdated(event: DenUpdated): void {
  updateDen(
    event,
    event.params._denManager,
    getDenOperationFromBorrowerOperation(event.params._operation, true),
    event.params._borrower,
    event.params._coll,
    event.params._debt,
    event.params._stake
  );
  createDenManagerSnapshot(event.address, event.block);
}
