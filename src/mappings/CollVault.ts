import { Initialized, Deposit, Withdraw, Transfer, Rebalance } from "../../generated/templates/CollateralVault/CollVault";
import { getVaultSnapshot, getVault } from "../entities/Vault";
import { updateVaultPosition, updateVaultPositionTransfer } from "../entities/VaultPosition";
import { poolOperation, VaultSnapshotType } from "../types/enums";
import { ZERO_ADDRESS } from "../utils/constants";

export function handleInitialized(event: Initialized): void {
  getVault(event.address);
}
export function handleDeposit(event: Deposit): void {
  updateVaultPosition(event, event.address, poolOperation.DEPOSIT, event.params.owner, event.params.shares, event.params.assets);
  getVaultSnapshot(event.address, event.block, VaultSnapshotType.EVENT);
}
export function handleWithdraw(event: Withdraw): void {
  updateVaultPosition(event, event.address, poolOperation.WITHDRAW, event.params.owner, event.params.shares, event.params.assets);
  getVaultSnapshot(event.address, event.block, VaultSnapshotType.EVENT);
}

export function handleTransfer(event: Transfer): void {
  if (event.params.from.toHex() !== ZERO_ADDRESS && event.params.to.toHex() !== ZERO_ADDRESS) {
    updateVaultPositionTransfer(event, event.address, poolOperation.TRANSFER, event.params.from, event.params.value);
    updateVaultPositionTransfer(event, event.address, poolOperation.TRANSFER, event.params.to, event.params.value);
  }
  getVaultSnapshot(event.address, event.block, VaultSnapshotType.EVENT);
}
export function handleRebalance(event: Rebalance): void {
  getVaultSnapshot(event.address, event.block, VaultSnapshotType.REBALANCE);
}
