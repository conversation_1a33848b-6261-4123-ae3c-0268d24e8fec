import { DenUpdated, Borrowing<PERSON>ee<PERSON><PERSON>, Den<PERSON>reated, DenManagerRemoved } from "../../generated/BorrowerOperations/BorrowerOperations";

import { getDenOperationFromBorrowerOperation } from "../types/TypeOperation";

import { getDen, setBorrowingFeeOfLastDenChange, updateDen } from "../entities/Den";
import { createDenManagerSnapshot, increaseTotalBorrowingFeesPaid } from "../entities/DenManager";
import { store } from "@graphprotocol/graph-ts";

export function handleDenCreated(event: DenCreated): void {
  getDen(event.params.denManager, event.params._borrower);

  createDenManagerSnapshot(event.params.denManager, event.block);
}

export function handleDenUpdated(event: DenUpdated): void {
  updateDen(
    event,
    event.params._denManager,
    getDenOperationFromBorrowerOperation(event.params.operation),
    event.params._borrower,
    event.params._coll,
    event.params._debt,
    event.params.stake
  );
  createDenManagerSnapshot(event.params._denManager, event.block);
}

export function handleBorrowingFeePaid(event: BorrowingFeePaid): void {
  setBorrowingFeeOfLastDenChange(event.params.amount);
  increaseTotalBorrowingFeesPaid(event.params.denManager, event.params.amount);
  createDenManagerSnapshot(event.params.denManager, event.block);
}

export function handleDenManagerRemoved(event: DenManagerRemoved): void {
  store.remove("DenManager", event.params.denManager.toHex());
}
