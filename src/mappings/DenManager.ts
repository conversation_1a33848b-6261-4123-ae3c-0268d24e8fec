import { DenUpdated, Redemption, LTermsUpdated, TotalStakesUpdated, BaseRateUpdated, NewParameters } from "../../generated/templates/DenManager/DenManager";

import { getDenOperationFromDenManagerOperation } from "../types/TypeOperation";

import { finishCurrentRedemption } from "../entities/Redemption";
import { updateDen } from "../entities/Den";
import { createDenManagerSnapshot, getDenManager, takeBorrowingFeeRateSnapshot } from "../entities/DenManager";

export function handleDenUpdated(event: DenUpdated): void {
  updateDen(
    event,
    event.address,
    getDenOperationFromDenManagerOperation(event.params._operation),
    event.params._borrower,
    event.params._coll,
    event.params._debt,
    event.params._stake
  );
  createDenManagerSnapshot(event.address, event.block);
}

export function handleRedemption(event: Redemption): void {
  finishCurrentRedemption(event, event.params._attemptedDebtAmount, event.params._actualDebtAmount, event.params._collateralSent, event.params._collateralFee);
  createDenManagerSnapshot(event.address, event.block);
}

export function handleLTermsUpdated(event: LTermsUpdated): void {
  let denManager = getDenManager(event.address);

  denManager.rawTotalRedistributedCollateral = event.params._L_collateral;
  denManager.rawTotalRedistributedDebt = event.params._L_debt;

  denManager.save();
  createDenManagerSnapshot(event.address, event.block);
}

export function handleTotalStakesUpdated(event: TotalStakesUpdated): void {
  let denManager = getDenManager(event.address);

  // TODO logic to handle user's stakes
  denManager.totalStakedAmount = denManager.totalStakedAmount.plus(event.params._newTotalStakes);
  denManager.save();
  createDenManagerSnapshot(event.address, event.block);
}

export function handleBaseRateUpdated(event: BaseRateUpdated): void {
  takeBorrowingFeeRateSnapshot(event, event.address, event.params._baseRate);
  createDenManagerSnapshot(event.address, event.block);
}

export function handleNewParameterSet(event: NewParameters): void {
  let denManager = getDenManager(event.address);
  denManager.borrowingFeeFloor = event.params.params.borrowingFeeFloor;
  denManager.maxBorrowingFee = event.params.params.maxBorrowingFee;
  denManager.interestRate = event.params.params.interestRateInBps;
  denManager.save();
  createDenManagerSnapshot(event.address, event.block);
}
