import { getVault, updateVault } from "../entities/Vault";
import { VaultModified, NewCollateralVault } from "../../generated/CollateralVaultRegistry/CollVaultRegistry";
import { log } from "@graphprotocol/graph-ts";

export function handleNewCollateralVault(event: NewCollateralVault): void {
  log.debug("new CollVault Address: ${} on block ${}", [event.params.vault.toHexString(), event.params.vault.toString()]);
  getVault(event.params.vault);
}

export function handleVaultModified(event: VaultModified): void {
  log.debug("update CollVault Address: ${} on block ${}", [event.params.vault.toHexString(), event.params.vault.toString()]);
  updateVault(event.params.vault);
}
