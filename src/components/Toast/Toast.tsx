import { Alert, Box, Snackbar, useMediaQuery, useTheme } from "@mui/material";
import { useNotifications } from "../../Hooks/useNotifications";
import { ToastProps } from "../../@type/Notifications";

export function Toast({ onClose, message, time = 5000, severity = "info", id }: ToastProps) {
  const { removeToast } = useNotifications();
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  function dismiss(reason: string) {
    if (reason != "clickaway") {
      onClose?.();
      removeToast(id);
    }
  }

  return (
    <Snackbar
      key={id}
      className=""
      anchorOrigin={{ vertical: isMdUp ? "top" : "bottom", horizontal: "center" }}
      disableWindowBlurListener={true}
      open={true}
      autoHideDuration={time}
      onClose={(_event, reason) => dismiss(reason)}
      sx={{
        "&.MuiSnackbar-root": { top: { md: "100px" } },
      }}
    >
      <Alert
        variant="filled"
        severity={severity}
        sx={{ width: "100%", py: 1, alignItems: "center", borderRadius: 3 }}
        onClose={() => {
          dismiss("clicked");
        }}
      >
        <Box sx={{ overflow: "hidden", width: "100%", lineHeight: "1.1", display: "flex", alignItems: "center", justifyContent: "space-between" }}>{message}</Box>
      </Alert>
    </Snackbar>
  );
}

export default Toast;
