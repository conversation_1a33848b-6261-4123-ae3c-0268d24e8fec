import React, { useEffect } from "react";
import { PublicClient } from "viem";
import { BeraBorrow, BeraBorrowDeploymentJSON } from "@Beraborrowofficial/sdk";
import mainnetDeployment from "../../utils/deployments/mainnet.json";
import { useAccount, usePublicClient } from "wagmi";
import { ANON_ADDRESS, ANON_BERABORROW, ANON_CLIENT, QUERY_CLIENT } from "../../utils/constants";
import { accountAtom, beraborrowAtom, clientAtom } from "../../Atoms/Account";
import { queryClientAtom } from "jotai-tanstack-query";
import { useHydrateAtoms } from "jotai/utils";
import { useWatchBlocks } from "../../Hooks/useWatchBlocks";
import { useNFT } from "../../Hooks/useNFT";
import { useAtom, useSetAtom } from "jotai";
import { useTermsApproval } from "../../Hooks/useTermsApproval";
import useSafeAutoConnecter from "../../Hooks/useSafeAutoConnector";
import { systemChangeAtom } from "../../Atoms/System";
const BeraBorrowAccount: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const client: PublicClient = usePublicClient() as PublicClient;
  const { address, isConnected } = useAccount();
  const setAccount = useSetAtom(accountAtom);
  const setClient = useSetAtom(clientAtom);
  const setBeraBorrow = useSetAtom(beraborrowAtom);
  useAtom(systemChangeAtom);
  useTermsApproval();
  useWatchBlocks();
  useNFT();
  useSafeAutoConnecter();
  useHydrateAtoms([
    [queryClientAtom, QUERY_CLIENT],
    [accountAtom, address || ANON_ADDRESS],
    // eslint-disable-next-line
    // @ts-ignore
    [beraborrowAtom, address ? new BeraBorrow(client, address, mainnetDeployment as BeraBorrowDeploymentJSON) : ANON_BERABORROW],
  ]);

  //initialize Atoms with address change
  useEffect(() => {
    //TODO: to handle anon users and change of addresses, phase 2
    if (address) {
      // eslint-disable-next-line
      // @ts-ignore
      setBeraBorrow(new BeraBorrow(client, address, mainnetDeployment as BeraBorrowDeploymentJSON));
      setAccount(address);
      setClient(client);
    } else {
      setBeraBorrow(ANON_BERABORROW);
      setAccount(ANON_ADDRESS);
      setClient(ANON_CLIENT);
    }
    QUERY_CLIENT.invalidateQueries();
  }, [address, isConnected]);

  return <>{children}</>;
};

export default BeraBorrowAccount;
