import React from "react";

import { Box, styled, Typography, Stack } from "@mui/material";
import LinearProgress, { linearProgressClasses } from "@mui/material/LinearProgress";

import { useAccessProgressStore } from "./store";
import { SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import { formatToken } from "../../../utils/helpers";
import { CLUB_LEVELS } from "../../../utils/constants";

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 15,
  borderRadius: 15,

  border: `2px solid var(--border-color)`, // Add a solid border with customizable color

  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: "transparent",
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: theme.palette.primary.main,
  },
}));
const AccessProgress: React.FC = () => {
  const { userTotalVolume, borrowLevel, borrowLevelLowerCase, nextLevel, nextLevelLowerCase, progressPercentage } = useAccessProgressStore();

  return (
    <Box>
      <Typography variant="h4" color="var(--text-secondary)" pb={1}>
        Your Den Progress
      </Typography>
      <Box display="flex" justifyContent="space-between">
        <Box>
          <Typography variant="h1" sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <img src={`/imgs/level-${borrowLevelLowerCase}.png`} alt={`${borrowLevel} icon`} height="29px" width="29px" />
            {borrowLevel}
          </Typography>
        </Box>
        <Box>
          <Typography variant="h1" sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <img src={`/imgs/level-${nextLevelLowerCase}.png`} alt={`${nextLevel} icon`} height="29px" width="29px" />
            {nextLevel}
          </Typography>
        </Box>
      </Box>
      <Stack spacing={2} sx={{ flexGrow: 1, py: 2 }}>
        <BorderLinearProgress variant="determinate" value={progressPercentage} />
      </Stack>
      <Box display="flex" justifyContent="space-between">
        <Box>
          <Typography variant="h4" sx={{ display: "flex", alignItems: "center", gap: 1, color: "var(--text-secondary)" }}>
            ${formatToken(userTotalVolume, SCALING_FACTOR_DECIMALS, 0)}
          </Typography>
        </Box>
        <Box>
          <Typography variant="h4" sx={{ display: "flex", alignItems: "center", gap: 1, color: "var(--text-secondary)" }}>
            ${formatToken(CLUB_LEVELS[nextLevelLowerCase], SCALING_FACTOR_DECIMALS, 0)}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default AccessProgress;
