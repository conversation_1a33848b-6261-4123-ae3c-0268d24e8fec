import { useAtomValue } from "jotai";
import { getUserTotalDepositVolumeAtom } from "../../../Atoms/Den";
import { accountAtom, borrowLevelAtom } from "../../../Atoms/Account";
import { getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { CLUB_LEVELS } from "../../../utils/constants";

export const useAccessProgressStore = () => {
  const { data: userTotalVolume } = useAtomValue(getUserTotalDepositVolumeAtom);

  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const borrowLevel = useAtomValue(borrowLevelAtom);
  const borrowLevelLowerCase = borrowLevel.toLowerCase() as "gold" | "silver" | "bronze" | "basic";
  const account = useAtomValue(accountAtom);

  const textColor: string = borrowLevel === "Gold" ? "#C2AE90" : borrowLevel === "Silver" ? "#ADADAD" : borrowLevel === "Bronze" ? "#AA847F" : "#322621";
  const nextLevel = borrowLevel === "Basic" ? "Bronze" : borrowLevel === "Bronze" ? "Silver" : "Gold";
  const nextLevelLowerCase = nextLevel.toLowerCase() as "gold" | "silver" | "bronze";
  const prevLevelValue = borrowLevel === "Basic" ? 0n : CLUB_LEVELS[borrowLevel.toLowerCase() as "gold" | "silver" | "bronze"];
  const progressPercentage = borrowLevel === "Gold" ? 100 : Number(((userTotalVolume - prevLevelValue) * 100n) / (CLUB_LEVELS[nextLevelLowerCase] - prevLevelValue));
  return {
    progressPercentage,
    textColor,
    nextLevel,
    account,
    borrowLevel,
    nextLevelLowerCase,
    borrowLevelLowerCase,
    userTotalVolume,
    collateral: { ...collateralDetails },
  };
};
