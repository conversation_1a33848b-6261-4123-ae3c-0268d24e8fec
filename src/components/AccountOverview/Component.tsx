import { Grid, Box } from "@mui/material";
import React from "react";
import AccessCard from "./AccessCard/Component";
import AccessProgress from "./AccessProgress/Component";
import WithSuspense from "../../providers/WithSuspense";
// import { useAtomValue } from "jotai";
// import { borrowLevelAtom } from "../../Atoms/Account";

const Component: React.FC = () => {
  //   const borrowLevel = useAtomValue(borrowLevelAtom);

  return (
    <Box
      sx={
        {
          // p: 2,
          // width: "100%",
          // borderRadius: "22.5px",
          // backgroundBlendMode: "overlay, color, hue, screen, normal",
          // ...(borrowLevel === "Gold"
          //   ? {
          //       background:
          //         "linear-gradient(0deg, #AA997F 0%, #AA997F 100%), linear-gradient(0deg, rgba(170, 153, 127, 0.25) 0%, rgba(170, 153, 127, 0.25) 100%), linear-gradient(0deg, #AA997F 0%, #AA997F 100%), linear-gradient(211deg, rgba(25, 25, 25, 0.00) 23.86%, rgba(82, 81, 80, 0.50) 50.02%, rgba(25, 25, 25, 0.00) 81.34%), linear-gradient(117deg, #9F9994 -11.54%, #191919 85.6%)",
          //       boxShadow: "0px 1px 0.3px 0.3px rgba(255, 249, 249, 0.24) inset, 0px 1.8px 1.8px 0px rgba(0, 0, 0, 0.25), 0px -1px 0.9px 0.3px rgba(255, 249, 249, 0.13) inset",
          //     }
          //   : borrowLevel === "Silver"
          //     ? {
          //         background:
          //           "linear-gradient(0deg, #ADADAD 0%, #ADADAD 100%), linear-gradient(0deg, rgba(171, 171, 171, 0.25) 0%, rgba(171, 171, 171, 0.25) 100%), linear-gradient(0deg, #AAA 0%, #AAA 100%), linear-gradient(211deg, rgba(25, 25, 25, 0.00) 23.86%, rgba(82, 81, 80, 0.50) 50.02%, rgba(25, 25, 25, 0.00) 81.34%), linear-gradient(117deg, #9F9994 -11.54%, #191919 85.6%)",
          //         boxShadow: "0px 1px 0.25px 0.405px rgba(255, 249, 249, 0.24) inset, 0px 2px 2px 0px rgba(0, 0, 0, 0.25), 0px -1px 1px 0.162px rgba(255, 249, 249, 0.13) inset",
          //       }
          //     : borrowLevel === "Bronze"
          //       ? {
          //           background:
          //             "linear-gradient(0deg, #AA847F 0%, #AA847F 100%), linear-gradient(0deg, rgba(170, 132, 127, 0.25) 0%, rgba(170, 132, 127, 0.25) 100%), linear-gradient(0deg, #AA847F 0%, #AA847F 100%), linear-gradient(211deg, rgba(25, 25, 25, 0.00) 23.86%, rgba(82, 81, 80, 0.50) 50.02%, rgba(25, 25, 25, 0.00) 81.34%), linear-gradient(117deg, #9F9994 -11.54%, #191919 85.6%)",
          //           boxShadow: "0px 1px 0.25px 0.405px rgba(255, 249, 249, 0.24) inset, 0px 2px 2px 0px rgba(0, 0, 0, 0.25), 0px -1px 1px 0.162px rgba(255, 249, 249, 0.13) inset",
          //         }
          //       : {
          //           background: "linear-gradient(117deg, #745E53 -11.54%, #40332D 85.6%)",
          //           boxShadow: "0px 1px 0.25px 0.405px rgba(255, 249, 249, 0.24) inset, 0px 2px 2px 0px rgba(0, 0, 0, 0.25), 0px -1px 1px 0.162px rgba(255, 249, 249, 0.13) inset",
          //         }),
        }
      }
    >
      <Grid container spacing={1} pt={1}>
        <Grid item xs={12} md={6}>
          <AccessCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <AccessProgress />
        </Grid>
      </Grid>
    </Box>
  );
};

const AccountOverview = WithSuspense(Component);
export default AccountOverview;
