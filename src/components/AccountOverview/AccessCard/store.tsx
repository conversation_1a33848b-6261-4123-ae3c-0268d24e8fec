import { useAtom, useAtomValue } from "jotai";
import { getUserTotalDepositVolumeAtom, getUserDensTVLAtom } from "../../../Atoms/Den";
import { accountAtom, borrowLevelAtom, borrowLevelEffectAtom } from "../../../Atoms/Account";
import { SxProps, Theme } from "@mui/material";
import { collateralTypeSelectorAtom, getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { useNotifications } from "../../../Hooks/useNotifications";
import AccessCardPopup from "./Popup";
import { getStabilityDepositAtom, convertSharesToAssetsAtom } from "../../../Atoms/StabilityPool";
import { useState } from "react";
import { zeroAddress } from "viem";
import { useHydrateAtoms } from "jotai/utils";

export const useAccessCardStore = () => {
  useHydrateAtoms([[collateralTypeSelectorAtom, "den"]]);
  const { data: userTotalVolume } = useAtomValue(getUserTotalDepositVolumeAtom);
  const { data: userDenTVL } = useAtomValue(getUserDensTVLAtom);
  const { data: currentDeposit } = useAtomValue(getStabilityDepositAtom);

  const { data: shareToAssetRatio } = useAtomValue(convertSharesToAssetsAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const borrowLevel = useAtomValue(borrowLevelAtom);
  const account = useAtomValue(accountAtom);

  useAtom(borrowLevelEffectAtom);
  const { addModal } = useNotifications();
  const myTotalAssets = currentDeposit.convertSharesToAssets(currentDeposit.shares, shareToAssetRatio);
  const [copied, setCopied] = useState<boolean>(false);

  const usersTVL = userDenTVL + myTotalAssets;

  const dispatcher = (action: { type: "openModal" } | { type: "copyAddress" }) => {
    switch (action.type) {
      case "openModal":
        addModal({ id: "accessCard", title: "Den Levels", Component: <AccessCardPopup /> });
        break;
      case "copyAddress":
        if (!account || account === zeroAddress) {
          return;
        }
        navigator.clipboard
          .writeText(account)
          .then(() => {
            setCopied(true);
            setTimeout(() => {
              setCopied(false);
            }, 2000);
          })
          .catch((err) => {
            console.error("Failed to copy text: ", err);
          });
        break;

      default:
        break;
    }
  };

  const textColor: string = borrowLevel === "Gold" ? "#C2AE90" : borrowLevel === "Silver" ? "#ADADAD" : borrowLevel === "Bronze" ? "#AA847F" : "#322621";
  const sxProps: SxProps<Theme> = {};
  // borrowLevel === "Gold"
  //   ? {
  //       background:
  //         "linear-gradient(0deg, #AA997F 0%, #AA997F 100%), linear-gradient(0deg, rgba(170, 153, 127, 0.25) 0%, rgba(170, 153, 127, 0.25) 100%), linear-gradient(0deg, #AA997F 0%, #AA997F 100%), linear-gradient(211deg, rgba(25, 25, 25, 0.00) 23.86%, rgba(82, 81, 80, 0.50) 50.02%, rgba(25, 25, 25, 0.00) 81.34%), linear-gradient(117deg, #9F9994 -11.54%, #191919 85.6%)",
  //       boxShadow: "0px 1px 0.3px 0.3px rgba(255, 249, 249, 0.24) inset, 0px 1.8px 1.8px 0px rgba(0, 0, 0, 0.25), 0px -1px 0.9px 0.3px rgba(255, 249, 249, 0.13) inset",
  //     }
  //   : borrowLevel === "Silver"
  //     ? {
  //         background:
  //           "linear-gradient(0deg, #ADADAD 0%, #ADADAD 100%), linear-gradient(0deg, rgba(171, 171, 171, 0.25) 0%, rgba(171, 171, 171, 0.25) 100%), linear-gradient(0deg, #AAA 0%, #AAA 100%), linear-gradient(211deg, rgba(25, 25, 25, 0.00) 23.86%, rgba(82, 81, 80, 0.50) 50.02%, rgba(25, 25, 25, 0.00) 81.34%), linear-gradient(117deg, #9F9994 -11.54%, #191919 85.6%)",
  //         boxShadow: "0px 1px 0.25px 0.405px rgba(255, 249, 249, 0.24) inset, 0px 2px 2px 0px rgba(0, 0, 0, 0.25), 0px -1px 1px 0.162px rgba(255, 249, 249, 0.13) inset",
  //       }
  //     : borrowLevel === "Bronze"
  //       ? {
  //           background:
  //             "linear-gradient(0deg, #AA847F 0%, #AA847F 100%), linear-gradient(0deg, rgba(170, 132, 127, 0.25) 0%, rgba(170, 132, 127, 0.25) 100%), linear-gradient(0deg, #AA847F 0%, #AA847F 100%), linear-gradient(211deg, rgba(25, 25, 25, 0.00) 23.86%, rgba(82, 81, 80, 0.50) 50.02%, rgba(25, 25, 25, 0.00) 81.34%), linear-gradient(117deg, #9F9994 -11.54%, #191919 85.6%)",
  //           boxShadow: "0px 1px 0.25px 0.405px rgba(255, 249, 249, 0.24) inset, 0px 2px 2px 0px rgba(0, 0, 0, 0.25), 0px -1px 1px 0.162px rgba(255, 249, 249, 0.13) inset",
  //         }
  //       : {
  //           background: "linear-gradient(117deg, #745E53 -11.54%, #40332D 85.6%)",
  //           boxShadow: "0px 1px 0.25px 0.405px rgba(255, 249, 249, 0.24) inset, 0px 2px 2px 0px rgba(0, 0, 0, 0.25), 0px -1px 1px 0.162px rgba(255, 249, 249, 0.13) inset",
  //         };

  return {
    sxProps,
    textColor,
    dispatcher,
    usersTVL,
    account,
    copied,
    borrowLevel,
    userTotalVolume,
    collateral: { ...collateralDetails },
  };
};
export const getDenInfoScopedAtoms = () => [];
