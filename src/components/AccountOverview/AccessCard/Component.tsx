import React from "react";

import { Box, Button, Grid, IconButton, Typography } from "@mui/material";
import { Avatar } from "../../Avatar";
import { useAccessCardStore } from "./store";
import { InfoOutlined } from "@mui/icons-material";
import { formatToken, truncateAddress } from "../../../utils/helpers";
import { zeroAddress } from "viem";
import { SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import CheckIcon from "@mui/icons-material/Check";

const AccessCard: React.FC = () => {
  const { account, borrowLevel, sxProps, textColor, dispatcher, usersTVL, copied } = useAccessCardStore();
  return (
    <Box
      sx={{
        width: "100%",
        ...sxProps,
      }}
    >
      <Box display="flex" justifyContent="">
        <Avatar height={80} width={80} border="2.7px solid rgba(255, 145, 65, 0.25)" badge={`/imgs/level-${borrowLevel.toLowerCase()}.png`} />
        <Box pl={2}>
          <Typography variant="h1" fontWeight={700} pb={1}>
            {account !== zeroAddress ? truncateAddress(account) : ""}&nbsp;
            {copied ? (
              <IconButton sx={{ color: "inherit" }}>
                <CheckIcon />
              </IconButton>
            ) : (
              <IconButton onClick={() => dispatcher({ type: "copyAddress" })} sx={{ color: "inherit" }}>
                <ContentCopyIcon />
              </IconButton>
            )}{" "}
          </Typography>
          <Button
            onClick={() => dispatcher({ type: "openModal" })}
            variant="contained"
            sx={{
              borderColor: "transparent",
              borderRadius: "8.64px",
              background: "rgba(194, 174, 144, 0.20)",
              padding: "4px 8px !important", // Smaller padding to match text height
              minHeight: "auto", // Ensure button shrinks to content
              display: "flex",
              alignItems: "center", // Vertically center content
            }}
          >
            <Typography variant={"h6"} sx={{ display: "flex", alignItems: "center", fontSize: "inherit", fontWeight: 700, color: textColor }}>
              {borrowLevel} Club Member
              <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", fontWeight: "inherit", verticalAlign: "middle", ml: 0.5 }} />
            </Typography>
          </Button>
        </Box>
      </Box>
      <Box>
        <Grid container justifyContent={{ xs: "center", lg: "end" }}>
          <Grid item xs={4} textAlign={{ xs: "center", lg: "left" }}>
            <Typography variant="h4" color="var(--text-secondary)">
              Net Worth
            </Typography>
            <Typography variant="h4">${formatToken(usersTVL, SCALING_FACTOR_DECIMALS, 0, undefined, true)}</Typography>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default AccessCard;
