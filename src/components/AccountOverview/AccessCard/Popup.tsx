import React from "react";

import { Box, Divider, Typography } from "@mui/material";
import { Avatar } from "../../Avatar";
import { useAccessCardStore } from "./store";
import { formatToken } from "../../../utils/helpers";
import { CLUB_LEVELS } from "../../../utils/constants";
import { SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";

const AccessCardPopup: React.FC = () => {
  const { borrowLevel, userTotalVolume, collateral } = useAccessCardStore();
  return (
    <Box>
      <Box
        display="flex"
        alignItems="center" // Ensure vertical alignment
        sx={{ background: "var(--background-accent)", p: 2, borderRadius: "16px" }}
      >
        <Avatar height={60} border="1.7px solid #FF914140" badge={`/imgs/level-${borrowLevel.toLowerCase()}.png`} />
        <Box pl={3}>
          <Typography variant={"h2"} sx={{ display: "flex", color: "var(--text-primary)", alignItems: "center", fontWeight: 600 }}>
            {borrowLevel} Club Member
          </Typography>
          <Typography variant={"h5"} sx={{ display: "flex", color: "var(--text-secondary)", alignItems: "center", fontWeight: 600 }}>
            Total Volume: ${formatToken(userTotalVolume, collateral.vaultDecimals, 0)}
          </Typography>
        </Box>
      </Box>
      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />
      <Typography variant={"h4"} sx={{ display: "flex", color: "var(--text-primary)", alignItems: "center", fontWeight: 600 }}>
        Generate more volume in your Dens to level up your club membership and unlock new rooms.
      </Typography>
      <Box
        display="flex"
        alignItems="center" // Ensure vertical alignment
        sx={{ background: "var(--background-accent)", p: 2, borderRadius: "16px", mt: 2 }}
      >
        <Box width={60} height={60}>
          <img src={"/imgs/level-gold.png"} width="100%" height="100%" alt="wallet-level" style={{ borderRadius: "50%" }} />
        </Box>{" "}
        <Box pl={3}>
          <Typography variant={"h2"} sx={{ display: "flex", color: "var(--text-primary)", alignItems: "center", fontWeight: 600 }}>
            Gold Club Member
          </Typography>
          <Typography variant={"h5"} sx={{ display: "flex", color: "var(--text-secondary)", alignItems: "center", fontWeight: 600 }}>
            Total Volume Above: ${formatToken(CLUB_LEVELS.gold, SCALING_FACTOR_DECIMALS, 0)}
          </Typography>
        </Box>
      </Box>
      <Box
        display="flex"
        alignItems="center" // Ensure vertical alignment
        sx={{ background: "var(--background-accent)", p: 2, borderRadius: "16px", mt: 2 }}
      >
        <Box width={60} height={60}>
          <img src={"/imgs/level-silver.png"} width="100%" height="100%" alt="wallet-level" style={{ borderRadius: "50%" }} />
        </Box>{" "}
        <Box pl={3}>
          <Typography variant={"h2"} sx={{ display: "flex", color: "var(--text-primary)", alignItems: "center", fontWeight: 600 }}>
            Silver Club Member
          </Typography>
          <Typography variant={"h5"} sx={{ display: "flex", color: "var(--text-secondary)", alignItems: "center", fontWeight: 600 }}>
            Total Volume Above: ${formatToken(CLUB_LEVELS.silver, SCALING_FACTOR_DECIMALS, 0)}
          </Typography>
        </Box>
      </Box>
      <Box
        display="flex"
        alignItems="center" // Ensure vertical alignment
        sx={{ background: "var(--background-accent)", p: 2, borderRadius: "16px", mt: 2 }}
      >
        <Box width={60} height={60}>
          <img src={"/imgs/level-bronze.png"} width="100%" height="100%" alt="wallet-level" style={{ borderRadius: "50%" }} />
        </Box>

        <Box pl={3}>
          <Typography variant={"h2"} sx={{ display: "flex", color: "var(--text-primary)", alignItems: "center", fontWeight: 600 }}>
            Bronze Club Member
          </Typography>
          <Typography variant={"h5"} sx={{ display: "flex", color: "var(--text-secondary)", alignItems: "center", fontWeight: 600 }}>
            Total Volume Above: ${formatToken(CLUB_LEVELS.bronze, SCALING_FACTOR_DECIMALS, 0)}
          </Typography>
        </Box>
      </Box>
      <Box
        display="flex"
        alignItems="center" // Ensure vertical alignment
        sx={{ background: "var(--background-accent)", p: 2, borderRadius: "16px", mt: 2 }}
      >
        <Box width={60} height={60}>
          <img src={"/imgs/level-basic.png"} width="100%" height="100%" alt="wallet-level" style={{ borderRadius: "50%" }} />
        </Box>

        <Box pl={3}>
          <Typography variant={"h2"} sx={{ display: "flex", color: "var(--text-primary)", alignItems: "center", fontWeight: 600 }}>
            Basic Club Member
          </Typography>
          <Typography variant={"h5"} sx={{ display: "flex", color: "var(--text-secondary)", alignItems: "center", fontWeight: 600 }}>
            &nbsp;
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default AccessCardPopup;
