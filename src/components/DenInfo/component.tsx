import { Box, Button, Chip, CircularProgress, Divider, Grid, Typography } from "@mui/material";
import { formatBigIntPercentage, formatToken } from "../../utils/helpers";
import { useDenInfoStore } from "./store";
import WithSuspense from "../../providers/WithSuspense";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { East, InfoOutlined } from "@mui/icons-material";
import { HealthFactorBar } from "../HealthFactorBar/HealthFactorBar";
import StyledTooltip from "../StyledFeeTooltip";
import TransactionButton from "../Transaction/Transaction";
import CollateralPill from "../CollateralPill/component";
import StyledAccordion from "../StyledAccordion";
import VaultComposition from "../VaultComposition/Component";
const Component: React.FC = () => {
  const {
    denFiatValues,
    collateral,
    dispatcher,
    isManaging,
    userDen,
    updatedDen,
    collateralSurplusBalance,
    interestRates,
    isRecoveryMode,
    vaultDetails,
    borrowType,
    isDelegated,
    denManagerAddr,
    showCurrentBorrow,
    wrappedDenManagerAddress,
    withdrawFee,
  } = useDenInfoStore();

  return (
    <Box
      component="div"
      display="flex" // Ensures the Box behaves as a flex container
      flexDirection="column" // Stacks the children vertically
      justifyContent="space-between" // Spaces the children with space between
      sx={{ height: "100%" }} // Takes the full height of the parent container
      flex={1} // Ensures it grows to fill available space
    >
      <Box>
        <Grid container px={{ xs: 1, md: 2 }}>
          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h1">Overview</Typography>
              <CollateralPill contractAddress={collateral.contractAddress} urlRedirect={`/den/${borrowType !== "withdraw" ? "borrow" : "manage"}`} />
            </Box>
            <Divider sx={{ my: 1.2, backgroundColor: `var(--border-color)`, width: "100%" }} />

            {isRecoveryMode && (
              <Box>
                <Chip
                  sx={{
                    p: 1,
                    fontWeight: 600,
                    fontSize: 12,
                    height: "auto",
                    "& .MuiChip-label": {
                      display: "block",
                      whiteSpace: "normal",
                    },
                    "& .chip-icon img ": {
                      filter: "invert(25%) sepia(90%) saturate(7000%) hue-rotate(340deg) brightness(95%) contrast(102%)",
                    },
                  }}
                  variant="filled"
                  color={"error"}
                  size="small"
                  icon={<img src="/imgs/recoveryModeRed.svg" height="20px" alt="" />}
                  label="System Wide Recovery Mode Active: Collateral Ratio below 150%. Adjust ratio or wait until system is out of recovery mode."
                />
                <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />
              </Box>
            )}
            <StyledAccordion
              hideIcon={!vaultDetails}
              expanded={!vaultDetails || showCurrentBorrow}
              onClickSummary={() => vaultDetails && dispatcher({ type: "setShowCurrentBorrow" })}
              title="Current Borrowing"
            >
              <Box sx={{ width: "100%", alignItems: "center" }} gap={2}>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="subtitle2" color={"var(--text-secondary)"} mb={1}>
                    Interest on Debt
                  </Typography>

                  {interestRates !== undefined ? (
                    <Typography variant="subtitle2" lineHeight={1}>
                      <strong>{formatBigIntPercentage(BigInt(interestRates), 2, true, true, 4)}%</strong>
                    </Typography>
                  ) : (
                    <>
                      <CircularProgress size={"14px"} color="inherit" />
                    </>
                  )}
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="subtitle2" color={"var(--text-secondary)"} mb={1}>
                    Withdrawal Fee
                  </Typography>

                  <Typography variant="subtitle2" lineHeight={1}>
                    <strong>{formatBigIntPercentage(BigInt(withdrawFee), 2, true, true, 4)}%</strong>
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="subtitle2" color={"var(--text-secondary)"} mb={1} textAlign={"right"}>
                    {collateral.wrappedCollateralTicker ?? collateral.ticker} Value
                  </Typography>
                  <Box>
                    <Typography variant="subtitle2" component={"p"} textAlign={"right"}>
                      <strong>
                        ${formatToken(denFiatValues.collateralValue, collateral.vaultDecimals, 2)}
                        {userDen.collateral !== updatedDen.collateral && (
                          <>
                            <East fontSize="inherit" color="primary" style={{ verticalAlign: "middle", lineHeight: "inherit" }} />$
                            {formatToken(denFiatValues.updatedCollateralValue, collateral.vaultDecimals, 2)}
                          </>
                        )}
                      </strong>
                    </Typography>
                  </Box>
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="subtitle2" color={"var(--text-secondary)"} mb={1} textAlign={"right"}>
                    {collateral.wrappedCollateralTicker ?? collateral.ticker} Amount
                  </Typography>
                  <Box>
                    <Typography variant="subtitle2" textAlign={"right"}>
                      <strong>
                        {formatToken(userDen.collateral, collateral.vaultDecimals, 2)}
                        {userDen.collateral !== updatedDen.collateral && (
                          <>
                            <East fontSize="inherit" color="primary" style={{ verticalAlign: "middle", lineHeight: "inherit" }} />
                            {formatToken(updatedDen.collateral, collateral.vaultDecimals, 2)}
                          </>
                        )}
                      </strong>
                    </Typography>
                  </Box>
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="subtitle2" color={"var(--text-secondary)"} mb={1}>
                    {BERABORROW_ADDRESSES.debtToken.ticker} Borrow
                  </Typography>
                  <Box>
                    <Typography variant="subtitle2" textAlign={"right"}>
                      <strong>
                        {formatToken(userDen.debt, collateral.vaultDecimals, 2)}
                        {userDen.debt !== updatedDen.debt && (
                          <>
                            <East fontSize="inherit" color="primary" style={{ verticalAlign: "middle", lineHeight: "inherit" }} />
                            {formatToken(updatedDen.debt, BERABORROW_ADDRESSES.debtToken.decimals, 2)}
                          </>
                        )}
                      </strong>
                    </Typography>
                  </Box>
                </Box>

                <Box display="flex" justifyContent="space-between">
                  <Typography variant="subtitle2" color={"var(--text-secondary)"} mb={1}>
                    Liquidation Price
                  </Typography>
                  <Box>
                    <Typography variant="subtitle2" textAlign={"right"}>
                      <strong>
                        ${formatToken(denFiatValues.liquidationPrice, collateral.vaultDecimals, 2)}
                        {denFiatValues.liquidationPrice !== denFiatValues.updatedLiquidationPrice && (
                          <>
                            <East fontSize="inherit" color="primary" style={{ verticalAlign: "middle", lineHeight: "inherit" }} />$
                            {formatToken(denFiatValues.updatedLiquidationPrice, BERABORROW_ADDRESSES.debtToken.decimals, 2)}
                          </>
                        )}
                      </strong>
                    </Typography>
                  </Box>
                </Box>

                <Box display="flex" justifyContent="space-between">
                  <Typography variant="subtitle2" color={"var(--text-secondary)"} mb={1}>
                    Current Price
                  </Typography>
                  <Typography variant="subtitle2">
                    <strong>${formatToken(denFiatValues.collateralPrice, collateral.vaultDecimals, 2)}</strong>
                  </Typography>
                </Box>
                <>
                  <HealthFactorBar />
                </>
                {!!collateralSurplusBalance && (
                  <Box display="flex" justifyContent="space-between" alignItems="center" pt={2}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography component={"span"} variant="h4" display="flex" alignItems="center">
                        <StyledTooltip
                          title={
                            <Box display={"flex"} flexDirection={"column"} gap={1}>
                              Claim Surplus Collateral from Recovery Mode liquidations or Due to Den being closed by Redemption
                            </Box>
                          }
                        >
                          <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", mr: 0.5 }} />
                        </StyledTooltip>
                        Surplus {collateral.wrappedCollateralTicker ?? collateral.ticker}: {formatToken(collateralSurplusBalance, collateral.vaultDecimals, 2)}
                      </Typography>
                    </Box>

                    <TransactionButton
                      sx={{ lineHeight: 1, fontSize: "14px", py: 0.8, px: 1.5, borderRadius: "10px" }}
                      transaction={{ type: "claimCollateralSurplus", variables: [] }}
                    >
                      Claim
                    </TransactionButton>
                  </Box>
                )}
              </Box>
            </StyledAccordion>
            {!!vaultDetails && (
              <Box mt={1.5}>
                <StyledAccordion
                  expanded={!showCurrentBorrow}
                  onClickSummary={() => dispatcher({ type: "setShowCurrentBorrow" })}
                  key={denManagerAddr}
                  title={(collateral.wrappedCollateralTicker ?? collateral.ticker) + " Vault"}
                  denManagerAddr={wrappedDenManagerAddress}
                >
                  <VaultComposition
                    key={denManagerAddr}
                    denManagerAddress={denManagerAddr}
                    shares={userDen.collateral}
                    collPrice={collateral.price}
                    removeWithdrawFee={withdrawFee}
                  />
                </StyledAccordion>
              </Box>
            )}
          </Grid>
        </Grid>
      </Box>

      <Box mt={1} mb={"10px"}>
        {isManaging && (
          <>
            {isRecoveryMode ? (
              <>
                <StyledTooltip
                  title={
                    <Box display={"flex"} flexDirection={"column"} gap={1}>
                      Close Position disabled while in Recovery Mode
                    </Box>
                  }
                >
                  <span>
                    <Button disabled fullWidth variant="outlined" sx={{ borderRadius: 4, py: 1.5, fontSize: "18px" }}>
                      <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", mr: 0.5 }} />
                      Close Position
                    </Button>
                  </span>
                </StyledTooltip>
              </>
            ) : (
              <Button
                disabled={isRecoveryMode}
                fullWidth
                variant="outlined"
                sx={{ borderRadius: 4, py: 1.5, fontSize: "18px" }}
                onClick={() => {
                  dispatcher({ type: isDelegated ? "close" : "delegate" });
                }}
              >
                {!isDelegated && "Delegate and"} Close Position
              </Button>
            )}
          </>
        )}
      </Box>
    </Box>
  );
};
const DenInfo = WithSuspense(Component, "paper");
export default DenInfo;
