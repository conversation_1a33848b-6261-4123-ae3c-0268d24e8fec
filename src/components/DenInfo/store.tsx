import { useAtomValue, use<PERSON>tom } from "jotai";
import { getCollateralBalanceAtom, getCollateralDetailsAtom } from "../../Atoms/Tokens";
import {
  denBorrowAmountsAtom,
  denBorrowTypeAtom,
  denManagersAtom,
  getBorrowingMintFeeRateAtom,
  getDenFullDetailsAtom,
  getMCRAtom,
  getCollateralSurplusBalanceAtom,
  getDenInterestRateAtom,
  getIsApprovedDelegateCollVaultRouterAtom,
  denManagerAtom,
  getGTCRAtom,
  //   getLeverageFlashLoanFeeAtom,
  showCurrentBorrowAtom,
  getDenManagerCollateralPrice,
} from "../../Atoms/Den";
import { Hex } from "viem";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { useNavigate } from "react-router-dom";
import ClosePosition from "../ClosePosition/component";
import { useNotifications } from "../../Hooks/useNotifications";
import { borrowLevelEffectAtom, beraborrowConstantsAtom, account<PERSON>tom } from "../../Atoms/Account";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { _vaultAddrAtom, getVaultDepositAtom, previewVaultedSharesAtom } from "../../Atoms/Vault";
import DelegateBorrow from "../DelegateBorrow/component";

export const useDenInfoStore = () => {
  const navigate = useNavigate();
  useAtom(borrowLevelEffectAtom);
  const { addModal } = useNotifications();
  const { data: isDelegated } = useAtomValue(getIsApprovedDelegateCollVaultRouterAtom);
  const { data: collateralBalance } = useAtomValue(getCollateralBalanceAtom);
  //   const { data: debtPrice } = useAtomValue(getDebtPriceAtom);
  const { data: denDetails } = useAtomValue(getDenFullDetailsAtom);
  const { data: _currentBorrowingRate } = useAtomValue(getBorrowingMintFeeRateAtom);
  const { data: collateralSurplusBalance } = useAtomValue(getCollateralSurplusBalanceAtom);
  const { data: interestRates } = useAtomValue(getDenInterestRateAtom);
  const { data: previewVaultedShares } = useAtomValue(previewVaultedSharesAtom);
  const { data: mcr } = useAtomValue(getMCRAtom);
  //   const { data: flashLoanRate } = useAtomValue(getLeverageFlashLoanFeeAtom);
  const { data: vaultDeposit } = useAtomValue(getVaultDepositAtom);

  const [showCurrentBorrow, setShowCurrentBorrow] = useAtom(showCurrentBorrowAtom);
  const denManagers = useAtomValue(denManagersAtom);
  const denManagerAddr = useAtomValue(denManagerAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const formAmounts = useAtomValue(denBorrowAmountsAtom);
  const borrowType = useAtomValue(denBorrowTypeAtom);
  const { data: GTCR } = useAtomValue(getGTCRAtom);
  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);
  const { den: userDen, ccr } = denDetails; // price: collateralPrice
  const beraborrowConstants = useAtomValue(beraborrowConstantsAtom);
  const { withdrawFee } = vaultDeposit;
  const isRecoveryMode = GTCR <= ccr;
  const isManaging = userDen.status === "open";
  //   const isLeveraging = formAmounts.leverage !== undefined;
  const currentBorrowingRate = isRecoveryMode ? 0n : _currentBorrowingRate;
  const borrowFees =
    formAmounts.debt == 0n
      ? 0n
      : (borrowType === "open" ? beraborrowConstants.liquidationReserve : 0n) + (borrowType !== "withdraw" ? (formAmounts.debt * currentBorrowingRate) / SCALING_FACTOR : 0n); //fees

  //   const leveragedDen = formAmounts.applyLeverage(formAmounts.getCollateralShares(previewVaultedShares), currentBorrowingRate, collateralPrice, debtPrice);
  //   const LeveragedFees =
  // leveragedDen.debt == 0n
  //   ? 0n
  //   : (borrowType === "open" ? beraborrowConstants.liquidationReserve : 0n) + (borrowType !== "withdraw" ? (leveragedDen.debt * currentBorrowingRate) / SCALING_FACTOR : 0n); //fees
  const updatedDen =
    //   isLeveraging
    //     ? userDen
    //         .addCollateral(leveragedDen.getCollateralShares(previewVaultedShares, leveragedDen.collateral - formAmounts.getFlashLoanFee(flashLoanRate)))
    //         .addDebt(leveragedDen.debt + LeveragedFees)
    //     :
    borrowType !== "withdraw"
      ? userDen.addCollateral(formAmounts.getCollateralShares(previewVaultedShares)).addDebt(formAmounts.debt).addDebt(borrowFees) //fees
      : userDen.subtractCollateral(formAmounts.collateral).subtractDebt(formAmounts.debt);

  const denFiatValues = {
    nectValue: userDen.debt,
    updatedNectValue: updatedDen.debt,
    collateralValue: (userDen.collateral * collateralPrice) / SCALING_FACTOR,
    updatedCollateralValue: (updatedDen.collateral * collateralPrice) / SCALING_FACTOR,
    collateralPrice,
    liquidationPrice: userDen.calculateLiquidationPrice(mcr),
    updatedLiquidationPrice: updatedDen.calculateLiquidationPrice(mcr),
  };
  const wrappedDenManagerAddress = BERABORROW_ADDRESSES.denManagers[denManagerAddr].denManager;
  const vaultDetails = BERABORROW_ADDRESSES.denManagers[denManagerAddr].vault && BERABORROW_ADDRESSES.vaults[BERABORROW_ADDRESSES.denManagers[denManagerAddr].vault];
  const wrappedCollateralTicker = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.denManagers[BERABORROW_ADDRESSES.wrappedDenManagers[denManagerAddr]]?.collateral]
    ?.ticker as string | undefined;
  const dispatcher = (action: { type: "changeDenManager"; payload: Hex } | { type: "close" } | { type: "delegate" } | { type: "setShowCurrentBorrow" }) => {
    switch (action.type) {
      case "changeDenManager": {
        const denManager = action.payload as Hex;
        const ticker = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.denManagers[denManager].collateral].ticker;
        navigate(`/den/${borrowType !== "withdraw" ? "borrow" : "manage"}/${ticker}`);
        break;
      }
      case "close":
        addModal({
          id: "close-denBorrow",
          title: "Close Position",
          Component: <ClosePosition />,
        });

        break;
      case "delegate":
        addModal({
          id: "delegateBorrow",
          title: "In progress",
          Component: (
            <DelegateBorrow
              formAmounts={formAmounts}
              onConfirmation={() => {
                dispatcher({ type: "close" });
              }}
            />
          ),
        });
        break;
      case "setShowCurrentBorrow":
        setShowCurrentBorrow(!showCurrentBorrow);
        break;
      default:
        break;
    }
  };

  return {
    interestRates,
    borrowType,
    userDen,
    updatedDen,
    denFiatValues,
    dispatcher,
    denManagers,
    collateralSurplusBalance,
    isManaging,
    collateral: { balance: collateralBalance, price: collateralPrice, ...collateralDetails, wrappedCollateralTicker },
    isRecoveryMode,
    denManagerAddr,
    isDelegated,
    vaultDetails,
    showCurrentBorrow,
    wrappedDenManagerAddress,
    withdrawFee,
  };
};
export const getDenInfoScopedAtoms = () => [_vaultAddrAtom, accountAtom, getVaultDepositAtom, denManagerAtom];
