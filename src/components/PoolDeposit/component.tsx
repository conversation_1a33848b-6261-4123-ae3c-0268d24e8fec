import React from "react";
import { usePoolDepositStore } from "./store";
import { Box, Divider, Grid, Typography, useTheme } from "@mui/material";
import { formatBigIntPercentage, formatToken } from "../../utils/helpers";
import { SCALING_FACTOR, SCALING_FACTOR_BP_DECIMALS, SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import TransactionButton from "../Transaction/Transaction";
import WithSuspense from "../../providers/WithSuspense";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import StyledTextfield from "../StyledTextField";
import TickerPill from "../TickerPill";
import { AmountSelector } from "../AmountSelector";
import TokenIcon from "../TokenIcons";
import { InfoOutlined } from "@mui/icons-material";
import StyledTooltip from "../StyledFeeTooltip";

const Component: React.FC = () => {
  const theme = useTheme();
  const { nectBalance, currentDeposit, validationError, formValue, depositAmount, dispatcher, fees, entryFee, depositAssets, depositShares, assets } = usePoolDepositStore();

  return (
    <>
      <Grid container pb={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1">Deposit {BERABORROW_ADDRESSES.debtToken.ticker.toUpperCase()}</Typography>
          <StyledTextfield
            value={formValue}
            error={validationError?.type === "insufficient_balance"}
            onChange={(e) => dispatcher({ type: "setPoolDeposit", payload: e.target.value })}
            endComponent={
              <Box display="flex" flexDirection="column" alignItems="start">
                <TickerPill contractAddress={BERABORROW_ADDRESSES.debtToken.contractAddress} ticker={BERABORROW_ADDRESSES.debtToken.ticker} />
                <AmountSelector handleItemClick={(percent) => dispatcher({ type: "setPoolDepositPercentage", payload: percent })} mt={1} ml={"auto"} />
              </Box>
            }
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="caption" fontSize={12}>
                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                    Balance:
                  </Typography>{" "}
                  {formatToken(nectBalance, BERABORROW_ADDRESSES.debtToken.decimals, 2)}
                </Typography>
              </Box>
            }
          />
        </Grid>
      </Grid>
      <Typography variant="subtitle1">Potential Composition </Typography>

      <Box
        sx={{
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "16px",
          },
          borderRadius: "12px",
          border: `1px solid ${theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h2" component="span" display="flex" alignItems="center" fontWeight={600} color="var(--text-primary)">
            ${formatToken(depositAssets, SCALING_FACTOR_DECIMALS, 2)}
          </Typography>
          <StyledTooltip title={<>Fee charged on depositing into the Pool</>}>
            <Typography variant="body2" fontSize={12} display="flex" alignItems="center">
              <Typography variant="body2" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center">
                <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle" }} />
                Fee:
              </Typography>
              &nbsp; ${formatToken(fees, BERABORROW_ADDRESSES.debtToken.decimals, 2)}
              {" (" + formatBigIntPercentage(entryFee, 2, undefined, undefined, SCALING_FACTOR_BP_DECIMALS) + "%) "}
            </Typography>
          </StyledTooltip>
        </Box>
        <Divider sx={{ my: 1.5, backgroundColor: `var(--border-color)` }} />

        {assets.map((item) => {
          return (
            <Box key={item.contractAddress}>
              {!!item.balance && (
                <Box key={item.contractAddress} display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                  <Box>
                    <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600}>
                      <TokenIcon contractAddress={item.contractAddress} height={"25px"} width={"25px"} />
                      <Box pl={1}>{formatToken(currentDeposit.getMyShare(item.balance, depositShares), item.decimals, 2, item.ticker)}</Box>
                    </Typography>
                  </Box>
                  <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                    ${formatToken((currentDeposit.getMyShare(item.balance, depositShares) * item.price) / SCALING_FACTOR, item.decimals, 2)}
                  </Typography>
                </Box>
              )}
            </Box>
          );
        })}
      </Box>

      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />

      {/* SDK add subgraph to check if the last block had a withdrawal from this account */}
      <TransactionButton
        disabled={!!validationError || !nectBalance}
        transaction={{ type: "depositDebt", variables: [depositAmount] }}
        onConfirmation={() => dispatcher({ type: "confirmedPoolDeposit" })}
        validationError={validationError}
      >
        Deposit
      </TransactionButton>
    </>
  );
};

const PoolDeposit = WithSuspense(Component, "paper");
export default PoolDeposit;
