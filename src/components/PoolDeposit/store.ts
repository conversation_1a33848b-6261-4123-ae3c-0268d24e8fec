import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { getDebtBalanceAtom } from "../../Atoms/Tokens";
import {
  getStabilityDepositAtom,
  stabilityPoolDepositAmountAtom,
  formValuesStabilityPoolDeposit<PERSON>tom,
  formValidationsStabilityPoolDeposit<PERSON>tom,
  convertSharesToAssetsAtom,
  previewDeposit<PERSON>tom,
  getPoolAPYAtom,
} from "../../Atoms/StabilityPool";
import { formatBigIntPercentage, formatDecimal, regexDecimalToken } from "../../utils/helpers";
import { useHydrateAtoms } from "jotai/utils";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { SCALING_FACTOR_BP, SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import { useLocation } from "react-router-dom";
import { postHog<PERSON>vent<PERSON>tom, updatePost<PERSON>og<PERSON><PERSON><PERSON><PERSON>, resetPostHog<PERSON><PERSON><PERSON>tom } from "../../Atoms/Account";
import { PoolDepositDispatcherActionType } from "./types";

export const usePoolDepositStore = () => {
  const location = useLocation();
  const { data: nectBalance } = useAtomValue(getDebtBalanceAtom);
  const { data: currentDeposit } = useAtomValue(getStabilityDepositAtom);
  const { data: shareToAssetRatio } = useAtomValue(convertSharesToAssetsAtom);
  const { data: debtToSharesRatio } = useAtomValue(previewDepositAtom);
  const { data: apy } = useAtomValue(getPoolAPYAtom);

  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const resetPostHogEvent = useSetAtom(resetPostHogEventAtom);
  const depositAmount = useAtomValue(stabilityPoolDepositAmountAtom);
  const [formValue, setFormValue] = useAtom(formValuesStabilityPoolDepositAtom);
  const validationError = useAtomValue(formValidationsStabilityPoolDepositAtom);
  useHydrateAtoms([
    [formValuesStabilityPoolDepositAtom, formatDecimal(nectBalance, BERABORROW_ADDRESSES.debtToken.decimals)],
    [
      postHogEventAtom,
      {
        type: "poolDeposit",
        page_rel_path: location.pathname,
        token_deposited: BERABORROW_ADDRESSES.debtToken.ticker,
        qty_deposited_token: formatDecimal(nectBalance, BERABORROW_ADDRESSES.debtToken.decimals),
        current_apy: formatBigIntPercentage(apy),
        current_shares: formatDecimal(currentDeposit.shares, BERABORROW_ADDRESSES.debtToken.decimals),
        current_pool_tvl: formatDecimal(currentDeposit.convertSharesToAssets(currentDeposit.sharesTotalSupply, shareToAssetRatio), SCALING_FACTOR_DECIMALS),
      },
    ],
  ]);
  const assets = currentDeposit
    .getUnderlyingAssets()
    .map((item) => {
      const collateral =
        BERABORROW_ADDRESSES.collateralTokens[item.contractAddress] ??
        (item.contractAddress === BERABORROW_ADDRESSES.debtToken.contractAddress
          ? BERABORROW_ADDRESSES.debtToken
          : item.contractAddress === BERABORROW_ADDRESSES.pollenToken.contractAddress
            ? BERABORROW_ADDRESSES.pollenToken
            : undefined);
      return { ...item, ...collateral };
    })
    .filter((item) => item); //incase of undefined
  const depositShares = currentDeposit.convertDebtToShares(depositAmount, debtToSharesRatio);
  const depositAssets = currentDeposit.convertSharesToAssets(depositShares, shareToAssetRatio);

  const fees = currentDeposit.convertSharesToAssets((depositAmount * currentDeposit.entryFee) / SCALING_FACTOR_BP, shareToAssetRatio);
  const dispatcher = (action: PoolDepositDispatcherActionType) => {
    switch (action.type) {
      case "setPoolDeposit":
        updatePostHogEvent({
          token_deposited: BERABORROW_ADDRESSES.debtToken.ticker,
          qty_deposited_token: regexDecimalToken(action.payload, BERABORROW_ADDRESSES.debtToken.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, BERABORROW_ADDRESSES.debtToken.decimals),
          current_pool_tvl: formatDecimal(currentDeposit.convertSharesToAssets(currentDeposit.sharesTotalSupply, shareToAssetRatio), SCALING_FACTOR_DECIMALS),
        });
        setFormValue(regexDecimalToken(action.payload, BERABORROW_ADDRESSES.debtToken.decimals));

        break;
      case "confirmedPoolDeposit":
        resetPostHogEvent({ type: "poolDeposit", page_rel_path: location.pathname });
        setFormValue("0");
        break;
      case "setPoolDepositPercentage":
        const value = formatDecimal((nectBalance * BigInt(action.payload)) / 100n, BERABORROW_ADDRESSES.debtToken.decimals, action.payload === 100 ? undefined : 8, true);
        updatePostHogEvent({
          last_clicked_input_btn: action.payload + "%",
          token_deposited: BERABORROW_ADDRESSES.debtToken.ticker,
          qty_deposited_token: regexDecimalToken(value, BERABORROW_ADDRESSES.debtToken.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, BERABORROW_ADDRESSES.debtToken.decimals),
          current_pool_tvl: formatDecimal(currentDeposit.convertSharesToAssets(currentDeposit.sharesTotalSupply, shareToAssetRatio), SCALING_FACTOR_DECIMALS),
        });

        setFormValue(regexDecimalToken(value, BERABORROW_ADDRESSES.debtToken.decimals));
        break;
      default:
        break;
    }
  };
  return {
    nectBalance,
    currentDeposit,
    validationError,
    formValue,
    depositAmount,
    dispatcher,
    fees,
    entryFee: currentDeposit.entryFee,
    depositAssets,
    depositShares,
    assets,
  };
};

export const getPoolDepositScopedAtoms = () => [formValidationsStabilityPoolDepositAtom, formValuesStabilityPoolDepositAtom, stabilityPoolDepositAmountAtom];
