import { useAtomValue } from "jotai";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { _vaultAddrAtom, previewRedeemUnderlyingAtom, vaultDetailsAtom } from "../../../Atoms/Vault";
import { _managedVaultAtom, getManagedVaultDepositDetailsAtom, getsNectVaultDetailsAtom, getManagedVaultsApysAtom, getBoycoDenFullDetailsAtom } from "../../../Atoms/Boyco";
import { getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { getBorrowingMintFeeRateAtom } from "../../../Atoms/Den";
import { isAddressEqual } from "viem";
export const useManagedVaultDepositStore = () => {
  const { data: currentDeposit } = useAtomValue(getManagedVaultDepositDetailsAtom);
  const { data: sNectDeposit } = useAtomValue(getsNectVaultDetailsAtom);
  const { data: apys } = useAtomValue(getManagedVaultsApysAtom);
  const { data: den } = useAtomValue(getBoycoDenFullDetailsAtom);
  const { data: currentBorrowingRate } = useAtomValue(getBorrowingMintFeeRateAtom);
  const { data: underlyingAssets } = useAtomValue(previewRedeemUnderlyingAtom);
  const vaultAddr = useAtomValue(_managedVaultAtom);
  const vaultDetails = useAtomValue(vaultDetailsAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const price = currentDeposit.vaultPrice;
  const vaultShare = currentDeposit.totalSupply === 0n ? 0n : (currentDeposit.shares * SCALING_FACTOR) / currentDeposit.totalSupply;
  const vaultTvl = (currentDeposit.totalSupply * price) / SCALING_FACTOR;
  const collateralBalance = underlyingAssets.find((el) => isAddressEqual(el.contractAddress, collateralDetails.contractAddress));
  const userCollShares = (den.den.collateral * currentDeposit.shares) / currentDeposit.totalSupply;
  const collateralAmount = ((collateralBalance?.balance ?? SCALING_FACTOR) * userCollShares) / SCALING_FACTOR;
  return {
    currentDeposit,
    apys,
    den,
    collateralAmount,
    vaultShare,
    vaultAddr,
    shares: currentDeposit.shares,
    price,
    vaultDetails,
    vaultTvl,
    sNectDeposit,
    collateralDetails,
    currentBorrowingRate,
    collateralBalance,
  };
};
