import React from "react";
import { Box, Typography, Divider } from "@mui/material";
import { formatBigIntPercentage, formatToken, formatTokenWithMillionsOrBillions } from "../../../utils/helpers";
import WithSuspense from "../../../providers/WithSuspense";
import { DEFAULT_SLIPPAGE_TOLERANCE, SCALING_FACTOR } from "@Beraborrowofficial/sdk";
// import PreloadComponent from "../PreloadComponent/Component";
// import CollateralSelector from "../CollateralSelector/wrapper";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import CollateralPill from "../../CollateralPill/component";
import StyledTooltip from "../../StyledFeeTooltip";
import { InfoOutlined } from "@mui/icons-material";
import { useManagedVaultDepositStore } from "./store";
import TokenIcon from "../../TokenIcons";
const Component: React.FC = () => {
  const { apys, vaultAddr, vaultDetails, currentDeposit, vaultTvl, collateralDetails, den, collateralAmount } = useManagedVaultDepositStore();
  return (
    <Box key={vaultAddr + "info"} px={2}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography pt={"6px"} variant="h1">
          Overview
        </Typography>
        <CollateralPill
          contractAddress={BERABORROW_ADDRESSES.vaults[vaultAddr]?.collateral}
          urlRedirect={"/managed-vault/deposit"}
          excludeWrappedCollateral
          excludeDenManagers
          includeManagedVaults
        />
      </Box>
      <Divider sx={{ my: 1.5, backgroundColor: `var(--border-color)` }} />
      {/* <Box sx={{ backgroundColor: "var(--border-dark-secondary)", padding: "8px", borderRadius:'16px' }}> */}

      <Box>
        <Box
          display="flex"
          flexDirection="column"
          width="100%"
          mt="8px"
          sx={{
            padding: "8px",
            "@media (min-width:600px)": {
              padding: "16px",
            },
            borderRadius: "12px",
            backgroundColor: "var(--border-dark-secondary)",
          }}
        >
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={14} textAlign="center">
              Vault TVL
            </Typography>

            <Typography fontSize={16} variant="h3" textAlign="center">
              $ {formatTokenWithMillionsOrBillions(vaultTvl, collateralDetails.decimals, 2)}
            </Typography>
          </Box>
          <Divider sx={{ backgroundColor: `var(--border-color)`, margin: "8px 0" }} />
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={12} variant="subtitle1" textAlign="center">
              Deposit fee
            </Typography>

            <StyledTooltip title={<Box>Fee Paid when depositing</Box>}>
              <Typography fontSize={12} variant="h3" textAlign="center">
                {formatBigIntPercentage(BigInt(currentDeposit.details.entryFeeInBP), 4)} %
                <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5 }} />
              </Typography>
            </StyledTooltip>
          </Box>
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={12} variant="subtitle1" textAlign="center">
              NECT mint fee
            </Typography>

            <StyledTooltip title={<Box>Fee Paid when minting NECT & staking into sNECT, this is recouped over time via yield earned with sNECT.</Box>}>
              <Typography fontSize={12} variant="h3" textAlign="center">
                {formatBigIntPercentage(DEFAULT_SLIPPAGE_TOLERANCE, 2, undefined, undefined)} %
                <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5 }} />
              </Typography>
            </StyledTooltip>
          </Box>
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={12} variant="subtitle1" textAlign="center">
              Withdraw fee
            </Typography>

            <StyledTooltip title={<Box>Fee Paid when withdrawing</Box>}>
              <Typography fontSize={12} variant="h3" textAlign="center">
                {formatBigIntPercentage(BigInt(currentDeposit.details.exitFeeInBP), 4)} %
                <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5 }} />
              </Typography>
            </StyledTooltip>
          </Box>
        </Box>
        <Box
          display="flex"
          flexDirection="column"
          width="100%"
          mt="8px"
          sx={{
            padding: "8px",
            "@media (min-width:600px)": {
              padding: "16px",
            },
            borderRadius: "12px",
            backgroundColor: "var(--border-dark-secondary)",
          }}
        >
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={14} textAlign="center">
              Total APY
            </Typography>

            <Typography fontSize={14} variant="h3" textAlign="center" color="var(--text-success)">
              {formatBigIntPercentage(apys.apy, apys.apy > SCALING_FACTOR * 10n ? 0 : 2)} %
            </Typography>
          </Box>
          <Divider sx={{ backgroundColor: `var(--border-color)`, margin: "8px 0" }} />
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={12} variant="subtitle1" textAlign="center">
              APY on collateral
            </Typography>

            <StyledTooltip title={<Box>APY earned on your deposited collateral</Box>}>
              <Typography fontSize={14} variant="h3" textAlign="center" color="var(--text-success)">
                +{formatBigIntPercentage(apys.collApy, apys.collApy > SCALING_FACTOR * 10n ? 0 : 2)} %{" "}
                <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5, color: "var(--text-success)" }} />
              </Typography>
            </StyledTooltip>
          </Box>
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={12} variant="subtitle1" textAlign="center">
              Interest paid in relation to Debt
            </Typography>

            <StyledTooltip title={<Box>This is interest charged on the NECT minted</Box>}>
              <Typography fontSize={14} variant="h3" textAlign="center" color="var(--text-error)">
                -{formatBigIntPercentage(apys.effectiveDebtInterestRate, apys.effectiveDebtInterestRate > SCALING_FACTOR * 10n ? 0 : 2)} %{" "}
                <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5, color: "var(--text-error)" }} />
              </Typography>
            </StyledTooltip>
          </Box>
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={12} variant="subtitle1" textAlign="center">
              Auto-compounding sNECT APY
            </Typography>
            <StyledTooltip title={<Box>APY earned on staking NECT in Stability Pool and then staking sNECT in Beraborrow Auto-compounding vault</Box>}>
              <Typography fontSize={14} variant="h3" textAlign="center" color="var(--text-success)">
                +{formatBigIntPercentage(apys.effectiveBbsNectApy + apys.effectiveSNectApy, apys.effectiveBbsNectApy > SCALING_FACTOR * 10n ? 0 : 2)} %{" "}
                <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5, color: "var(--text-success)" }} />
              </Typography>
            </StyledTooltip>
          </Box>
        </Box>
        {!!currentDeposit.shares && (
          <>
            <Box
              display="flex"
              flexDirection="column"
              width="100%"
              mt="8px"
              sx={{
                padding: "8px",
                "@media (min-width:600px)": {
                  padding: "16px",
                },
                borderRadius: "12px",
                backgroundColor: "var(--border-dark-secondary)",
              }}
            >
              {" "}
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography fontSize={14} mb={0.5}>
                  Total Composition
                </Typography>
                <Typography fontSize={14} mb={0.5}>
                  $ {formatTokenWithMillionsOrBillions((currentDeposit.shares * currentDeposit.vaultPrice) / SCALING_FACTOR, collateralDetails.decimals, 2)}
                </Typography>
              </Box>
              <Divider sx={{ backgroundColor: `var(--border-color)`, margin: "8px 0" }} />
              <Typography variant="subtitle1" fontSize={14}>
                Collateral{" "}
                <StyledTooltip
                  title={
                    <Box>
                      {" "}
                      <Typography>This is your share of collateral in the vault </Typography>{" "}
                    </Box>
                  }
                >
                  <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.2 }} />
                </StyledTooltip>
              </Typography>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                <Box display="flex" flexDirection="column" alignItems="flex-start">
                  <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600}>
                    <TokenIcon contractAddress={collateralDetails.contractAddress} height="25px" width="25px" />
                    <Box display="flex" flexDirection="column">
                      <Typography fontSize={14} pl={1}>
                        {formatToken(collateralAmount, vaultDetails.decimals, 2)}
                      </Typography>
                    </Box>
                  </Typography>
                </Box>

                <Box display="flex" flexDirection="column" alignItems="flex-end">
                  <Typography fontSize={14} variant="h5" fontWeight={600} color="var(--text-primary)" mt={1}>
                    ${formatToken((collateralAmount * currentDeposit.collPrice) / SCALING_FACTOR, vaultDetails.decimals, 2)}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="subtitle1" fontSize={14}>
                Debt
                <StyledTooltip
                  title={
                    <Box>
                      <Typography> This is the debt minted against your collateral as Nect to deposit into sNect to allow you to earn additional APY</Typography>
                      <Typography>This amount as well as the sNect will increase and decrease proportionally as the price of your collateral changes</Typography>
                    </Box>
                  }
                >
                  <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.2 }} />
                </StyledTooltip>
              </Typography>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                <Box display="flex" flexDirection="column" alignItems="flex-start">
                  <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600}>
                    <TokenIcon contractAddress={BERABORROW_ADDRESSES.debtToken.contractAddress} height="25px" width="25px" />
                    <Box display="flex" flexDirection="column">
                      <Typography fontSize={14} pl={1}>
                        - {formatToken((den.den.debt * currentDeposit.shares) / currentDeposit.totalSupply, vaultDetails.decimals, 2)}
                      </Typography>
                    </Box>
                  </Typography>
                </Box>

                <Box display="flex" flexDirection="column" alignItems="flex-end" mt={1}>
                  <Typography fontSize={14} variant="h5" fontWeight={600} color="var(--text-primary)">
                    - ${formatToken((((den.den.debt * currentDeposit.shares) / currentDeposit.totalSupply) * currentDeposit.nectPrice) / SCALING_FACTOR, vaultDetails.decimals, 2)}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="subtitle1" fontSize={14}>
                sNect auto-compounding{" "}
                <StyledTooltip
                  title={
                    <Box>
                      <Typography> This is your sNect amount that you minted from the Nect debt that is auto-compounding and earning additional APY</Typography>
                      <Typography>This amount as well as the sNect will increase and decrease proportionally as the price of your collateral changes</Typography>
                    </Box>
                  }
                >
                  <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.2 }} />
                </StyledTooltip>
              </Typography>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                <Box display="flex" flexDirection="column" alignItems="flex-start">
                  <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600}>
                    <TokenIcon contractAddress={BERABORROW_ADDRESSES.stabilityPool} height="25px" width="25px" />
                    <Box display="flex" flexDirection="column">
                      <Typography fontSize={14} pl={1}>
                        + {formatToken((apys.sNectAmount * currentDeposit.shares) / currentDeposit.totalSupply, vaultDetails.decimals, 2)}
                      </Typography>
                    </Box>
                  </Typography>
                </Box>

                <Box display="flex" flexDirection="column" alignItems="flex-end" mt={1}>
                  <Typography fontSize={14} variant="h5" fontWeight={600} color="var(--text-primary)">
                    + $
                    {formatToken(
                      (((apys.sNectAmount * currentDeposit.shares) / currentDeposit.totalSupply) * currentDeposit.sNectPrice) / SCALING_FACTOR,
                      vaultDetails.decimals,
                      2
                    )}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </>
        )}
      </Box>
    </Box>
  );
};

const VaultInfo = WithSuspense(Component, "paper");
export default VaultInfo;
