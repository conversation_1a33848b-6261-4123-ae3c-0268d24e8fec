import { useAtomValue } from "jotai";
import { useNotifications } from "../../../Hooks/useNotifications";
import { formValidationsManagedVaultWithdrawAtom, managedVaultWithdrawAmountAtom } from "../../../Atoms/Boyco";

export const useWithdrawModalStore = () => {
  const { removeModal } = useNotifications();
  const validationError = useAtomValue(formValidationsManagedVaultWithdrawAtom);
  const withdrawAmount = useAtomValue(managedVaultWithdrawAmountAtom);

  const dispatcher = (type: "close" | "dismiss") => {
    switch (type) {
      case "close":
        break;
      case "dismiss":
        removeModal("ManagedVaultWithdraw");
        break;

      default:
        break;
    }
  };

  return {
    validationError,
    withdrawAmount,
    dispatcher,
  };
};
