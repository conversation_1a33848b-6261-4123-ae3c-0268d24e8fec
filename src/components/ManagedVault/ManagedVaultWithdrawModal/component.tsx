import { Box, Grid, Typography, Button } from "@mui/material";
import { useWithdrawModalStore } from "./store";
import TransactionButton from "../../Transaction/Transaction";
import AccessTimeFilledIcon from "@mui/icons-material/AccessTimeFilled";

const ManagedVaultWithdrawModal: React.FC = () => {
  const { dispatcher, validationError, withdrawAmount } = useWithdrawModalStore();
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
      <Box>
        <AccessTimeFilledIcon sx={{ height: "36px", width: "36px" }} fontSize={"small"} color="primary" />
        <Typography variant="h3">Your Withdrawal will be added to the queue and be claimable at the end of next epoch</Typography>

        <Typography variant="h5" mt={1}>
          When you withdraw,Your collateral is used to repay any outstanding debt.
        </Typography>
        <Typography variant="h5" mt={1}>
          {" "}
          Are you sure you want to withdraw and potentially forfeit your NECT yield or wait for it to earned back?{" "}
        </Typography>
      </Box>

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box>
            <TransactionButton
              disabled={!!validationError}
              transaction={{
                type: "redeemIntentManagedVault",
                variables: [withdrawAmount],
              }}
              onConfirmation={() => dispatcher("dismiss")}
              validationError={validationError}
            >
              Withdraw
            </TransactionButton>
          </Box>
        </Grid>
        <Grid item xs={12}>
          <Box>
            <Button variant="outlined" onClick={() => dispatcher("dismiss")} fullWidth>
              Cancel
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};
export default ManagedVaultWithdrawModal;
