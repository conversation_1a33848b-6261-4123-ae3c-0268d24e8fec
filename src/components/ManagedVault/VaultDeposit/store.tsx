import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { getCollateralBalanceAtom, getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { formatBigIntPercentage, formatDecimal, regexDecimalToken } from "../../../utils/helpers";
import { useHydrateAtoms } from "jotai/utils";
import { SCALING_FACTOR, SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import { useLocation } from "react-router-dom";
import { postHogEventAtom, updatePostHogEventAtom, resetPostHogEventAtom } from "../../../Atoms/Account";
import { VaultDepositDispatcherActionType } from "./types";
import { getVaultApyAtom, vaultDetailsAtom, vaultDepositAmountAtom, formValuesVaultDepositAtom, _vaultAddrAtom, formValidationsVaultDepositAtom } from "../../../Atoms/Vault";
import { useNotifications } from "../../../Hooks/useNotifications";
import ApproveManagedVault from "../../Approvals/ApproveManagedVault/component";
import { useEffect } from "react";
import { QUERY_CLIENT } from "../../../utils/constants";
import {
  _managedVaultAtom,
  formValidationsManagedVaultDepositAtom,
  formValuesManagedVaultDepositAtom,
  getCollateralManagedVaultAllowanceAtom,
  getManagedVaultDepositDetailsAtom,
  getsNectVaultDetailsAtom,
  managedVaultDepositAmountAtom,
  EpochDetailsAtom,
} from "../../../Atoms/Boyco";
export const useManagedVaultDepositStore = () => {
  const location = useLocation();
  const { data: collateralBalance } = useAtomValue(getCollateralBalanceAtom);
  const { data: currentDeposit } = useAtomValue(getManagedVaultDepositDetailsAtom);
  const { data: sNectDeposit } = useAtomValue(getsNectVaultDetailsAtom);
  const { data: epochDetails } = useAtomValue(EpochDetailsAtom);

  const price = currentDeposit.collPrice;
  const { data: apy } = useAtomValue(getVaultApyAtom);
  const { data: allowance } = useAtomValue(getCollateralManagedVaultAllowanceAtom);
  const vaultDetails = useAtomValue(vaultDetailsAtom);

  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const vaultAddr = useAtomValue(_managedVaultAtom);

  const { addModal, isModalPresent } = useNotifications();
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const resetPostHogEvent = useSetAtom(resetPostHogEventAtom);
  const depositAmount = useAtomValue(managedVaultDepositAmountAtom);
  const [formValue, setFormValue] = useAtom(formValuesManagedVaultDepositAtom);
  const validationError = useAtomValue(formValidationsManagedVaultDepositAtom);
  const vaultTvl = (currentDeposit.totalSupply * currentDeposit.vaultPrice) / SCALING_FACTOR;
  const depositShares = depositAmount; // (depositAmount * currentDeposit.collateralToShareRatio) / SCALING_FACTOR;
  const depositAssets = depositShares; //(depositShares * currentDeposit.shareToAssetRatio) / SCALING_FACTOR;
  useHydrateAtoms([
    [formValuesVaultDepositAtom, formatDecimal(collateralBalance, collateralDetails.decimals, 8)],
    [
      postHogEventAtom,
      {
        type: "managedVaultDeposit",
        page_rel_path: location.pathname,
        token_deposited: collateralDetails.ticker,
        qty_deposited_token: formatDecimal(collateralBalance, collateralDetails.decimals),
        current_apy: formatBigIntPercentage(apy),
        current_shares: formatDecimal(currentDeposit.shares, collateralDetails.decimals),
        current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
      },
    ],
  ]);

  useEffect(() => {
    setFormValue(collateralBalance ? formatDecimal(collateralBalance, collateralDetails.decimals, 8) : "0");
  }, [location.pathname]);
  const dispatcher = (action: VaultDepositDispatcherActionType) => {
    switch (action.type) {
      case "setManagedVaultDeposit":
        updatePostHogEvent({
          token_deposited: collateralDetails.ticker,
          qty_deposited_token: regexDecimalToken(action.payload, collateralDetails.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, collateralDetails.decimals),
          current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
        });
        setFormValue(regexDecimalToken(action.payload, collateralDetails.decimals));

        break;
      case "confirmedManagedVaultDeposit":
        if (!isModalPresent("ApproveVault")) {
          resetPostHogEvent({ type: "vaultDeposit", page_rel_path: location.pathname });
          QUERY_CLIENT.invalidateQueries().then(() => {
            setFormValue("0");
          });
        }

        break;
      case "setManagedVaultDepositPercentage":
        const value = formatDecimal((collateralBalance * BigInt(action.payload)) / 100n, collateralDetails.decimals, action.payload === 100 ? undefined : 8, true);
        updatePostHogEvent({
          last_clicked_input_btn: action.payload + "%",
          token_deposited: collateralDetails.ticker,
          qty_deposited_token: regexDecimalToken(value, collateralDetails.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, collateralDetails.decimals),
          current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
        });

        setFormValue(regexDecimalToken(value, collateralDetails.decimals));
        break;
      case "approveManagedVault":
        updatePostHogEvent({ type: `approveManagedVault` });
        addModal({
          id: "ApproveManagedVault",
          title: "Approve " + collateralDetails.ticker + " Deposit",
          Component: (
            <ApproveManagedVault
              onConfirmation={() => {
                dispatcher({ type: "confirmedManagedVaultDeposit" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      default:
        break;
    }
  };
  const granularity = 48n;
  const withdrawal_epoch_adjusted_by_granularity = (epochDetails.epoch / granularity) * granularity + (granularity - 1n);
  const withdrawal_timestamp_adjusted_by_granularity = (withdrawal_epoch_adjusted_by_granularity + 1n) * 900n;
  return {
    collateralBalance,
    currentDeposit,
    validationError,
    formValue,
    depositAmount,
    dispatcher,
    depositAssets,
    depositShares,
    vaultDetails,
    collateralDetails,
    vaultAddr,
    price: price,
    needApproval: allowance < depositAmount,
    epochDetails,
    sNectDeposit,
    withdrawal_timestamp_adjusted_by_granularity,
  };
};

export const getVaultDepositScopedAtoms = () => [formValidationsVaultDepositAtom, formValuesVaultDepositAtom, vaultDepositAmountAtom];
