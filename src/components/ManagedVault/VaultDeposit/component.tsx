import React from "react";
import { useManagedVaultDepositStore } from "./store";
import { Box, Divider, Grid, Typography } from "@mui/material";
import { formatToken } from "../../../utils/helpers";
import TransactionButton from "../../Transaction/Transaction";
import WithSuspense from "../../../providers/WithSuspense";
import StyledTextfield from "../../StyledTextField";
import { AmountSelector } from "../../AmountSelector";
import { ValidationButton } from "../../ValidationButton";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import TokenIcon from "../../TokenIcons";
import VaultWithdrawalQueue from "../vaultWithdrawalQueue/component";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
const Component: React.FC = () => {
  const { collateralBalance, validationError, formValue, dispatcher, depositAssets, collateralDetails, vaultAddr, needApproval, depositAmount, price } =
    useManagedVaultDepositStore();
  return (
    <>
      <Grid container pb={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1" mb={0.5}>
            Deposit {collateralDetails.ticker.toUpperCase()}
          </Typography>
          <StyledTextfield
            value={formValue}
            error={validationError?.type === "insufficient_balance"}
            onChange={(e) => dispatcher({ type: "setManagedVaultDeposit", payload: e.target.value })}
            startIcons={<TokenIcon contractAddress={BERABORROW_ADDRESSES.managedVaults[vaultAddr].collateral} height="35px" width="35px" />}
            endComponent={
              <Box position="absolute" right="8px" bottom="8px" display="flex" flexDirection="column" alignItems="flex-end">
                <Typography variant="caption" fontSize={12} textAlign="right">
                  <Typography variant="caption" color="var(--text-secondary)" component="span" fontSize={12}>
                    $ {formatToken((depositAmount * price) / SCALING_FACTOR, collateralDetails.decimals, 2)}
                  </Typography>{" "}
                </Typography>

                <AmountSelector
                  handleItemClick={(percent) =>
                    dispatcher({
                      type: "setManagedVaultDepositPercentage",
                      payload: percent,
                    })
                  }
                  mt={1}
                />
              </Box>
            }
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="caption" fontSize={12}>
                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                    Balance:
                  </Typography>{" "}
                  {formatToken(collateralBalance, collateralDetails.decimals, 2)}
                </Typography>
              </Box>
            }
          />
        </Grid>
      </Grid>

      <Box display={needApproval ? "block" : "none"}>
        <ValidationButton validationError={validationError} fullWidth variant="contained" onClick={() => dispatcher({ type: "approveManagedVault" })} disabled={!!validationError}>
          Approve and Deposit
        </ValidationButton>
      </Box>
      <Box display={needApproval ? "none" : "block"}>
        <TransactionButton
          disabled={!!validationError || !collateralBalance}
          transaction={{ type: "depositManagedVault", variables: [depositAssets] }}
          onConfirmation={() => dispatcher({ type: "confirmedManagedVaultDeposit" })}
          validationError={validationError}
        >
          Deposit
        </TransactionButton>
      </Box>
      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />

      <VaultWithdrawalQueue />
    </>
  );
};

const ManagedVaultDeposit = WithSuspense(Component, "paper");
export default ManagedVaultDeposit;
