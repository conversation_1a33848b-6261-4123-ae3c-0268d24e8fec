import { useAtomValue } from "jotai";

import { getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { EpochDetailsAtom, hasRedeemClaimAtom } from "../../../Atoms/Boyco";

export const useManagedVaultWithdrawStore = () => {
  const { data: epochDetails } = useAtomValue(EpochDetailsAtom);
  const { data: claimAbles } = useAtomValue(hasRedeemClaimAtom);

  const collateralDetails = useAtomValue(getCollateralDetailsAtom);

  const granularity = 48n;
  const withdrawal_epoch_adjusted_by_granularity = (epochDetails.epoch / granularity) * granularity + (granularity - 1n);
  const withdrawal_timestamp_adjusted_by_granularity = (withdrawal_epoch_adjusted_by_granularity + 1n) * 900n;

  const getNextWeekdayEventTime = (): string => {
    const now = new Date();
    const currentDay = now.getUTCDay(); // 0 = Sunday, 6 = Saturday

    const eventDate = new Date(now);

    // Set event time to today at 12:00 UTC
    eventDate.setUTCHours(12, 0, 0, 0);

    // If it's Saturday or Sunday, bump to Monday
    if (currentDay === 6) {
      eventDate.setUTCDate(eventDate.getUTCDate() + 2); // Saturday → Monday
    } else if (currentDay === 0) {
      eventDate.setUTCDate(eventDate.getUTCDate() + 1); // Sunday → Monday
    } else if (now.getTime() >= eventDate.getTime()) {
      // It's a weekday but past 12:00 UTC → move to next weekday
      eventDate.setUTCDate(eventDate.getUTCDate() + 1);

      // If that lands on Saturday or Sunday, skip to Monday
      const nextDay = eventDate.getUTCDay();
      if (nextDay === 6) eventDate.setUTCDate(eventDate.getUTCDate() + 2);
      else if (nextDay === 0) eventDate.setUTCDate(eventDate.getUTCDate() + 1);
    }

    // Format
    const dayName = eventDate.toLocaleDateString("en-GB", {
      weekday: "long",
      timeZone: "UTC",
    });
    const time = eventDate.toLocaleTimeString("en-GB", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      timeZone: "UTC",
    });

    return `${dayName} ${time}`;
  };
  return {
    collateralDetails,
    epochDetails,
    claimAbles,
    withdrawal_timestamp_adjusted_by_granularity,
    getNextWeekdayEventTime,
  };
};
