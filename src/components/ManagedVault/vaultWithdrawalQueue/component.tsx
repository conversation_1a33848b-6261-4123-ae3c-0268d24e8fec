import React from "react";
import { useManagedVaultWithdrawStore } from "./store";
import { Box, Typography } from "@mui/material";
import { formatToken } from "../../../utils/helpers";
import TransactionButton from "../../Transaction/Transaction";
import WithSuspense from "../../../providers/WithSuspense";

import AccessTimeFilledIcon from "@mui/icons-material/AccessTimeFilled";
import StyledAccordion from "../../StyledAccordion";

const Component: React.FC = () => {
  const { collateralDetails, epochDetails, claimAbles, getNextWeekdayEventTime } = useManagedVaultWithdrawStore();

  return (
    <>
      <Box
        mt={1}
        display="flex"
        alignItems="center"
        justifyContent="center"
        sx={{ backgroundColor: "#EC6F1533", color: "var(--primary-main)", borderRadius: "10px", padding: "0 8px", minHeight: "32px" }}
      >
        <AccessTimeFilledIcon sx={{ height: "12px", width: "12px" }} fontSize={"small"} />
        <Typography
          p={1}
          fontSize={12}
        >{` Queued Withdrawals are processed Monday-Friday, the next one is  ${epochDetails.timestamp ? "on " + getNextWeekdayEventTime() : "in: 24 Hours"}`}</Typography>
      </Box>

      {claimAbles
        .filter((item) => !!item.amount)
        .map((item) => {
          return item.claimable ? (
            <Box sx={{ p: 1, mt: 2 }} key={item.epoch}>
              <StyledAccordion title={collateralDetails.ticker + " claimable"} collateralAddress={collateralDetails.contractAddress}>
                <Box sx={{ p: 1, mt: 2 }}>
                  <TransactionButton
                    transaction={{
                      type: "claimIntentManagedVault",
                      variables: [item.epoch, item.minAmount],
                    }}
                    variant="outlined"
                  >
                    Claim Withdrawal
                  </TransactionButton>
                </Box>
              </StyledAccordion>
            </Box>
          ) : (
            <Box sx={{ p: 1, mt: 2 }} key={item.epoch}>
              <StyledAccordion title={`Queued Withdrawal`} collateralAddress={collateralDetails.contractAddress}>
                <Typography textAlign={"center"} variant="subtitle2">
                  Your withdrawal of {formatToken(item.amount, collateralDetails.decimals)} shares will be processed at {getNextWeekdayEventTime()}
                  {/* {item.epoch !== epochDetails.epoch
                    ? "soon"
                    : epochDetails.CutOffStartTimestamp
                      ? " at: " + new Date(Number(withdrawal_timestamp_adjusted_by_granularity) * 1000).toLocaleTimeString()
                      : " in: 24 Hours"} */}
                </Typography>
                <Box sx={{ p: 1, mt: 2 }}>
                  {epochDetails.epoch === item.epoch && (
                    <TransactionButton
                      transaction={{
                        type: "cancelIntentManagedVault",
                        variables: [item.epoch, item.amount],
                      }}
                      variant="outlined"
                    >
                      Cancel Withdrawal
                    </TransactionButton>
                  )}
                </Box>
              </StyledAccordion>
            </Box>
          );
        })}
    </>
  );
};

const VaultWithdrawalQueue = WithSuspense(Component);
export default VaultWithdrawalQueue;
