import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { formatDecimal, regexDecimalToken, formatBigIntPercentage } from "../../../utils/helpers";
import { useHydrateAtoms } from "jotai/utils";
import { SCALING_FACTOR, SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import { useLocation } from "react-router-dom";
import { updatePostHogEventAtom, resetPostHogEventAtom, postHogEventAtom } from "../../../Atoms/Account";
import { ManagedVaultWithdrawDispatcherActionType } from "./types";
import { getVaultApyAtom, vaultDetailsAtom, vaultWithdrawAmountAtom, formValuesVaultWithdrawAtom, formValidationsVaultWithdrawAtom, _vaultAddrAtom } from "../../../Atoms/Vault";
import { useEffect } from "react";
import { QUERY_CLIENT } from "../../../utils/constants";
import { getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import {
  getManagedVaultDepositDetailsAtom,
  formValuesManagedVaultWithdrawAtom,
  formValidationsManagedVaultWithdrawAtom,
  managedVaultWithdrawAmountAtom,
} from "../../../Atoms/Boyco";
import { useNotifications } from "../../../Hooks/useNotifications";
import ManagedVaultWithdrawModal from "../ManagedVaultWithdrawModal/component";

export const useManagedVaultWithdrawStore = () => {
  const location = useLocation();
  const { data: currentDeposit } = useAtomValue(getManagedVaultDepositDetailsAtom);

  const { addModal } = useNotifications();

  const { data: apy } = useAtomValue(getVaultApyAtom);
  const withdrawAmount = useAtomValue(managedVaultWithdrawAmountAtom);
  const vaultDetails = useAtomValue(vaultDetailsAtom);
  const [formValue, setFormValue] = useAtom(formValuesManagedVaultWithdrawAtom);
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const resetPostHogEvent = useSetAtom(resetPostHogEventAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const vaultAddr = useAtomValue(_vaultAddrAtom);
  const validationError = useAtomValue(formValidationsManagedVaultWithdrawAtom);
  const vaultTvl = (currentDeposit.totalSupply * currentDeposit.price) / SCALING_FACTOR;
  const maxAmount = currentDeposit.shares;
  useHydrateAtoms([
    [formValuesVaultWithdrawAtom, formatDecimal(maxAmount, collateralDetails.decimals, 0)],
    [
      postHogEventAtom,
      {
        type: "managedVaultWithdraw",
        page_rel_path: location.pathname,
        token_deposited: vaultDetails.ticker,
        qty_deposited_token: formatDecimal(maxAmount, collateralDetails.decimals),
        current_apy: formatBigIntPercentage(apy),
        current_shares: formatDecimal(currentDeposit.shares, collateralDetails.decimals),
        current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
      },
    ],
  ]);

  useEffect(() => {
    setFormValue(maxAmount ? formatDecimal(maxAmount, collateralDetails.decimals) : "0");
  }, [location.pathname]);
  const dispatcher = (action: ManagedVaultWithdrawDispatcherActionType) => {
    switch (action.type) {
      case "setManagedVaultWithdraw":
        updatePostHogEvent({
          token_withdrew: collateralDetails.ticker,
          qty_withdrew_token: regexDecimalToken(action.payload, collateralDetails.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, collateralDetails.decimals),
          current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
        });
        setFormValue(regexDecimalToken(action.payload, collateralDetails.decimals));
        break;
      case "confirmedManagedVaultWithdraw":
        resetPostHogEvent({ type: "poolWithdraw", page_rel_path: location.pathname });
        QUERY_CLIENT.invalidateQueries().then(() => {
          setFormValue("0");
        });

        break;
      case "setManagedVaultWithdrawPercentage":
        const value = formatDecimal((currentDeposit.shares * BigInt(action.payload)) / 100n, collateralDetails.decimals, action.payload === 100 ? undefined : 8);
        updatePostHogEvent({
          last_clicked_input_btn: action.payload + "%",
          token_withdrew: vaultDetails.ticker,
          qty_withdrew_token: regexDecimalToken(value, collateralDetails.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, collateralDetails.decimals),
          current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
        });
        setFormValue(regexDecimalToken(value, collateralDetails.decimals));
        break;
      case "popupManagedVaultWithdraw":
        addModal({
          id: "ManagedVaultWithdraw",
          title: "Are you sure?",
          Component: <ManagedVaultWithdrawModal />,
        });
        break;
      default:
        break;
    }
  };

  return {
    validationError,
    formValue,
    withdrawAmount,
    maxAmount,
    dispatcher,
    collateralDetails,
    vaultAddr,
  };
};
export const getVaultWithdrawScopedAtoms = () => [formValidationsVaultWithdrawAtom, formValuesVaultWithdrawAtom, vaultWithdrawAmountAtom];
