import { Hex } from "viem";

export type ManagedVaultWithdrawDispatcherActionType =
  | ChangeDeposit
  | ConfirmedVaultWithdraw
  | ChangeWithdrawPercent
  | SetSelectedAsset
  | EnableZap
  | ApproveLsp
  | PopupManagedVaultWithdraw;

type ChangeDeposit = {
  type: "setManagedVaultWithdraw";
  payload: string;
};
type ConfirmedVaultWithdraw = {
  type: "confirmedManagedVaultWithdraw";
};
type ChangeWithdrawPercent = {
  type: "setManagedVaultWithdrawPercentage";
  payload: number;
};
type SetSelectedAsset = {
  type: "setSelectedAsset";
  payload: Hex | undefined;
};
type EnableZap = {
  type: "enableZap";
};
type ApproveLsp = {
  type: "approveLsp";
};
type PopupManagedVaultWithdraw = {
  type: "popupManagedVaultWithdraw";
};
