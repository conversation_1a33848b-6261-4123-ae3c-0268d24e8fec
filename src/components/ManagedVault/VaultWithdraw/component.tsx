import React from "react";
import { useManagedVaultWithdrawStore } from "./store";
import { Box, Divider, Grid, Typography } from "@mui/material";
import { formatToken } from "../../../utils/helpers";
import TransactionButton from "../../Transaction/Transaction";
import WithSuspense from "../../../providers/WithSuspense";
import StyledTextfield from "../../StyledTextField";
import { AmountSelector } from "../../AmountSelector";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import TokenIcon from "../../TokenIcons";
import VaultWithdrawalQueue from "../vaultWithdrawalQueue/component";

const Component: React.FC = () => {
  const { validationError, formValue, withdrawAmount, maxAmount, dispatcher, collateralDetails, vaultAddr } = useManagedVaultWithdrawStore();

  return (
    <>
      <Grid container pb={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1" mb={0.5}>
            Burn Shares
          </Typography>
          <StyledTextfield
            value={formValue}
            error={validationError?.type === "insufficient_balance"}
            onChange={(e) => dispatcher({ type: "setManagedVaultWithdraw", payload: e.target.value })}
            startIcons={<TokenIcon contractAddress={BERABORROW_ADDRESSES.vaults[vaultAddr].collateral} height="35px" width="35px" />}
            endComponent={
              <Box display="flex" position="absolute" right="8px" bottom="8px">
                <AmountSelector handleItemClick={(percent) => dispatcher({ type: "setManagedVaultWithdrawPercentage", payload: percent })} mt={1} ml={"auto"} />
              </Box>
            }
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="caption" fontSize={12}>
                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                    Deposited:
                  </Typography>{" "}
                  {formatToken(maxAmount, collateralDetails.decimals, 2)}
                </Typography>
              </Box>
            }
          />
        </Grid>
      </Grid>

      <Box>
        <TransactionButton
          onClick={() => dispatcher({ type: "popupManagedVaultWithdraw" })}
          disabled={!!validationError || formValue === ""}
          transaction={{
            type: "redeemIntentManagedVault",
            variables: [withdrawAmount],
          }}
          onConfirmation={() => dispatcher({ type: "confirmedManagedVaultWithdraw" })}
          validationError={validationError}
        >
          Queue Withdrawal
        </TransactionButton>
      </Box>
      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />

      <VaultWithdrawalQueue />
    </>
  );
};

const ManagedVaultWithdraw = WithSuspense(Component, "paper");
export default ManagedVaultWithdraw;
