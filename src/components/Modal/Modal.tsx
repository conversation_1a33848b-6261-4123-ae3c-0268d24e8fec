import { CloseOutlined } from "@mui/icons-material";
import { Box, IconButton, Modal as MuiModal, Typography } from "@mui/material";
import "./Modal.scss";
import { useNotifications } from "../../Hooks/useNotifications";
import { ModalProps } from "../../@type/Notifications";
export function Modal({ Component, id, hideBackdrop = false, onClose, dismissable = true, maxWidth, title }: ModalProps) {
  const { removeModal } = useNotifications();
  function dismiss() {
    onClose?.();
    removeModal(id);
  }

  return (
    <MuiModal
      id={id}
      open={true}
      hideBackdrop={hideBackdrop}
      onClose={() => {
        dismissable && dismiss();
      }}
    >
      <Box
        className="standard-modal"
        sx={{
          maxWidth: maxWidth ?? "400px",
          boxShadow: 10,
          borderRadius: 8,
          px: { xs: 1, sm: 2 },
          py: 3,
          background: "var(--background-gradient)",
        }}
      >
        {title && (
          <Typography variant="h3" pb={3} sx={{ fontSize: 22, mt: -1 }} textAlign={"center"}>
            {title}
          </Typography>
        )}
        {dismissable && (
          <IconButton
            className="close-button"
            onClick={() => {
              dismiss();
            }}
            sx={{ borderRadius: "50%", border: "1px solid var(--border-color)" }}
          >
            <CloseOutlined color="inherit" />
          </IconButton>
        )}
        <Box sx={{ flex: 1, overflowY: "auto", minHeight: 0 }}>{Component}</Box>
      </Box>
    </MuiModal>
  );
}

export default Modal;
