import { CircularProgress, Container, useMediaQuery, useTheme } from "@mui/material";

const PaperLoader = () => {
  const theme = useTheme();
  const isSmDown = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Container
      sx={{ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", minHeight: "360px", height: isSmDown ? "500px" : "100%", margin: "auto 0" }}
    >
      <CircularProgress />
    </Container>
  );
};
export default PaperLoader;
