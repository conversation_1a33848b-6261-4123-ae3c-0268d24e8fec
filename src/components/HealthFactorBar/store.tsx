import { useAtomValue } from "jotai";
import { getDenFullDetailsAtom, getDenManagerCollateralPrice, getMCRAtom, _denManagerAddrAtom } from "../../Atoms/Den";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";

export const useHealthFactorStore = () => {
  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);
  const { data: denDetails } = useAtomValue(getDenFullDetailsAtom);
  const { data: mcr } = useAtomValue(getMCRAtom);
  const { den: userDen, densTotal, ccr } = denDetails;
  const healthFactorValue = userDen.collateralRatio(collateralPrice);
  const ltv = userDen.ltv(collateralPrice);
  const averageHealthFactor = densTotal.collateralRatio(collateralPrice);
  const criticalHealthFactor = ccr;
  const maxHealthFactor = ccr + averageHealthFactor - SCALING_FACTOR;
  const healthState: "error" | "success" | "warning" = healthFactorValue < criticalHealthFactor ? "error" : healthFactorValue < averageHealthFactor ? "warning" : "success";
  return {
    ltv,
    percentage: healthFactorValue,
    healthState: healthState,
    criticalHealthFactor,
    averageHealthFactor,
    maxHealthFactor: maxHealthFactor,
    minHealthFactor: mcr,
  };
};
