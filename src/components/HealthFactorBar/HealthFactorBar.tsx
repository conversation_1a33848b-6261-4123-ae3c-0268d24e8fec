import React from "react";
import { Box, Chip, Divider, Typography } from "@mui/material";
import { styled, useMediaQuery, useTheme } from "@mui/system";
import { formatBigIntPercentage } from "../../utils/helpers";
import { InfoOutlined } from "@mui/icons-material"; //Favorite,
import { useHealthFactorStore } from "./store";
import StyledTooltip from "../StyledFeeTooltip";

interface CollateralHealthBarProps {
  details?: boolean;
}

export const HealthFactorBar: React.FC<CollateralHealthBarProps> = () => {
  const theme = useTheme();
  const isMdDown = useMediaQuery(theme.breakpoints.down("md"));
  const { percentage, criticalHealthFactor, averageHealthFactor, maxHealthFactor, minHealthFactor, ltv } = useHealthFactorStore();
  //healthState,
  const range = Number(formatBigIntPercentage(maxHealthFactor - minHealthFactor, 2, true, true));
  const minNumber = Number(formatBigIntPercentage(minHealthFactor, 2, true, true));
  const highRiskPercentage =
    ((Number(formatBigIntPercentage(criticalHealthFactor < averageHealthFactor ? criticalHealthFactor : averageHealthFactor, 2, true, true)) - minNumber) / range) * 100;
  const lowRiskPercentage =
    ((Number(formatBigIntPercentage(averageHealthFactor > criticalHealthFactor ? averageHealthFactor : criticalHealthFactor, 2, true, true)) - minNumber) / range) * 100;
  const currentPercentage = Math.min(Math.max(((Number(formatBigIntPercentage(percentage, 2, true, true)) - minNumber) / range) * 100, 0), 100);

  const GradientBar = styled(Box)(() => ({
    background: `linear-gradient(
      to right, 
      #EC0000 ${highRiskPercentage}%, 
      #EC9C00 ${highRiskPercentage}%, 
      #EC9C00 ${lowRiskPercentage}%, 
      #3DB250 ${lowRiskPercentage}%
    )`,
    height: 15,
    borderRadius: "5px",
    position: "relative",
  }));
  //here
  return (
    <>
      <Divider sx={{ my: "10px", backgroundColor: `var(--border-color)` }} />
      <Box display="flex" justifyContent="space-between" alignItems="center" mb="8px">
        <Box display="flex" flexDirection="column" alignItems="flex-start" gap="4px">
          <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
            Collateral Ratio &nbsp;
            <StyledTooltip
              placement="right-start"
              title={
                <Box display={"flex"} flexDirection={"column"} gap={1}>
                  <Typography variant="h6" fontWeight={700} color={"var(--text-primary)"}>
                    Collateral Ratio
                  </Typography>
                  The ratio of your collateral's value to your NECT debt. It's vital to maintain this ratio above the minimum threshold to avoid liquidations.
                  <a href={"https://beraborrow.gitbook.io/docs/borrowing/collateral-ratio-and-liquidation"} target="_blank">
                    Read more
                  </a>
                </Box>
              }
            >
              <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "bottom" }} />
            </StyledTooltip>
          </Typography>
          <Typography>{percentage ? formatBigIntPercentage(percentage, 2, true, true) + "%" : "-"}</Typography>
        </Box>
        <Box display="flex" flexDirection="column" alignItems="flex-start" gap="4px">
          <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
            LTV &nbsp;
            <StyledTooltip
              placement="right-start"
              title={
                <Box display={"flex"} flexDirection={"column"} gap={1}>
                  <Typography variant="h6" fontWeight={700} color={"var(--text-primary)"}>
                    LTV
                  </Typography>
                  The Loan-to-Value ratio. Is a percentage that compares the amount borrowed (NECT debt) to the value of the Collateral, this is the inverse of Collateral Ratio
                </Box>
              }
            >
              <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "bottom" }} />
            </StyledTooltip>
          </Typography>
          <Typography>{ltv ? formatBigIntPercentage(ltv, 2, true, true) + "%" : "-"}</Typography>
        </Box>
      </Box>

      <Box>
        <GradientBar>
          {currentPercentage !== 0 && (
            <Box position="absolute" top={-2} left={`calc(${currentPercentage}% - 2px)`} width={4} height="calc(100% + 4px)" bgcolor="white" borderRadius="2px" />
          )}
        </GradientBar>
      </Box>
      {!isMdDown && (
        <Box display="flex" justifyContent={"space-between"} pt={"10px"} color="var(--text-secondary)">
          <StyledTooltip
            title={
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="h6" fontWeight={700} color="var(--text-primary)">
                  Minimum Collateral Ratio
                </Typography>
                It is the collateral ratio by which you can have the greatest capital efficiency, but you risk redemptions and sudden collateral price volatility can trigger
                liquidations against your Den.
              </Box>
            }
          >
            <Chip
              variant="filled"
              label={"MCR: " + formatBigIntPercentage(minHealthFactor, 0) + "%"}
              color={"error"}
              size="small"
              icon={<InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "bottom" }} />}
              sx={{ fontWeight: 700, fontSize: 12, opacity: 0.75 }}
            />
          </StyledTooltip>
          <StyledTooltip
            title={
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="h6" fontWeight={700} color="var(--text-primary)">
                  Critical Collateral Ratio
                </Typography>
                The system enters Recovery Mode when this value is greater than or equal to the TCR.
              </Box>
            }
          >
            <Chip
              variant="filled"
              label={"CCR: " + formatBigIntPercentage(criticalHealthFactor, 0) + "%"}
              color={"warning"}
              size="small"
              icon={<InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "bottom" }} />}
              sx={{ fontWeight: 700, fontSize: 12, opacity: 0.75 }}
            />
          </StyledTooltip>

          <StyledTooltip
            title={
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="h6" fontWeight={700} color="var(--text-primary)">
                  Total Collateral Ratio
                </Typography>
                Refers to the average collateral Ratio of the total value of all collaterals in the Den Manager, at their present prices, to the total outstanding debt in the Den
                Manager.
              </Box>
            }
          >
            <Chip
              variant="filled"
              label={"TCR: " + formatBigIntPercentage(averageHealthFactor, 0) + "%"}
              color={averageHealthFactor > criticalHealthFactor ? "success" : "warning"}
              size="small"
              icon={<InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "bottom" }} />}
              sx={{ fontWeight: 700, fontSize: 12, opacity: 0.75 }}
            />
          </StyledTooltip>
        </Box>
      )}
    </>
  );
};
