import React from "react";
import withScopedProvider from "../../../providers/WithScopedProvider";
import Token from "./Token";
import { Hex } from "viem";
import { useAtomValue } from "jotai";
import { getCollateralDetailsAtom, getCollateralBalanceAtom, getCollateralPrice, collateralTokenAtom, collateralTypeSelectorAtom } from "../../../Atoms/Tokens";
import { denManagerAtom, _denManagerAddrAtom } from "../../../Atoms/Den";
import { useHydrateAtoms } from "jotai/utils";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import { queryClientAtom } from "jotai-tanstack-query";
import { useQueryClient } from "@tanstack/react-query";
interface TokenProps {
  denManagerAddress: Hex;
}

const Component: React.FC<TokenProps> = ({ denManagerAddress }) => {
  const queryClient = useQueryClient();
  useHydrateAtoms([
    [denManagerAtom, denManagerAddress],
    [queryClientAtom, queryClient],
  ]);
  const { data: collateralBalance } = useAtomValue(getCollateralBalanceAtom);
  const { data: collateralPrice } = useAtomValue(getCollateralPrice);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const token = { price: collateralPrice, balance: collateralBalance, ...collateralDetails, vaulted: !!BERABORROW_ADDRESSES.denManagers[denManagerAddress].vault };
  return <Token token={token} />;
};

const CollateralToken = withScopedProvider(Component, [
  getCollateralDetailsAtom,
  collateralTokenAtom,
  collateralTypeSelectorAtom,
  denManagerAtom,
  _denManagerAddrAtom,
  queryClientAtom,
]);
export default CollateralToken;
