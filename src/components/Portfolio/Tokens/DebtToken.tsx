import React from "react";
import Token from "./Token";
import { useAtomValue } from "jotai";
import { getDebtBalanceAtom, getDebtPriceAtom } from "../../../Atoms/Tokens";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
const Component: React.FC = () => {
  const { data: balance } = useAtomValue(getDebtBalanceAtom);
  const { data: price } = useAtomValue(getDebtPriceAtom);
  const token = { price, balance, ...BERABORROW_ADDRESSES.debtToken, contractAddress: BERABORROW_ADDRESSES.debtToken.contractAddress };

  return <Token token={token} />;
};

const DebtToken = Component;
export default DebtToken;
