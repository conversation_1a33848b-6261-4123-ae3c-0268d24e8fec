import React from "react";
import { Box, IconButton, Typography, useTheme } from "@mui/material";
import { formatToken } from "../../../utils/helpers";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import TokenIcon from "../../TokenIcons";
import { useWatchAsset } from "wagmi";
import StyledTooltip from "../../StyledFeeTooltip";
import { zeroAddress } from "viem";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";

interface TokenProps {
  token: {
    contractAddress: `0x${string}`;
    decimals: number;
    ticker: string;
    price: bigint;
    balance: bigint;
    vaulted?: boolean;
  };
}

const Token: React.FC<TokenProps> = ({ token }) => {
  const theme = useTheme();
  const { watchAsset } = useWatchAsset();

  const handleAddAsset = () => {
    watchAsset({
      type: "ERC20",
      options: {
        address: token.contractAddress,
        symbol: token.ticker,
        decimals: token.decimals,
      },
    });
  };

  return (
    <>
      <Box
        sx={{
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "12px",
          },
          borderRadius: "12px",
          border: `1px solid ${theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h1" component="span" display="flex" alignItems="center" fontWeight={600}>
              <TokenIcon contractAddress={token.contractAddress} height={"25px"} width={"25px"} />
              <Box pl={1}>{formatToken(token.balance, token.decimals, 2, token.ticker)}</Box>
            </Typography>
          </Box>

          <Typography variant="h1" component="span" display="flex" alignItems="center" fontWeight={600} color="var(--text-primary)">
            <Box> ${formatToken((token.balance * token.price) / SCALING_FACTOR, token.decimals, 2)}</Box>
            <Box>
              {token.contractAddress !== zeroAddress && !BERABORROW_ADDRESSES.collateralTokens[token.contractAddress]?.lpToken ? (
                <StyledTooltip title={"Import " + token.ticker + " to wallet"} placement="right">
                  <Box display="flex" alignItems="center" pl={2} onClick={handleAddAsset}>
                    <IconButton sx={{ color: "white", p: 0 }}>
                      <AddCircleOutlineIcon />
                    </IconButton>
                  </Box>
                </StyledTooltip>
              ) : (
                <Box display="flex" alignItems="center" pl={2} sx={{ opacity: 0 }}>
                  <AddCircleOutlineIcon />
                </Box>
              )}
            </Box>
          </Typography>
        </Box>
      </Box>
    </>
  );
};

export default Token;
