import React from "react";
import Token from "./Token";
import { useAtomValue } from "jotai";
import { getStabilityDepositAtom, convertSharesToAssetsAtom } from "../../../Atoms/StabilityPool";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
const Component: React.FC = () => {
  const { data: currentDeposit } = useAtomValue(getStabilityDepositAtom);

  const { data: shareToAssetRatio } = useAtomValue(convertSharesToAssetsAtom);

  const token = {
    price: shareToAssetRatio,
    balance: currentDeposit.shares,
    ...BERABORROW_ADDRESSES.debtToken,
    ticker: "s" + BERABORROW_ADDRESSES.debtToken.ticker,
    contractAddress: BERABORROW_ADDRESSES.stabilityPool,
  };

  return <Token token={token} />;
};

const SdebtToken = Component;
export default SdebtToken;
