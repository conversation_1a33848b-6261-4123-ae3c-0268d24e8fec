import React from "react";
import Token from "./Token";
import { useAtomValue } from "jotai";
import { getPollenBalanceAtom, getPollenPriceAtom } from "../../../Atoms/Tokens";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
const Component: React.FC = () => {
  const { data: balance } = useAtomValue(getPollenBalanceAtom);
  const { data: price } = useAtomValue(getPollenPriceAtom);

  const token = { price, balance, ...BERABORROW_ADDRESSES.pollenToken, contractAddress: BERABORROW_ADDRESSES.pollenToken.contractAddress };

  return <Token token={token} />;
};

const PollenToken = Component;
export default PollenToken;
