import React from "react";
import { Grid } from "@mui/material";
import { useAtomValue } from "jotai";
import { denManagersAtom } from "../../Atoms/Den";
import WithSuspense from "../../providers/WithSuspense";
import CollateralToken from "./Tokens/CollateralToken";
import DebtToken from "./Tokens/DebtToken";
import SdebtToken from "./Tokens/sDebtToken";
import { useHydrateAtoms } from "jotai/utils";
import { collateralTypeSelectorAtom } from "../../Atoms/Tokens";

// import PollenToken from "./Tokens/PollenToken";

const Component: React.FC = () => {
  const denManagers = useAtomValue(denManagersAtom);
  useHydrateAtoms([[collateralTypeSelectorAtom, "den"]]);
  return (
    <>
      <Grid container pb={2} gap={1}>
        <Grid item xs={12}>
          <SdebtToken />
        </Grid>
        <Grid item xs={12}>
          <DebtToken />
        </Grid>
        {/* <Grid item xs={12}>
          <PollenToken />
        </Grid> */}
        {denManagers.map((denManager) => (
          <Grid item xs={12} key={denManager.contractAddress}>
            <CollateralToken denManagerAddress={denManager.contractAddress} />
          </Grid>
        ))}
      </Grid>
    </>
  );
};

const Portfolio = WithSuspense(Component, "paper");
export default Portfolio;
