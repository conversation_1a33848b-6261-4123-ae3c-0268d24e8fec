import { Container, Typography } from "@mui/material";
import { useEffect } from "react";
import { useRouteError } from "react-router-dom";
import { BaseError } from "viem";

const AppError = () => {
  const error = useRouteError(); // Add appropriate type here
  const viemError = error as BaseError;
  useEffect(() => {
    console.error(error);
  }, []);
  return (
    <Container sx={{ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", height: "100vh" }}>
      <Typography textAlign={"center"} fontSize={30} variant="h1" pb={1}>
        Error
      </Typography>
      <Typography textAlign={"center"} variant="h2">
        {viemError?.shortMessage ? (viemError?.name ? viemError?.name + ": " : "") + viemError?.shortMessage : (viemError.message ?? "An unexpected Error Occurred")}
      </Typography>
    </Container>
  );
};
export default AppError;
