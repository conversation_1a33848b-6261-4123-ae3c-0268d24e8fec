import { useAtomValue } from "jotai";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { _vaultAddrAtom, getVaultDepositAtom, getVaultApyAtom, vaultDetailsAtom, getInfraredVaultPriceAtom, getInfraredTokenPriceAtom } from "../../Atoms/Vault";

export const useVaultDepositStore = () => {
  const { data: currentDeposit } = useAtomValue(getVaultDepositAtom);
  const { data: apy } = useAtomValue(getVaultApyAtom);
  const { data: _price } = useAtomValue(getInfraredVaultPriceAtom);
  const { data: collPrice } = useAtomValue(getInfraredTokenPriceAtom);
  const vaultAddr = useAtomValue(_vaultAddrAtom);
  const vaultDetails = useAtomValue(vaultDetailsAtom);
  const price = currentDeposit.price || _price;
  const vaultShare = currentDeposit.totalSupply === 0n ? 0n : (currentDeposit.shares * SCALING_FACTOR) / currentDeposit.totalSupply;
  const vaultTvl = (currentDeposit.totalSupply * price) / SCALING_FACTOR;

  return {
    currentDeposit,
    apy,
    vaultShare,
    vaultAddr,
    shares: currentDeposit.shares,
    vaultDetails,
    vaultTvl,
    collPrice,
  };
};
