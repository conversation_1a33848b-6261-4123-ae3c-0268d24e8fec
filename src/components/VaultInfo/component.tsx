import React from "react";
import { useVaultDepositStore } from "./store";
import { Box, Grid, Typography, useTheme, Divider, useMediaQuery } from "@mui/material";
import { formatBigIntPercentage, formatTokenWithMillionsOrBillions } from "../../utils/helpers";
import WithSuspense from "../../providers/WithSuspense";
import VaultComposition from "../VaultComposition/Component";
import { SCALING_FACTOR_BP_DECIMALS, SCALING_FACTOR_DECIMALS, SCALING_FACTOR } from "@Beraborrowofficial/sdk";
// import PreloadComponent from "../PreloadComponent/Component";
// import CollateralSelector from "../CollateralSelector/wrapper";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import CollateralPill from "../CollateralPill/component";
import { InfoOutlined } from "@mui/icons-material";
import StyledTooltip from "../StyledFeeTooltip";
const Component: React.FC = () => {
  const { apy, vaultShare, vaultAddr, collPrice, currentDeposit, vaultTvl } = useVaultDepositStore();
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  return (
    <Box key={vaultAddr} pt={isMdUp ? "10px" : "8px"} px={2}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography pt={"6px"} variant="h1">
          Overview
        </Typography>
        <CollateralPill
          contractAddress={BERABORROW_ADDRESSES.vaults[vaultAddr]?.collateral}
          urlRedirect={"/vault/deposit"}
          excludeWrappedCollateral
          excludeDenManagers
          includePublicVaults
        />
      </Box>
      <Divider sx={{ my: 1.5, backgroundColor: `var(--border-color)` }} />
      <Grid container pb={0.5} spacing={0.5}>
        {!!apy && (
          <Grid item xs={!!vaultTvl ? 4 : 6}>
            <Box
              sx={{
                padding: "8px",
                "@media (min-width:600px)": {
                  padding: "16px",
                },
                textAlign: "center",
                borderRadius: "12px",
                border: `1px solid ${theme.palette.background.default}`,
                backgroundColor: theme.palette.background.paper,
              }}
            >
              <StyledTooltip
                title={
                  <Box display={"flex"} flexDirection={"column"} gap={1}>
                    The displayed APY is calculated over 24 hours and annualised, it accounts for the 5% performance fee deducted from the yield generated
                  </Box>
                }
              >
                <span>
                  <Typography variant="subtitle1">
                    APY <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", mr: 0.5 }} />
                  </Typography>
                </span>
              </StyledTooltip>

              <Typography variant="h3" color={"var(--text-success)"}>
                {formatBigIntPercentage(apy, apy > SCALING_FACTOR * 10n ? 0 : 2)} %
              </Typography>
            </Box>
          </Grid>
        )}
        {!!vaultTvl && (
          <Grid item xs={!!apy ? 4 : 6}>
            <Box
              sx={{
                padding: "8px",
                "@media (min-width:600px)": {
                  padding: "16px",
                },
                textAlign: "center",
                borderRadius: "12px",
                border: `1px solid ${theme.palette.background.default}`,
                backgroundColor: theme.palette.background.paper,
              }}
            >
              <Typography variant="subtitle1">Vault TVL</Typography>
              <Typography variant="h3" color={"var(--text-primary)"}>
                $ {formatTokenWithMillionsOrBillions(vaultTvl, SCALING_FACTOR_DECIMALS, 2)}
              </Typography>
            </Box>
          </Grid>
        )}
        <Grid item xs={!!apy && !!vaultTvl ? 4 : !vaultTvl && !apy ? 12 : 6}>
          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              textAlign: "center",

              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <Typography variant="subtitle1">Vault Share</Typography>

            <Typography variant="h3" color={"var(--text-primary)"}>
              {formatBigIntPercentage(vaultShare, 2, undefined, undefined, undefined, 4)} %
            </Typography>
          </Box>
        </Grid>
      </Grid>
      <Grid container pb={1} spacing={0.5}>
        <Grid item xs={6}>
          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <Typography variant="body2" fontSize={12} display="flex" alignItems="center" textAlign={"center"} justifyContent="center">
              <Typography variant="body2" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center" pr={0.5}>
                Deposit Fee:{" "}
              </Typography>
              0%
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={6}>
          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <Typography variant="body2" fontSize={12} display="flex" alignItems="center" textAlign={"center"} justifyContent="center">
              <Typography variant="body2" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center" pr={0.5}>
                Withdraw Fee:{" "}
              </Typography>
              {formatBigIntPercentage(BigInt(currentDeposit.withdrawFee), 2, undefined, undefined, SCALING_FACTOR_BP_DECIMALS) + "%"}
            </Typography>
          </Box>
        </Grid>
      </Grid>
      {!!currentDeposit.totalSupply && (
        <>
          <Typography variant="subtitle1" mb={0.5}>
            My Composition
          </Typography>
          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <VaultComposition
              key={currentDeposit.shares}
              denManagerAddress={vaultAddr}
              vaultAddress={vaultAddr}
              collPrice={collPrice}
              removeWithdrawFee={currentDeposit.withdrawFee}
            />
          </Box>
        </>
      )}
      {/* <PreloadComponent delay={1000}>
        <CollateralSelector urlRedirect={"/vault/deposit"} excludeWrappedCollateral excludeDenManagers includePublicVaults isModal />
      </PreloadComponent> */}
    </Box>
  );
};

const VaultInfo = WithSuspense(Component, "paper");
export default VaultInfo;
