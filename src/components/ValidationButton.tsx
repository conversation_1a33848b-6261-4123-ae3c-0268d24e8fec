import { Box, BoxProps, Button, ButtonProps } from "@mui/material";
import { FormError } from "../@type";
import { InfoOutlined } from "@mui/icons-material";
import theme from "../theme/theme";
import StyledTooltip from "./StyledFeeTooltip";
import ConnectButton from "./ConnectButton/component";

type Props = ButtonProps & {
  validationError: FormError | undefined;
};

const TooltipContent = (props: BoxProps) => {
  const { children, ...boxProps } = props;
  return (
    <Box
      {...boxProps}
      sx={{
        padding: "16px",
        borderRadius: "16px",
        backgroundColor: theme.palette.background.paper,
      }}
    >
      {children}
    </Box>
  );
};
export const ValidationButton = (props: Props) => {
  const { validationError, ...buttonProps } = props;

  return (
    <ConnectButton sx={{ borderRadius: 4, py: 1.5, fontSize: "18px" }} {...buttonProps} disabled={false}>
      <StyledTooltip
        title={
          validationError &&
          (validationError.description || validationError.link) && (
            <TooltipContent display={"flex"} flexDirection={"column"} gap={1}>
              {validationError?.description}
              {validationError?.link && (
                <a href={validationError?.link} target="_blank">
                  Read more
                </a>
              )}
            </TooltipContent>
          )
        }
      >
        <span>
          <Button fullWidth variant="contained" sx={{ borderRadius: 4, py: 1.5, fontSize: "18px" }} {...buttonProps}>
            {validationError && (
              <>
                {validationError.description && <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", mr: 0.5 }} />}
                {validationError.shortMessage ?? props.children}
              </>
            )}
            {!validationError && props.children}
          </Button>
        </span>
      </StyledTooltip>
    </ConnectButton>
  );
};
