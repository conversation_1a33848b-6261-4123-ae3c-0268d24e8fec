import { Box, Button } from "@mui/material";
import React from "react";

type AmountSelectorProps = React.ComponentProps<typeof Box> & {
  handleItemClick: (percent: number) => void;
};

export const AmountSelector: React.FC<AmountSelectorProps> = ({ handleItemClick, ...boxProps }) => {
  return (
    <Box sx={{ display: "flex", gap: 1 }} {...boxProps}>
      {[25, 50, 100].map((percent) => (
        <Button
          key={percent}
          onClick={(event) => {
            event.stopPropagation();
            handleItemClick(percent);
          }}
          size="small"
          variant="contained"
          sx={{ opacity: 0.5 }}
        >
          {percent === 100 ? "Max" : `${percent}%`}
        </Button>
      ))}
    </Box>
  );
};
