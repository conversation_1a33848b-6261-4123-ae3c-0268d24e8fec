import { Box, useTheme, Typography, useMediaQuery } from "@mui/material";

const VaultHeader: React.FC = () => {
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("sm"));
  if (isMdUp) {
    return (
      <Box
        alignItems="center"
        width="100%"
        sx={{
          marginTop: "24px",
          display: "grid",
          gridTemplateColumns: "3fr repeat(4, 2fr) 1fr",
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "16px 12px",
          },
        }}
      >
        <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="flex-start">
          Asset
        </Typography>
        <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
          APY
        </Typography>
        <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
          TVL
        </Typography>
        <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
          Protocol
        </Typography>
        <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
          My shares
        </Typography>
        <Typography display="flex" component={"span"} justifyContent="flex-end">
          {""}
        </Typography>
      </Box>
    );
  }
};
export default VaultHeader;
