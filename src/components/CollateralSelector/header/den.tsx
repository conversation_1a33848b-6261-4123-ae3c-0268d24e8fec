import { Box, useTheme, Typography, useMediaQuery } from "@mui/material";
import StyledTooltip from "../../StyledFeeTooltip";
import { InfoOutlined } from "@mui/icons-material";

const DenHeader: React.FC = () => {
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  return (
    <Box
      alignItems="center"
      width="100%"
      sx={{
        minWidth: isMdUp ? "auto" : "800px",
        marginTop: "24px",
        display: "grid",
        gridTemplateColumns: "repeat(7, 2fr)",
        padding: "8px",
        "@media (min-width:600px)": {
          padding: "16px 12px",
        },
      }}
    >
      <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="flex-start">
        Collateral
      </Typography>
      <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
        APY
      </Typography>
      <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
        TVL
      </Typography>
      <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
        Nect Minted
      </Typography>
      <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
        Interest Rate
      </Typography>
      <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
        <StyledTooltip
          title={
            <Box>
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="h6" fontWeight={700} color="var(--text-primary)">
                  Minimum Collateral Ratio
                </Typography>
                It is the collateral ratio by which you can have the greatest capital efficiency, but you risk redemptions and sudden liquidations.
              </Box>
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="h6" fontWeight={700} color="var(--text-primary)">
                  Critical Collateral Ratio
                </Typography>
                The system enters Recovery Mode when this value is greater than or equal to the Total Collateral Ratio.
              </Box>
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="h6" fontWeight={700} color="var(--text-primary)">
                  Total Collateral Ratio
                </Typography>
                Refers to the average collateral Ratio of the total value of all collaterals in the Den Manager
              </Box>
            </Box>
          }
        >
          <Box display="inline-flex" alignItems="center" gap={0.5}>
            MCR / CCR / TCR
            <InfoOutlined
              sx={{
                fontSize: "inherit",
                color: "var(--text-secondary)",
              }}
            />
          </Box>
        </StyledTooltip>
      </Typography>
      <Typography display="flex" component={"span"} color={"var(--text-secondary)"} justifyContent="center">
        My Collateral Ratio
      </Typography>
    </Box>
  );
};
export default DenHeader;
