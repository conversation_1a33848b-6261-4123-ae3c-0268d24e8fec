import { Box, Button, Grid, Paper, Typography, useMediaQuery, useTheme } from "@mui/material";
import { useAtom, useAtomValue } from "jotai";
import { denManagerAtom, denManagersAtom, denPageFilterAtom, denPageSwitchTypeAtom, getDenManagersDetailsAtom } from "../../Atoms/Den";
import { psmBondAtom, psmBondCollateralsAtom } from "../../Atoms/PsmBond";
import WithSuspense from "../../providers/WithSuspense";
import DenItem from "./denManager/component";
import PsmItem from "./psm/Component";
import VaultItem from "./vault/Component";
import ManagedVaultItem from "./managedVault/component";

import { vaultAtom } from "../../Atoms/Vault";
import { publicVaultsAtom, publicVaultsWithDetailAtom } from "../../Atoms/Vault";
import VaultHeader from "./header/vault";
import ViewListRoundedIcon from "@mui/icons-material/ViewListRounded";
import GridViewOutlinedIcon from "@mui/icons-material/GridViewOutlined";
import DenHeader from "./header/den";
import { DenMangersFullDetails } from "../../@type/Dens";
import { managedVaultsDetailsAtom, managedVaultAtom, managedVaultsAtom } from "../../Atoms/Boyco";
import ManagedVaultHeader from "./header/managedVault";
import WithdrawTable from "../BoycoWithdrawals/WithdrawTable";

interface SelectorProps {
  urlRedirect: string;
  excludeDenManagers?: boolean;
  excludeWrappedCollateral?: boolean;
  includePsm?: boolean;
  includePublicVaults?: boolean;
  includeManagedVaults?: boolean;
  isModal?: boolean;
}
const CollateralSelector: React.FC<SelectorProps> = ({
  urlRedirect,
  excludeWrappedCollateral,
  excludeDenManagers,
  includePsm,
  includePublicVaults,
  isModal,
  includeManagedVaults,
}) => {
  const theme = useTheme();
  const _denManagers = useAtomValue(denManagersAtom);
  const denManagerAddr = useAtomValue(denManagerAtom);
  const psmAddr = useAtomValue(psmBondAtom);
  const psmBondCollaterals = useAtomValue(psmBondCollateralsAtom);
  const _managedVaults = useAtomValue(managedVaultsAtom);
  const { data: managedVaultsDetails } = useAtomValue(managedVaultsDetailsAtom);
  const managedVaults = managedVaultsDetails ?? _managedVaults;
  const managedVaultAddr = useAtomValue(managedVaultAtom);

  const vaultAddr = useAtomValue(vaultAtom);
  const _publicVaults = useAtomValue(publicVaultsAtom);
  const { data: publicVaultsWithDetails } = useAtomValue(publicVaultsWithDetailAtom);
  const publicVaults = publicVaultsWithDetails ?? _publicVaults;
  const { data: denManagersDetails } = useAtomValue(getDenManagersDetailsAtom);
  const denManagers: DenMangersFullDetails[] = denManagersDetails ?? _denManagers;
  const [viewMode, setViewMode] = useAtom(denPageSwitchTypeAtom);
  const [filterMode, setFilterMode] = useAtom(denPageFilterAtom);
  const isMdUp = useMediaQuery(theme.breakpoints.up("sm"));
  const isMobileTable = useMediaQuery(theme.breakpoints.down("md"));
  return (
    <Grid container alignItems="center" justifyContent="space-evenly">
      {(includePsm ? psmBondCollaterals : []).map((item) => (
        <PsmItem key={item} collateralAddress={item} urlRedirect={urlRedirect} selected={psmAddr === item} />
      ))}
      {(includePublicVaults || includeManagedVaults) && !isModal && (
        <Box
          display="flex"
          flexDirection="column"
          width="100%"
          sx={{
            backgroundColor: isMdUp ? "transparent" : "#241E1B80",
            borderRadius: "12px",
            padding: "16px 12px",
          }}
        >
          <Typography
            textAlign={"center"}
            fontSize={isMdUp ? 35 : 32}
            variant="h1"
            sx={{
              margin: "24px 0",
            }}
          >
            {includeManagedVaults && "Managed "} Vaults
          </Typography>
          <Box
            width={viewMode === "list" && isMobileTable ? "calc(100vw - 20px)" : "auto"}
            padding={isMobileTable && viewMode === "list" ? "0 15px 0 0" : 0}
            overflow={isMobileTable && viewMode === "list" ? "scroll" : "auto"}
          >
            {includeManagedVaults ? <ManagedVaultHeader /> : <VaultHeader />}
            <Box display="flex" width="100%" flexWrap="wrap" flexDirection={isMdUp ? "row" : "column"} justifyContent="flex-start" gap="16px">
              {(includeManagedVaults ? managedVaults : []).map((item) => (
                <ManagedVaultItem
                  key={item.contractAddress}
                  managedVault={item}
                  urlRedirect={urlRedirect}
                  selected={managedVaultAddr === item.contractAddress}
                  isModal={isModal}
                  isGrid={false}
                />
              ))}
              {includeManagedVaults && <WithdrawTable />}

              {(includePublicVaults ? publicVaults : []).map((item) => (
                <VaultItem
                  key={item.contractAddress}
                  contractAddress={item.contractAddress}
                  urlRedirect={urlRedirect}
                  selected={vaultAddr === item.contractAddress}
                  isModal={isModal}
                />
              ))}
            </Box>
          </Box>
        </Box>
      )}
      {!excludeDenManagers && !isModal && (
        <Box
          display="flex"
          flexDirection="column"
          width="100%"
          sx={{
            backgroundColor: isMdUp ? "transparent" : "#241E1B80",
            borderRadius: "12px",
            padding: "16px 12px",
          }}
        >
          <Box display="flex" alignItems="center" flexDirection={"column"} width="100%">
            <Typography
              textAlign={"start"}
              fontSize={isMdUp ? 35 : 32}
              variant="h1"
              sx={{
                margin: "24px 0",
              }}
            >
              Explore Dens
            </Typography>
            <Box display="flex" mb="8px" gap="8px" alignItems="center" justifyContent="space-between" width="100%" flexDirection={isMdUp ? "row" : "column"}>
              <Box display="flex" gap="8px">
                <Button
                  sx={{
                    textTransform: "inherit",
                    textDecoration: "unset",
                    fontSize: "14px",
                    fontWeight: 600,
                    padding: "8px",
                    maxHeight: "36px",
                    minWidth: "56px",
                    borderRadius: 12,
                    border: `1px solid`,
                    boxSizing: "border-box",
                    backgroundColor: filterMode === "all" ? "var(--border-dark-secondary)" : "var(--background-default)",
                    cursor: "pointer",
                    borderColor: "var(--background-default)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  onClick={() => setFilterMode("all")}
                >
                  <Typography color={filterMode === "all" ? "var(--text-primary)" : "var(--text-secondary)"}>All</Typography>
                </Button>
                <Button
                  sx={{
                    textTransform: "inherit",
                    textDecoration: "unset",
                    fontSize: "14px",
                    fontWeight: 600,
                    padding: "8px",
                    maxHeight: "36px",
                    minWidth: "56px",
                    borderRadius: 12,
                    border: `1px solid`,
                    boxSizing: "border-box", // Ensures padding/border are included
                    backgroundColor: filterMode === "positions" ? "var(--border-dark-secondary)" : "var(--background-default)",
                    color: "var(--text-secondary-default)",
                    borderColor: "var(--background-default)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "4px",
                  }}
                  onClick={() => setFilterMode("positions")}
                >
                  <Typography color={filterMode === "positions" ? "var(--text-primary)" : "var(--text-secondary)"}>My positions</Typography>
                  <Paper
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      maxHeight: "25px",
                      backgroundColor: "var(--background-default)",
                      borderColor: "var(--background-default)",
                    }}
                  >
                    {denManagersDetails && denManagersDetails.length > 0 ? denManagersDetails.filter((den) => den.den?.status === "open").length : 0}
                  </Paper>
                </Button>
              </Box>
              <Box display="flex" gap="8px">
                {/* <Box
                  sx={{
                    textTransform: "inherit",
                    textDecoration: "unset",
                    fontSize: "14px",
                    fontWeight: 600,
                    padding: "8px",
                    maxHeight: "36px",
                    minWidth: "56px",
                    borderRadius: 12,
                    border: `1px solid`,
                    boxSizing: "border-box", // Ensures padding/border are included
                    backgroundColor: "var(--background-default)",
                    color: "var(--text-secondary-default)",
                    borderColor: "var(--background-default)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <SortRounded sx={{ fill: "var(--text-secondary)" }} />
                  <Typography color={"var(--text-secondary)"}>Sort</Typography>
                </Box> */}
                <Box
                  display="flex"
                  gap="8px"
                  alignItems="center"
                  sx={{
                    textTransform: "inherit",
                    textDecoration: "unset",
                    fontSize: "14px",
                    fontWeight: 600,
                    padding: "4px",
                    height: "36px",
                    minWidth: "80px",
                    borderRadius: 12,
                    border: `1px solid`,
                    boxSizing: "border-box", // Ensures padding/border are included
                    backgroundColor: "var(--border-dark-secondary)",
                    color: "var(--text-secondary-default)",
                    borderColor: "var(--background-default)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Button sx={{ width: "26px", minWidth: "0", height: "100%" }} variant="text" onClick={() => setViewMode("grid")}>
                    <GridViewOutlinedIcon
                      sx={{
                        fill: viewMode === "grid" ? "var(--icon-active)" : "var(--icon-disabled)",
                        color: viewMode === "list" ? "var(--icon-active)" : "var(--icon-disabled)",
                        height: "18px",
                        width: "18px",
                      }}
                    />
                  </Button>

                  <Button sx={{ width: "26px", minWidth: "0", height: "100%" }} variant="text" onClick={() => setViewMode("list")}>
                    <ViewListRoundedIcon
                      sx={{
                        fill: viewMode === "list" ? "var(--icon-active)" : "var(--icon-disabled)",
                        color: viewMode === "list" ? "var(--icon-active)" : "var(--icon-disabled)",
                        height: "18px",
                        width: "18px",
                      }}
                    />
                  </Button>
                </Box>
              </Box>
            </Box>
          </Box>
          <Box
            width={viewMode === "list" && isMobileTable ? "calc(100vw - 20px)" : "auto"}
            padding={isMobileTable && viewMode === "list" ? "0 15px 0 0" : 0}
            overflow={isMobileTable && viewMode === "list" ? "scroll" : "auto"}
          >
            {viewMode === "list" && <DenHeader />}

            <Box display="flex" width="100%" flexWrap="wrap" flexDirection={isMdUp ? "row" : "column"} justifyContent="flex-start" gap="16px">
              {denManagers &&
                denManagers
                  .filter((item) => (excludeWrappedCollateral ? !item.wrappedCollateral : true))
                  .filter((item) => (filterMode === "positions" ? item.den?.status === "open" : true))
                  .map((item) => (
                    <DenItem
                      key={item.contractAddress}
                      denManager={item}
                      urlRedirect={urlRedirect}
                      selected={includePsm && psmAddr ? false : denManagerAddr === item.contractAddress}
                      isGrid={viewMode === "grid"}
                    />
                  ))}
            </Box>
          </Box>
        </Box>
      )}
      {isModal &&
        (includePublicVaults ? publicVaults : []).map((item) => (
          <VaultItem key={item.contractAddress} contractAddress={item.contractAddress} urlRedirect={urlRedirect} selected={vaultAddr === item.contractAddress} isModal={isModal} />
        ))}
      {isModal &&
        (includeManagedVaults ? managedVaults : []).map((item) => (
          <ManagedVaultItem key={item.contractAddress} managedVault={item} urlRedirect={urlRedirect} selected={managedVaultAddr === item.contractAddress} isModal={isModal} />
        ))}
      {isModal &&
        !excludeDenManagers &&
        denManagers
          .filter((item) => (excludeWrappedCollateral ? !item.wrappedCollateral : true))
          .map((item) => (
            <DenItem
              key={item.contractAddress}
              denManager={item}
              urlRedirect={urlRedirect}
              selected={includePsm && psmAddr ? false : denManagerAddr === item.contractAddress}
              isModal
            />
          ))}
    </Grid>
  );
};
export default WithSuspense(CollateralSelector, "paper");
