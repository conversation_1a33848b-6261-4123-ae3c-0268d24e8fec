import { useAtomValue } from "jotai";
import {
  _denManagerAddr<PERSON>tom,
  denManagerAtom,
  getDenFullDetailsAtom,
  getDenInterestRateAtom,
  getDenManagerCollateralPrice,
  getDensDetailsAtom,
  getDensDetailsNoWaitAtom,
  getDensMaxDebtAtom,
  getMCRAtom,
  protocolAtom,
} from "../../../Atoms/Den";
import { useHydrateAtoms } from "jotai/utils";
import { useQueryClient } from "@tanstack/react-query";
import { queryClientAtom } from "jotai-tanstack-query";
import { Hex } from "viem";
import { ANON_ADDRESS, BERABORROW_ADDRESSES } from "../../../utils/constants";
import { useNavigate } from "react-router-dom";
import { useNotifications } from "../../../Hooks/useNotifications";
import { _vaultAddr<PERSON>tom, getVault<PERSON>py<PERSON>tom, vaultAtom } from "../../../Atoms/Vault";
import { accountAtom } from "../../../Atoms/Account";
import { denRedeemAmountsAtom } from "../../../Atoms/Redemptions";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { collateralTypeSelectorAtom } from "../../../Atoms/Tokens";
import { useAccount } from "wagmi";
import {
  getBoycoDenFullDetailsAtom,
  managedVaultAtom,
  getManagedVaultsApysAtom,
  getManagedVaultDepositDetailsAtom,
  getsNectVaultDetailsAtom,
  getBBsNectVaultApyAtom,
  getBBsNectPreviewRedeemUnderlyingAtom,
} from "../../../Atoms/Boyco";
import { getPoolAPYAtom } from "../../../Atoms/StabilityPool";

export const useManagedVaultSelectorStore = (managedVaultAddr: Hex, urlRedirect: string) => {
  const { removeModal } = useNotifications();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { address } = useAccount();
  useHydrateAtoms([
    [queryClientAtom, queryClient],
    [managedVaultAtom, managedVaultAddr],
    [denManagerAtom, BERABORROW_ADDRESSES.managedVaults[managedVaultAddr].denManager],
    [collateralTypeSelectorAtom, "managedVault"],
    [accountAtom, address ?? ANON_ADDRESS],
  ]);

  const { data: densDetails } = useAtomValue(getBoycoDenFullDetailsAtom);
  const { data: apys } = useAtomValue(getManagedVaultsApysAtom);
  const { data: interestRates } = useAtomValue(getDenInterestRateAtom);
  const { data: currentDeposit } = useAtomValue(getManagedVaultDepositDetailsAtom);

  const { data: mcr } = useAtomValue(getMCRAtom);
  const { data: maxDebt } = useAtomValue(getDensMaxDebtAtom);
  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);
  const { den: userDen, densTotal, ccr } = densDetails;
  const averageHealthFactor = densTotal.collateralRatio(collateralPrice);
  const criticalHealthFactor = ccr;
  const collateralRatio = userDen.collateralRatio(collateralPrice);
  const ltv = userDen.ltv(collateralPrice);
  const collateralValue = (userDen.collateral * collateralPrice) / SCALING_FACTOR;
  const userBalance = userDen.collateral;
  const vaultTvl = (currentDeposit.totalSupply * currentDeposit.vaultPrice) / SCALING_FACTOR;

  const dispatcher = (action: { type: "changeDenManager"; payload: Hex }) => {
    switch (action.type) {
      case "changeDenManager": {
        const managedVault = action.payload as Hex;
        const collateral = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.managedVaults[managedVault].collateral];
        const ticker = collateral.ticker;
        navigate(`${urlRedirect}/${ticker}`);
        removeModal("DenManagerSelector");
        break;
      }
      default:
        break;
    }
  };

  return {
    userBalance,
    debt: densTotal.debt,
    collateralValue,
    densDetails,
    dispatcher,
    apy: apys.apy,
    interestRates,
    criticalHealthFactor,
    averageHealthFactor,
    minHealthFactor: mcr,
    ltv,
    collateralRatio,
    maxDebt,
    currentDeposit,
    vaultTvl,
  };
};

export const getManagedVaultSelectorScopedAtoms = () => [
  denManagerAtom,
  managedVaultAtom,
  getMCRAtom,
  accountAtom,
  vaultAtom,
  getDenFullDetailsAtom,
  getDenInterestRateAtom,
  getVaultApyAtom,
  denRedeemAmountsAtom,
  queryClientAtom,
  getDensDetailsAtom,
  getDensDetailsNoWaitAtom,
  _denManagerAddrAtom,
  getDenManagerCollateralPrice,
  collateralTypeSelectorAtom,
  getBoycoDenFullDetailsAtom,
  getManagedVaultDepositDetailsAtom,
  getsNectVaultDetailsAtom,
  getBBsNectVaultApyAtom,
  getBBsNectPreviewRedeemUnderlyingAtom,
  getPoolAPYAtom,
  protocolAtom,
  getManagedVaultsApysAtom,
  getDensMaxDebtAtom,
];
