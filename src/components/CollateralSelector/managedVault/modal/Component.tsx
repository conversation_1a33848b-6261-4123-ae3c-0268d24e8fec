import { Grid, <PERSON>, Typography, <PERSON><PERSON>, Theme, Chip } from "@mui/material";
import { ManagedVaultDetails } from "../../../../@type/Boyco";
import { useManagedVaultSelectorStore, getManagedVaultSelectorScopedAtoms } from "../store";
import { formatBigIntPercentage, formatTokenWithMillionsOrBillions } from "../../../../utils/helpers";
import TokenIcon from "../../../TokenIcons";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import WithScopedProvider from "../../../../providers/WithScopedProvider";
import { InfoOutlined } from "@mui/icons-material";

const ModalItem: React.FC<{ managedVault: ManagedVaultDetails; urlRedirect: string; selected?: boolean; disabled?: boolean; theme: Theme }> = ({
  managedVault,
  urlRedirect,
  selected,
  disabled,
  theme,
}) => {
  const { densDetails, dispatcher, apy } = useManagedVaultSelectorStore(managedVault.contractAddress, urlRedirect);
  return (
    <Grid item xs={12} mb={1} key={managedVault.contractAddress}>
      <Button
        onClick={() => {
          dispatcher({ type: "changeDenManager", payload: managedVault.contractAddress });
        }}
        disabled={disabled}
        fullWidth
        variant="contained"
        size="small"
        sx={{
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "16px 12px",
          },
          borderRadius: "12px",
          border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper,
          "&.Mui-disabled": {
            borderRadius: "12px",
            border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.background.default}`,
            backgroundColor: theme.palette.background.paper, // Ensure disabled bg is same as normal
            color: theme.palette.text.primary, // Keep text color normal
            opacity: 0.5, // Prevent MUI from reducing opacity
          },
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
          {/* Left Side: Token Icon and Text */}
          <Box display="flex" alignItems="center">
            <TokenIcon contractAddress={managedVault.collateralAddress} vaulted={!!managedVault.vaultAddress} vaultSize={"60%"} height="35px" width="35px" />
            <Box pl={2} textAlign={"left"}>
              <Typography variant="h4" fontWeight={800}>
                {managedVault.collateralTicker}
              </Typography>
              <Typography>
                <Typography component={"span"} color={"var(--text-secondary)"}>
                  TVL:
                </Typography>{" "}
                ${formatTokenWithMillionsOrBillions((densDetails.den.collateral * densDetails.price) / SCALING_FACTOR, managedVault.vaultDecimals, 3)}
              </Typography>
            </Box>
          </Box>

          {/* Right Side: Text vertically aligned */}
          <Box textAlign="right">
            {/* Vault APY if available */}
            {managedVault.vaultAddress && apy > 1000000000000n ? (
              <Chip
                icon={<InfoOutlined />}
                variant="outlined"
                label={formatBigIntPercentage(apy, 2) + "% APY"}
                color={"success"}
                size="small"
                sx={{ fontWeight: 600, fontSize: 10, opacity: 0.75, ml: 1 }}
              />
            ) : (
              <Box height="20px" />
            )}
            {/* My Position */}

            <Box height="20px" />
          </Box>
        </Box>
      </Button>
    </Grid>
  );
};
export default WithScopedProvider(ModalItem, getManagedVaultSelectorScopedAtoms());
