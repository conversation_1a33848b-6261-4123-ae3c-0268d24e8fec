import { useTheme } from "@mui/material";
import { getManagedVaultSelectorScopedAtoms } from "./store";
import WithScopedProvider from "../../../providers/WithScopedProvider";
import ModalItem from "./modal/Component";
import ManagedVaultGridPageItem from "./page/GridView";
import ManagedVaultTablePageItem from "./page/TableView";
import { ManagedVaultDetails } from "../../../@type/Boyco";
const ManagedVaultItem: React.FC<{ managedVault: ManagedVaultDetails; urlRedirect: string; selected?: boolean; disabled?: boolean; isModal?: boolean; isGrid?: boolean }> = ({
  managedVault,
  urlRedirect,
  selected,
  disabled,
  isModal,
  isGrid,
}) => {
  const theme = useTheme();
  const content = isModal ? (
    <ModalItem managedVault={managedVault} urlRedirect={urlRedirect} selected={selected} disabled={disabled} theme={theme} />
  ) : isGrid ? (
    <ManagedVaultGridPageItem managedVault={managedVault} urlRedirect={urlRedirect} selected={selected} disabled={disabled} theme={theme} />
  ) : (
    <ManagedVaultTablePageItem managedVault={managedVault} urlRedirect={urlRedirect} selected={selected} disabled={disabled} theme={theme} />
  );
  return content;
};
export default WithScopedProvider(ManagedVaultItem, getManagedVaultSelectorScopedAtoms());
