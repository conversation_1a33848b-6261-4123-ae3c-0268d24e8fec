import { Box, Typography, Button, Theme, useMediaQuery } from "@mui/material";
import { useManagedVaultSelectorStore, getManagedVaultSelectorScopedAtoms } from "../store";
import { formatBigIntPercentage, formatToken, formatTokenWithMillionsOrBillions } from "../../../../utils/helpers";
import TokenIcon from "../../../TokenIcons";
import WithScopedProvider from "../../../../providers/WithScopedProvider";
import { ManagedVaultDetails } from "../../../../@type/Boyco";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";

const ManagedVaultTablePageItem: React.FC<{ managedVault: ManagedVaultDetails; urlRedirect: string; selected?: boolean; disabled?: boolean; theme: Theme }> = ({
  managedVault,
  urlRedirect,
  theme,
}) => {
  const { dispatcher, apy, criticalHealthFactor, averageHealthFactor, minHealthFactor, collateralRatio, currentDeposit, vaultTvl } = useManagedVaultSelectorStore(
    managedVault.contractAddress,
    urlRedirect
  );
  const isSmUp = useMediaQuery(theme.breakpoints.up("sm"));
  return (
    <Button
      onClick={() => {
        dispatcher({ type: "changeDenManager", payload: managedVault.contractAddress });
      }}
      fullWidth
      variant="contained"
      size="small"
      sx={{
        minWidth: "800px",
        padding: "8px",
        "@media (min-width:600px)": {
          padding: "16px",
        },
        borderRadius: isSmUp ? "12px" : "18px",
        border: "2px solid var(--border-dark)",
        backgroundColor: theme.palette.background.paper,
        "&:hover": {
          border: "2px solid var(--border-dark)",
          backgroundColor: theme.palette.common.black,
          opacity: 0.9,
        },
        "&.Mui-disabled": {
          border: "2px solid var(--border-dark)",
          backgroundColor: "var(--border-dark)",
        },
      }}
    >
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: "repeat(6, 2fr)",
        }}
        alignItems="center"
        width="100%"
      >
        <Box display="flex" alignItems="center">
          <TokenIcon contractAddress={managedVault.collateralAddress} height="35px" width="35px" />
          <Box pl={2} textAlign={"left"}>
            <Typography variant="h4" fontWeight={800} color="var(--text-primary)">
              {managedVault.collateralTicker}
            </Typography>
          </Box>
        </Box>
        {/* Left Side: Token Icon and Text */}
        <Box display="flex" alignItems="center" justifyContent="center">
          {apy > 1000000000000n && (
            <Typography component="span" color={"var(--text-success)"}>
              {apy ? formatBigIntPercentage(apy, 2) + "%" : "-"}
            </Typography>
          )}
        </Box>

        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
            ${formatTokenWithMillionsOrBillions(vaultTvl, managedVault.collateralDecimals, 3)}
          </Typography>
        </Box>
        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
            {collateralRatio ? `${formatBigIntPercentage(collateralRatio, 0)}%` : "-"}
          </Typography>
        </Box>
        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
            {`${formatBigIntPercentage(minHealthFactor, 0)}% / ${formatBigIntPercentage(criticalHealthFactor, 0)}% / ${formatBigIntPercentage(averageHealthFactor, 0)}%`}
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
            {currentDeposit.shares ? `$${formatToken((currentDeposit.shares * currentDeposit.vaultPrice) / SCALING_FACTOR, managedVault.collateralDecimals, 2)}` : "-"}
          </Typography>
        </Box>
      </Box>
    </Button>
  );
};
export default WithScopedProvider(ManagedVaultTablePageItem, getManagedVaultSelectorScopedAtoms());
