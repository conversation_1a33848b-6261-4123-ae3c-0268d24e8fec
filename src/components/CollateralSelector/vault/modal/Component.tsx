import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON>, Chip, Theme } from "@mui/material";
import { useVaultSelectorStore } from "../store";
import TokenIcon from "../../../TokenIcons";
import { Hex } from "viem";
import { BERABORROW_ADDRESSES } from "../../../../utils/constants";
import OpenInNewRoundedIcon from "@mui/icons-material/OpenInNewRounded";
import { formatBigIntPercentage, formatTokenWithMillionsOrBillions } from "../../../../utils/helpers";
import { InfoOutlined } from "@mui/icons-material";
import StyledTooltip from "../../../StyledFeeTooltip";

type VaultItemProps = {
  contractAddress: Hex;
  urlRedirect: string;
  isModal?: boolean;
  selected?: boolean;
  disabled?: boolean;
};

const ModalItem: React.FC<VaultItemProps & { theme: Theme }> = ({ contractAddress, urlRedirect, selected, disabled, theme }) => {
  const { dispatcher, vaultCollateral, vaultApy, vaultTvl } = useVaultSelectorStore(contractAddress, urlRedirect);
  return (
    <Button
      onClick={() => {
        dispatcher({ type: "change", payload: contractAddress });
      }}
      fullWidth
      disabled={disabled}
      variant="contained"
      size="small"
      sx={{
        padding: "8px",
        "@media (min-width:600px)": {
          padding: "16px 12px",
        },
        borderRadius: "12px",
        border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.background.default}`,
        backgroundColor: theme.palette.background.paper,
        "&.Mui-disabled": {
          borderRadius: "12px",
          border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper, // Ensure disabled bg is same as normal
          color: theme.palette.text.primary, // Keep text color normal
          opacity: 0.5, // Prevent MUI from reducing opacity
        },
      }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
        {/* Left Side: Token Icon and Text */}
        <Box display="flex" alignItems="center">
          <TokenIcon contractAddress={BERABORROW_ADDRESSES.vaults[contractAddress].collateral} height="35px" width="35px" />
          <Box pl={2} textAlign={"left"}>
            <Typography variant="h4" fontWeight={800}>
              {BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.vaults[contractAddress].collateral].ticker}
            </Typography>
            {vaultCollateral.getUrl && (
              <Link
                href={vaultCollateral.getUrl}
                target="_blank"
                onClick={(e) => {
                  e.stopPropagation();
                }}
                sx={{
                  color: "var(--text-secondary)", // Remove the default underline
                }}
              >
                <Typography
                  component="span"
                  sx={{
                    display: "inline-flex",
                    alignItems: "center",
                    textDecoration: "none", // Remove the default underline
                    "&:hover": {
                      color: "var(--primary-main)",
                      textDecoration: "underline", // Apply underline on hover
                    },
                  }}
                >
                  <Box component="span" pr={0.5} mt={0.3}>
                    {vaultCollateral?.protocol?.toUpperCase() || "SWAP"}
                  </Box>
                  <OpenInNewRoundedIcon fontSize="inherit" />
                </Typography>
              </Link>
            )}
          </Box>
        </Box>
        <Box textAlign="right">
          {!!vaultApy ? (
            <StyledTooltip title={` Auto-compounding ${vaultCollateral?.ticker} vault`}>
              <Chip
                icon={<InfoOutlined />}
                variant="outlined"
                label={formatBigIntPercentage(vaultApy, 2) + "% APY"}
                color={"success"}
                size="small"
                sx={{ fontWeight: 600, fontSize: 10, opacity: 0.75, ml: 1 }}
              />
            </StyledTooltip>
          ) : (
            <Box height="20px" />
          )}{" "}
          {!!vaultTvl ? (
            <Typography mt={0.3}>
              <Typography component={"span"} color={"var(--text-secondary)"}>
                TVL:
              </Typography>
              ${formatTokenWithMillionsOrBillions(vaultTvl, BERABORROW_ADDRESSES.vaults[contractAddress].decimals, 0)}
            </Typography>
          ) : (
            <Box height="20px" />
          )}
        </Box>
      </Box>
    </Button>
  );
};

export default ModalItem;
