import { <PERSON>, Typo<PERSON>, But<PERSON>, Link, Theme, useMediaQuery } from "@mui/material";
import { useVaultSelectorStore } from "../store";
import TokenIcon from "../../../TokenIcons";
import { Hex } from "viem";
import { BERABORROW_ADDRESSES } from "../../../../utils/constants";
import ArrowOutwardRoundedIcon from "@mui/icons-material/ArrowOutwardRounded";
import { formatBigIntPercentage, formatTokenWithMillionsOrBillions, formatToken } from "../../../../utils/helpers";

type VaultItemProps = {
  contractAddress: Hex;
  urlRedirect: string;
  isModal?: boolean;
  selected?: boolean;
  disabled?: boolean;
};

const PageItem: React.FC<VaultItemProps & { theme: Theme }> = ({ contractAddress, urlRedirect, selected, disabled, theme }) => {
  const { dispatcher, vaultCollateral, vaultApy, vaultTvl, currentDeposit } = useVaultSelectorStore(contractAddress, urlRedirect);

  const isMdUp = useMediaQuery(theme.breakpoints.up("sm"));
  return (
    <Button
      onClick={() => {
        dispatcher({ type: "change", payload: contractAddress });
      }}
      fullWidth
      disabled={disabled}
      variant="contained"
      size="small"
      sx={{
        padding: "8px",
        "@media (min-width:600px)": {
          padding: "16px",
        },
        borderRadius: isMdUp ? "12px" : "18px",
        backgroundColor: theme.palette.background.paper,
        "&:hover": {
          backgroundColor: theme.palette.background.paper,
        },
        "&.Mui-disabled": {
          borderRadius: "12px",
          border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.background.default}`,
          color: theme.palette.text.primary, // Keep text color normal
          opacity: 0.5, // Prevent MUI from reducing opacity
        },
      }}
    >
      {isMdUp ? (
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "3fr repeat(4, 2fr) 1fr",
          }}
          alignItems="center"
          width="100%"
        >
          <Box display="flex" alignItems="center">
            <TokenIcon contractAddress={BERABORROW_ADDRESSES.vaults[contractAddress].collateral} height="35px" width="35px" />
            <Box pl={2} textAlign={"left"}>
              <Typography variant="h4" fontWeight={800}>
                {BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.vaults[contractAddress].collateral].ticker}
              </Typography>
            </Box>
          </Box>
          <Box display="flex" alignItems="center" justifyContent="center">
            <Typography component="span" color={"var(--text-success)"}>
              +{formatBigIntPercentage(vaultApy, 2) + "%"}
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" justifyContent="center">
            <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
              ${formatTokenWithMillionsOrBillions(vaultTvl, BERABORROW_ADDRESSES.vaults[contractAddress].decimals, 0)}
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" justifyContent="center">
            {vaultCollateral.getUrl && (
              <Link
                href={vaultCollateral.getUrl}
                target="_blank"
                onClick={(e) => {
                  e.stopPropagation();
                }}
                sx={{
                  color: "var(--text-secondary)", // Remove the default underline
                }}
              >
                <Typography
                  component="span"
                  sx={{
                    display: "inline-flex",
                    alignItems: "center",
                    justifyContent: "center",
                    textDecoration: "none", // Remove the default underline
                    "&:hover": {
                      color: "var(--primary-main)",
                      textDecoration: "underline", // Apply underline on hover
                    },
                  }}
                >
                  <Box component="span" pr={0.5} mt={0.3}>
                    {vaultCollateral?.protocol?.toUpperCase() || "SWAP"}
                  </Box>
                  <ArrowOutwardRoundedIcon fontSize="inherit" />
                </Typography>
              </Link>
            )}
          </Box>
          <Box display="flex" alignItems="center" justifyContent="center">
            <Typography mt={0.3}>
              <Typography component={"span"} color={"var(--text-primary)"}>
                {formatToken(currentDeposit.shares, BERABORROW_ADDRESSES.vaults[contractAddress].decimals, 2)}
              </Typography>
            </Typography>
          </Box>

          <Box display="flex" alignItems="center" justifyContent="flex-end">
            <Button
              size="small"
              variant="contained"
              sx={{
                height: "33px",
                width: "60px",
                borderRadius: "9px",
                background: "var(--primary-gradient)",
                color: "white",
                fontSize: "12px",
                fontFamily: "var(--w3m-font-family)",
                fontWeight: "500",
              }}
            >
              View
            </Button>
          </Box>
        </Box>
      ) : (
        <Box display="flex" flexDirection={"column"} width="100%" height={"78px"} padding={"8px"} justifyContent="space-between">
          {/* Left Side: Token Icon and Text */}
          <Box display="flex" justifyContent="space-between">
            <Box display="flex">
              <TokenIcon contractAddress={BERABORROW_ADDRESSES.vaults[contractAddress].collateral} height="35px" width="35px" />
              <Box pl={2} textAlign={"left"}>
                <Typography variant="h4" fontWeight={800}>
                  {BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.vaults[contractAddress].collateral].ticker}
                </Typography>
                {vaultCollateral.getUrl && (
                  <Link
                    href={vaultCollateral.getUrl}
                    target="_blank"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    sx={{
                      color: "var(--text-secondary)", // Remove the default underline
                    }}
                  >
                    <Typography
                      component="span"
                      sx={{
                        display: "inline-flex",
                        alignItems: "center",
                        textDecoration: "none", // Remove the default underline
                        "&:hover": {
                          color: "var(--primary-main)",
                          textDecoration: "underline", // Apply underline on hover
                        },
                      }}
                    >
                      <Box component="span" pr={0.5} mt={0.3}>
                        {/* {vaultCollateral?.protocol?.toUpperCase() || "SWAP"} */}
                        {"SWAP"}
                      </Box>
                      <ArrowOutwardRoundedIcon fontSize="inherit" />
                    </Typography>
                  </Link>
                )}
              </Box>
            </Box>
            <Box display="flex" alignItems="center">
              <Button
                size="small"
                variant="contained"
                sx={{
                  height: "33px",
                  width: "60px",
                  borderRadius: "9px",
                  background: "var(--primary-gradient)",
                  color: "white",
                  fontSize: "12px",
                  fontFamily: "var(--w3m-font-family)",
                  fontWeight: "500",
                }}
              >
                View
              </Button>
            </Box>
          </Box>
          <Box display="flex" justifyContent="space-between">
            <Typography>
              <Typography component={"span"} color={"var(--text-secondary)"}>
                {"APY: "}
              </Typography>
              <Typography component="span" color={"var(--text-success)"}>
                +{formatBigIntPercentage(vaultApy, 2) + "%"}
              </Typography>
            </Typography>
            <Typography>
              <Typography component={"span"} color={"var(--text-secondary)"}>
                {"TVL: "}
              </Typography>
              <Typography component="span" color={"var(--text-primary)"}>
                ${formatTokenWithMillionsOrBillions(vaultTvl, BERABORROW_ADDRESSES.vaults[contractAddress].decimals, 0)}
              </Typography>
            </Typography>
            <Typography>
              <Typography component={"span"} color={"var(--text-secondary)"}>
                {"My shares: "}
              </Typography>
              <Typography component="span" color={"var(--text-primary)"}>
                {formatToken(currentDeposit.shares, BERABORROW_ADDRESSES.vaults[contractAddress].decimals, 2)}
              </Typography>
            </Typography>
          </Box>
        </Box>
      )}
    </Button>
  );
};

export default PageItem;
