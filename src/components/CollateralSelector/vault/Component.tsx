import { Grid, useTheme } from "@mui/material";
import { getVaultSelectorScopedAtoms } from "./store";
import { Hex } from "viem";
import WithScopedProvider from "../../../providers/WithScopedProvider";
import ModalItem from "./modal/Component";
import PageItem from "./page/Component";

type VaultItemProps = {
  contractAddress: Hex;
  urlRedirect: string;
  isModal?: boolean;
  selected?: boolean;
  disabled?: boolean;
};

const VaultItem: React.FC<VaultItemProps> = ({ contractAddress, urlRedirect, selected, disabled, isModal }) => {
  const theme = useTheme();

  const content = isModal ? (
    <ModalItem contractAddress={contractAddress} urlRedirect={urlRedirect} selected={selected} disabled={disabled} theme={theme} />
  ) : (
    <PageItem contractAddress={contractAddress} urlRedirect={urlRedirect} selected={selected} disabled={disabled} theme={theme} />
  );

  return (
    <Grid item xs={12} mb={1} key={contractAddress}>
      {content}
    </Grid>
  );
};
export default WithScopedProvider(VaultItem, getVaultSelectorScopedAtoms());
