import { Hex } from "viem";
import { ANON_ADDRESS, BERABORROW_ADDRESSES } from "../../../utils/constants";
import { useNavigate } from "react-router-dom";
import { useNotifications } from "../../../Hooks/useNotifications";
import { useHydrateAtoms } from "jotai/utils";
import { useQueryClient } from "@tanstack/react-query";
import { queryClientAtom } from "jotai-tanstack-query";
import { getVaultApyAtom, vaultAtom, _vaultAddrAtom, getVaultDepositAtom, getInfraredVaultPriceAtom } from "../../../Atoms/Vault";
import { useAtomValue } from "jotai";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { collateralTypeSelectorAtom } from "../../../Atoms/Tokens";
import { _denManagerAddrAtom } from "../../../Atoms/Den";
import { useAccount } from "wagmi";
import { accountAtom } from "../../../Atoms/Account";
export const useVaultSelectorStore = (contractAddress: Hex, urlRedirect: string) => {
  const queryClient = useQueryClient();
  const { address } = useAccount();
  useHydrateAtoms([
    [queryClientAtom, queryClient],
    [vaultAtom, contractAddress],
    [accountAtom, address ?? ANON_ADDRESS],
    [collateralTypeSelectorAtom, "vault"],
  ]);
  const { removeModal } = useNotifications();
  const navigate = useNavigate();

  const { data: currentDeposit } = useAtomValue(getVaultDepositAtom);
  const { data: _price } = useAtomValue(getInfraredVaultPriceAtom);
  const price = currentDeposit.price || _price;
  const vaultTvl = (currentDeposit.totalSupply * price) / SCALING_FACTOR;
  const { data: vaultApy } = useAtomValue(getVaultApyAtom);

  const vaultCollateral = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.vaults[contractAddress].collateral];
  const dispatcher = (action: { type: "change"; payload: Hex }) => {
    switch (action.type) {
      case "change": {
        const collateral = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.vaults[contractAddress].collateral];
        const ticker = (collateral.lpToken ? collateral.lpToken.provider + "-" : "") + collateral.ticker;
        navigate(`${urlRedirect}/${ticker}`);
        removeModal("DenManagerSelector");
        break;
      }
      default:
        break;
    }
  };

  return { dispatcher, vaultApy, vaultCollateral, vaultTvl, currentDeposit };
};
export const getVaultSelectorScopedAtoms = () => [
  vaultAtom,
  getVaultApyAtom,
  accountAtom,
  _vaultAddrAtom,
  queryClientAtom,
  collateralTypeSelectorAtom,
  _denManagerAddrAtom,
  getInfraredVaultPriceAtom,
  getVaultDepositAtom,
];
