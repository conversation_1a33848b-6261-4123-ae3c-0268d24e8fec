import { Hex } from "viem";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import { useNavigate } from "react-router-dom";
import { useNotifications } from "../../../Hooks/useNotifications";
import { useHydrateAtoms } from "jotai/utils";
import { useQueryClient } from "@tanstack/react-query";
import { queryClientAtom } from "jotai-tanstack-query";
import { psmBondAtom, getMaxPsmRedeemAtom } from "../../../Atoms/PsmBond";
import { useAtomValue } from "jotai";
export const usePsmSelectorStore = (contractAddress: Hex, urlRedirect: string) => {
  const { removeModal } = useNotifications();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { data: psmTvl } = useAtomValue(getMaxPsmRedeemAtom);

  useHydrateAtoms([
    [queryClientAtom, queryClient],
    [psm<PERSON>ond<PERSON>tom, contractAddress],
  ]);
  const dispatcher = (action: { type: "changeDenManager"; payload: Hex }) => {
    switch (action.type) {
      case "changeDenManager": {
        const ticker = BERABORROW_ADDRESSES.collateralTokens[contractAddress].ticker;
        navigate(`${urlRedirect}/${ticker}`);
        removeModal("DenManagerSelector");
        break;
      }
      default:
        break;
    }
  };

  return { dispatcher, psmTvl };
};
export const getPsmSelectorScopedAtoms = () => [psmBondAtom];
