import { Grid, Box, useTheme, Typography, But<PERSON> } from "@mui/material";
import { usePsmSelectorStore, getPsmSelectorScopedAtoms } from "./store";
import TokenIcon from "../../TokenIcons";
import { Hex } from "viem";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import WithScopedProvider from "../../../providers/WithScopedProvider";

const PsmItem: React.FC<{ collateralAddress: Hex; urlRedirect: string; selected?: boolean; disabled?: boolean }> = ({ collateralAddress, urlRedirect, selected, disabled }) => {
  const { dispatcher } = usePsmSelectorStore(collateralAddress, urlRedirect);
  const theme = useTheme();

  return (
    <Grid item xs={12} mb={1} key={collateralAddress}>
      <Button
        onClick={() => {
          dispatcher({ type: "changeDenManager", payload: collateralAddress });
        }}
        fullWidth
        disabled={disabled}
        variant="contained"
        size="small"
        sx={{
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "16px 12px",
          },
          borderRadius: "12px",
          border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper,
          "&.Mui-disabled": {
            borderRadius: "12px",
            border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.background.default}`,
            backgroundColor: theme.palette.background.paper, // Ensure disabled bg is same as normal
            color: theme.palette.text.primary, // Keep text color normal
            opacity: 0.5, // Prevent MUI from reducing opacity
          },
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
          {/* Left Side: Token Icon and Text */}
          <Box display="flex" alignItems="center">
            <TokenIcon contractAddress={collateralAddress} height="35px" width="35px" />
            <Box pl={2} textAlign={"left"}>
              <Typography variant="h4" fontWeight={800}>
                {BERABORROW_ADDRESSES.collateralTokens[collateralAddress]?.ticker}
              </Typography>
            </Box>
          </Box>

          <Box textAlign="right">
            <Box height="20px" />
          </Box>
        </Box>
      </Button>
    </Grid>
  );
};
export default WithScopedProvider(PsmItem, getPsmSelectorScopedAtoms());
