import { Grid, <PERSON>, Typography, Button, Theme } from "@mui/material";
import { DenManagerDetails } from "../../../../@type/Dens";
import { useDenManagerSelectorStore, getDenManagerSelectorScopedAtoms } from "../store";
import { formatBigIntPercentage, formatTokenWithMillionsOrBillions } from "../../../../utils/helpers";
import TokenIcon from "../../../TokenIcons";
import VaultApy from "../../../VaultApy/Component";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import WithScopedProvider from "../../../../providers/WithScopedProvider";

const ModalItem: React.FC<{ denManager: DenManagerDetails; urlRedirect: string; selected?: boolean; disabled?: boolean; theme: Theme }> = ({
  denManager,
  urlRedirect,
  selected,
  disabled,
  theme,
}) => {
  const { densDetails, dispatcher } = useDenManagerSelectorStore(denManager.contractAddress, urlRedirect);
  return (
    <Grid item xs={12} mb={1} key={denManager.contractAddress}>
      <Button
        onClick={() => {
          dispatcher({ type: "changeDenManager", payload: denManager.contractAddress });
        }}
        disabled={disabled}
        fullWidth
        variant="contained"
        size="small"
        sx={{
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "16px 12px",
          },
          borderRadius: "12px",
          border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper,
          "&.Mui-disabled": {
            borderRadius: "12px",
            border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.background.default}`,
            backgroundColor: theme.palette.background.paper, // Ensure disabled bg is same as normal
            color: theme.palette.text.primary, // Keep text color normal
            opacity: 0.5, // Prevent MUI from reducing opacity
          },
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
          {/* Left Side: Token Icon and Text */}
          <Box display="flex" alignItems="center">
            <TokenIcon contractAddress={denManager.collateralAddress} vaulted={!!denManager.vaultAddress} vaultSize={"60%"} height="35px" width="35px" />
            <Box pl={2} textAlign={"left"}>
              <Typography variant="h4" fontWeight={800}>
                {denManager.collateralTicker}
              </Typography>
              <Typography>
                <Typography component={"span"} color={"var(--text-secondary)"}>
                  TVL:
                </Typography>{" "}
                ${formatTokenWithMillionsOrBillions((densDetails.densTotal.collateral * densDetails.price) / SCALING_FACTOR, denManager.vaultDecimals, 3)}
              </Typography>
            </Box>
          </Box>

          {/* Right Side: Text vertically aligned */}
          <Box textAlign="right">
            {/* Vault APY if available */}
            {denManager.vaultAddress ? <VaultApy address={denManager.contractAddress} /> : <Box height="20px" />}
            {/* My Position */}

            {densDetails.den.collateral && !disabled ? (
              <>
                <Typography>
                  <Typography component={"span"} color={"var(--text-secondary)"}>
                    My Collateral Ratio:
                  </Typography>
                  {formatBigIntPercentage(densDetails.den.collateralRatio(densDetails.price), 0)}%
                </Typography>
              </>
            ) : disabled ? (
              <>
                <Typography>
                  <Typography component={"span"} color={"var(--text-secondary)"}></Typography>
                  Coming Soon
                </Typography>
              </>
            ) : (
              <Box height="20px" />
            )}
          </Box>
        </Box>
      </Button>
    </Grid>
  );
};
export default WithScopedProvider(ModalItem, getDenManagerSelectorScopedAtoms());
