import { useTheme } from "@mui/material";
import { DenManagerDetails } from "../../../@type/Dens";
import { getDenManagerSelectorScopedAtoms } from "./store";
import WithScopedProvider from "../../../providers/WithScopedProvider";
import ModalItem from "./modal/Component";
import DenGridPageItem from "./page/GridView";
import DenTablePageItem from "./page/TableView";

const DenItem: React.FC<{ denManager: DenManagerDetails; urlRedirect: string; selected?: boolean; disabled?: boolean; isModal?: boolean; isGrid?: boolean }> = ({
  denManager,
  urlRedirect,
  selected,
  disabled,
  isModal,
  isGrid,
}) => {
  const theme = useTheme();
  const content = isModal ? (
    <ModalItem denManager={denManager} urlRedirect={urlRedirect} selected={selected} disabled={disabled} theme={theme} />
  ) : isGrid ? (
    <DenGridPageItem denManager={denManager} urlRedirect={urlRedirect} selected={selected} disabled={disabled} theme={theme} />
  ) : (
    <DenTablePageItem denManager={denManager} urlRedirect={urlRedirect} selected={selected} disabled={disabled} theme={theme} />
  );
  return content;
};
export default WithScopedProvider(DenItem, getDenManagerSelectorScopedAtoms());
