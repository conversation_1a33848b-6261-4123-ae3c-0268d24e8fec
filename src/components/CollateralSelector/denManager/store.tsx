import { useAtomValue } from "jotai";
import {
  _denManagerAddr<PERSON>tom,
  denManagerAtom,
  getDenFullDetailsAtom,
  getDenInterestRateAtom,
  getDenManagerCollateralPrice,
  getDensDetailsAtom,
  getDensDetailsNoWaitAtom,
  getDensMaxDebtAtom,
  getMCRAtom,
} from "../../../Atoms/Den";
import { useHydrateAtoms } from "jotai/utils";
import { useQueryClient } from "@tanstack/react-query";
import { queryClientAtom } from "jotai-tanstack-query";
import { Hex } from "viem";
import { ANON_ADDRESS, BERABORROW_ADDRESSES } from "../../../utils/constants";
import { useNavigate } from "react-router-dom";
import { useNotifications } from "../../../Hooks/useNotifications";
import { _vaultAddrAtom, getVault<PERSON>py<PERSON>tom, vaultAtom } from "../../../Atoms/Vault";
import { accountAtom } from "../../../Atoms/Account";
import { denRedeemAmountsAtom } from "../../../Atoms/Redemptions";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { collateralTypeSelectorAtom } from "../../../Atoms/Tokens";
import { useAccount } from "wagmi";

export const useDenManagerSelectorStore = (denManagerAddr: Hex, urlRedirect: string) => {
  const { removeModal } = useNotifications();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { address } = useAccount();
  useHydrateAtoms([
    [queryClientAtom, queryClient],
    [denManagerAtom, denManagerAddr],
    [vaultAtom, undefined],
    [collateralTypeSelectorAtom, "den"],
    [accountAtom, address ?? ANON_ADDRESS],
  ]);

  const { data: densDetails } = useAtomValue(getDenFullDetailsAtom);
  const { data: vaultApy } = useAtomValue(getVaultApyAtom);
  const { data: interestRates } = useAtomValue(getDenInterestRateAtom);

  const { data: mcr } = useAtomValue(getMCRAtom);
  const { data: maxDebt } = useAtomValue(getDensMaxDebtAtom);
  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);
  const { den: userDen, densTotal, ccr } = densDetails;
  const averageHealthFactor = densTotal.collateralRatio(collateralPrice);
  const criticalHealthFactor = ccr;
  const collateralRatio = userDen.collateralRatio(collateralPrice);
  const ltv = userDen.ltv(collateralPrice);
  const collateralValue = (userDen.collateral * collateralPrice) / SCALING_FACTOR;
  const userBalance = userDen.collateral;
  const dispatcher = (action: { type: "changeDenManager"; payload: Hex }) => {
    switch (action.type) {
      case "changeDenManager": {
        const denManager = action.payload as Hex;
        const collateral = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.denManagers[denManager].collateral];
        const ticker = collateral.ticker;
        navigate(`${urlRedirect}/${ticker}`);
        removeModal("DenManagerSelector");
        break;
      }
      default:
        break;
    }
  };

  return {
    userBalance,
    debt: densTotal.debt,
    collateralValue,
    densDetails,
    dispatcher,
    vaultApy,
    interestRates,
    criticalHealthFactor,
    averageHealthFactor,
    minHealthFactor: mcr,
    ltv,
    collateralRatio,
    maxDebt,
  };
};

export const getDenManagerSelectorScopedAtoms = () => [
  denManagerAtom,
  getMCRAtom,
  accountAtom,
  vaultAtom,
  getDenFullDetailsAtom,
  getDenInterestRateAtom,
  getVaultApyAtom,
  denRedeemAmountsAtom,
  queryClientAtom,
  getDensDetailsAtom,
  getDensDetailsNoWaitAtom,
  _denManagerAddrAtom,
  getDenManagerCollateralPrice,
];
