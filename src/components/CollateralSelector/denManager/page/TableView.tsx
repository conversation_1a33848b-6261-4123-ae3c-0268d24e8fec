import { Box, Typography, Button, Theme, useMediaQuery } from "@mui/material";
import { DenManagerDetails } from "../../../../@type/Dens";
import { useDenManagerSelectorStore, getDenManagerSelectorScopedAtoms } from "../store";
import { formatBigIntPercentage, formatTokenWithMillionsOrBillions } from "../../../../utils/helpers";
import TokenIcon from "../../../TokenIcons";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import WithScopedProvider from "../../../../providers/WithScopedProvider";

const DenTablePageItem: React.FC<{ denManager: DenManagerDetails; urlRedirect: string; selected?: boolean; disabled?: boolean; theme: Theme }> = ({
  denManager,
  urlRedirect,
  theme,
}) => {
  const { densDetails, dispatcher, vaultApy, interestRates, criticalHealthFactor, averageHealthFactor, minHealthFactor, collateralRatio, maxDebt } = useDenManagerSelectorStore(
    denManager.contractAddress,
    urlRedirect
  );
  const isSmUp = useMediaQuery(theme.breakpoints.up("sm"));
  return (
    <Button
      onClick={() => {
        dispatcher({ type: "changeDenManager", payload: denManager.contractAddress });
      }}
      fullWidth
      variant="contained"
      size="small"
      sx={{
        minWidth: "800px",
        padding: "8px",
        "@media (min-width:600px)": {
          padding: "16px",
        },
        borderRadius: isSmUp ? "12px" : "18px",
        border: "2px solid var(--border-dark)",
        backgroundColor: theme.palette.background.paper,
        "&:hover": {
          border: "2px solid var(--border-dark)",
          backgroundColor: theme.palette.common.black,
          opacity: 0.9,
        },
        "&.Mui-disabled": {
          border: "2px solid var(--border-dark)",
          backgroundColor: "var(--border-dark)",
        },
      }}
    >
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: "repeat(7, 2fr)",
        }}
        alignItems="center"
        width="100%"
      >
        <Box display="flex" alignItems="center">
          <TokenIcon contractAddress={denManager.collateralAddress} height="35px" width="35px" />
          <Box pl={2} textAlign={"left"}>
            <Typography variant="h4" fontWeight={800} color="var(--text-primary)">
              {denManager.collateralTicker}
            </Typography>
          </Box>
        </Box>
        {/* Left Side: Token Icon and Text */}
        <Box display="flex" alignItems="center" justifyContent="center">
          {!!vaultApy && (
            <Typography component="span" color={"var(--text-success)"}>
              {formatBigIntPercentage(vaultApy, 2) + "%"}
            </Typography>
          )}
        </Box>

        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
            ${formatTokenWithMillionsOrBillions((densDetails.densTotal.collateral * densDetails.price) / SCALING_FACTOR, denManager.vaultDecimals, 3)}
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
            {formatTokenWithMillionsOrBillions(densDetails.densTotal.debt, denManager.vaultDecimals, 3)}/ {formatTokenWithMillionsOrBillions(maxDebt, denManager.vaultDecimals, 3)}
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
            {interestRates !== undefined ? `${formatBigIntPercentage(BigInt(interestRates), 2, true, true, 4)}%` : "0.00"}
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
            {`${formatBigIntPercentage(minHealthFactor, 0)}% / ${formatBigIntPercentage(criticalHealthFactor, 0)}% / ${formatBigIntPercentage(averageHealthFactor, 0)}%`}
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
            {collateralRatio ? `${formatBigIntPercentage(collateralRatio, 0)}%` : "-"}
          </Typography>
        </Box>
      </Box>
    </Button>
  );
};
export default WithScopedProvider(DenTablePageItem, getDenManagerSelectorScopedAtoms());
