import { Box, Typography, Button, Theme, useMediaQuery, Chip } from "@mui/material";
import { DenManagerDetails } from "../../../../@type/Dens";
import { useDenManagerSelectorStore, getDenManagerSelectorScopedAtoms } from "../store";
import { formatBigIntPercentage, formatTokenWithMillionsOrBillions } from "../../../../utils/helpers";
import TokenIcon from "../../../TokenIcons";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import WithScopedProvider from "../../../../providers/WithScopedProvider";
import StyledTooltip from "../../../StyledFeeTooltip";
import { InfoOutlined } from "@mui/icons-material";

const DenGridPageItem: React.FC<{ denManager: DenManagerDetails; urlRedirect: string; selected?: boolean; disabled?: boolean; theme: Theme }> = ({
  denManager,
  urlRedirect,
  disabled,
  theme,
}) => {
  const { densDetails, dispatcher, vaultApy, interestRates, criticalHealthFactor, averageHealthFactor, minHealthFactor, collateralRatio, debt, maxDebt } =
    useDenManagerSelectorStore(denManager.contractAddress, urlRedirect);
  const isLgDown = useMediaQuery(theme.breakpoints.down("lg"));
  const isMdDown = useMediaQuery(theme.breakpoints.down("md"));
  return (
    <Button
      onClick={() => {
        dispatcher({ type: "changeDenManager", payload: denManager.contractAddress });
      }}
      disabled={disabled}
      variant="contained"
      size="small"
      sx={{
        backdropFilter: {
          xs: "none", // No blur on mobile
          md: "blur(5px)", // Apply blur for medium and larger screens
        },
        transition: "0.1s ease-in-out",
        boxSizing: "border-box",
        flex: isLgDown ? "1 0 calc(25% - 16px)" : "1 0 calc(30% - 16px)",
        minWidth: isMdDown ? "100%" : "200px",
        maxWidth: "calc(25% - 16px)",
        gap: "16px",
        overflow: "hidden",
        display: "flex",
        justifyContent: "flex-start",
        flexDirection: "column",
        padding: "0px",
        "@media (min-width:600px)": {
          padding: "0",
        },
        backgroundColor: theme.palette.background.paper,
        border: "2px solid var(--border-dark)",
        "&:hover": {
          border: "2px solid var(--border-dark)",
          backgroundColor: theme.palette.common.black,
          opacity: 0.9,
        },
        "&.Mui-disabled": {
          border: "2px solid var(--border-dark)",
          backgroundColor: "var(--border-dark)",
        },
      }}
    >
      <Box
        display="flex"
        width="100%"
        flexDirection="column"
        sx={{
          boxSizing: "border-box",
          padding: "12px",
          "@media (min-width:600px)": {
            padding: "12px",
          },
        }}
      >
        <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
          <TokenIcon contractAddress={denManager.collateralAddress} vaulted={!!denManager.vaultAddress} vaultSize="55%" height="50px" width="50px" />
          <Typography variant="h3" fontWeight={800} color="var(--text-primary)" textAlign="center">
            {denManager.collateralTicker}
          </Typography>

          <Chip
            variant="filled"
            label={`${formatBigIntPercentage(vaultApy, 2)}% APY`}
            size="small"
            sx={{
              opacity: vaultApy ? 1 : 0,
              fontWeight: 600,
              fontSize: 12,
              height: "25px",
              color: "var(--text-success)",
              backgroundColor: "var(--chip-success-bg)",
            }}
          />
        </Box>
        {/* Left Side: Token Icon and Text */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            width: "100%",
            flexDirection: "column",
            gap: "8px",
            padding: "8px",
            "&.Mui-disabled": {
              borderRadius: "12px",
              backgroundColor: theme.palette.background.paper, // Ensure disabled bg is same as normal
              color: theme.palette.text.primary, // Keep text color normal
              opacity: 0.5, // Prevent MUI from reducing opacity
            },
          }}
        >
          <Typography width="100%" display="flex" justifyContent="space-between">
            <Typography component="span" color="var(--text-secondary)">
              TVL:
            </Typography>{" "}
            <Typography component="span" color="var(--text-primary)">
              ${formatTokenWithMillionsOrBillions((densDetails.densTotal.collateral * densDetails.price) / SCALING_FACTOR, denManager.vaultDecimals, 3)}
            </Typography>
          </Typography>
          <Typography width="100%" display="flex" justifyContent="space-between">
            <Typography component="span" color="var(--text-secondary)">
              NECT Minted:
            </Typography>{" "}
            <Typography component="span" color="var(--text-primary)">
              {`${formatTokenWithMillionsOrBillions(debt, denManager.vaultDecimals, 2)}`} / {`${formatTokenWithMillionsOrBillions(maxDebt, denManager.vaultDecimals, 2)}`}
            </Typography>
          </Typography>
          <Typography width="100%" display="flex" justifyContent="space-between">
            <Typography component="span" color="var(--text-secondary)">
              Interest Rate:
            </Typography>{" "}
            <Typography component="span" color="var(--text-primary)">
              {interestRates !== undefined ? `${formatBigIntPercentage(BigInt(interestRates), 2, true, true, 4)}%` : "-"}
            </Typography>
          </Typography>
        </Box>

        <Box display="flex" gap="4px" justifyContent="space-between" mt="8px">
          <StyledTooltip
            title={
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="h6" fontWeight={700} color="var(--text-primary)">
                  Minimum Collateral Ratio
                </Typography>
                It is the collateral ratio by which you can have the greatest capital efficiency, but you risk redemptions and sudden collateral price volatility can trigger
                liquidations against your Den.
              </Box>
            }
          >
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              gap="2px"
              sx={{ backgroundColor: "var(--border-dark)", height: "19px", padding: "0 4px", borderRadius: "6px", width: "32%" }}
            >
              <InfoOutlined sx={{ fontSize: "inherit", height: "19px", lineHeight: "inherit", color: "var(--text-secondary)" }} />
              <Typography component={"span"} color={"var(--text-secondary)"} fontSize="9px">
                {`MCR: ${formatBigIntPercentage(minHealthFactor, 0)}%`}
              </Typography>
            </Box>
          </StyledTooltip>

          <StyledTooltip
            title={
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="h6" fontWeight={700} color="var(--text-primary)">
                  Critical Collateral Ratio
                </Typography>
                The system enters Recovery Mode when this value is greater than or equal to the TCR.
              </Box>
            }
          >
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              gap="2px"
              sx={{ backgroundColor: "var(--border-dark)", height: "19px", padding: "0 4px", borderRadius: "6px", width: "32%" }}
            >
              <InfoOutlined sx={{ fontSize: "inherit", height: "19px", lineHeight: "inherit", color: "var(--text-secondary)" }} />
              <Typography component={"span"} color={"var(--text-secondary)"} fontSize="9px">
                {`CCR: ${formatBigIntPercentage(criticalHealthFactor, 0)}%`}
              </Typography>
            </Box>
          </StyledTooltip>

          <StyledTooltip
            title={
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="h6" fontWeight={700} color="var(--text-primary)">
                  Total Collateral Ratio
                </Typography>
                Refers to the average collateral Ratio of the total value of all collaterals in the Den Manager, at their present prices, to the total outstanding debt in the Den
                Manager.
              </Box>
            }
          >
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              gap="2px"
              sx={{ backgroundColor: "var(--border-dark)", height: "19px", padding: "0 4px", borderRadius: "6px", width: "32%" }}
            >
              <InfoOutlined sx={{ fontSize: "inherit", height: "19px", lineHeight: "inherit", color: "var(--text-secondary)" }} />
              <Typography component={"span"} color={"var(--text-secondary)"} fontSize="9px">
                {`TCR: ${formatBigIntPercentage(averageHealthFactor, 0)}%`}
              </Typography>
            </Box>
          </StyledTooltip>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            width: "100%",
            padding: "8px",
            borderRadius: "12px",
            backgroundColor: "var(--background-tertiary)",
            border: "2px solid var(--border-dark)",
          }}
          mt="8px"
        >
          <Typography width="100%" display="flex" justifyContent="space-between">
            <Typography component="span" color="var(--text-secondary)">
              My Collateral Ratio &nbsp;:
            </Typography>{" "}
            <Typography component="span" color="var(--text-primary)">
              {collateralRatio ? `${formatBigIntPercentage(collateralRatio, 2, true, true)}%` : "-"}
            </Typography>
          </Typography>
        </Box>
        <Box width={"100%"} py={1}>
          <Button variant={collateralRatio ? "outlined" : "contained"} fullWidth>
            {collateralRatio ? "Manage" : "Borrow"}
          </Button>
        </Box>
      </Box>
    </Button>
  );
};
export default WithScopedProvider(DenGridPageItem, getDenManagerSelectorScopedAtoms());
