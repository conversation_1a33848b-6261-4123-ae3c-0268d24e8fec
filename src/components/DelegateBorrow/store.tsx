import { useAtomValue } from "jotai";
import { getCollateralDetailsAtom } from "../../Atoms/Tokens";
import { getDenManagerCollateralPrice, denBorrowAmountsAtom, protocolAtom } from "../../Atoms/Den";
import { useTransaction } from "../../Hooks/useTransaction";
import { useEffect, useRef } from "react";
import { useNotifications } from "../../Hooks/useNotifications";
import { BERABORROW_ADDRESSES } from "../../utils/constants";

export const useDelegateBorrowStore = (onConfirmation?: () => void) => {
  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const formAmounts = useAtomValue(denBorrowAmountsAtom);
  const protocol = useAtomValue(protocolAtom);
  const hasSubmittedRef = useRef(false);
  const { removeModal } = useNotifications();
  const delegateTerm = formAmounts.leverage ? "Leverage" : "Vault";
  const {
    submit,
    isConfirming,
    isPending,
    isIdle,
    isConfirmed,
    error: errorContract,
  } = useTransaction(
    {
      type: "approveDelegate",
      variables: [formAmounts.leverage ? BERABORROW_ADDRESSES.leverageRouter : BERABORROW_ADDRESSES.protocols[protocol].collVaultRouter],
    },
    true
  );
  const error = errorContract;
  error && console.error(error);

  useEffect(() => {
    if (!hasSubmittedRef.current) {
      submit();
      hasSubmittedRef.current = true;
    }
  }, []);
  useEffect(() => {
    if (errorContract || isConfirmed) {
      isConfirmed && onConfirmation?.();
      removeModal("delegateBorrow");
    }
  }, [errorContract || isConfirmed]);
  return {
    collateral: { price: collateralPrice, ...collateralDetails },
    isConfirming,
    isPending,
    isIdle,
    error,
    delegateTerm,
  };
};

export const getDelegateBorrowScopedAtoms = () => [];
