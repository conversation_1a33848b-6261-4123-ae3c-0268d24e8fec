import { Box, CircularProgress, Divider, Grid, IconButton, Typography, useTheme } from "@mui/material";
import { useDelegateBorrowStore } from "./store";
import { Done, SwapHorizontalCircleSharp } from "@mui/icons-material";
import { Den } from "@Beraborrowofficial/sdk";
import { formatToken } from "../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../utils/constants";

const DelegateBorrow: React.FC<{ formAmounts: Den; onConfirmation?: () => void }> = ({ formAmounts, onConfirmation }) => {
  const { collateral, isConfirming, isPending, delegateTerm } = useDelegateBorrowStore(onConfirmation);
  const theme = useTheme();
  return (
    <Box
      sx={{
        [theme.breakpoints.up("lg")]: {
          padding: "32px 64px",
        },
      }}
    >
      <Grid container alignItems="center" justifyContent="space-evenly" pb={3}>
        <Grid item xs={12}>
          <Typography variant="h1" style={{ color: "#f0f0f0" }}>
            Delegate {delegateTerm}
          </Typography>
        </Grid>
        <Grid item xs={12} md={5}>
          <Typography variant="h6" style={{ color: "#f0f0f0" }}>
            Collateral
          </Typography>
          <Typography variant="h6" style={{ color: "#f0f0f0" }}>
            {formatToken(formAmounts.collateral, collateral.decimals, 8, collateral.ticker)}
          </Typography>
        </Grid>
        <Grid item xs={12} md={2}>
          <IconButton>
            <SwapHorizontalCircleSharp style={{ color: "#f0f0f0" }} />
          </IconButton>
        </Grid>
        <Grid item xs={12} md={5}>
          <Typography variant="h6" style={{ color: "#f0f0f0" }}>
            Debt
          </Typography>
          <Typography variant="h6" style={{ color: "#f0f0f0" }}>
            {formatToken(formAmounts.debt, BERABORROW_ADDRESSES.debtToken.decimals, 8, BERABORROW_ADDRESSES.debtToken.ticker)}
          </Typography>
        </Grid>
      </Grid>

      <Divider sx={{ mb: 2, backgroundColor: `var(--text-primary)` }} />

      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>
          Approve {collateral.ticker} {delegateTerm} Delegation
        </Typography>
        {isPending ? <CircularProgress size={16} color="inherit" /> : <Done color="success" />}
      </Box>
      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>Signing Message</Typography>
        {isConfirming ? <CircularProgress size={16} color="inherit" /> : isPending ? <></> : <Done color="success" />}
      </Box>
    </Box>
  );
};
export default DelegateBorrow;
