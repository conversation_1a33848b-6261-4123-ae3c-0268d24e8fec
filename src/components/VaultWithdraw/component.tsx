import React from "react";
import { useVaultWithdrawStore } from "./store";
import { Box, Divider, Grid, Typography, useTheme } from "@mui/material";
import { formatToken, formatBigIntPercentage } from "../../utils/helpers";
import TransactionButton from "../Transaction/Transaction";
import WithSuspense from "../../providers/WithSuspense";
import { SCALING_FACTOR_BP_DECIMALS } from "@Beraborrowofficial/sdk";
import StyledTextfield from "../StyledTextField";
import { AmountSelector } from "../AmountSelector";
import EmissionsCountDown from "../EmissionsCountDown/Component";
import StyledTooltip from "../StyledFeeTooltip";
import { InfoOutlined } from "@mui/icons-material";
import VaultComposition from "../VaultComposition/Component";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import TokenIcon from "../TokenIcons";

const Component: React.FC = () => {
  const theme = useTheme();
  const { validationError, formValue, withdrawAmount, maxAmount, dispatcher, withdrawFee, fees, vaultDetails, vaultAddr, currentDeposit, collPrice } = useVaultWithdrawStore();

  return (
    <>
      <Grid container pb={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1" mb={0.5}>
            Burn {vaultDetails.ticker.toUpperCase()}
          </Typography>
          <StyledTextfield
            value={formValue}
            error={validationError?.type === "insufficient_balance"}
            onChange={(e) => dispatcher({ type: "setVaultWithdraw", payload: e.target.value })}
            startIcons={<TokenIcon contractAddress={BERABORROW_ADDRESSES.vaults[vaultAddr].collateral} height="35px" width="35px" />}
            endComponent={
              <Box display="flex" position="absolute" right="8px" bottom="8px">
                <AmountSelector handleItemClick={(percent) => dispatcher({ type: "setVaultWithdrawPercentage", payload: percent })} mt={1} ml={"auto"} />
              </Box>
            }
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="caption" fontSize={12}>
                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                    Deposited:
                  </Typography>{" "}
                  {formatToken(maxAmount, vaultDetails.decimals, 2)}
                </Typography>
              </Box>
            }
          />
        </Grid>
      </Grid>
      {!!currentDeposit.totalSupply && (
        <>
          <Box display={"flex"} justifyContent={"space-between"}>
            <Typography variant="subtitle1" mb={0.5}>
              Composition Withdrawn{" "}
            </Typography>
            <StyledTooltip placement={"right"} title={"Fee charged on withdrawal from Vault"}>
              <Typography variant="body2" fontSize={12} display="flex" alignItems="center">
                <Typography variant="body2" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center">
                  <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle" }} />
                  Fee:
                </Typography>
                &nbsp; ${formatToken(fees, vaultDetails.decimals, 2)}
                {" (" + formatBigIntPercentage(BigInt(withdrawFee), 2, undefined, undefined, SCALING_FACTOR_BP_DECIMALS) + "%) "}
              </Typography>
            </StyledTooltip>
          </Box>
          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <VaultComposition denManagerAddress={vaultAddr} vaultAddress={vaultAddr} shares={withdrawAmount} collPrice={collPrice} />
            <Box sx={{ position: "relative" }}></Box>
          </Box>
        </>
      )}
      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />

      <Box>
        <TransactionButton
          disabled={!!validationError || !withdrawAmount}
          transaction={{
            type: "redeemVault",
            variables: [withdrawAmount],
          }}
          onConfirmation={() => dispatcher({ type: "confirmedVaultWithdraw" })}
          validationError={validationError}
        >
          Withdraw
        </TransactionButton>
      </Box>

      <EmissionsCountDown />
    </>
  );
};

const VaultWithdraw = WithSuspense(Component, "paper");
export default VaultWithdraw;
