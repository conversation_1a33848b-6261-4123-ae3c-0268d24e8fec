import { Hex } from "viem";

export type VaultWithdrawDispatcherActionType = ChangeDeposit | ConfirmedVaultWithdraw | ChangeWithdrawPercent | SetSelectedAsset | EnableZap | ApproveLsp;

type ChangeDeposit = {
  type: "setVaultWithdraw";
  payload: string;
};
type ConfirmedVaultWithdraw = {
  type: "confirmedVaultWithdraw";
};
type ChangeWithdrawPercent = {
  type: "setVaultWithdrawPercentage";
  payload: number;
};
type SetSelectedAsset = {
  type: "setSelectedAsset";
  payload: Hex | undefined;
};
type EnableZap = {
  type: "enableZap";
};
type ApproveLsp = {
  type: "approveLsp";
};
