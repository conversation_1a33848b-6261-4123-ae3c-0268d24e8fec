import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { formatDecimal, regexDecimalToken, formatBigIntPercentage } from "../../utils/helpers";
import { useHydrateAtoms } from "jotai/utils";
import { SCALING_FACTOR, SCALING_FACTOR_BP, SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import { useLocation } from "react-router-dom";
import { updatePostHogEventAtom, resetPostHogEventAtom, postHogEventAtom } from "../../Atoms/Account";
import { VaultWithdrawDispatcherActionType } from "./types";
import {
  getVaultDepositAtom,
  getVaultApyAtom,
  vaultDetailsAtom,
  vaultWithdrawAmountAtom,
  formValuesVaultWithdrawAtom,
  formValidationsVaultWithdrawAtom,
  _vaultAddrAtom,
  getInfraredTokenPriceAtom,
} from "../../Atoms/Vault";
import { useEffect } from "react";
import { QUERY_CLIENT } from "../../utils/constants";
import { getCollateralDetailsAtom } from "../../Atoms/Tokens";

export const useVaultWithdrawStore = () => {
  const location = useLocation();
  const { data: currentDeposit } = useAtomValue(getVaultDepositAtom);
  const { data: collPrice } = useAtomValue(getInfraredTokenPriceAtom);
  const { data: apy } = useAtomValue(getVaultApyAtom);
  const withdrawAmount = useAtomValue(vaultWithdrawAmountAtom);
  const vaultDetails = useAtomValue(vaultDetailsAtom);
  const [formValue, setFormValue] = useAtom(formValuesVaultWithdrawAtom);
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const resetPostHogEvent = useSetAtom(resetPostHogEventAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const vaultAddr = useAtomValue(_vaultAddrAtom);
  const validationError = useAtomValue(formValidationsVaultWithdrawAtom);
  const { withdrawFee } = currentDeposit;
  const fees = (withdrawAmount * BigInt(withdrawFee)) / SCALING_FACTOR_BP;
  const vaultTvl = (currentDeposit.totalSupply * currentDeposit.price) / SCALING_FACTOR;
  const maxAmount = currentDeposit.shares;
  const myAssets = (withdrawAmount * currentDeposit.shareToAssetRatio) / SCALING_FACTOR;
  useHydrateAtoms([
    [formValuesVaultWithdrawAtom, formatDecimal(maxAmount, vaultDetails.decimals, 0)],
    [
      postHogEventAtom,
      {
        type: "vaultWithdraw",
        page_rel_path: location.pathname,
        token_deposited: vaultDetails.ticker,
        qty_deposited_token: formatDecimal(maxAmount, vaultDetails.decimals),
        current_apy: formatBigIntPercentage(apy),
        current_shares: formatDecimal(currentDeposit.shares, vaultDetails.decimals),
        current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
      },
    ],
  ]);

  useEffect(() => {
    setFormValue(maxAmount ? formatDecimal(maxAmount, vaultDetails.decimals) : "0");
  }, [location.pathname]);
  const dispatcher = (action: VaultWithdrawDispatcherActionType) => {
    switch (action.type) {
      case "setVaultWithdraw":
        updatePostHogEvent({
          token_withdrew: vaultDetails.ticker,
          qty_withdrew_token: regexDecimalToken(action.payload, vaultDetails.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, vaultDetails.decimals),
          current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
        });
        setFormValue(regexDecimalToken(action.payload, vaultDetails.decimals));
        break;
      case "confirmedVaultWithdraw":
        resetPostHogEvent({ type: "poolWithdraw", page_rel_path: location.pathname });
        QUERY_CLIENT.invalidateQueries().then(() => {
          setFormValue("0");
        });
        break;
      case "setVaultWithdrawPercentage":
        const value = formatDecimal((currentDeposit.shares * BigInt(action.payload)) / 100n, vaultDetails.decimals, action.payload === 100 ? undefined : 8);
        updatePostHogEvent({
          last_clicked_input_btn: action.payload + "%",
          token_withdrew: vaultDetails.ticker,
          qty_withdrew_token: regexDecimalToken(value, vaultDetails.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, vaultDetails.decimals),
          current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
        });
        setFormValue(regexDecimalToken(value, vaultDetails.decimals));
        break;

      default:
        break;
    }
  };
  return {
    currentDeposit,
    validationError,
    formValue,
    dispatcher,
    withdrawFee,
    withdrawAmount,
    fees,
    myAssets,
    maxAmount,
    vaultDetails,
    vaultAddr,
    collateralDetails,
    collPrice,
  };
};
export const getVaultWithdrawScopedAtoms = () => [formValidationsVaultWithdrawAtom, formValuesVaultWithdrawAtom, vaultWithdrawAmountAtom];
