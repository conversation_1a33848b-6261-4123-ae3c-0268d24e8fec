import { Box, Button, Typography } from "@mui/material";
import { useNotifications } from "../Hooks/useNotifications";
import { useNavigate } from "react-router-dom";

const RedemptionModal = () => {
  const navigate = useNavigate();
  const { removeModal } = useNotifications();
  return (
    <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center" }}>
      <Typography variant="h5" pb={2}>
        Redemptions form a pivotal aspect of Beraborrow's operations, allowing users to convert $NECT into Collateral at an exact dollar equivalent value.&nbsp;
        <a href="https://beraborrow.gitbook.io/docs/nect-stablecoin/redemptions" target="_blank">
          Learn More
        </a>
      </Typography>
      <Typography variant="h5" pb={2}>
        This process is separate from your Den (borrow) position. When you redeem, you're acting against another user's Den position, <b>not closing your own.</b> <br /> <br />
        To close your own position, please use the "Close Position" button in the Borrow section.
      </Typography>

      <Box
        sx={{
          display: "flex",
          flexDirection: {
            xs: "column", // Stack buttons vertically on extra small screens
            sm: "row", // Align buttons side-by-side on small screens and above
          },
          justifyContent: {
            sm: "center", // Apply space between on small screens and above
          },
          gap: 2, // Space between buttons
          width: "100%",
        }}
      >
        <Button
          variant="contained"
          onClick={() => {
            navigate("/den/borrow");
            removeModal("redemption-warning");
          }}
        >
          Close my position
        </Button>
        <Button
          variant="outlined"
          onClick={() => {
            removeModal("redemption-warning");
          }}
        >
          Dismiss
        </Button>
      </Box>
    </Box>
  );
};
export default RedemptionModal;
