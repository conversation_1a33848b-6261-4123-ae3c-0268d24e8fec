import { useAtomValue } from "jotai";
import React from "react";
import { profileNFTAtom } from "../Atoms/Account";
import { Box } from "@mui/material";

type Props = {
  width?: number;
  height?: number;
  border?: string;
  badge?: string;
};
export const Avatar: React.FC<Props> = ({ width, height, border, badge }) => {
  const profileAsset = useAtomValue(profileNFTAtom);
  const { type, url } = profileAsset;
  return (
    <Box
      sx={{
        position: "relative",
        width: width,
        height: height,
      }}
    >
      {/* Border Box */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          width: width,
          height: height,
        }}
      />

      {/* Image and Badge */}
      <Box
        sx={{
          position: "relative",
          zIndex: 10,
          borderRadius: "50%",
          width: width,
          height: height,
        }}
      >
        {badge && (
          <Box
            width={"40%"}
            height={"40%"}
            sx={{
              zIndex: 20,
              position: "absolute",
              right: "-3px", // Adjust for half the border width
              top: "-2px", // Adjust for half the border width
            }}
          >
            <img src={badge} width="100%" height="100%" style={{ borderRadius: "50%" }} />
          </Box>
        )}

        {type == "Video" ? (
          <video controls width="100%" height="100%" style={{ borderRadius: "50%" }} autoPlay loop muted poster="/imgs/wallet-avatar.png">
            <source src={url} />
            <img src={"/imgs/wallet-avatar.png"} width="100%" height="100%" alt="wallet-avatar" style={{ borderRadius: "50%", border: border, boxSizing: "border-box" }} />
          </video>
        ) : (
          <img src={url || "/imgs/wallet-avatar.png"} width="100%" height="100%" alt="wallet-avatar" style={{ borderRadius: "50%", border: border, boxSizing: "border-box" }} />
        )}
      </Box>
    </Box>
  );
};
