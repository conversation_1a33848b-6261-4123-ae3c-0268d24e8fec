import { useAtomValue } from "jotai";
import { getVaultApyAtom } from "../../Atoms/Vault";
import { _denManagerAddrAtom, denManagerAtom } from "../../Atoms/Den";
import { Hex } from "viem";
import { queryClient<PERSON>tom } from "jotai-tanstack-query";
import { useHydrateAtoms } from "jotai/utils";
import { useQueryClient } from "@tanstack/react-query";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
export const useVaultApyStore = (address: Hex) => {
  const queryClient = useQueryClient();
  useHydrateAtoms([
    [queryClientAtom, queryClient],
    [denManagerAtom, address],
  ]);
  const { data: vaultApy } = useAtomValue(getVaultApyAtom);
  const vaultDetails = BERABORROW_ADDRESSES.denManagers[address].vault && BERABORROW_ADDRESSES.vaults[BERABORROW_ADDRESSES.denManagers[address].vault];
  return { vaultApy, vaultDetails };
};
export const getVaultApyScopedAtoms = () => [getVaultApyAtom, _denManagerAddrAtom, denManagerAtom, queryClientAtom];
