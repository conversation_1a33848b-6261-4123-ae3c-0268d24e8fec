import { Chip } from "@mui/material";
import withScopedProvider from "../../providers/WithScopedProvider";
import WithSuspense from "../../providers/WithSuspense";
import { useVaultApyStore, getVaultApyScopedAtoms } from "./store";
import { formatBigIntPercentage } from "../../utils/helpers";
import { Hex } from "viem";
import StyledTooltip from "../StyledFeeTooltip";
import { InfoOutlined } from "@mui/icons-material";

const Component: React.FC<{ address: Hex }> = ({ address }) => {
  const { vaultApy, vaultDetails } = useVaultApyStore(address);
  return (
    <>
      {!!vaultApy ? (
        <StyledTooltip title={` Auto-compounding ${vaultDetails?.ticker} vault`}>
          <Chip
            icon={<InfoOutlined />}
            variant="outlined"
            label={formatBigIntPercentage(vaultApy, 2) + "% APY"}
            color={"success"}
            size="small"
            sx={{ fontWeight: 600, fontSize: 10, opacity: 0.75, ml: 1 }}
          />
        </StyledTooltip>
      ) : (
        <></>
      )}
    </>
  );
};
const VaultApy = WithSuspense(withScopedProvider(Component, getVaultApyScopedAtoms()));
export default VaultApy;
