import { Accordion, AccordionDetails, AccordionSummary, Box, Divider, Typography, useTheme } from "@mui/material";
import { formatBigIntPercentage, formatToken } from "../utils/helpers";
import { ExpandMore } from "@mui/icons-material";
import WithSuspense from "../providers/WithSuspense";
import { Den } from "@Beraborrowofficial/sdk";
import { CollateralDetails } from "../@type/Token";

type RedeemFeeBreakdownProps = {
  stayOpen?: boolean;
  formAmounts: Den;
  collateral: CollateralDetails & { price: bigint };
  redemptionFeeRate: bigint;
  redemptionFee: bigint;
};
const RedeemFeeBreakdown: React.FC<RedeemFeeBreakdownProps> = ({ stayOpen, formAmounts, collateral, redemptionFeeRate, redemptionFee }) => {
  const theme = useTheme();

  return (
    <>
      <Accordion
        defaultExpanded
        expanded={stayOpen === true ? true : undefined}
        sx={{
          padding: "16px",
          borderRadius: "16px",
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <AccordionSummary
          expandIcon={stayOpen === true ? undefined : <ExpandMore />}
          sx={
            stayOpen === true
              ? {
                  cursor: "default", // Set cursor to default
                  "& .MuiAccordionSummary-content": {
                    cursor: "default", // Ensure the content also has the default cursor
                  },
                  "& .MuiAccordionSummary-expandIconWrapper": {
                    cursor: "default", // Keep the pointer cursor on the expand icon if needed
                  },
                }
              : {}
          }
        >
          <Typography variant="h4" textAlign={"left"}>
            Fee Breakdown
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box display="flex" justifyContent="space-between" mb={1}>
            <Typography variant="subtitle2" color={"var(--text-secondary)"}>
              Collateral
            </Typography>
            <Typography variant="subtitle2">
              <strong>{formatToken(formAmounts.collateral, collateral.vaultDecimals, 2, collateral.ticker)} </strong>
            </Typography>
          </Box>
          <Box display="flex" justifyContent="space-between" mb={1}>
            <Typography variant="subtitle2" color={"var(--text-secondary)"}>
              Redemption fee <strong>({formatBigIntPercentage(redemptionFeeRate, 3)}%)</strong>
            </Typography>
            <Typography variant="subtitle2">
              <strong style={{ whiteSpace: "nowrap" }}>{formatToken(redemptionFee, collateral.vaultDecimals, 5, collateral.ticker)} </strong>
            </Typography>
          </Box>
          <Divider sx={{ mb: 2, backgroundColor: `var(--text-primary)` }} />
          <Box display="flex" justifyContent="space-between">
            <Typography variant="subtitle2" color={"var(--text-secondary)"}>
              Net Collateral
            </Typography>
            <Typography variant="subtitle2">
              <strong>{formatToken(formAmounts.collateral - redemptionFee, collateral.vaultDecimals, 6, collateral.ticker)} </strong>
            </Typography>
          </Box>
        </AccordionDetails>
      </Accordion>
    </>
  );
};
export default WithSuspense(RedeemFeeBreakdown);
