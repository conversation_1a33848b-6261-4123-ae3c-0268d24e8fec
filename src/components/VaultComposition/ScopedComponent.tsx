import { useQueryClient } from "@tanstack/react-query";
import withScopedProvider from "../../providers/WithScopedProvider";
import VaultComposition from "./Component";
import { getVaultCompositionScopedAtoms } from "./store";
import { _vaultAddrAtom, vaultAtom } from "../../Atoms/Vault";
import { _denManagerAddrAtom, denManagerAtom } from "../../Atoms/Den";
import { Hex } from "viem";
import { queryClientAtom } from "jotai-tanstack-query";
import { useHydrateAtoms } from "jotai/utils";
import { collateralTypeSelectorAtom } from "../../Atoms/Tokens";
import { accountAtom } from "../../Atoms/Account";
import { useAccount } from "wagmi";
import { ANON_ADDRESS } from "../../utils/constants";

const Component: React.FC<{ denManagerAddress: Hex; vaultAddress?: Hex | undefined; shares: bigint; collPrice: bigint; removeWithdrawFee: number }> = ({
  removeWithdrawFee,
  denManagerAddress,
  shares,
  vaultAddress,
  collPrice,
}) => {
  const queryClient = useQueryClient();
  const { address } = useAccount();
  useHydrateAtoms([
    [queryClientAtom, queryClient],
    [vaultAtom, vaultAddress],
    [denManagerAtom, denManagerAddress],
    [accountAtom, address ?? ANON_ADDRESS],
    [vaultAtom, vaultAddress],
    [collateralTypeSelectorAtom, vaultAddress ? "vault" : "den"],
  ]);
  return (
    <>
      <VaultComposition denManagerAddress={denManagerAddress} vaultAddress={vaultAddress} shares={shares} collPrice={collPrice} removeWithdrawFee={removeWithdrawFee} />
    </>
  );
};

const VaultCompositionScoped = withScopedProvider(Component, getVaultCompositionScopedAtoms());
export default VaultCompositionScoped;
