import { useAtomValue } from "jotai";
import {
  _vaultAddr<PERSON>tom,
  previewRedeemUnderlyingAtom,
  vaultAtom,
  getVaultDepositAtom,
  getInfraredVaultPriceAtom,
  vaultDetailsAtom,
  getVaultApyAtom,
  getVaultDepositedAmountAtom,
  getVaultsDetailsAtom,
  getVaultsDetailsNoWaitAtom,
  getInfraredPricingAtom,
  getInfraredTokenPriceAtom,
} from "../../Atoms/Vault";
import { _denManagerAddrAtom, denManagerAtom } from "../../Atoms/Den";
import { Hex } from "viem";
import { queryClientAtom } from "jotai-tanstack-query";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { SCALING_FACTOR, SCALING_FACTOR_BP } from "@Beraborrowofficial/sdk";
import { accountAtom } from "../../Atoms/Account";
import { collateralTypeSelector<PERSON>tom, getCollateralDetailsAtom } from "../../Atoms/Tokens";
export const useVaultCompositionStore = (DenManagerAddress: Hex, removeWithdrawFee = 0, vaultAddr?: Hex) => {
  const { data: underlyingAssets } = useAtomValue(previewRedeemUnderlyingAtom);
  const { data: currentDeposit } = useAtomValue(getVaultDepositAtom);
  const deposited = useAtomValue(getVaultDepositedAmountAtom);

  const vaultDetails = vaultAddr ? BERABORROW_ADDRESSES.vaults[vaultAddr] : BERABORROW_ADDRESSES.vaults[BERABORROW_ADDRESSES.denManagers[DenManagerAddress].vault];
  const withdrawFeeFraction = (BigInt(removeWithdrawFee) * SCALING_FACTOR) / SCALING_FACTOR_BP;
  const ratio = SCALING_FACTOR - withdrawFeeFraction;
  return { underlyingAssets, vaultDetails, ratio, currentDeposit, deposited };
};
export const getVaultCompositionScopedAtoms = () => [
  getInfraredVaultPriceAtom,
  getVaultDepositedAmountAtom,
  getCollateralDetailsAtom,
  vaultDetailsAtom,
  accountAtom,
  getVaultApyAtom,
  previewRedeemUnderlyingAtom,
  getVaultDepositAtom,
  _vaultAddrAtom,
  vaultAtom,
  collateralTypeSelectorAtom,
  denManagerAtom,
  _denManagerAddrAtom,
  queryClientAtom,
  previewRedeemUnderlyingAtom,
  getVaultDepositAtom,
  _denManagerAddrAtom,
  _vaultAddrAtom,
  vaultAtom,
  denManagerAtom,
  queryClientAtom,
  getVaultsDetailsAtom,
  getVaultsDetailsNoWaitAtom,
  getInfraredPricingAtom,
  getInfraredTokenPriceAtom,
];
