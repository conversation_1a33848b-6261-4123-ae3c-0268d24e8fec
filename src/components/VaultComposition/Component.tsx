import { Box, Typography } from "@mui/material";
import { useVaultCompositionStore } from "./store";
import { Hex, isAddressEqual } from "viem";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { formatToken } from "../../utils/helpers";
import TokenIcon from "../TokenIcons";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
// import StyledTooltip from "../StyledFeeTooltip";
// import { InfoOutlined } from "@mui/icons-material";

const Component: React.FC<{ denManagerAddress: Hex; vaultAddress?: Hex | undefined; shares?: bigint; collPrice: bigint; removeWithdrawFee?: number }> = ({
  denManagerAddress,
  shares,
  vaultAddress,
  collPrice,
  removeWithdrawFee,
}) => {
  const { underlyingAssets, vaultDetails, ratio, currentDeposit } = useVaultCompositionStore(denManagerAddress, removeWithdrawFee, vaultAddress);
  const vault = underlyingAssets.find((el) => isAddressEqual(el.contractAddress, vaultDetails.collateral));
  const vaultToken = BERABORROW_ADDRESSES.collateralTokens[vaultDetails.collateral];
  return (
    <>
      {!!vaultDetails ? (
        <>
          {vault && (
            <Box key={vault.contractAddress}>
              {vault && !!vault.balance && (
                <Box key={vault.contractAddress} display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                  <Box>
                    <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600}>
                      <TokenIcon contractAddress={vault.contractAddress} height={"25px"} width={"25px"} />{" "}
                      <Box display="flex" flexDirection="column">
                        <Typography fontSize={14} pl={1}>
                          {formatToken(((shares ?? currentDeposit.shares) * vault.balance) / ratio, vaultToken.decimals, 2, vaultToken.ticker)}
                        </Typography>
                        {/* {!!deposited && (
                          <StyledTooltip
                            title={
                              <Box>
                                <Typography color={"var(--text-primary)"} variant="subtitle2" fontWeight={800}>
                                  What is this?
                                </Typography>
                                TODO
                              </Box>
                            }
                          >
                            <Typography pl={1} fontSize={12} variant="h5" component="span" display="flex" alignItems="center" fontWeight={600} color="var(--text-success)">
                              + {formatToken(((shares ?? currentDeposit.shares) * vault.balance) / ratio - deposited, vaultToken.decimals, 2)}
                              <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5, color: "var(--text-success)" }} />
                            </Typography>
                          </StyledTooltip>
                        )} */}
                      </Box>
                    </Typography>
                  </Box>
                  <Box display="flex" flexDirection="column" alignItems="flex-end">
                    <Typography fontSize={14} variant="h5" fontWeight={600} color="var(--text-primary)">
                      $
                      {formatToken(
                        ((((shares ?? currentDeposit.shares) * vault.balance) / ratio) * (vault.price || (collPrice as bigint))) / SCALING_FACTOR,
                        vaultToken.decimals,
                        2
                      )}
                    </Typography>
                    {/* <StyledTooltip
                      title={
                        <Box>
                          <Typography color={"var(--text-primary)"} variant="subtitle2" fontWeight={800}>
                            What is this?
                          </Typography>
                          TODO
                        </Box>
                      }
                    >
                      <Typography pl={1} fontSize={12} variant="h5" component="span" display="flex" alignItems="center" fontWeight={600} color="var(--text-success)">
                        + {formatToken(((((shares ?? currentDeposit.shares) * vault.balance) / ratio) * (vault.price as bigint)) / SCALING_FACTOR, vaultToken.decimals, 2)}
                        <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5, color: "var(--text-success)" }} />
                      </Typography>
                    </StyledTooltip> */}
                  </Box>
                </Box>
              )}
            </Box>
          )}
          {underlyingAssets
            .filter((el) => !isAddressEqual(el.contractAddress, vaultDetails.collateral))
            .map((item) => {
              const token =
                BERABORROW_ADDRESSES.vaults[item.contractAddress] ??
                BERABORROW_ADDRESSES.collateralTokens[item.contractAddress] ??
                (item.contractAddress === BERABORROW_ADDRESSES.debtToken.contractAddress
                  ? BERABORROW_ADDRESSES.debtToken
                  : item.contractAddress === BERABORROW_ADDRESSES.pollenToken.contractAddress
                    ? BERABORROW_ADDRESSES.pollenToken
                    : undefined);
              return (
                <Box key={item.contractAddress}>
                  {token && !!item.balance && (
                    <Box key={item.contractAddress} display="flex" justifyContent="space-between" alignItems="center">
                      <Box>
                        <Typography fontSize={12} variant="h5" component="span" display="flex" alignItems="center" fontWeight={600} color="var(--text-secondary)">
                          <TokenIcon contractAddress={item.contractAddress} height={"25px"} width={"25px"} />{" "}
                          <Box pl={1}>{formatToken(((shares ?? currentDeposit.shares) * item.balance) / ratio, token.decimals, 2, token.ticker)}</Box>
                        </Typography>
                      </Box>
                      {!!item.price && (
                        <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                          ${formatToken(((((shares ?? currentDeposit.shares) * item.balance) / ratio) * item.price) / SCALING_FACTOR, token.decimals, 2)}
                        </Typography>
                      )}
                    </Box>
                  )}
                </Box>
              );
            })}
          {/* <Box
            mt={1}
            display="flex"
            alignItems="center"
            justifyContent="center"
            sx={{ backgroundColor: "#EC6F1533", color: "var(--primary-main)", borderRadius: "10px", padding: "0 8px", minHeight: "32px" }}
          >
            <Typography fontSize={10}>{`Borrow against your auto-compounding ${vaultToken.ticker}`}</Typography>
            <CallMadeRoundedIcon sx={{ height: "8px", width: "8px" }} fontSize={"small"} />
          </Box> */}
        </>
      ) : (
        <></>
      )}
    </>
  );
};
const VaultComposition = Component;
export default VaultComposition;
