import React from "react";
import { But<PERSON>, <PERSON>, Typography, useMediaQuery, useTheme } from "@mui/material";
import TokenIcon from "./TokenIcons";
import { Hex } from "viem";
interface TickerPillProps {
  contractAddress: Hex;
  ticker: string;
  label?: string;
  vaulted?: boolean;
  staked?: boolean;
}
const Component: React.FC<TickerPillProps> = ({ ticker, contractAddress, vaulted, label, staked }) => {
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  return (
    <Button disabled size="small" variant="contained" sx={{ width: "fit-content" }}>
      <TokenIcon contractAddress={contractAddress} height={isMdUp ? "30px" : "25px"} vaulted={vaulted} staked={staked} pr={1} />{" "}
      <Typography variant={isMdUp ? "h4" : "h5"} color={"var(--text-primary)"}>
        {staked && "s"}
        {ticker}
      </Typography>
      {label !== undefined && <Chip variant="outlined" label={label} color={"success"} size="small" sx={{ fontWeight: 600, fontSize: 10, opacity: 0.75, ml: 1, mt: "2px" }} />}
    </Button>
  );
};

const TickerPill = Component;
export default TickerPill;
