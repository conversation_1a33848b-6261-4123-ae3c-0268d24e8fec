import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from "jotai";
import {
  stabilityPoolWithdrawAmountAtom,
  formValuesStabilityPoolWithdrawAtom,
  formValidationsStabilityPoolWithdrawAtom,
  convertSharesToAssetsAtom,
  getStabilityDeposit<PERSON>tom,
  getPoolAPYAtom,
  poolAssetSelectedAtom,
  enableZapAtom,
  getRedeemPreferredUnderlyingToAnyAtom,
  preferredUnderlyingTokensAtom,
  getLspAllowanceAtom,
} from "../../Atoms/StabilityPool";
import { formatDecimal, regexDecimalToken, formatBigIntPercentage } from "../../utils/helpers";
import { useHydrateAtoms } from "jotai/utils";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { SCALING_FACTOR, SCALING_FACTOR_BP, SCALING_FACTOR_DECIMALS, Token, TokenBalance } from "@<PERSON>raborrowofficial/sdk";
import { useLocation } from "react-router-dom";
import { updatePost<PERSON>og<PERSON><PERSON><PERSON><PERSON>, resetPostHogEventAtom, postHogEvent<PERSON>tom } from "../../Atoms/Account";
import { PoolWithdrawDispatcherActionType } from "./types";
import { useNotifications } from "../../Hooks/useNotifications";
import ApproveLSPRouter from "../Approvals/ApproveLSPRouter/component";
import { WithdrawDebtOnlyTransaction, redeemDebtOnlyTransaction, WithdrawDebtTransaction } from "../../@type/Transactions";

export const usePoolWithdrawStore = () => {
  const location = useLocation();
  const [zap, _setZap] = useAtom(enableZapAtom);
  const { addModal } = useNotifications();
  const { data: currentDeposit } = useAtomValue(getStabilityDepositAtom);
  const { data: shareToAssetRatio } = useAtomValue(convertSharesToAssetsAtom);
  const { data: apy } = useAtomValue(getPoolAPYAtom);
  const { data: redeemedAssets, isLoading } = useAtomValue(getRedeemPreferredUnderlyingToAnyAtom);
  const { data: lspAllowance } = useAtomValue(getLspAllowanceAtom);
  const [formValue, setFormValue] = useAtom(formValuesStabilityPoolWithdrawAtom);
  const [poolAssetSelected, setPoolAssetSelected] = useAtom(poolAssetSelectedAtom);
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const resetPostHogEvent = useSetAtom(resetPostHogEventAtom);
  const withdrawAmount = useAtomValue(stabilityPoolWithdrawAmountAtom);
  const preferredUnderlyingTokens = useAtomValue(preferredUnderlyingTokensAtom);
  const validationError = useAtomValue(formValidationsStabilityPoolWithdrawAtom);
  const maxAmount = currentDeposit.shares;
  const fees = currentDeposit.convertSharesToAssets((withdrawAmount * currentDeposit.exitFee) / SCALING_FACTOR_BP, shareToAssetRatio);
  const myAssets = currentDeposit.convertSharesToAssets(withdrawAmount, shareToAssetRatio);
  const preferredUnderlyingContractAddresses = preferredUnderlyingTokens?.map((item) => item.contractAddress) ?? [];
  const debtPrice = currentDeposit.assets.find((item) => item.contractAddress === BERABORROW_ADDRESSES.debtToken.contractAddress)?.price;
  if (debtPrice === undefined) throw Error("debt Price not found");
  const withdrawalDebtValue = (myAssets * SCALING_FACTOR) / debtPrice;
  const displayAssetsWithdrawal = (redeemedAssets ?? (currentDeposit.getUnderlyingAssets() as (Omit<TokenBalance, "underlyingAssets"> & { userBalance: bigint } & Token)[]))
    .map((item) => {
      const collateral =
        BERABORROW_ADDRESSES.collateralTokens[item.contractAddress] ??
        (item.contractAddress === BERABORROW_ADDRESSES.debtToken.contractAddress
          ? BERABORROW_ADDRESSES.debtToken
          : item.contractAddress === BERABORROW_ADDRESSES.pollenToken.contractAddress
            ? BERABORROW_ADDRESSES.pollenToken
            : undefined);
      return {
        ...item,
        ...collateral,
        userBalance: item?.userBalance ?? 0n,
      };
    })
    .filter((item) => !!item?.ticker && !!item?.balance); // filter out undefined tickers
  const debtTokenOnly = displayAssetsWithdrawal.length === 1 && !!debtPrice;

  useHydrateAtoms([
    [enableZapAtom, false], //zap disabled
    [poolAssetSelectedAtom, undefined],
    [formValuesStabilityPoolWithdrawAtom, formatDecimal(maxAmount, BERABORROW_ADDRESSES.debtToken.decimals, 0)],
    [
      postHogEventAtom,
      {
        type: "poolWithdraw",
        page_rel_path: location.pathname,
        token_deposited: BERABORROW_ADDRESSES.debtToken.ticker,
        qty_deposited_token: formatDecimal(maxAmount, BERABORROW_ADDRESSES.debtToken.decimals),
        current_apy: formatBigIntPercentage(apy),
        current_shares: formatDecimal(currentDeposit.shares, BERABORROW_ADDRESSES.debtToken.decimals),
        current_pool_tvl: formatDecimal(currentDeposit.convertSharesToAssets(currentDeposit.sharesTotalSupply, shareToAssetRatio), SCALING_FACTOR_DECIMALS),
      },
    ],
  ]);

  const dispatcher = (action: PoolWithdrawDispatcherActionType) => {
    switch (action.type) {
      case "setPoolWithdraw":
        updatePostHogEvent({
          token_withdrew: "s" + BERABORROW_ADDRESSES.debtToken.ticker,
          qty_withdrew_token: regexDecimalToken(action.payload, BERABORROW_ADDRESSES.debtToken.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, BERABORROW_ADDRESSES.debtToken.decimals),
          current_pool_tvl: formatDecimal(currentDeposit.convertSharesToAssets(currentDeposit.sharesTotalSupply, shareToAssetRatio), SCALING_FACTOR_DECIMALS),
        });
        setFormValue(regexDecimalToken(action.payload, BERABORROW_ADDRESSES.debtToken.decimals));
        break;
      case "confirmedPoolWithdraw":
        resetPostHogEvent({ type: "poolWithdraw", page_rel_path: location.pathname });
        setFormValue("0");
        break;
      case "setPoolWithdrawPercentage":
        const value = formatDecimal((maxAmount * BigInt(action.payload)) / 100n, BERABORROW_ADDRESSES.debtToken.decimals, action.payload === 100 ? undefined : 8);
        updatePostHogEvent({
          last_clicked_input_btn: action.payload + "%",
          token_withdrew: "s" + BERABORROW_ADDRESSES.debtToken.ticker,
          qty_withdrew_token: regexDecimalToken(value, BERABORROW_ADDRESSES.debtToken.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, BERABORROW_ADDRESSES.debtToken.decimals),
          current_pool_tvl: formatDecimal(currentDeposit.convertSharesToAssets(currentDeposit.sharesTotalSupply, shareToAssetRatio), SCALING_FACTOR_DECIMALS),
        });
        setFormValue(regexDecimalToken(value, BERABORROW_ADDRESSES.debtToken.decimals));
        break;
      case "enableZap":
        // setZap(!zap);
        break;
      case "setSelectedAsset":
        setPoolAssetSelected(action.payload);
        break;
      case "approveLsp":
        updatePostHogEvent({ type: `approveLSPRouter` });
        addModal({
          id: "ApproveLSPRouter",
          title: "Approve withdrawal",
          Component: (
            <ApproveLSPRouter
              onConfirmation={() => {
                dispatcher({ type: "confirmedPoolWithdraw" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      default:
        break;
    }
  };

  const transactionProps: redeemDebtOnlyTransaction | WithdrawDebtTransaction | WithdrawDebtOnlyTransaction =
    // poolAssetSelected && poolAssetSelected !== BERABORROW_ADDRESSES.debtToken.contractAddress
    //   ? {
    //       type: "redeemPreferredUnderlying",
    //       variables: [
    //         withdrawAmount,
    //         withdrawalDebtValue,
    //         preferredUnderlyingContractAddresses,
    //         displayAssetsWithdrawal.filter((item) => !!item.userBalance).length,
    //         DEFAULT_SLIPPAGE_TOLERANCE,
    //       ],
    //     }
    //   : debtTokenOnly
    //     ?
    {
      type: "redeemDebtOnly",
      variables: [withdrawAmount],
    };
  // : {
  //     type: "withdrawDebt",
  //     variables: [
  //       withdrawAmount,
  //       myAssets,
  //       currentDeposit.assets.filter((item) => item.balance).map((item) => item.contractAddress),
  //       currentDeposit.assets.filter((item) => item.balance).map((item) => currentDeposit.getMyShare(item.balance, withdrawAmount)),
  //       BigInt(displayAssetsWithdrawal.filter((item) => item.balance).length),
  //     ],
  //   };

  return {
    currentDeposit,
    validationError,
    formValue,
    withdrawAmount,
    maxAmount,
    dispatcher,
    myAssets,
    exitFee: currentDeposit.exitFee,
    fees,
    poolAssetSelected,
    displayAssetsWithdrawal,
    zap,
    needApproval: !debtTokenOnly && lspAllowance < withdrawAmount,
    isLoading,
    preferredUnderlyingContractAddresses,
    withdrawalDebtValue,
    transactionProps,
    isOogaboogaAvailable: false, //!!oogaboogaHealth?.healthy,
  };
};
export const getPoolWithdrawScopedAtoms = () => [formValidationsStabilityPoolWithdrawAtom, formValuesStabilityPoolWithdrawAtom, stabilityPoolWithdrawAmountAtom];
