import { Hex } from "viem";

export type PoolWithdrawDispatcherActionType = ChangeDeposit | ConfirmedPoolWithdraw | ChangeWithdrawPercent | SetSelectedAsset | EnableZap | ApproveLsp;

type ChangeDeposit = {
  type: "setPoolWithdraw";
  payload: string;
};
type ConfirmedPoolWithdraw = {
  type: "confirmedPoolWithdraw";
};
type ChangeWithdrawPercent = {
  type: "setPoolWithdrawPercentage";
  payload: number;
};
type SetSelectedAsset = {
  type: "setSelectedAsset";
  payload: Hex | undefined;
};
type EnableZap = {
  type: "enableZap";
};
type ApproveLsp = {
  type: "approveLsp";
};
