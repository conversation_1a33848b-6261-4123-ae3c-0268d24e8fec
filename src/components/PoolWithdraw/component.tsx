import React from "react";
import { usePoolWithdrawStore } from "./store";
import { Backdrop, Box, Button, CircularProgress, Divider, Grid, Typography, useTheme } from "@mui/material";
import { formatToken, formatBigIntPercentage } from "../../utils/helpers";
import TransactionButton from "../Transaction/Transaction";
import WithSuspense from "../../providers/WithSuspense";
import { BERABORROW_ADDRESSES, PRODUCTION } from "../../utils/constants";
import TickerPill from "../TickerPill";
import { SCALING_FACTOR, SCALING_FACTOR_DECIMALS, SCALING_FACTOR_BP_DECIMALS } from "@Beraborrowofficial/sdk";
import StyledTextfield from "../StyledTextField";
import { AmountSelector } from "../AmountSelector";
import EmissionsCountDown from "../EmissionsCountDown/Component";
import TokenIcon from "../TokenIcons";
import StyledTooltip from "../StyledFeeTooltip";
import { InfoOutlined } from "@mui/icons-material";
import { ValidationButton } from "../ValidationButton";

const Component: React.FC = () => {
  const theme = useTheme();
  const {
    validationError,
    formValue,
    withdrawAmount,
    maxAmount,
    dispatcher,
    myAssets,
    exitFee,
    fees,
    poolAssetSelected,
    displayAssetsWithdrawal,
    zap,
    isLoading,
    needApproval,
    transactionProps,
  } = usePoolWithdrawStore();
  return (
    <>
      <Grid container pb={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1">Burn s{BERABORROW_ADDRESSES.debtToken.ticker.toUpperCase()}</Typography>
          <StyledTextfield
            value={formValue}
            error={validationError?.type === "insufficient_balance"}
            onChange={(e) => dispatcher({ type: "setPoolWithdraw", payload: e.target.value })}
            endComponent={
              <Box display="flex" flexDirection="column" alignItems="start">
                <TickerPill contractAddress={BERABORROW_ADDRESSES.debtToken.contractAddress} staked ticker={BERABORROW_ADDRESSES.debtToken.ticker} />
                <AmountSelector handleItemClick={(percent) => dispatcher({ type: "setPoolWithdrawPercentage", payload: percent })} mt={1} ml={"auto"} />
              </Box>
            }
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="caption" fontSize={12}>
                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                    Deposited:
                  </Typography>{" "}
                  {formatToken(maxAmount, BERABORROW_ADDRESSES.debtToken.decimals, 2)}
                </Typography>
              </Box>
            }
          />
        </Grid>
      </Grid>
      <Box display={"flex"} justifyContent={"space-between"}>
        <Typography variant="subtitle1">Composition Withdrawn </Typography>
        <StyledTooltip placement={"right"} title={"Fee charged on withdrawal from Pool"}>
          <Typography variant="body2" fontSize={12} display="flex" alignItems="center">
            <Typography variant="body2" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center">
              <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle" }} />
              Fee:
            </Typography>
            &nbsp; ${formatToken(fees, BERABORROW_ADDRESSES.debtToken.decimals, 2)}
            {" (" + formatBigIntPercentage(exitFee, 2, undefined, undefined, SCALING_FACTOR_BP_DECIMALS) + "%) "}
          </Typography>
        </StyledTooltip>
      </Box>
      <Box
        sx={{
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "16px",
          },
          borderRadius: "12px",
          border: `1px solid ${theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h2" component="span" display="flex" alignItems="center" fontWeight={600} color="var(--text-primary)">
            ${formatToken(myAssets - fees, SCALING_FACTOR_DECIMALS, 2)}
          </Typography>
          {!PRODUCTION && (
            <StyledTooltip placement={"right"} title={"Please select your preferred token you would like to receive"}>
              <Box>
                <Button
                  onClick={() => dispatcher({ type: "setSelectedAsset", payload: undefined })}
                  size="small"
                  variant="contained"
                  sx={{
                    ml: 1,
                    "&.Mui-disabled": { color: theme.palette.text.primary, backgroundColor: theme.palette.primary.main },
                  }}
                  disabled={poolAssetSelected === undefined}
                >
                  <Typography variant="h5" component="span" display="flex" alignItems="center" sx={{}}>
                    <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", mr: 0.5 }} /> Withdraw Basket
                  </Typography>
                </Button>
              </Box>
            </StyledTooltip>
          )}
        </Box>
        <Divider sx={{ my: 1.5, backgroundColor: `var(--border-color)` }} />

        <Box sx={{ position: "relative" }}>
          <Backdrop sx={{ zIndex: (theme) => theme.zIndex.drawer + 1, position: "absolute" }} open={isLoading}>
            <CircularProgress />
          </Backdrop>
          {displayAssetsWithdrawal.map((item) => {
            return (
              <Box key={item.contractAddress}>
                <Box key={item.contractAddress} display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                  <Box>
                    <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600}>
                      <TokenIcon contractAddress={item.contractAddress} height={"25px"} width={"25px"} />{" "}
                      <Box pl={1}>{formatToken(item?.userBalance ?? 0n, item.decimals, poolAssetSelected ? 4 : 2, item.ticker)}</Box>
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600} color="var(--text-primary)">
                      ${formatToken(((item.userBalance ?? 0n) * item.price) / SCALING_FACTOR, item.decimals, 2)}{" "}
                      {!PRODUCTION && (
                        <Button
                          onClick={() => dispatcher({ type: "setSelectedAsset", payload: item.contractAddress })}
                          size="small"
                          variant="contained"
                          sx={{
                            opacity: BERABORROW_ADDRESSES.debtToken.contractAddress === item.contractAddress ? 0 : 1,
                            ml: 1,
                            "&.Mui-disabled":
                              poolAssetSelected === item.contractAddress
                                ? { color: theme.palette.text.primary, backgroundColor: theme.palette.primary.main }
                                : { color: theme.palette.text.secondary },
                          }}
                          disabled={
                            poolAssetSelected === item.contractAddress ||
                            (!zap && poolAssetSelected && item.balance === 0n) ||
                            BERABORROW_ADDRESSES.debtToken.contractAddress === item.contractAddress
                          }
                        >
                          Max
                        </Button>
                      )}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            );
          })}
        </Box>
      </Box>
      {/* <Box
        sx={{
          mt: 1,
          padding: "8px 12px",
          borderRadius: "12px",
          border: `1px solid ${theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <StyledTooltip title={isOogaboogaAvailable ? "Zap into preferred asset via OogaBooga" : "Zap Currently not available"} placement="right">
            <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600}>
              <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", mr: 0.5, color: theme.palette.text.secondary }} /> Zap into preferred asset
            </Typography>
          </StyledTooltip>
          <Box>
            <Switch
              disabled={poolAssetSelected === undefined || !isOogaboogaAvailable}
              checked={poolAssetSelected === undefined || !isOogaboogaAvailable ? false : zap}
              onChange={() => {
                dispatcher({ type: "enableZap" });
              }}
            />
          </Box>
        </Box>
      </Box> */}

      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />

      {/* SDK add subgraph to check if the last block had a withdrawal from this account */}
      <Box display={needApproval ? "block" : "none"}>
        <ValidationButton validationError={validationError} fullWidth variant="contained" onClick={() => dispatcher({ type: "approveLsp" })} disabled={!!validationError}>
          Approve and Withdraw
        </ValidationButton>
      </Box>
      <Box display={needApproval ? "none" : "block"}>
        <TransactionButton
          disabled={!!validationError || !withdrawAmount}
          transaction={transactionProps}
          onConfirmation={() => dispatcher({ type: "confirmedPoolWithdraw" })}
          validationError={validationError}
        >
          Withdraw
        </TransactionButton>
      </Box>

      <EmissionsCountDown />
    </>
  );
};

const PoolWithdraw = WithSuspense(Component, "paper");
export default PoolWithdraw;
