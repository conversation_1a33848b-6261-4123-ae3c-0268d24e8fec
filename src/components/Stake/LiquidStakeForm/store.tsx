import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { useLocation } from "react-router-dom";
import { useTheme } from "@mui/material";
import { useEffect, useRef } from "react";
import {
  getSPollenBalanceAtom,
  getStakedSPollenBalanceAtom,
  getSPollenStakingAllowanceAtom,
  formValidationsLiquidStakeAtom,
  formValidationsLiquidUnstakeAtom,
  formValuesLiquidStakeAtom,
  liquidStakeAmountAtom,
} from "../../../Atoms/Stake";
import { formatToken, regexDecimalToken } from "../../../utils/helpers";
import { getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import { updatePostHogEventAtom } from "../../../Atoms/Account";
import { useNotifications } from "../../../Hooks/useNotifications";
import { GenericApproval, APPROVAL_CONFIGS } from "../../Approvals/GenericApproval";

export type LiquidStakeFormDispatcherActionType =
  | { type: "setAmount"; payload: string }
  | { type: "setPercentage"; payload: bigint }
  | { type: "approveStaking" }
  | { type: "confirmedTransaction" }
  | { type: "clearForm" };

export const useLiquidStakeFormStore = () => {
  const location = useLocation();
  const isStakeMode = location.pathname.endsWith("/stake");

  const { data: sPollenBalance } = useAtomValue(getSPollenBalanceAtom);
  const { data: stakedSPollenBalance } = useAtomValue(getStakedSPollenBalanceAtom);
  const { data: allowance } = useAtomValue(getSPollenStakingAllowanceAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);

  const [formValue, setFormValue] = useAtom(formValuesLiquidStakeAtom);

  const validationError = useAtomValue(isStakeMode ? formValidationsLiquidStakeAtom : formValidationsLiquidUnstakeAtom);

  const amount = useAtomValue(liquidStakeAmountAtom);

  // For stake mode: use sPOLLEN wallet balance, for unstake mode: use staked sPOLLEN balance
  const balance = isStakeMode ? sPollenBalance || 0n : stakedSPollenBalance || 0n;

  const theme = useTheme();
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const { addModal } = useNotifications();

  const previousModeRef = useRef(isStakeMode);
  useEffect(() => {
    if (previousModeRef.current !== isStakeMode) {
      setFormValue("0");
      previousModeRef.current = isStakeMode;
    }
  }, [isStakeMode, setFormValue]);

  const dispatcher = (action: LiquidStakeFormDispatcherActionType) => {
    switch (action.type) {
      case "setAmount": {
        const sanitizedValue = regexDecimalToken(action.payload, collateralDetails.decimals);
        setFormValue(sanitizedValue);
        break;
      }
      case "setPercentage": {
        const calculatedAmount = (BigInt(action.payload) * balance) / 100n;
        const formattedAmount = formatToken(calculatedAmount, collateralDetails.decimals);
        setFormValue(regexDecimalToken(formattedAmount, collateralDetails.decimals));
        break;
      }
      case "approveStaking":
        updatePostHogEvent({ type: `approveSPollenStaking` });
        addModal({
          id: "ApproveSPollenStaking",
          title: "Approve sPOLLEN Staking",
          Component: (
            <GenericApproval
              amount={amount}
              config={APPROVAL_CONFIGS.sPollenStaking}
              onConfirmation={() => {
                dispatcher({ type: "confirmedTransaction" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      case "confirmedTransaction":
        setFormValue("0");
        break;
      case "clearForm":
        setFormValue("0");
        break;
      default:
        break;
    }
  };

  const transactionType = isStakeMode ? ("stakePollen" as const) : ("unstakePollen" as const);
  const buttonText = isStakeMode ? "Stake sPOLLEN" : "Unstake sPOLLEN";
  const inputTokenSymbol = "sPOLLEN";
  const outputTokenSymbol = "sPOLLEN";
  const needsApproval = isStakeMode && allowance < amount;
  const balanceLabel = isStakeMode ? "Wallet" : "Staked";

  const inputTokenAddress = BERABORROW_ADDRESSES.sPollenToken.contractAddress;
  const outputTokenAddress = BERABORROW_ADDRESSES.sPollenToken.contractAddress;

  const parsedAmount = amount;
  const isDisabled = !parsedAmount || parsedAmount === 0n || !!validationError;

  const buttonStyle = {
    borderRadius: 4,
    py: 1.5,
    fontSize: theme.typography.h3.fontSize,
    fontWeight: theme.typography.h3.fontWeight,
  };

  return {
    mode: isStakeMode ? "stake" : ("unstake" as const),
    balance,
    allowance,
    formValue,
    amount,
    validationError,
    dispatcher,
    transactionType,
    buttonText,
    inputTokenSymbol,
    outputTokenSymbol,
    needsApproval,
    theme,
    inputTokenAddress,
    outputTokenAddress,
    parsedAmount,
    isDisabled,
    buttonStyle,
    balanceLabel,
    collateralDetails,
  };
};
