"use client";

import { Box, Typography } from "@mui/material";
import { useLiquidStakeFormStore } from "./store";
import WithSuspense from "../../../providers/WithSuspense";
import { StakeFormBase } from "../shared/StakeFormBase";

const Component = () => {
  const storeData = useLiquidStakeFormStore();
  const { theme, mode, dispatcher } = storeData;

  const customHeader = (
    <Box sx={{ display: "flex", justifyContent: "space-between", mb: "6px" }}>
      <Typography variant="h4" sx={{ color: theme.palette.text.secondary }}>
        {mode === "stake" ? "Amount to Stake" : "Amount to Unstake"}
      </Typography>
    </Box>
  );

  return (
    <StakeFormBase
      formData={storeData}
      handlers={{
        handleAmountChange: (value: string) => dispatcher({ type: "setAmount", payload: value }),
        handlePercentageClick: (percentage: number) => dispatcher({ type: "setPercentage", payload: BigInt(percentage) }),
        dispatcher,
      }}
      customHeader={customHeader}
      showEstimatedApr={mode === "stake"}
      approvalTokenName="sPOLLEN"
    />
  );
};

const LiquidStakeForm = WithSuspense(Component, "paper");
export default LiquidStakeForm;
