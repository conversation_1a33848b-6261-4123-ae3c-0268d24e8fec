import { Box, TextField, Typography } from "@mui/material";
import { ReactNode } from "react";
import { formatToken } from "../../../utils/helpers";
import { AmountSelector } from "../../AmountSelector";
import TickerPill from "../../TickerPill";
import TransactionButton from "../../Transaction/Transaction";
import { ValidationButton } from "../../ValidationButton";
import { FormError } from "../../../@type";

export const APR_VALUE = "1.23%";

export const containerStyle = {
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
} as const;

export const AprBadge = ({ children, theme }: { children: ReactNode; theme: any }) => (
  <Box
    sx={{
      color: theme.palette.success.main,
      backgroundColor: `${theme.palette.success.main}1A`,
      borderRadius: "13px",
      px: "6px",
      py: "3px",
      fontSize: theme.typography.body2.fontSize,
      textAlign: "center",
    }}
  >
    {children}
  </Box>
);

export const StakingOption = ({
  title,
  aprValue,
  isSelected = false,
  isComingSoon = false,
  theme,
}: {
  title: string;
  aprValue: string;
  isSelected?: boolean;
  isComingSoon?: boolean;
  theme: any;
}) => (
  <Box
    sx={{
      ...{ flex: 1 },
      ...containerStyle,
      ...(isSelected
        ? {
            py: "10px",
            px: "6px",
            borderRadius: 2,
            backgroundColor: "transparent",
            border: `2px solid ${theme.palette.primary.main}`,
            textAlign: "center",
          }
        : {}),
    }}
  >
    <Typography
      variant="h6"
      sx={{
        mb: "6px",
        color: isSelected ? theme.palette.text.secondary : theme.palette.text.primary,
        ...(isSelected ? {} : { fontWeight: 600 }),
      }}
    >
      {title}
    </Typography>
    <AprBadge theme={theme}>{isComingSoon ? "Coming soon!" : `${aprValue} APR`}</AprBadge>
  </Box>
);

interface StakeFormBaseProps {
  formData: {
    balance: bigint;
    formValue: string;
    validationError: FormError | undefined;
    inputTokenSymbol: string;
    inputTokenAddress: `0x${string}`;
    mode: string;
    balanceLabel: string;
    collateralDetails: { decimals: number };
    needsApproval: boolean;
    transactionType: "stakePollen" | "unstakePollen" | "stakeLpPollen" | "unstakeLpPollen" | "wrapPollen" | "unwrapPollen";
    buttonText: string;
    parsedAmount: bigint;
    isDisabled: boolean;
    buttonStyle: any;
    theme: any;
  };
  handlers: {
    handleAmountChange: (value: string) => void;
    handlePercentageClick: (percentage: number) => void;
    dispatcher: (action: any) => void;
  };
  customHeader?: ReactNode;
  showEstimatedApr?: boolean;
  approvalTokenName?: string;
}

export const StakeFormBase = ({ formData, handlers, customHeader, showEstimatedApr = false, approvalTokenName = "sPOLLEN" }: StakeFormBaseProps) => {
  const {
    balance,
    formValue,
    validationError,
    inputTokenSymbol,
    inputTokenAddress,
    balanceLabel,
    collateralDetails,
    needsApproval,
    transactionType,
    buttonText,
    parsedAmount,
    isDisabled,
    buttonStyle,
    theme,
  } = formData;

  const { handleAmountChange, handlePercentageClick, dispatcher } = handlers;

  return (
    <Box
      sx={{
        padding: "16px",
        borderRadius: "16px",
        pt: 0,
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px",
          borderRadius: "16px",
          background: theme.palette.background.paper,
          mb: "14px",
        }}
      >
        <StakingOption title="Basic Staking" aprValue={APR_VALUE} isSelected theme={theme} />
        <StakingOption title="Auto-compounding" aprValue={APR_VALUE} isComingSoon theme={theme} />
      </Box>

      {customHeader}

      <Box
        sx={{
          backgroundColor: theme.palette.background.paper,
          borderRadius: 3,
          padding: "12px",
          mb: "14px",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", paddingBottom: 1 }}>
          <TextField
            value={formValue}
            onChange={(e) => handleAmountChange(e.target.value)}
            placeholder="0.0"
            error={!!validationError}
            sx={{
              paddingLeft: 0,
              flex: 1,
              "& .MuiOutlinedInput-root": {
                backgroundColor: "transparent",
                border: "none",
                fontSize: theme.typography.h2.fontSize,
                fontWeight: theme.typography.h2.fontWeight,
                color: theme.palette.text.primary,
                padding: 0,
                paddingRight: "10px",
                "& .MuiOutlinedInput-input": {
                  padding: 0,
                },
                "& fieldset": {
                  border: "none",
                },
                "& input::placeholder": {
                  color: theme.palette.text.secondary,
                  opacity: 1,
                },
              },
            }}
          />
          <TickerPill contractAddress={inputTokenAddress} ticker={inputTokenSymbol} />
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
          <Typography variant="h6" sx={{ color: theme.palette.text.secondary }}>
            {balanceLabel}: {formatToken(balance, collateralDetails.decimals, 2)} {inputTokenSymbol}
          </Typography>
          <AmountSelector handleItemClick={handlePercentageClick} />
        </Box>
      </Box>

      {showEstimatedApr && (
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: "14px" }}>
          <Typography variant="h4" sx={{ color: theme.palette.text.secondary }}>
            Estimated APR:
          </Typography>
          <Typography variant="h4" sx={{ color: theme.palette.success.main }}>
            {APR_VALUE}
          </Typography>
        </Box>
      )}

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        {needsApproval ? (
          <ValidationButton
            validationError={validationError}
            fullWidth
            variant="contained"
            onClick={() => dispatcher({ type: "approveStaking" })}
            disabled={!!validationError}
            sx={buttonStyle}
          >
            Approve {approvalTokenName}
          </ValidationButton>
        ) : (
          <TransactionButton
            transaction={{
              type: transactionType,
              variables: [parsedAmount],
            }}
            disabled={isDisabled}
            validationError={validationError}
            onConfirmation={() => dispatcher({ type: "confirmedTransaction" })}
            sx={buttonStyle}
          >
            {buttonText}
          </TransactionButton>
        )}
      </Box>
    </Box>
  );
};
