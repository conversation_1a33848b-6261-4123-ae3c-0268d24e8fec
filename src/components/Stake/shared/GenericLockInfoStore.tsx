import { atom, useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import {
  getSPollenBalanceAtom,
  getVePollenBalanceAtom,
  getLpPollenBalanceAtom,
  lockDurationAtom,
  getSPollenProtocolDataAtom,
  getUserLockPosition<PERSON>tom,
  getLockingAprData<PERSON>tom,
} from "../../../Atoms/Stake";
import { formatToken } from "../../../utils/helpers";
import { updatePostHogEventAtom } from "../../../Atoms/Account";
import { useNotifications } from "../../../Hooks/useNotifications";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";

const positionExpandedAtom = atom<boolean>(false);
const rewardsExpandedAtom = atom<boolean>(false);

export type GenericLockInfoDispatcherActionType = { type: "claimRewards" } | { type: "claimProtocolFees" };

type LockTokenType = "spollen" | "lp";

interface TokenConfig {
  inputTokenSymbol: string;
  outputTokenSymbol: string;
  tokenAddress: `0x${string}`;
  claimEventType: string;
  claimModalId: string;
  claimModalTitle: string;
}

export const useGenericLockInfoStore = (tokenType: LockTokenType) => {
  const [positionExpanded, setPositionExpanded] = useAtom(positionExpandedAtom);
  const [rewardsExpanded, setRewardsExpanded] = useAtom(rewardsExpandedAtom);

  const isLpToken = tokenType === "lp";

  const tokenConfig: TokenConfig = {
    inputTokenSymbol: isLpToken ? "BERA-POLLEN" : "sPOLLEN",
    outputTokenSymbol: isLpToken ? "veBERA-POLLEN" : "vePOLLEN",
    tokenAddress: isLpToken ? BERABORROW_ADDRESSES.pollenLpToken.contractAddress : BERABORROW_ADDRESSES.sPollenToken.contractAddress,
    claimEventType: isLpToken ? "claimLpPollenRewards" : "claimVeLockRewards",
    claimModalId: isLpToken ? "ClaimLpPollenRewards" : "ClaimVeLockRewards",
    claimModalTitle: isLpToken ? "Claim LP Rewards" : "Claim Rewards",
  };

  const { data: sPollenBalance } = useAtomValue(getSPollenBalanceAtom);
  const { data: lpPollenBalance } = useAtomValue(getLpPollenBalanceAtom);
  const { data: vePollenBalance } = useAtomValue(getVePollenBalanceAtom);

  const tokenBalance = isLpToken ? lpPollenBalance : sPollenBalance;
  const veTokenBalance = isLpToken ? 0n : vePollenBalance;

  const { data: sPollenProtocolData } = useAtomValue(getSPollenProtocolDataAtom);
  const { data: sPollenUserLockPosition } = useAtomValue(getUserLockPositionAtom);

  const protocolData = isLpToken ? { totalLocked: 0n } : sPollenProtocolData;

  const userLockPosition = isLpToken ? { amount: 0n, expiry: 0n, hasPosition: false } : sPollenUserLockPosition;

  const { data: aprData } = useAtomValue(getLockingAprDataAtom);
  const lockDuration = useAtomValue(lockDurationAtom);

  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const { addModal } = useNotifications();

  const protocolFees = 0n;
  const pendingRewards = 0n;

  const getMultiplier = (duration: number): number => {
    const multipliers: Record<number, number> = {
      30: 1.25,
      90: 2,
      180: 3,
      360: 4,
    };
    return multipliers[duration] || 1;
  };

  const userMultiplier = getMultiplier(lockDuration);

  const togglePosition = () => {
    setPositionExpanded(!positionExpanded);
  };

  const toggleRewards = () => {
    setRewardsExpanded(!rewardsExpanded);
  };

  const hasPendingRewards = (protocolFees && protocolFees > 0n) || (pendingRewards && pendingRewards > 0n);
  const totalRewards = (protocolFees || 0n) + (pendingRewards || 0n);
  const claimButtonText = hasPendingRewards ? `Claim $${formatToken(totalRewards, 18, 2)}` : "Claim Rewards";
  const isClaimDisabled = !hasPendingRewards;

  const dispatcher = (action: GenericLockInfoDispatcherActionType) => {
    switch (action.type) {
      case "claimRewards":
        updatePostHogEvent({ type: tokenConfig.claimEventType });
        addModal({
          id: tokenConfig.claimModalId,
          title: tokenConfig.claimModalTitle,
          Component: (
            <div>
              <p>{isLpToken ? "Claim LP rewards functionality coming soon" : "Claim rewards functionality would be implemented here"}</p>
              {!isLpToken && <p>This would include both protocol fees and staking rewards</p>}
            </div>
          ),
          maxWidth: isLpToken ? "400px" : "600px",
        });
        break;
      case "claimProtocolFees":
        updatePostHogEvent({ type: isLpToken ? "claimLpProtocolFees" : "claimProtocolFees" });

        break;
      default:
        break;
    }
  };

  return {
    positionExpanded,
    rewardsExpanded,
    togglePosition,
    toggleRewards,
    tokenBalance,
    veTokenBalance,
    protocolData,
    userLockPosition,
    aprData,
    userMultiplier,
    protocolFees,
    pendingRewards,
    claimButtonText,
    isClaimDisabled,
    dispatcher,
    tokenConfig,
  };
};
