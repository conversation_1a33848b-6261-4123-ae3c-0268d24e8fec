"use client";

import { Box, Typography, useTheme } from "@mui/material";

export interface StatCardData {
  label: string;
  value: string;
  subtitle?: string;
  valueColor?: string;
  subtitleColor?: string;
}

interface StatCardProps extends StatCardData {
  variant?: "default" | "compact";
}

const StatCard = ({ label, value, subtitle, valueColor, subtitleColor, variant = "default" }: StatCardProps) => {
  const theme = useTheme();

  const isCompact = variant === "compact";

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        py: "12px",
        px: "12px",
        borderRadius: isCompact ? "12px" : "13px",
        textAlign: "center",
        background: theme.palette.background.paper,
        gap: isCompact ? "4px" : "6px",
      }}
    >
      <Typography
        variant={isCompact ? "body2" : "h4"}
        sx={{
          color: theme.palette.text.secondary,
          mb: isCompact ? 0 : "6px",
        }}
      >
        {label}
      </Typography>
      <Typography
        variant="h4"
        sx={{
          color: valueColor ?? theme.palette.text.primary,
          fontWeight: isCompact ? 600 : "normal",
          mb: isCompact ? 0 : "6px",
        }}
      >
        {value}
      </Typography>
      {subtitle && (
        <Typography
          variant={isCompact ? "body2" : "body1"}
          sx={{
            color: subtitleColor ?? theme.palette.text.secondary,
          }}
        >
          {subtitle}
        </Typography>
      )}
    </Box>
  );
};

export default StatCard;
