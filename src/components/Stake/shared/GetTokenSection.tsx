"use client";

import ArrowOutwardIcon from "@mui/icons-material/ArrowOutward";
import { Box, Paper, Typography, useTheme } from "@mui/material";
import { Hex } from "viem";
import TokenIcon from "../../TokenIcons";

interface GetTokenSectionProps {
  tokenName: string;
  tokenAddress: Hex;
  variant?: "primary" | "paper";
  onClick?: () => void;
}

const GetTokenSection = ({ tokenName, tokenAddress, variant = "primary", onClick }: GetTokenSectionProps) => {
  const theme = useTheme();

  const isPrimary = variant === "primary";

  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        mb: isPrimary ? "15px" : 2,
        borderRadius: 4,
        background: isPrimary ? `${theme.palette.primary.main}33` : theme.palette.background.paper,
        display: "flex",
        alignItems: "center",
        gap: "10px",
        justifyContent: "center",
        cursor: onClick ? "pointer" : "default",
        "&:hover": onClick
          ? {
              opacity: 0.8,
            }
          : {},
      }}
      onClick={onClick}
    >
      <TokenIcon contractAddress={tokenAddress} height={24} />

      <Typography
        variant="h6"
        component="span"
        sx={{
          color: theme.palette.primary.main,
          fontWeight: 600,
          display: "flex",
          alignItems: "center",
          textTransform: "capitalize",
        }}
      >
        Get {tokenName}
        <Box
          component="span"
          sx={{
            mx: 0.5,
            cursor: "pointer",
            textTransform: "lowercase",
            textDecoration: isPrimary ? "none" : "underline",
            "&:hover": {
              textDecoration: "underline",
            },
          }}
        >
          here
        </Box>
        <ArrowOutwardIcon fontSize="small" />
      </Typography>
    </Paper>
  );
};

export default GetTokenSection;
