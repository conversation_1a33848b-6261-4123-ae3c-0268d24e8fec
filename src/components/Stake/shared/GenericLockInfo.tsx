"use client";

import { Box, useTheme } from "@mui/material";
import WithSuspense from "../../../providers/WithSuspense";
import { formatToken } from "../../../utils/helpers";
import { ValidationButton } from "../../ValidationButton";
import BaseStakeInfo from "./BaseStakeInfo";
import { ExpandableSectionData, InfoRowData } from "./ExpandableSection";
import { useGenericLockInfoStore } from "./GenericLockInfoStore";
import { StatCardData } from "./StatCard";

interface GenericLockInfoProps {
  tokenType: "spollen" | "lp";
}

const Component = ({ tokenType }: GenericLockInfoProps) => {
  const theme = useTheme();

  const {
    positionExpanded,
    rewardsExpanded,
    togglePosition,
    toggleRewards,
    tokenBalance,
    veTokenBalance,
    protocolData,
    userLockPosition,
    aprData,
    userMultiplier,
    protocolFees,
    pendingRewards,
    claimButtonText,
    isClaimDisabled,
    dispatcher,
    tokenConfig,
  } = useGenericLockInfoStore(tokenType);

  const getCurrentApr = (): string => {
    if (userLockPosition?.hasPosition && userLockPosition.amount > 0n) {
      const currentDuration = 30;
      return aprData?.[currentDuration] ? `${(Number(aprData[currentDuration]) / 100).toFixed(2)}%` : "0.00%";
    }
    return aprData?.[30] ? `${(Number(aprData[30]) / 100).toFixed(2)}%` : "0.00%";
  };

  const statCards: StatCardData[] = [
    {
      label: "Total Locked",
      value: protocolData?.totalLocked ? formatToken(protocolData.totalLocked, 18, 2) : "0",
      subtitle: protocolData?.totalLocked ? `${formatToken(protocolData.totalLocked, 18, 1)} ${tokenConfig.inputTokenSymbol}` : `0 ${tokenConfig.inputTokenSymbol}`,
    },
    {
      label: "Current APR",
      value: getCurrentApr(),
      subtitle: userLockPosition?.hasPosition ? `${userMultiplier}x Multiplier` : "No active lock",
      valueColor: theme.palette.success.main,
      subtitleColor: userLockPosition?.hasPosition ? "#ec6f15" : theme.palette.text.secondary,
    },
  ];

  const getLockExpiryDisplay = (): string => {
    if (userLockPosition?.expiry && userLockPosition.expiry > 0n) {
      const expiryDate = new Date(Number(userLockPosition.expiry) * 1000);
      const now = new Date();
      if (expiryDate > now) {
        const diffTime = expiryDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return `${diffDays} days`;
      }
      return "Expired";
    }
    return "No lock";
  };

  const positionRows: InfoRowData[] = [
    {
      label: `${tokenConfig.inputTokenSymbol} balance`,
      value: tokenBalance ? formatToken(tokenBalance, 18, 4) : "0.00",
    },
    {
      label: `${tokenConfig.inputTokenSymbol} locked`,
      value: userLockPosition?.amount ? formatToken(userLockPosition.amount, 18, 4) : "0.00",
    },
    {
      label: `${tokenConfig.outputTokenSymbol} balance`,
      value: veTokenBalance ? formatToken(veTokenBalance, 18, 4) : "0.00",
    },
    {
      label: "Lock expires in",
      value: getLockExpiryDisplay(),
    },
  ];

  const rewardsRows: InfoRowData[] = [
    {
      label: "Protocol Fees",
      value: protocolFees ? `$${formatToken(protocolFees, 18, 2)}` : "$0.00",
    },
    {
      label: "Pending Rewards",
      value: pendingRewards ? `$${formatToken(pendingRewards, 18, 2)}` : "$0.00",
    },
  ];

  const expandableSections: ExpandableSectionData[] = [
    {
      title: "Your Position",
      expanded: positionExpanded,
      onToggle: togglePosition,
      rows: positionRows,
    },
    {
      title: "Your Rewards",
      expanded: rewardsExpanded,
      onToggle: toggleRewards,
      rows: rewardsRows,
    },
  ];

  const claimButton = (
    <ValidationButton
      validationError={undefined}
      fullWidth
      variant="contained"
      onClick={() => dispatcher({ type: "claimRewards" })}
      disabled={isClaimDisabled}
      sx={{
        borderRadius: 4,
        py: 1.5,
        fontSize: theme.typography.h3.fontSize,
        fontWeight: theme.typography.h3.fontWeight,
        backgroundColor: theme.palette.background.paper,
        color: theme.palette.text.secondary,
        "&:hover": {
          backgroundColor: theme.palette.background.default,
        },
        "&:disabled": {
          backgroundColor: theme.palette.background.paper,
          color: theme.palette.text.disabled,
        },
      }}
    >
      {claimButtonText}
    </ValidationButton>
  );

  return (
    <Box sx={{ width: "100%", maxWidth: "450px", mx: "auto" }}>
      <BaseStakeInfo
        tokenName={tokenConfig.inputTokenSymbol}
        tokenAddress={tokenConfig.tokenAddress}
        statCards={statCards}
        expandableSections={expandableSections}
        getTokenVariant="paper"
        statCardVariant="default"
        expandableSectionVariant="default"
      />
      {claimButton}
    </Box>
  );
};

const GenericLockInfo = WithSuspense(Component, "paper");

export default GenericLockInfo;
