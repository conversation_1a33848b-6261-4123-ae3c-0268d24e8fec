"use client";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Box, Typography, useTheme } from "@mui/material";
import { ReactNode } from "react";

export interface InfoRowData {
  label: string;
  value: string | ReactNode;
}

export interface ExpandableSectionData {
  title: string;
  expanded: boolean;
  onToggle: () => void;
  rows: InfoRowData[];
}

interface ExpandableSectionProps extends ExpandableSectionData {
  variant?: "default" | "compact";
}

const ExpandableSection = ({ title, expanded, onToggle, rows, variant = "default" }: ExpandableSectionProps) => {
  const theme = useTheme();

  const isCompact = variant === "compact";

  const STYLES = {
    cardContainer: {
      borderRadius: isCompact ? "16px" : "13px",
      p: isCompact ? "16px" : "12px",
      overflow: "hidden",
      mb: isCompact ? "6px" : "8px",
      background: theme.palette.background.paper,
    },
    expandableHeader: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      cursor: "pointer",
      mb: "8px",
    },
    infoRow: {
      display: "flex",
      justifyContent: "space-between",
      mb: "8px",
      "&:last-child": { mb: 0 },
    },
  };

  return (
    <Box sx={STYLES.cardContainer}>
      <Box sx={STYLES.expandableHeader} onClick={onToggle}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
          {title}
        </Typography>
        <ExpandMoreIcon
          sx={{
            transform: expanded ? "rotate(180deg)" : "rotate(0deg)",
            transition: "transform 0.3s",
          }}
        />
      </Box>
      {expanded && (
        <Box sx={{ px: "4px" }}>
          {rows.map((row) => (
            <Box key={row.label} sx={STYLES.infoRow}>
              <Typography variant="h5" sx={{ color: theme.palette.text.secondary }}>
                {row.label}
              </Typography>
              <Typography sx={{ color: theme.palette.text.primary }}>{row.value}</Typography>
            </Box>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default ExpandableSection;
