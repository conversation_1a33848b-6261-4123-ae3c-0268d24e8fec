"use client";

import { Box, useTheme } from "@mui/material";
import { ReactNode } from "react";
import { Hex } from "viem";
import TransactionButton from "../../Transaction/Transaction";
import StatCard, { StatCardData } from "./StatCard";
import ExpandableSection, { ExpandableSectionData } from "./ExpandableSection";
import GetTokenSection from "./GetTokenSection";
import { TransactionProps } from "../../../@type/Transactions";

interface BaseStakeInfoProps {
  tokenName: string;
  tokenAddress: Hex;
  statCards: StatCardData[];
  expandableSections: ExpandableSectionData[];
  claimButton?: {
    transaction: TransactionProps;
    disabled: boolean;
    text: string;
  };
  getTokenVariant?: "primary" | "paper";
  statCardVariant?: "default" | "compact";
  expandableSectionVariant?: "default" | "compact";
  additionalContent?: ReactNode;
  onGetTokenClick?: () => void;
}

const BaseStakeInfo = ({
  tokenName,
  tokenAddress,
  statCards,
  expandableSections,
  claimButton,
  getTokenVariant = "primary",
  statCardVariant = "default",
  expandableSectionVariant = "default",
  additionalContent,
  onGetTokenClick,
}: BaseStakeInfoProps) => {
  const theme = useTheme();

  return (
    <Box sx={{ width: "100%", maxWidth: "450px", mx: "auto" }}>
      <GetTokenSection tokenName={tokenName} tokenAddress={tokenAddress} variant={getTokenVariant} onClick={onGetTokenClick} />

      {additionalContent}

      {statCards.length > 0 && (
        <Box sx={{ display: "flex", gap: "6px", mb: "6px" }}>
          {statCards.map((card) => (
            <StatCard key={card.label} {...card} variant={statCardVariant} />
          ))}
        </Box>
      )}

      {expandableSections.map((section) => (
        <ExpandableSection key={section.title} {...section} variant={expandableSectionVariant} />
      ))}

      {claimButton && (
        <TransactionButton
          transaction={claimButton.transaction}
          disabled={claimButton.disabled}
          sx={{
            borderRadius: 4,
            py: 1.5,
            fontSize: theme.typography.h3.fontSize,
            fontWeight: theme.typography.h3.fontWeight,
            "&:disabled": {
              backgroundColor: theme.palette.action.disabled,
              color: theme.palette.text.disabled,
            },
          }}
        >
          {claimButton.text}
        </TransactionButton>
      )}
    </Box>
  );
};

export default BaseStakeInfo;
