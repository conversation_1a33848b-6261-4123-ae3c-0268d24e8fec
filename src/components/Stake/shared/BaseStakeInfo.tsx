"use client";

import { Box, useTheme } from "@mui/material";
import { ReactNode } from "react";
import { Hex } from "viem";
import { useNavigate } from "react-router-dom";
import TransactionButton from "../../Transaction/Transaction";
import StatCard, { StatCardData } from "./StatCard";
import ExpandableSection, { ExpandableSectionData } from "./ExpandableSection";
import GetTokenSection from "./GetTokenSection";
import { TransactionProps } from "../../../@type/Transactions";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";

interface BaseStakeInfoProps {
  tokenName: string;
  tokenAddress: Hex;
  statCards: StatCardData[];
  expandableSections: ExpandableSectionData[];
  claimButton?: {
    transaction: TransactionProps;
    disabled: boolean;
    text: string;
  };
  getTokenVariant?: "primary" | "paper";
  statCardVariant?: "default" | "compact";
  expandableSectionVariant?: "default" | "compact";
  additionalContent?: ReactNode;
  onGetTokenClick?: () => void;
}

const BaseStakeInfo = ({
  tokenName,
  tokenAddress,
  statCards,
  expandableSections,
  claimButton,
  getTokenVariant = "primary",
  statCardVariant = "default",
  expandableSectionVariant = "default",
  additionalContent,
  onGetTokenClick,
}: BaseStakeInfoProps) => {
  const theme = useTheme();
  const navigate = useNavigate();

  const handleGetTokenClick = () => {
    if (onGetTokenClick) {
      onGetTokenClick();
      return;
    }

    if (tokenAddress === BERABORROW_ADDRESSES.sPollenToken.contractAddress) {
      navigate("/stake/wrapping/wrap");
    } else if (tokenAddress === BERABORROW_ADDRESSES.pollenLpToken.contractAddress) {
      window.open(
        "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet",
        "_blank"
      );
    } else if (tokenAddress === BERABORROW_ADDRESSES.pollenToken.contractAddress) {
      window.open("https://app.oogabooga.io/?fromToken=0X0000000000000000000000000000000000000000&toToken=0XC99E948E9D183848A6C4F5E6C1D225F02F171D79&amount=1", "_blank");
    }
  };

  return (
    <Box sx={{ width: "100%", maxWidth: "450px", mx: "auto" }}>
      <GetTokenSection tokenName={tokenName} tokenAddress={tokenAddress} variant={getTokenVariant} onClick={handleGetTokenClick} />

      {additionalContent}

      {statCards.length > 0 && (
        <Box sx={{ display: "flex", gap: "6px", mb: "6px" }}>
          {statCards.map((card) => (
            <StatCard key={card.label} {...card} variant={statCardVariant} />
          ))}
        </Box>
      )}

      {expandableSections.map((section) => (
        <ExpandableSection key={section.title} {...section} variant={expandableSectionVariant} />
      ))}

      {claimButton && !claimButton.disabled && (
        <TransactionButton
          transaction={claimButton.transaction}
          disabled={claimButton.disabled}
          sx={{
            borderRadius: 4,
            py: 1.5,
            fontSize: theme.typography.h3.fontSize,
            fontWeight: theme.typography.h3.fontWeight,
            "&:disabled": {
              backgroundColor: theme.palette.action.disabled,
              color: theme.palette.text.disabled,
            },
          }}
        >
          {claimButton.text}
        </TransactionButton>
      )}
    </Box>
  );
};

export default BaseStakeInfo;
