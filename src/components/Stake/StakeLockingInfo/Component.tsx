import React from "react";
import { Box, Typo<PERSON>, Button, Accordion, AccordionSummary, AccordionDetails, useTheme, useMediaQuery, Grid } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import WithSuspense from "../../../providers/WithSuspense";

const Component: React.FC = () => {
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));

  return (
    <Box pt={isMdUp ? "10px" : "8px"} px={2}>
      <Grid container pb={0.5} spacing={0.5}>
        <Grid item xs={4}>
          <Box
            sx={{
              textAlign: "center",
              padding: "6px",
              "@media (min-width:600px)": {
                padding: "10px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <Typography variant="subtitle1" fontSize={13}>
              Total Locked
            </Typography>
            <Typography variant="subtitle1" color="var(--text-primary)" fontSize={13}>
              $1.23M
            </Typography>
            <Typography variant="subtitle1" fontSize={11}>
              123.4K vePOLLEN
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={4}>
          <Box
            sx={{
              textAlign: "center",
              padding: "6px",
              "@media (min-width:600px)": {
                padding: "10px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <Typography variant="subtitle1" fontSize={13}>
              Your Multiplier{" "}
            </Typography>
            <Typography variant="subtitle1" color="var(--primary-main)" fontSize={13}>
              1x Multiplier
            </Typography>
            <Typography variant="subtitle1" fontSize={11}>
              123.4K vePOLLEN
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={4}>
          <Box
            sx={{
              textAlign: "center",
              padding: "6px",
              "@media (min-width:600px)": {
                padding: "10px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <Typography variant="subtitle1" fontSize={13}>
              Your Current APR
            </Typography>
            <Typography variant="subtitle1" color="var(--text-success)" fontSize={13}>
              1.23%
            </Typography>
            <Typography variant="subtitle1" fontSize={11}>
              Percentage Rate
            </Typography>
          </Box>
        </Grid>
      </Grid>
      <Accordion
        sx={{
          backgroundColor: theme.palette.background.paper,
          borderRadius: "12px !important",
          mb: 1,
        }}
        defaultExpanded
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography fontSize={16}>Your Position</Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ paddingBottom: 0 }}>
          <Box display="flex" justifyContent="space-between" mb={1}>
            <Typography color="text.secondary">POLLEN-BERA balance</Typography>
            <Typography>0.00</Typography>
          </Box>
          <Box display="flex" justifyContent="space-between">
            <Typography color="text.secondary">vePOLLEN balance</Typography>
            <Typography>0.00</Typography>
          </Box>
        </AccordionDetails>
      </Accordion>
      <Accordion
        sx={{
          backgroundColor: theme.palette.background.paper,
          borderRadius: "12px !important",
          mb: 1,
        }}
        defaultExpanded
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography fontSize={16}>Your Rewards</Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ paddingBottom: 0 }}>
          <Box display="flex" justifyContent="space-between" mb={1}>
            <Typography color="text.secondary">Protocol Fees</Typography>
            <Typography>$0.00</Typography>
          </Box>
          <Box display="flex" justifyContent="space-between">
            <Typography color="text.secondary">Pending Rewards</Typography>
            <Typography>$0.00</Typography>
          </Box>
        </AccordionDetails>
      </Accordion>

      <Button variant="contained" sx={{ marginTop: 2 }} fullWidth>
        Unlock
      </Button>
    </Box>
  );
};

const StakeLockingInfo = WithSuspense(Component, "paper");
export default StakeLockingInfo;
