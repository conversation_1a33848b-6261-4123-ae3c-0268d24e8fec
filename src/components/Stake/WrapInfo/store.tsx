import { atom, useAtom, useAtomValue } from "jotai";
import { getPollenBalanceAtom } from "../../../Atoms/Tokens";
import { getSPollenProtocolDataAtom, getSPollenBalanceAtom } from "../../../Atoms/Stake";

export const positionExpandedAtom = atom<boolean>(true);

export const useStakeInfoStoreLogic = () => {
  const [positionExpanded, setPositionExpanded] = useAtom(positionExpandedAtom);

  const { data: stakedBalance } = useAtomValue(getSPollenBalanceAtom);
  const { data: pollenBalance } = useAtomValue(getPollenBalanceAtom);
  const { data: protocolData } = useAtomValue(getSPollenProtocolDataAtom);

  const togglePosition = () => {
    setPositionExpanded(!positionExpanded);
  };

  return {
    positionExpanded,
    togglePosition,
    stakedBalance,
    pollenBalance,
    protocolData,
  };
};

export const getStakeInfoScopedAtoms = () => [positionExpandedAtom];
