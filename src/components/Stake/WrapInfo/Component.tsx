"use client";

import { Box, Typography, useTheme } from "@mui/material";
import WithSuspense from "../../../providers/WithSuspense";
import { formatToken } from "../../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import BaseStakeInfo from "../shared/BaseStakeInfo";
import { ExpandableSectionData, InfoRowData } from "../shared/ExpandableSection";
import { useStakeInfoStoreLogic } from "./store";

const Component = () => {
  const theme = useTheme();

  const { positionExpanded, togglePosition, stakedBalance, pollenBalance, protocolData } = useStakeInfoStoreLogic();

  const positionRows: InfoRowData[] = [
    {
      label: "POLLEN balance",
      value: pollenBalance ? formatToken(pollenBalance, 18, 4) : "1,234.56",
    },
    {
      label: "sPOLLEN balance",
      value: stakedBalance ? formatToken(stakedBalance, 18, 4) : "0.00",
    },
  ];

  const expandableSections: ExpandableSectionData[] = [
    {
      title: "Your Position",
      expanded: positionExpanded,
      onToggle: togglePosition,
      rows: positionRows,
    },
  ];

  const additionalContent = (
    <Box
      sx={{
        padding: "16px",
        mb: 2,
        borderRadius: 4,
        background: theme.palette.background.paper,
      }}
    >
      <Typography variant="h6" sx={{ mb: 2, color: theme.palette.text.primary, ...theme.typography.h4 }}>
        What is sPOLLEN?
      </Typography>
      <Typography variant="body1" sx={{ color: theme.palette.text.secondary, mb: 1, ...theme.typography.h5 }}>
        sPOLLEN is the staked version of POLLEN. Wrapping your POLLEN tokens allows you to:
      </Typography>
      <Box component="ul" sx={{ pl: 2, mt: 1, color: theme.palette.text.secondary, listStyleType: "disc", ...theme.typography.h5 }}>
        <Box component="li" sx={{ mb: 1 }}>
          Participate in single-sided staking
        </Box>
        <Box component="li" sx={{ mb: 1 }}>
          Lock for voting power and additional rewards
        </Box>
        <Box component="li">Maintain your position while earning rewards</Box>
      </Box>
    </Box>
  );

  const protocolStatsContent = protocolData && (
    <Box
      sx={{
        p: 3,
        mt: 2,
        borderRadius: 4,
        backgroundColor: theme.palette.background.paper,
      }}
    >
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: theme.palette.text.primary }}>
        Protocol Staking Stats
      </Typography>
      <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
        <Typography variant="body1" sx={{ color: theme.palette.text.secondary }}>
          Current APR
        </Typography>
        <Typography variant="body1" sx={{ fontWeight: 700, color: theme.palette.success.main }}>
          {protocolData?.stakingAprWeekly ? (Number(protocolData.stakingAprWeekly) / 1e16).toFixed(2) : "0.00"}%
        </Typography>
      </Box>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Typography variant="body1" sx={{ color: theme.palette.text.secondary }}>
          Total Wrapped
        </Typography>
        <Typography variant="body1" sx={{ fontWeight: 500, color: theme.palette.text.primary }}>
          {protocolData?.totalWrapped ? formatToken(protocolData.totalWrapped, 18, 2) : "0"} sPOLLEN
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ width: "100%", maxWidth: "450px", mx: "auto" }}>
      <BaseStakeInfo
        tokenName="POLLEN"
        tokenAddress={BERABORROW_ADDRESSES.pollenToken.contractAddress}
        statCards={[]}
        expandableSections={expandableSections}
        getTokenVariant="paper"
        statCardVariant="default"
        expandableSectionVariant="default"
        additionalContent={additionalContent}
      />

      {protocolStatsContent}
    </Box>
  );
};

const WrapInfo = WithSuspense(Component, "paper");
export default WrapInfo;
