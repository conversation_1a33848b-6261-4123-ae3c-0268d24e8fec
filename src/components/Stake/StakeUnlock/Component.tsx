import React from "react";
import { <PERSON>, <PERSON><PERSON>, Di<PERSON>r, <PERSON>rid, Typography } from "@mui/material";
import { formatToken } from "../../../utils/helpers";
import WithSuspense from "../../../providers/WithSuspense";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import StyledTextfield from "../../StyledTextField";
import TickerPill from "../../TickerPill";
import { AmountSelector } from "../../AmountSelector";
import UnlockDurationDisplay from "../LockForm/UnlockDurationDisplay";

const Component: React.FC = () => {
  const pollenBalance = BigInt(0);

  return (
    <>
      <Grid container pb={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1">Amount to Lock</Typography>
          <StyledTextfield
            defaultValue={0}
            onChange={() => {}}
            endComponent={
              <Box display="flex" flexDirection="column" alignItems="center">
                <TickerPill contractAddress={BERABORROW_ADDRESSES.pollenToken.contractAddress} ticker={BERABORROW_ADDRESSES.pollenToken.ticker} />
                <AmountSelector handleItemClick={() => {}} mt={1} ml={"auto"} />
              </Box>
            }
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="caption" fontSize={12}>
                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                    Wallet:
                  </Typography>{" "}
                  {formatToken(pollenBalance, BERABORROW_ADDRESSES.pollenToken.decimals, 2) + " POLLEN"}
                </Typography>
              </Box>
            }
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Typography variant="subtitle1">You will receive</Typography>
        <Box sx={{ display: "flex", gap: "5px" }}>
          <Typography variant="subtitle1" fontSize={14}>
            Lock Expires on:
          </Typography>
          <Typography variant="subtitle1" fontSize={14} color="var(--text-primary)">
            {new Date().toLocaleDateString()}
          </Typography>
        </Box>
      </Box>

      <StyledTextfield
        placeholder="0.0"
        disabled
        endComponent={<TickerPill contractAddress={BERABORROW_ADDRESSES.pollenToken.contractAddress} ticker={BERABORROW_ADDRESSES.pollenToken.ticker} />}
      />

      <UnlockDurationDisplay tokenType="spollen" />

      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />

      <Button variant="contained" fullWidth>
        Withdraw
      </Button>
    </>
  );
};

const StakeUnlock = WithSuspense(Component, "paper");
export default StakeUnlock;
