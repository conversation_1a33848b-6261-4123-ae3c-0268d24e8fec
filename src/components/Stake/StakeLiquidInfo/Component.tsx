"use client";

import { useTheme } from "@mui/material";
import WithSuspense from "../../../providers/WithSuspense";
import { formatToken } from "../../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import BaseStakeInfo from "../shared/BaseStakeInfo";
import { StatCardData } from "../shared/StatCard";
import { ExpandableSectionData, InfoRowData } from "../shared/ExpandableSection";
import { useStakeLiquidInfoStore } from "./store";

const Component = () => {
  const theme = useTheme();

  const { positionExpanded, rewardsExpanded, togglePosition, toggleRewards, sPollenBalance, stakedSPollenBalance, pendingRewards, claimButtonText, isClaimDisabled, protocolData } =
    useStakeLiquidInfoStore();

  const statCards: StatCardData[] = [
    {
      label: "Total Staked",
      value: protocolData?.totalStakingBalance ? formatToken(protocolData.totalStakingBalance, 18, 2) : "0",
      subtitle: protocolData?.totalStakingBalance ? `${formatToken(protocolData.totalStakingBalance, 18, 1)} sPOLLEN` : "0 sPOLLEN",
    },
    {
      label: "Current APR",
      value: protocolData?.stakingAprWeekly ? `${(Number(protocolData.stakingAprWeekly) / 1e16).toFixed(2)}%` : "0.00%",
      subtitle: "",
      valueColor: theme.palette.success.main,
      subtitleColor: theme.palette.success.main,
    },
  ];

  const positionRows: InfoRowData[] = [
    {
      label: "sPOLLEN balance",
      value: formatToken(sPollenBalance || 0n, 18, 4),
    },
    {
      label: "sPOLLEN staked",
      value: formatToken(stakedSPollenBalance || 0n, 18, 4),
    },
  ];

  const rewardRows: InfoRowData[] = [
    {
      label: "Auto-compounded POLLEN",
      value: "$0.00",
    },
    {
      label: "Pending Rewards",
      value: pendingRewards ? `${formatToken(pendingRewards, 18, 4)} POLLEN` : "0.00 POLLEN",
    },
  ];

  const expandableSections: ExpandableSectionData[] = [
    {
      title: "Your Position",
      expanded: positionExpanded,
      onToggle: togglePosition,
      rows: positionRows,
    },
    {
      title: "Your Rewards",
      expanded: rewardsExpanded,
      onToggle: toggleRewards,
      rows: rewardRows,
    },
  ];

  return (
    <BaseStakeInfo
      tokenName="sPOLLEN"
      tokenAddress={BERABORROW_ADDRESSES.sPollenToken.contractAddress}
      statCards={statCards}
      expandableSections={expandableSections}
      claimButton={{
        transaction: {
          type: "claimPollenRewards",
          variables: [],
        },
        disabled: isClaimDisabled,
        text: claimButtonText,
      }}
      getTokenVariant="primary"
      statCardVariant="default"
      expandableSectionVariant="default"
    />
  );
};

const StakeLiquidInfo = WithSuspense(Component, "paper");
export default StakeLiquidInfo;
