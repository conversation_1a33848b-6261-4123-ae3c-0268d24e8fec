import { atom, useAtom, useAtomValue } from "jotai";
import { getSPollenBalanceAtom, getStakedSPollenBalanceAtom, getSPollenProtocolDataAtom, getSPollenPendingRewardsAtom } from "../../../Atoms/Stake";
import { formatToken } from "../../../utils/helpers";

const positionExpandedAtom = atom<boolean>(false);
const rewardsExpandedAtom = atom<boolean>(false);

export const useStakeLiquidInfoStore = () => {
  const [positionExpanded, setPositionExpanded] = useAtom(positionExpandedAtom);
  const [rewardsExpanded, setRewardsExpanded] = useAtom(rewardsExpandedAtom);

  const { data: sPollenBalance } = useAtomValue(getSPollenBalanceAtom);
  const { data: stakedSPollenBalance } = useAtomValue(getStakedSPollenBalanceAtom);
  const { data: protocolData } = useAtomValue(getSPollenProtocolDataAtom);
  const { data: pendingRewards } = useAtomValue(getSPollenPendingRewardsAtom);

  const togglePosition = () => {
    setPositionExpanded(!positionExpanded);
  };

  const toggleRewards = () => {
    setRewardsExpanded(!rewardsExpanded);
  };

  const hasPendingRewards = pendingRewards && pendingRewards > 0n;
  const claimButtonText = hasPendingRewards ? `Claim ${formatToken(pendingRewards, 18, 4)} POLLEN` : "Claim";
  const isClaimDisabled = !hasPendingRewards;

  return {
    positionExpanded,
    rewardsExpanded,
    togglePosition,
    toggleRewards,
    sPollenBalance,
    stakedSPollenBalance,
    protocolData,
    pendingRewards,
    claimButtonText,
    isClaimDisabled,
  };
};

export const getStakeLiquidInfoScopedAtoms = () => [positionExpandedAtom, rewardsExpandedAtom];
