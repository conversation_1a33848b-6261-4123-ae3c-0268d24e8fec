import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";
import { getSPollenBalanceAtom, getVePollenBalanceAtom, lockDuration<PERSON>tom, getSPollenProtocolDataAtom, getUserLockPositionAtom, getLockingAprDataAtom } from "../../../Atoms/Stake";
import { formatToken } from "../../../utils/helpers";
import { updatePostHogEventAtom } from "../../../Atoms/Account";
import { useNotifications } from "../../../Hooks/useNotifications";

const positionExpandedAtom = atom<boolean>(false);
const rewardsExpandedAtom = atom<boolean>(false);

export type LockInfoDispatcherActionType = { type: "claimRewards" } | { type: "claimProtocolFees" };

export const useLockInfoStore = () => {
  const [positionExpanded, setPositionExpanded] = useAtom(positionExpandedAtom);
  const [rewardsExpanded, setRewardsExpanded] = useAtom(rewardsExpandedAtom);

  const { data: sPollenBalance } = useAtomValue(getSPollenBalanceAtom);
  const { data: vePollenBalance } = useAtomValue(getVePollenBalanceAtom);
  const { data: protocolData } = useAtomValue(getSPollenProtocolDataAtom);
  const { data: userLockPosition } = useAtomValue(getUserLockPositionAtom);
  const { data: aprData } = useAtomValue(getLockingAprDataAtom);
  const lockDuration = useAtomValue(lockDurationAtom);

  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const { addModal } = useNotifications();

  // Mock data - these should be fetched from contracts
  const protocolFees = 0n; // Should be fetched from fee distributor
  const pendingRewards = 0n; // Should be fetched from rewards contract

  // Calculate user multiplier based on lock duration
  const getMultiplier = (duration: number): number => {
    const multipliers: Record<number, number> = {
      30: 1.25,
      90: 2,
      180: 3,
      360: 4,
    };
    return multipliers[duration] || 1;
  };

  const userMultiplier = getMultiplier(lockDuration);

  const togglePosition = () => {
    setPositionExpanded(!positionExpanded);
  };

  const toggleRewards = () => {
    setRewardsExpanded(!rewardsExpanded);
  };

  const hasPendingRewards = (protocolFees && protocolFees > 0n) || (pendingRewards && pendingRewards > 0n);
  const totalRewards = (protocolFees || 0n) + (pendingRewards || 0n);
  const claimButtonText = hasPendingRewards ? `Claim $${formatToken(totalRewards, 18, 2)}` : "Claim";
  const isClaimDisabled = !hasPendingRewards;

  const dispatcher = (action: LockInfoDispatcherActionType) => {
    switch (action.type) {
      case "claimRewards":
        updatePostHogEvent({ type: "claimVeLockRewards" });
        // Here you would add the claim rewards modal/transaction
        addModal({
          id: "ClaimVeLockRewards",
          title: "Claim Rewards",
          Component: (
            <div>
              <p>Claim rewards functionality would be implemented here</p>
              <p>This would include both protocol fees and staking rewards</p>
            </div>
          ),
          maxWidth: "600px",
        });
        break;
      case "claimProtocolFees":
        updatePostHogEvent({ type: "claimProtocolFees" });
        // Separate claim for protocol fees only
        break;
      default:
        break;
    }
  };

  return {
    positionExpanded,
    rewardsExpanded,
    togglePosition,
    toggleRewards,
    sPollenBalance,
    vePollenBalance,
    protocolData,
    userLockPosition,
    aprData,
    userMultiplier,
    protocolFees,
    pendingRewards,
    claimButtonText,
    isClaimDisabled,
    dispatcher,
  };
};

export const getLockInfoScopedAtoms = () => [positionExpandedAtom, rewardsExpandedAtom];
