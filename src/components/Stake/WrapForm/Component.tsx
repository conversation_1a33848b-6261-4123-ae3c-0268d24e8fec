"use client";

import { Box, TextField, Typography } from "@mui/material";
import { formatToken } from "../../../utils/helpers";
import { AmountSelector } from "../../AmountSelector";
import TickerPill from "../../TickerPill";
import TransactionButton from "../../Transaction/Transaction";
import { ValidationButton } from "../../ValidationButton";
import { useStakeFormStore } from "./store";
import WithSuspense from "../../../providers/WithSuspense";

const Component = () => {
  const {
    balance,
    formValue,
    validationError,
    dispatcher,
    transactionType,
    buttonText,
    inputTokenSymbol,
    additionalText,
    needsApproval,
    theme,
    inputTokenAddress,
    parsedAmount,
    isDisabled,
    buttonStyle,
    outputTokenAddress,
    outputTokenSymbol,
    mode,
    collateralDetails,
  } = useStakeFormStore();

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 3,
        padding: 3,
        backgroundColor: theme.palette.background.paper,
        borderRadius: 2,
      }}
    >
      <Box sx={{ display: "flex", flexDirection: "column", gap: "6px" }}>
        <Typography variant="h4" sx={{ color: theme.palette.text.secondary }}>
          {mode === "deposit" ? "Amount to Wrap" : "Amount to Unwrap"}
        </Typography>

        <Box
          sx={{
            backgroundColor: theme.palette.background.paper,
            borderRadius: 3,
            padding: "12px",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", paddingBottom: 1 }}>
            <TextField
              value={formValue}
              onChange={(event) => dispatcher({ type: "setAmount", payload: event.target.value })}
              placeholder="0.0"
              error={!!validationError}
              sx={{
                paddingLeft: 0,
                flex: 1,
                "& .MuiOutlinedInput-root": {
                  backgroundColor: "transparent",
                  border: "none",
                  fontSize: theme.typography.h2.fontSize,
                  fontWeight: theme.typography.h2.fontWeight,
                  color: theme.palette.text.primary,
                  padding: 0,
                  paddingRight: "10px",

                  "& .MuiOutlinedInput-input": {
                    padding: 0,
                  },

                  "& fieldset": {
                    border: "none",
                  },
                  "& input::placeholder": {
                    color: theme.palette.text.secondary,
                    opacity: 1,
                  },
                },
              }}
            />
            <TickerPill contractAddress={inputTokenAddress} ticker={inputTokenSymbol} />
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
            <Typography variant="h6" sx={{ color: theme.palette.text.secondary }}>
              Wallet: {formatToken(balance, collateralDetails.decimals)} {inputTokenSymbol}
            </Typography>
            <AmountSelector handleItemClick={(percentage) => dispatcher({ type: "setPercentage", payload: percentage })} />
          </Box>
        </Box>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: "6px" }}>
        <Typography variant="h4" sx={{ color: theme.palette.text.secondary }}>
          You will receive
        </Typography>

        <Box
          sx={{
            backgroundColor: theme.palette.background.paper,
            borderRadius: 3,
            padding: "12px",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
            <Typography variant="h2" sx={{ color: theme.palette.text.primary }}>
              {formatToken(parsedAmount, collateralDetails.decimals, 4)}
            </Typography>
            <TickerPill contractAddress={outputTokenAddress} ticker={outputTokenSymbol} />
          </Box>
        </Box>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <Box display={needsApproval ? "block" : "none"}>
          <ValidationButton
            validationError={validationError}
            fullWidth
            variant="contained"
            onClick={() => dispatcher({ type: "approveWrapping" })}
            disabled={!!validationError}
            sx={buttonStyle}
          >
            Approve POLLEN
          </ValidationButton>
        </Box>
        <Box display={needsApproval ? "none" : "block"}>
          <TransactionButton
            transaction={{
              type: transactionType,
              variables: [parsedAmount],
            }}
            disabled={isDisabled}
            validationError={validationError}
            onConfirmation={() => dispatcher({ type: "confirmedTransaction" })}
            sx={buttonStyle}
          >
            {buttonText}
          </TransactionButton>
        </Box>

        {additionalText && (
          <Typography variant="body2" sx={{ color: theme.palette.text.secondary, textAlign: "center" }}>
            {additionalText}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

const WrapForm = WithSuspense(Component, "paper");
export default WrapForm;
