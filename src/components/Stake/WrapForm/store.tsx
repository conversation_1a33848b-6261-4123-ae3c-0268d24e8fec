import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { useHydrateAtoms } from "jotai/utils";
import { useLocation } from "react-router-dom";
import { useTheme } from "@mui/material";
import { useEffect, useRef } from "react";
import { formValidationsWrapAtom, formValidationsUnwrapAtom, formValuesWrapAtom, getPollenWrapAllowanceAtom, getSPollenBalanceAtom, wrapAmountAtom } from "../../../Atoms/Stake";
import { formatToken, regexDecimalToken } from "../../../utils/helpers";
import { getCollateralDetailsAtom, getPollenBalanceAtom } from "../../../Atoms/Tokens";
import { BERABORROW_ADDRESSES, QUERY_CLIENT } from "../../../utils/constants";
import { updatePostHogEventAtom } from "../../../Atoms/Account";
import { useNotifications } from "../../../Hooks/useNotifications";
import { GenericApproval, APPROVAL_CONFIGS } from "../../Approvals/GenericApproval";

export type StakeFormDispatcherActionType =
  | { type: "setAmount"; payload: string }
  | { type: "setPercentage"; payload: number }
  | { type: "confirmedTransaction" }
  | { type: "confirmedStake" } // Legacy - will be removed
  | { type: "approveWrapping" }
  | { type: "clearForm" };

export const useStakeFormStore = () => {
  const location = useLocation();
  const isWrapMode = location.pathname.includes("wrapping/wrap");

  const { data: pollenBalance } = useAtomValue(getPollenBalanceAtom);
  const { data: stakedBalance } = useAtomValue(getSPollenBalanceAtom);
  const { data: allowance } = useAtomValue(getPollenWrapAllowanceAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);

  const [formValue, setFormValue] = useAtom(formValuesWrapAtom);

  const validationError = useAtomValue(isWrapMode ? formValidationsWrapAtom : formValidationsUnwrapAtom);

  const amount = useAtomValue(wrapAmountAtom);

  const balance = isWrapMode ? pollenBalance : stakedBalance && stakedBalance > 0n ? stakedBalance : 0n;

  useHydrateAtoms([[formValuesWrapAtom, "0"]]);

  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const theme = useTheme();
  const { addModal } = useNotifications();

  const previousModeRef = useRef(isWrapMode);
  useEffect(() => {
    if (previousModeRef.current !== isWrapMode) {
      setFormValue("0");
      previousModeRef.current = isWrapMode;
    }
  }, [isWrapMode, setFormValue]);

  const dispatcher = (action: StakeFormDispatcherActionType) => {
    switch (action.type) {
      case "setAmount": {
        const sanitizedValue = regexDecimalToken(action.payload, collateralDetails.decimals);
        setFormValue(sanitizedValue);
        break;
      }
      case "setPercentage": {
        const calculatedAmount = (BigInt(action.payload) * balance) / 100n;
        const formattedAmount = formatToken(calculatedAmount, collateralDetails.decimals);
        const sanitizedAmount = regexDecimalToken(formattedAmount, collateralDetails.decimals);
        setFormValue(sanitizedAmount);
        setTimeout(() => setFormValue(sanitizedAmount), 0);
        break;
      }
      case "approveWrapping":
        updatePostHogEvent({ type: `approvePollenWrapping` });
        addModal({
          id: "ApprovePollenWrapping",
          title: "Approve POLLEN Wrapping",
          Component: (
            <GenericApproval
              amount={amount}
              config={APPROVAL_CONFIGS.pollenWrapping}
              onConfirmation={() => {
                dispatcher({ type: "confirmedTransaction" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      case "confirmedTransaction":
      case "confirmedStake":
        QUERY_CLIENT.invalidateQueries().then(() => {
          setFormValue("0");
        });
        break;
      case "clearForm":
        setFormValue("0");
        break;
      default:
        break;
    }
  };

  const transactionType = isWrapMode ? ("wrapPollen" as const) : ("unwrapPollen" as const);
  const buttonText = isWrapMode ? "Wrap POLLEN" : "Unwrap sPOLLEN";
  const inputTokenSymbol = isWrapMode ? "POLLEN" : "sPOLLEN";
  const outputTokenSymbol = isWrapMode ? "sPOLLEN" : "POLLEN";
  const additionalText = isWrapMode ? undefined : "Unwrapping will also claim any pending rewards";
  const needsApproval = isWrapMode && allowance < amount;

  const addresses = BERABORROW_ADDRESSES as typeof BERABORROW_ADDRESSES & { sPollenToken: { contractAddress: `0x${string}` } };
  const inputTokenAddress = isWrapMode ? BERABORROW_ADDRESSES.pollenToken.contractAddress : addresses.sPollenToken.contractAddress;
  const outputTokenAddress = isWrapMode ? addresses.sPollenToken.contractAddress : BERABORROW_ADDRESSES.pollenToken.contractAddress;

  const parsedAmount = amount;
  const isDisabled = !parsedAmount || parsedAmount === 0n || !!validationError;

  const buttonStyle = {
    borderRadius: 4,
    py: 1.5,
    fontSize: theme.typography.h3.fontSize,
    fontWeight: theme.typography.h3.fontWeight,
  };

  return {
    mode: isWrapMode ? "deposit" : ("withdraw" as const),
    balance,
    allowance,
    formValue,
    amount,
    validationError,
    dispatcher,
    transactionType,
    buttonText,
    inputTokenSymbol,
    outputTokenSymbol,
    additionalText,
    needsApproval,
    theme,
    inputTokenAddress,
    outputTokenAddress,
    parsedAmount,
    isDisabled,
    buttonStyle,
    collateralDetails,
  };
};
