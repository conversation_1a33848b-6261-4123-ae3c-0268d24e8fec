import { Theme } from "@mui/material/styles";

export const getStakeItemStyles = (theme: Theme) => ({
  container: () => ({
    borderRadius: "30px",
    backgroundColor: theme.palette.background.default,
    position: "relative",
    width: "100%",
    height: "345px",
    "&:hover": {
      backgroundColor: theme.palette.background.paper,
    },
  }),
  badgeContainer: {
    display: "flex",
    gap: 1,
    position: "absolute",
    top: -15,
    left: "50%",
    transform: "translateX(-50%)",
    zIndex: 1,
  },
  chip: (backgroundColor: string) => ({
    backgroundColor,
    color: theme.palette.text.primary,
    ...theme.typography.body1,
    height: "27px",
    borderRadius: "14px",
  }),
  content: {
    p: "10px",
    textAlign: "center",
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    height: "100%",
  },
  iconContainer: (gap = "1px") => ({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    mb: "10px",
    mt: "5px",
    position: "relative",
    height: "50px",
    gap,
  }),
  iconBox: (size = 48) => ({
    width: size,
    height: size,
    borderRadius: "50%",
    overflow: "hidden",
  }),
  overlappingContainer: {
    position: "relative",
    width: 60,
    height: 48,
  },
  overlappingIcon: (left: number, zIndex: number) => ({
    position: "absolute",
    left,
    top: 0,
    width: 48,
    height: 48,
    borderRadius: "50%",
    overflow: "hidden",
    zIndex,
  }),
  arrow: {
    fontSize: 24,
    color: theme.palette.custom.textDisabled,
    mx: "4px",
  },
  title: {
    color: theme.palette.text.primary,
    mb: "10px",
    ...theme.typography.h1,
  },
  description: {
    color: theme.palette.text.secondary,
    ...theme.typography.body1,
    mb: "16px",
    px: 5,
  },
  button: {
    background: `linear-gradient(to bottom, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
    color: theme.palette.text.primary,
    ...theme.typography.h4,
    py: 2,
    height: "56px",
    border: `2px solid ${theme.palette.custom.textDisabled}`,
    borderRadius: 2,
    // textTransform: "none",
    textDecoration: "none",
    "&:hover": {
      color: theme.palette.text.primary,
      background: `linear-gradient(to bottom, ${theme.palette.secondary.main}, #b73f00)`,
    },
  },
  bottomLinkContainer: {
    mt: 3,
    p: "10px",
    backgroundColor: theme.palette.custom.backgroundQuaternary,
    borderRadius: "20px",
    border: `2px solid ${theme.palette.text.disabled}`,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  bottomLink: {
    display: "flex",
    alignItems: "center",
    py: "14px",
    background: theme.palette.text.disabled,
    border: `2px solid ${theme.palette.text.disabled}`,
    width: "100%",
    borderRadius: "14px",
    justifyContent: "center",
    gap: "6px",
    textDecoration: "none",
    ...theme.typography.h5,
    "&:hover": {
      textDecoration: "none",
    },
  },
  bottomLinkText: {
    color: theme.palette.custom.textDisabled,
    fontWeight: "medium",
  },
  bottomLinkHighlight: {
    color: theme.palette.primary.main,
    fontWeight: "medium",
    "&:hover": {
      textDecoration: "underline",
    },
  },
  statsContainer: {
    backgroundColor: theme.palette.background.default,
    borderRadius: "20px",
    border: `2px solid ${theme.palette.text.disabled}`,
    p: "10px",
    mt: "auto",
  },
  statsDivider: {
    backgroundColor: theme.palette.text.disabled,
    height: "2px",
    width: "calc(100% + 20px)",
    transform: "translateX(-10px)",
    mb: "16px",
    mt: "10px",
  },
});
