"use client";

import type React from "react";
import { <PERSON>, But<PERSON>, Chip, Typography, <PERSON>, useTheme } from "@mui/material";
import { useNavigate } from "react-router-dom";
import WithSuspense from "../../../providers/WithSuspense";
import ArrowOutwardIcon from "@mui/icons-material/ArrowOutward";
import { StakeItemType, stakeItemConfigs } from "../../../Routes/Stake/stakeItemConfigs";
import { getStakeItemStyles } from "./styles";

interface StakeItemProps {
  type: StakeItemType;
  statsSection: React.ReactNode;
}

const IconImage: React.FC<{ src: string; alt: string; size?: number }> = ({ src, alt, size = 48 }) => <img src={src || "/placeholder.svg"} alt={alt} width={size} height={size} />;

const Badge: React.FC<{ label: string; backgroundColor: string; borderColor: string }> = ({ label, backgroundColor, borderColor }) => {
  const theme = useTheme();
  const styles = getStakeItemStyles(theme);

  return (
    <Chip
      label={label}
      sx={{
        ...styles.chip(backgroundColor),
        border: `2px solid ${borderColor}`,
      }}
    />
  );
};

const IconSection: React.FC<{ type: StakeItemType; icons: any[] }> = ({ type, icons }) => {
  const theme = useTheme();
  const styles = getStakeItemStyles(theme);

  if (type === "wrap") {
    return (
      <>
        <Box sx={styles.iconBox()}>
          <IconImage src={icons[0].path} alt={icons[0].alt} />
        </Box>
        <Typography sx={styles.arrow}>→</Typography>
        <Box sx={styles.iconBox()}>
          <IconImage src={icons[1].path} alt={icons[1].alt} />
        </Box>
      </>
    );
  }

  if (icons.length === 1) {
    return (
      <Box sx={styles.iconBox()}>
        <IconImage src={icons[0].path} alt={icons[0].alt} />
      </Box>
    );
  }

  return (
    <Box sx={styles.overlappingContainer}>
      <Box sx={styles.overlappingIcon(0, 2)}>
        <IconImage src={icons[0].path} alt={icons[0].alt} />
      </Box>
      <Box sx={styles.overlappingIcon(30, 1)}>
        <IconImage src={icons[1].path} alt={icons[1].alt} />
      </Box>
    </Box>
  );
};

const BottomLink: React.FC<{ bottomLink: any }> = ({ bottomLink }) => {
  const theme = useTheme();
  const styles = getStakeItemStyles(theme);

  return (
    <Box sx={styles.bottomLinkContainer}>
      <Link href={bottomLink.href} sx={styles.bottomLink}>
        {bottomLink.icons?.[0] && <IconImage src={bottomLink.icons[0]} alt="" size={18} />}
        <Typography component="span" sx={styles.bottomLinkText}>
          {bottomLink.text}
        </Typography>
        <Typography component="span" sx={styles.bottomLinkHighlight}>
          here
        </Typography>
        <ArrowOutwardIcon sx={{ fontSize: 16, color: theme.palette.primary.main }} />
      </Link>
    </Box>
  );
};

const Component: React.FC<StakeItemProps> = ({ type, statsSection }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const styles = getStakeItemStyles(theme);
  const config = stakeItemConfigs[type];

  return (
    <>
      <Box sx={styles.container}>
        <Box sx={styles.badgeContainer}>
          <Badge label={config.badge.label} backgroundColor={config.badge.backgroundColor} borderColor={config.badge.borderColor} />
          {config.badge.secondary && <Badge label={config.badge.secondary} backgroundColor={theme.palette.custom.backgroundTertiarySolid} borderColor="#3DB2502A" />}
        </Box>

        <Box sx={styles.content}>
          <Box sx={styles.iconContainer(type === "wrap" ? "1px" : "0px")}>
            <IconSection type={type} icons={config.icons} />
          </Box>

          <Typography variant="h5" sx={styles.title}>
            {config.title}
          </Typography>

          <Typography sx={styles.description}>{config.description}</Typography>

          <Box sx={styles.statsContainer}>
            {statsSection}

            <Box sx={styles.statsDivider} />

            <Button fullWidth onClick={() => navigate(config.route)} sx={styles.button}>
              {config.buttonLabel}
            </Button>
          </Box>
        </Box>
      </Box>

      {config.bottomLink && <BottomLink bottomLink={config.bottomLink} />}
    </>
  );
};

const StakeItem = WithSuspense(Component, "paper");
export default StakeItem;
