"use client";

import { Box, Typography } from "@mui/material";
import WithSuspense from "../../../providers/WithSuspense";
import { formatToken } from "../../../utils/helpers";
import { StakeFormBase } from "../shared/StakeFormBase";
import { useLpLiquidStakeFormStore } from "./store";

const Component = () => {
  const storeData = useLpLiquidStakeFormStore();
  const { balance, theme, mode, balanceLabel, collateralDetails, dispatcher } = storeData;

  const customHeader = (
    <Box
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        mb: "8px",
      }}
    >
      <Typography variant="h5" sx={{ color: theme.palette.text.secondary }}>
        Amount to {mode}
      </Typography>
      <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
        {balanceLabel}: {formatToken(balance || 0n, collateralDetails?.decimals || 18, 4)}
      </Typography>
    </Box>
  );

  return (
    <StakeFormBase
      formData={storeData}
      handlers={{
        handleAmountChange: (value: string) => dispatcher({ type: "setAmount", payload: value }),
        handlePercentageClick: (percentage: number) => dispatcher({ type: "setPercentage", payload: BigInt(percentage) }),
        dispatcher,
      }}
      customHeader={customHeader}
      showEstimatedApr={false}
      approvalTokenName="BERA-POLLEN"
    />
  );
};

const LpLiquidStakeForm = WithSuspense(Component, "paper");
export default LpLiquidStakeForm;
