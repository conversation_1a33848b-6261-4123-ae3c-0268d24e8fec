import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { useLocation } from "react-router-dom";
import { useTheme } from "@mui/material";
import { useEffect, useRef } from "react";
import {
  getLpPollenBalanceAtom,
  getStakedLpPollenBalanceAtom,
  getLpPollenStakingAllowanceAtom,
  formValidationsLpLiquidStakeAtom,
  formValidationsLpLiquidUnstakeAtom,
  formValuesLpLiquidStakeAtom,
  lpLiquidStakeAmountAtom,
} from "../../../Atoms/Stake";
import { formatToken, regexDecimalToken } from "../../../utils/helpers";
import { getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import { updatePostHogEventAtom } from "../../../Atoms/Account";
import { useNotifications } from "../../../Hooks/useNotifications";
import { GenericApproval, APPROVAL_CONFIGS } from "../../Approvals/GenericApproval";

export type LpLiquidStakeFormDispatcherActionType =
  | { type: "setAmount"; payload: string }
  | { type: "setPercentage"; payload: bigint }
  | { type: "approveStaking" }
  | { type: "confirmedTransaction" }
  | { type: "clearForm" };

export const useLpLiquidStakeFormStore = () => {
  const location = useLocation();
  const isStakeMode = location.pathname.endsWith("/stake");

  const { data: lpPollenBalance } = useAtomValue(getLpPollenBalanceAtom);
  const { data: stakedLpPollenBalance } = useAtomValue(getStakedLpPollenBalanceAtom);
  const { data: allowance } = useAtomValue(getLpPollenStakingAllowanceAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);

  const [formValue, setFormValue] = useAtom(formValuesLpLiquidStakeAtom);

  const validationError = useAtomValue(isStakeMode ? formValidationsLpLiquidStakeAtom : formValidationsLpLiquidUnstakeAtom);

  const amount = useAtomValue(lpLiquidStakeAmountAtom);

  const balance = isStakeMode ? lpPollenBalance : stakedLpPollenBalance;

  const theme = useTheme();
  const { addToast, addModal } = useNotifications();
  const setUpdatePostHogEvent = useSetAtom(updatePostHogEventAtom);

  const previousModeRef = useRef(isStakeMode);
  useEffect(() => {
    if (previousModeRef.current !== isStakeMode) {
      setFormValue("0");
      previousModeRef.current = isStakeMode;
    }
  }, [isStakeMode, setFormValue]);

  const dispatcher = (action: LpLiquidStakeFormDispatcherActionType) => {
    switch (action.type) {
      case "setAmount":
        setFormValue(regexDecimalToken(action.payload, collateralDetails.decimals));
        break;
      case "setPercentage":
        if (balance) {
          const percentage = Number(action.payload);
          const newAmount = (balance * BigInt(percentage)) / 100n;
          setFormValue(formatToken(newAmount, BERABORROW_ADDRESSES.pollenLpToken.decimals));
        }
        break;
      case "approveStaking":
        setUpdatePostHogEvent({ type: `approveLpPollenStaking` });
        addModal({
          id: "ApproveLpPollenStaking",
          title: "Approve BERA-POLLEN Staking",
          Component: (
            <GenericApproval
              amount={amount}
              config={APPROVAL_CONFIGS.lpPollenStaking}
              onConfirmation={() => {
                dispatcher({ type: "confirmedTransaction" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      case "confirmedTransaction":
        setFormValue("0");
        addToast({
          id: "lp-liquid-stake-success",
          message: isStakeMode ? "BERA-POLLEN staked successfully!" : "BERA-POLLEN unstaked successfully!",
          severity: "success",
        });
        setUpdatePostHogEvent({
          distinctId: "lp-liquid-stake",
          event: isStakeMode ? "lp_liquid_stake_success" : "lp_liquid_unstake_success",
          amount: formatToken(amount, BERABORROW_ADDRESSES.pollenLpToken.decimals),
        });
        break;
      case "clearForm":
        setFormValue("0");
        break;
    }
  };

  const transactionType = isStakeMode ? ("stakeLpPollen" as const) : ("unstakeLpPollen" as const);
  const buttonText = isStakeMode ? "Stake BERA-POLLEN" : "Unstake BERA-POLLEN";
  const inputTokenSymbol = "BERA-POLLEN";
  const needsApproval = isStakeMode && allowance < amount;
  const balanceLabel = isStakeMode ? "Wallet" : "Staked";

  const inputTokenAddress = BERABORROW_ADDRESSES.pollenLpToken.contractAddress;

  const parsedAmount = amount;
  const isDisabled = !!validationError || amount === 0n;

  const buttonStyle = {
    borderRadius: 4,
    py: 1.5,
    fontSize: theme.typography.h3.fontSize,
    fontWeight: theme.typography.h3.fontWeight,
    "&:disabled": {
      backgroundColor: theme.palette.action.disabled,
      color: theme.palette.text.disabled,
    },
  };

  const mode = isStakeMode ? "stake" : "unstake";

  return {
    balance,
    formValue,
    validationError,
    dispatcher,
    transactionType,
    buttonText,
    inputTokenSymbol,
    needsApproval,
    theme,
    inputTokenAddress,
    parsedAmount,
    isDisabled,
    buttonStyle,
    mode,
    balanceLabel,
    collateralDetails,
  };
};
