import { Box, Container, Grid, Paper, useTheme } from "@mui/material";
import { Outlet } from "react-router-dom";
import Toggler from "../../Toggler";
import StakeHeader from "./StakeHeader";

interface StakeLayoutProps {
  title: string;
  description: string;
  togglers: Array<{ label: string; link: string }>;
  InfoComponent: React.ComponentType;
}

const StakeLayout = ({ title, description, togglers, InfoComponent }: StakeLayoutProps) => {
  const theme = useTheme();

  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column", justifyContent: "start" }}>
      <Container
        maxWidth="md"
        sx={{
          py: 2,
          justifyContent: "start",
          [theme.breakpoints.down("md")]: {
            paddingX: "8px",
          },
        }}
      >
        <StakeHeader title={title} description={description} />

        <Paper
          sx={{
            backgroundColor: "var(--background-tertiary)",
            padding: "10px",
            backdropFilter: {
              xs: "none",
              md: "blur(5px)",
            },
          }}
        >
          <Box>
            <Grid container spacing={1}>
              <Grid item xs={12} md={6}>
                <Paper>
                  <Toggler togglers={togglers} />
                  <Outlet />
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <InfoComponent />
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default StakeLayout;
