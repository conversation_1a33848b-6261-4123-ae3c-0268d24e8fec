import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import { Box, Button, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";

interface StakeHeaderProps {
  title: string;
  description: string;
}

const StakeHeader = ({ title, description }: StakeHeaderProps) => {
  const navigate = useNavigate();

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        flexDirection: "column",
        gap: "40px",
        marginBottom: "50px",
        position: "relative",
        paddingX: "30px",
      }}
    >
      <Button
        onClick={() => navigate("/stake")}
        startIcon={<ArrowBackIosNewIcon sx={{ fontSize: 14 }} />}
        sx={{
          position: {
            md: "absolute",
          },
          left: "0px",
          top: "-10px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          padding: "10px 14px",
          gap: "10px",
          borderRadius: "48px",
          border: "1px solid var(--border-color)",
          fontSize: "14px",
          color: "text.secondary",
        }}
      >
        Back
      </Button>
      <Typography sx={{ fontWeight: 600, fontSize: "34px" }}>{title}</Typography>
      <Typography sx={{ color: "text.secondary", fontSize: "14px", textAlign: "center" }}>{description}</Typography>
    </Box>
  );
};

export default StakeHeader;
