import React, { useState } from "react";
import { Box, Button, Chip, Divider, Grid, MenuItem, Select, Typography } from "@mui/material";
import { formatToken } from "../../../utils/helpers";
import WithSuspense from "../../../providers/WithSuspense";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import StyledTextfield from "../../StyledTextField";
import TickerPill from "../../TickerPill";
import { AmountSelector } from "../../AmountSelector";
import { ExpandMore } from "@mui/icons-material";

const lockDurations = [
  { key: 1, value: 30, multi: 1.25 },
  { key: 2, value: 90, multi: 2 },
  { key: 3, value: 180, multi: 3 },
  { key: 4, value: 360, multi: 4 },
];

const Component: React.FC = () => {
  const pollenBalance = BigInt(0);
  const [days, setDays] = useState<number>(30);

  return (
    <>
      <Grid container pb={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1">Amount to Lock</Typography>
          <StyledTextfield
            defaultValue={0}
            onChange={() => {}}
            endComponent={
              <Box display="flex" flexDirection="column" alignItems="center">
                <TickerPill contractAddress={BERABORROW_ADDRESSES.pollenToken.contractAddress} ticker={BERABORROW_ADDRESSES.pollenToken.ticker} />
                <AmountSelector handleItemClick={() => {}} mt={1} ml={"auto"} />
              </Box>
            }
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="caption" fontSize={12}>
                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                    Wallet:
                  </Typography>{" "}
                  {formatToken(pollenBalance, BERABORROW_ADDRESSES.pollenToken.decimals, 2) + " POLLEN"}
                </Typography>
              </Box>
            }
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Typography variant="subtitle1">You will receive</Typography>
        <Box sx={{ display: "flex", gap: "5px" }}>
          <Typography variant="subtitle1" fontSize={14}>
            Lock Expires on:
          </Typography>
          <Typography variant="subtitle1" fontSize={14} color="var(--text-primary)">
            {new Date().toLocaleDateString()}
          </Typography>
        </Box>
      </Box>
      <StyledTextfield
        placeholder="0.0"
        disabled
        endComponent={<TickerPill contractAddress={BERABORROW_ADDRESSES.pollenToken.contractAddress} ticker={BERABORROW_ADDRESSES.pollenToken.ticker} />}
      />

      <Box>
        <Typography variant="subtitle1">Lock Duration</Typography>
        <Select
          MenuProps={{
            anchorOrigin: {
              vertical: "bottom",
              horizontal: "left",
            },
            transformOrigin: {
              vertical: "top",
              horizontal: "left",
            },
          }}
          fullWidth
          value={days ?? 30}
          onChange={(event) => {
            setDays(Number(event.target.value));
          }}
          IconComponent={ExpandMore}
        >
          {lockDurations.map((info) => (
            <MenuItem key={info.key} value={info.value}>
              <Box sx={{ display: "flex", alignItems: "center", width: "100%", gap: "10px" }}>
                <Typography fontSize={16}>{info.value === 360 ? "1 year" : info.value + " days"}</Typography>
                <Chip
                  label={`${info.multi}x Multiplier`}
                  sx={{
                    marginTop: "2px",
                    backgroundColor: "rgba(236, 111, 21, 0.20)",
                    color: "#ec6f15",
                    border: `1.2px solid $rgba(236, 111, 21, 0.7)`,
                    padding: "5px 8.3px",
                  }}
                />
              </Box>
            </MenuItem>
          ))}
        </Select>
      </Box>

      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />

      <Button variant="contained" fullWidth>
        Lock
      </Button>
    </>
  );
};

const StakeLock = WithSuspense(Component, "paper");
export default StakeLock;
