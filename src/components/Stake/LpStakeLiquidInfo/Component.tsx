"use client";

import { useTheme } from "@mui/material";
import WithSuspense from "../../../providers/WithSuspense";
import { formatToken } from "../../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import BaseStakeInfo from "../shared/BaseStakeInfo";
import { StatCardData } from "../shared/StatCard";
import { ExpandableSectionData, InfoRowData } from "../shared/ExpandableSection";
import { useLpStakeLiquidInfoStore } from "./store";

const Component = () => {
  const theme = useTheme();

  const BERA_POLLEN_DECIMALS = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.pollenLpToken.contractAddress].decimals;

  const {
    positionExpanded,
    rewardsExpanded,
    togglePosition,
    toggleRewards,
    lpPollenBalance,
    stakedLpPollenBalance,
    pendingRewards,
    claimButtonText,
    isClaimDisabled,
    protocolData,
  } = useLpStakeLiquidInfoStore();

  const statCards: StatCardData[] = [
    {
      label: "Total Staked",
      value: protocolData?.totalStakingBalance ? formatToken(protocolData.totalStakingBalance, BERA_POLLEN_DECIMALS, 2) : "0",
      subtitle: protocolData?.totalStakingBalance ? `${formatToken(protocolData.totalStakingBalance, BERA_POLLEN_DECIMALS, 1)} BERA-POLLEN` : "0 BERA-POLLEN",
    },
    {
      label: "Current APR",
      value: protocolData?.stakingAprWeekly ? `${(Number(protocolData.stakingAprWeekly) / 1e16).toFixed(2)}%` : "0.00%",
      subtitle: "",
      valueColor: theme.palette.success.main,
      subtitleColor: theme.palette.success.main,
    },
  ];

  const positionRows: InfoRowData[] = [
    {
      label: "BERA-POLLEN balance",
      value: lpPollenBalance ? formatToken(lpPollenBalance, BERA_POLLEN_DECIMALS, 4) : "0",
    },
    {
      label: "BERA-POLLEN staked",
      value: stakedLpPollenBalance ? formatToken(stakedLpPollenBalance, BERA_POLLEN_DECIMALS, 4) : "0",
    },
  ];

  const rewardsRows: InfoRowData[] = [
    {
      label: "Pending rewards",
      value: pendingRewards ? formatToken(pendingRewards, BERA_POLLEN_DECIMALS, 4) + " POLLEN" : "0 POLLEN",
    },
  ];

  const expandableSections: ExpandableSectionData[] = [
    {
      title: "Your Position",
      expanded: positionExpanded,
      onToggle: togglePosition,
      rows: positionRows,
    },
    {
      title: "Rewards",
      expanded: rewardsExpanded,
      onToggle: toggleRewards,
      rows: rewardsRows,
    },
  ];

  return (
    <BaseStakeInfo
      tokenName="BERA-POLLEN"
      tokenAddress={BERABORROW_ADDRESSES.pollenLpToken.contractAddress}
      statCards={statCards}
      expandableSections={expandableSections}
      claimButton={{
        transaction: {
          type: "claimLpPollenRewards",
          variables: [],
        },
        disabled: isClaimDisabled,
        text: claimButtonText,
      }}
      getTokenVariant="primary"
      statCardVariant="compact"
      expandableSectionVariant="compact"
    />
  );
};

const LpStakeLiquidInfo = WithSuspense(Component, "paper");
export default LpStakeLiquidInfo;
