import { atom, useAtom, useAtomValue } from "jotai";
import { getLpPollenBalanceAtom, getStakedLpPollenBalanceAtom, getLpProtocolDataAtom, getLpPollenPendingRewardsAtom } from "../../../Atoms/Stake";
import { formatToken } from "../../../utils/helpers";

const positionExpandedAtom = atom<boolean>(false);
const rewardsExpandedAtom = atom<boolean>(false);

export const useLpStakeLiquidInfoStore = () => {
  const [positionExpanded, setPositionExpanded] = useAtom(positionExpandedAtom);
  const [rewardsExpanded, setRewardsExpanded] = useAtom(rewardsExpandedAtom);

  const { data: lpPollenBalance } = useAtomValue(getLpPollenBalanceAtom);
  const { data: stakedLpPollenBalance } = useAtomValue(getStakedLpPollenBalanceAtom);
  const { data: protocolData } = useAtomValue(getLpProtocolDataAtom);
  const { data: pendingRewards } = useAtomValue(getLpPollenPendingRewardsAtom);

  const togglePosition = () => {
    setPositionExpanded(!positionExpanded);
  };

  const toggleRewards = () => {
    setRewardsExpanded(!rewardsExpanded);
  };

  const hasPendingRewards = pendingRewards && pendingRewards > 0n;
  const claimButtonText = hasPendingRewards ? `Claim ${formatToken(pendingRewards, 18, 4)} POLLEN` : "Claim";
  const isClaimDisabled = !hasPendingRewards;

  return {
    positionExpanded,
    rewardsExpanded,
    togglePosition,
    toggleRewards,
    lpPollenBalance,
    stakedLpPollenBalance,
    protocolData,
    pendingRewards,
    claimButtonText,
    isClaimDisabled,
  };
};

export const getLpStakeLiquidInfoScopedAtoms = () => [positionExpandedAtom, rewardsExpandedAtom];
