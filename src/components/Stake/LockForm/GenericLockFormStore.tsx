import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { useHydrateAtoms } from "jotai/utils";
import { useLocation } from "react-router-dom";
import { useTheme } from "@mui/material";
import { useEffect, useRef } from "react";
import {
  getSPollenBalanceAtom,
  getLpPollenBalanceAtom,
  getVePollenBalanceAtom,
  getSPollenLockAllowanceAtom,
  getLpPollenLockAllowanceAtom,
  formValidationsLockAtom,
  formValidationsUnlockAtom,
  formValidationsLpLockAtom,
  formValidationsLpUnlockAtom,
  formValuesLockAtom,
  lockAmountAtom,
  unlockAmountAtom,
  lockDurationAtom,
  lockExpiryAtom,
} from "../../../Atoms/Stake";
import { formatToken, regexDecimalToken } from "../../../utils/helpers";
import { getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import { updatePostHogEventAtom } from "../../../Atoms/Account";
import { useNotifications } from "../../../Hooks/useNotifications";
import { GenericApproval, APPROVAL_CONFIGS } from "../../Approvals/GenericApproval";
import { TransactionProps } from "../../../@type/Transactions";

export type LockFormDispatcherActionType =
  | { type: "setAmount"; payload: string }
  | { type: "setPercentage"; payload: number }
  | { type: "setLockDuration"; payload: number }
  | { type: "approveLocking" }
  | { type: "confirmedTransaction" }
  | { type: "clearForm" };

type LockTokenType = "spollen" | "lp";

export const useGenericLockFormStore = (tokenType: LockTokenType) => {
  const location = useLocation();
  const isLockMode = location.pathname.endsWith("/lock");

  const isLpToken = tokenType === "lp";

  const { data: sPollenBalance } = useAtomValue(getSPollenBalanceAtom);
  const { data: lpPollenBalance } = useAtomValue(getLpPollenBalanceAtom);
  const { data: vePollenBalance } = useAtomValue(getVePollenBalanceAtom);

  const { data: sPollenAllowance } = useAtomValue(getSPollenLockAllowanceAtom);
  const { data: lpPollenAllowance } = useAtomValue(getLpPollenLockAllowanceAtom);

  const sPollenDetails = useAtomValue(getCollateralDetailsAtom);

  const inputBalance = isLockMode ? (isLpToken ? lpPollenBalance : sPollenBalance) : vePollenBalance;

  const allowance = isLpToken ? lpPollenAllowance : sPollenAllowance;

  const collateralDetails = isLockMode ? (isLpToken ? BERABORROW_ADDRESSES.pollenLpToken : sPollenDetails) : { ...BERABORROW_ADDRESSES.sPollenToken, decimals: 18 }; // vePOLLEN has 18 decimals

  const validationError = useAtomValue(
    isLockMode ? (isLpToken ? formValidationsLpLockAtom : formValidationsLockAtom) : isLpToken ? formValidationsLpUnlockAtom : formValidationsUnlockAtom
  );

  const [formValue, setFormValue] = useAtom(formValuesLockAtom);
  const [lockDuration, setLockDuration] = useAtom(lockDurationAtom);

  const lockExpiry = useAtomValue(lockExpiryAtom);
  const lockAmount = useAtomValue(lockAmountAtom);
  const unlockAmount = useAtomValue(unlockAmountAtom);
  const amount = isLockMode ? lockAmount : unlockAmount;

  const balance = inputBalance;

  useHydrateAtoms([
    [formValuesLockAtom, "0"],
    [lockDurationAtom, 30],
  ]);

  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const theme = useTheme();
  const { addModal } = useNotifications();

  const previousModeRef = useRef(isLockMode);
  useEffect(() => {
    if (previousModeRef.current !== isLockMode) {
      setFormValue("0");
      previousModeRef.current = isLockMode;
    }
  }, [isLockMode, setFormValue]);

  const getMultiplier = (duration: number): number => {
    const multipliers: Record<number, number> = {
      30: 1.25,
      90: 2,
      180: 3,
      360: 4,
    };
    return multipliers[duration] || 1.25;
  };

  const vePollenAmount = (amount * 100n) / BigInt(Math.floor(getMultiplier(lockDuration) * 100));

  const sPollenUnlockAmount = (amount * BigInt(Math.floor(getMultiplier(lockDuration) * 100))) / 100n;

  const dispatcher = (action: LockFormDispatcherActionType) => {
    switch (action.type) {
      case "setAmount": {
        const sanitizedValue = regexDecimalToken(action.payload, collateralDetails.decimals);
        setFormValue(sanitizedValue);
        break;
      }
      case "setPercentage": {
        const calculatedAmount = (BigInt(action.payload) * (balance || 0n)) / 100n;
        const formattedAmount = formatToken(calculatedAmount, collateralDetails.decimals);
        setFormValue(regexDecimalToken(formattedAmount, collateralDetails.decimals));
        break;
      }
      case "setLockDuration": {
        setLockDuration(action.payload);
        break;
      }
      case "approveLocking": {
        const approvalConfig = isLpToken ? APPROVAL_CONFIGS.lpPollenLocking : APPROVAL_CONFIGS.sPollenLocking;
        const eventType = isLpToken ? "approveLpPollenLocking" : "approveSPollenLocking";
        const modalTitle = isLpToken ? "Approve BERA-POLLEN Locking" : "Approve sPOLLEN Locking";

        updatePostHogEvent({ type: eventType });
        addModal({
          id: approvalConfig.modalId,
          title: modalTitle,
          Component: (
            <GenericApproval
              amount={amount}
              config={approvalConfig}
              additionalVariables={[BigInt(lockExpiry)]}
              onConfirmation={() => {
                dispatcher({ type: "confirmedTransaction" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      }
      case "confirmedTransaction":
        setFormValue("0");
        break;
      case "clearForm":
        setFormValue("0");
        break;
      default:
        break;
    }
  };

  const mode = isLockMode ? "lock" : "unlock";
  const transactionType = isLockMode
    ? isLpToken
      ? ("increaseLpLockPosition" as const)
      : ("increaseLockPosition" as const)
    : isLpToken
      ? ("withdrawLpVotingEscrow" as const)
      : ("withdrawVotingEscrow" as const);
  const buttonText = isLockMode ? "Lock" : "Unlock";

  const inputTokenSymbol = isLockMode ? (isLpToken ? "BERA-POLLEN" : "sPOLLEN") : isLpToken ? "veBERA-POLLEN" : "vePOLLEN";
  const outputTokenSymbol = isLockMode ? (isLpToken ? "veBERA-POLLEN" : "vePOLLEN") : isLpToken ? "BERA-POLLEN" : "sPOLLEN";

  const inputTokenAddress = isLockMode
    ? isLpToken
      ? BERABORROW_ADDRESSES.pollenLpToken.contractAddress
      : BERABORROW_ADDRESSES.sPollenToken.contractAddress
    : isLpToken
      ? BERABORROW_ADDRESSES.lpVotingEscrow
      : BERABORROW_ADDRESSES.sPollenVotingEscrow;
  const outputTokenAddress = isLockMode
    ? isLpToken
      ? BERABORROW_ADDRESSES.lpVotingEscrow
      : BERABORROW_ADDRESSES.sPollenVotingEscrow
    : isLpToken
      ? BERABORROW_ADDRESSES.pollenLpToken.contractAddress
      : BERABORROW_ADDRESSES.sPollenToken.contractAddress;

  const needsApproval = isLockMode && allowance !== undefined && amount > 0n && allowance < amount;

  const parsedAmount = amount;
  const isDisabled = !parsedAmount || parsedAmount === 0n || !!validationError;

  const transaction: TransactionProps = isLockMode
    ? { type: isLpToken ? "increaseLpLockPosition" : "increaseLockPosition", variables: [parsedAmount, lockExpiry] }
    : { type: isLpToken ? "withdrawLpVotingEscrow" : "withdrawVotingEscrow", variables: [] };

  const buttonStyle = {
    borderRadius: 4,
    py: 1.5,
    fontSize: theme.typography.h3.fontSize,
    fontWeight: theme.typography.h3.fontWeight,
  };

  return {
    mode,
    balance,
    allowance,
    formValue,
    amount,
    validationError,
    dispatcher,
    transactionType,
    transaction,
    buttonText,
    inputTokenSymbol,
    outputTokenSymbol,
    needsApproval,
    theme,
    inputTokenAddress,
    outputTokenAddress,
    parsedAmount,
    isDisabled,
    buttonStyle,
    collateralDetails,
    lockDuration,
    lockExpiry,
    vePollenAmount,
    sPollenUnlockAmount,
    tokenType,
  };
};
