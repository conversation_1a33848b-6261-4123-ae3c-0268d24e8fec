import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { useHydrateAtoms } from "jotai/utils";
import { useLocation } from "react-router-dom";
import { useTheme } from "@mui/material";
import {
  getSPollenBalanceAtom,
  getVePollenBalanceAtom,
  getSPollenLockAllowanceAtom,
  formValidationsLockAtom,
  formValidationsUnlockAtom,
  formValuesLockAtom,
  lockAmountAtom,
  lockDurationAtom,
  lockExpiryAtom,
  canUnlockAtom,
  timeUntilUnlockAtom,
} from "../../../Atoms/Stake";
import { formatToken, regexDecimalToken } from "../../../utils/helpers";
import { getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import { updatePostHogEvent<PERSON>tom } from "../../../Atoms/Account";
import { useNotifications } from "../../../Hooks/useNotifications";
import { GenericApproval, APPROVAL_CONFIGS } from "../../Approvals/GenericApproval";

export type LockFormDispatcherActionType =
  | { type: "setAmount"; payload: string }
  | { type: "setPercentage"; payload: number }
  | { type: "setLockDuration"; payload: number }
  | { type: "approveLocking" }
  | { type: "confirmedTransaction" }
  | { type: "clearForm" };

export const useLockFormStore = () => {
  const location = useLocation();
  const isLockMode = location.pathname.includes("locking/lock");

  const { data: sPollenBalance } = useAtomValue(getSPollenBalanceAtom);
  const { data: vePollenBalance } = useAtomValue(getVePollenBalanceAtom);
  const { data: allowance } = useAtomValue(getSPollenLockAllowanceAtom);
  const sPollenDetails = useAtomValue(getCollateralDetailsAtom);

  // For unlock mode, we use vePOLLEN (18 decimals), for lock mode we use sPOLLEN details
  const collateralDetails = isLockMode ? sPollenDetails : { ...BERABORROW_ADDRESSES.sPollenToken, decimals: 18 }; // vePOLLEN has 18 decimals

  const [formValue, setFormValue] = useAtom(formValuesLockAtom);
  const [lockDuration, setLockDuration] = useAtom(lockDurationAtom);

  const validationError = useAtomValue(isLockMode ? formValidationsLockAtom : formValidationsUnlockAtom);
  const lockExpiry = useAtomValue(lockExpiryAtom);
  const amount = useAtomValue(lockAmountAtom);

  const balance = isLockMode ? sPollenBalance : vePollenBalance;

  useHydrateAtoms([
    [formValuesLockAtom, "0"],
    [lockDurationAtom, 30],
  ]);

  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const theme = useTheme();
  const { addModal } = useNotifications();

  // Calculate vePOLLEN amount based on lock duration multiplier
  const getMultiplier = (duration: number): number => {
    const multipliers: Record<number, number> = {
      30: 1.25,
      90: 2,
      180: 3,
      360: 4,
    };
    return multipliers[duration] || 1.25;
  };

  const vePollenAmount = (amount * BigInt(Math.floor(getMultiplier(lockDuration) * 100))) / 100n;

  // Get unlock status from atoms
  const { data: canUnlock } = useAtomValue(canUnlockAtom);
  const { data: timeUntilUnlock } = useAtomValue(timeUntilUnlockAtom);

  const dispatcher = (action: LockFormDispatcherActionType) => {
    switch (action.type) {
      case "setAmount": {
        const sanitizedValue = regexDecimalToken(action.payload, collateralDetails.decimals);
        setFormValue(sanitizedValue);
        break;
      }
      case "setPercentage": {
        const calculatedAmount = (BigInt(action.payload) * (balance || 0n)) / 100n;
        const formattedAmount = formatToken(calculatedAmount, collateralDetails.decimals);
        setFormValue(regexDecimalToken(formattedAmount, collateralDetails.decimals));
        break;
      }
      case "setLockDuration": {
        setLockDuration(action.payload);
        break;
      }
      case "approveLocking":
        updatePostHogEvent({ type: `approveSPollenLocking` });
        addModal({
          id: "ApproveSPollenLocking",
          title: "Approve sPOLLEN Locking",
          Component: (
            <GenericApproval
              amount={amount}
              config={APPROVAL_CONFIGS.sPollenLocking}
              additionalVariables={[BigInt(lockExpiry)]}
              onConfirmation={() => {
                dispatcher({ type: "confirmedTransaction" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      case "confirmedTransaction":
        setFormValue("0");
        break;
      case "clearForm":
        setFormValue("0");
        break;
      default:
        break;
    }
  };

  const mode = isLockMode ? "lock" : "unlock";
  const transactionType = isLockMode ? ("increaseLockPosition" as const) : ("withdrawVotingEscrow" as const);
  const buttonText = isLockMode ? "Lock" : "Unlock";

  // For lock: input sPOLLEN, output vePOLLEN
  // For unlock: input vePOLLEN, output sPOLLEN
  const inputTokenSymbol = isLockMode ? "sPOLLEN" : "vePOLLEN";
  const outputTokenSymbol = isLockMode ? "vePOLLEN" : "sPOLLEN";
  const inputTokenAddress = isLockMode ? BERABORROW_ADDRESSES.sPollenToken.contractAddress : BERABORROW_ADDRESSES.sPollenVotingEscrow; // Using VE contract address for vePOLLEN
  const outputTokenAddress = isLockMode
    ? BERABORROW_ADDRESSES.sPollenVotingEscrow // Using VE contract address for vePOLLEN
    : BERABORROW_ADDRESSES.sPollenToken.contractAddress;

  const needsApproval = isLockMode && allowance !== undefined && amount > 0n && allowance < amount;

  const parsedAmount = amount;
  const isDisabled = !parsedAmount || parsedAmount === 0n || !!validationError;

  const transaction = isLockMode
    ? { type: "increaseLockPosition" as const, variables: [parsedAmount, lockExpiry] as [bigint, bigint] }
    : { type: "withdrawVotingEscrow" as const, variables: [] as [] };

  const buttonStyle = {
    borderRadius: 4,
    py: 1.5,
    fontSize: theme.typography.h3.fontSize,
    fontWeight: theme.typography.h3.fontWeight,
  };

  return {
    mode,
    balance,
    allowance,
    formValue,
    amount,
    validationError,
    dispatcher,
    transactionType,
    transaction,
    buttonText,
    inputTokenSymbol,
    outputTokenSymbol,
    needsApproval,
    theme,
    inputTokenAddress,
    outputTokenAddress,
    parsedAmount,
    isDisabled,
    buttonStyle,
    collateralDetails,
    lockDuration,
    lockExpiry,
    vePollenAmount,
    canUnlock,
    timeUntilUnlock,
  };
};
