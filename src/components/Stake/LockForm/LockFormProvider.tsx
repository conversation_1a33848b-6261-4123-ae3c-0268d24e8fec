import React from "react";
import { useHydrateAtoms } from "jotai/utils";
import { collateralTypeSelectorAtom, stakingTokenAtom } from "../../../Atoms/Tokens";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import WithSuspense from "../../../providers/WithSuspense";

interface LockFormProviderProps {
  children: React.ReactNode;
  tokenType: "spollen" | "lp";
}

const Component: React.FC<LockFormProviderProps> = ({ children, tokenType }) => {
  const tokenAddress = tokenType === "lp" ? BERABORROW_ADDRESSES.pollenLpToken.contractAddress : BERABORROW_ADDRESSES.sPollenToken.contractAddress;

  useHydrateAtoms([
    [collateralTypeSelectorAtom, "staking"],
    [stakingTokenAtom, tokenAddress],
  ]);

  return <>{children}</>;
};

const LockFormProvider = WithSuspense(Component, "paper");
export default LockFormProvider;
