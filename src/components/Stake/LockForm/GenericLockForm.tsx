"use client";

import { Box, TextField, Typography } from "@mui/material";
import { formatToken } from "../../../utils/helpers";
import { AmountSelector } from "../../AmountSelector";
import TickerPill from "../../TickerPill";
import TransactionButton from "../../Transaction/Transaction";
import { ValidationButton } from "../../ValidationButton";
import { useGenericLockFormStore } from "./GenericLockFormStore";
import LockDurationDropdown from "./LockDurationDropdown";
import UnlockDurationDisplay from "./UnlockDurationDisplay";
import LockFormProvider from "./LockFormProvider";
import { useUnlockCountdown } from "./useUnlockCountdown";

interface GenericLockFormProps {
  tokenType: "spollen" | "lp";
}

const Component = ({ tokenType }: GenericLockFormProps) => {
  const {
    balance,
    formValue,
    validationError,
    dispatcher,
    transaction,
    buttonText,
    inputTokenSymbol,
    outputTokenSymbol,
    needsApproval,
    theme,
    inputTokenAddress,
    outputTokenAddress,
    isDisabled,
    buttonStyle,
    mode,
    collateralDetails,
    lockDuration,
    lockExpiry,
    vePollenAmount,
    sPollenUnlockAmount,
  } = useGenericLockFormStore(tokenType);

  const { canUnlock } = useUnlockCountdown(tokenType);
  const approveButtonText = tokenType === "lp" ? "Approve BERA-POLLEN" : "Approve sPOLLEN";

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 3,
        padding: 3,
        backgroundColor: theme.palette.background.paper,
        borderRadius: 2,
      }}
    >
      <Box sx={{ display: "flex", flexDirection: "column", gap: "6px" }}>
        <Typography variant="h4" sx={{ color: theme.palette.text.secondary }}>
          {mode === "lock" ? "Amount to Lock" : "Amount to unlock"}
        </Typography>
        <Box
          sx={{
            padding: "16px",
            borderRadius: 2,
            backgroundColor: theme.palette.background.default,
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", paddingBottom: 1 }}>
            <TextField
              value={formValue}
              onChange={(event) => dispatcher({ type: "setAmount", payload: event.target.value })}
              placeholder="0.0"
              error={!!validationError}
              sx={{
                paddingLeft: 0,
                flex: 1,
                "& .MuiOutlinedInput-root": {
                  backgroundColor: "transparent",
                  border: "none",
                  fontSize: theme.typography.h2.fontSize,
                  fontWeight: theme.typography.h2.fontWeight,
                  color: theme.palette.text.primary,
                  padding: 0,
                  paddingRight: "10px",

                  "& .MuiOutlinedInput-input": {
                    padding: 0,
                  },

                  "& fieldset": {
                    border: "none",
                  },
                  "& input::placeholder": {
                    color: theme.palette.text.secondary,
                    opacity: 1,
                  },
                },
              }}
            />
            <TickerPill contractAddress={inputTokenAddress} ticker={inputTokenSymbol} />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
            <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
              Wallet: {formatToken(balance || 0n, collateralDetails.decimals)} {inputTokenSymbol}
            </Typography>
            <AmountSelector handleItemClick={(percentage) => dispatcher({ type: "setPercentage", payload: percentage })} />
          </Box>
        </Box>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: "6px" }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h4" sx={{ color: theme.palette.text.secondary }}>
            You will receive
          </Typography>
          {mode === "lock" && (
            <Box sx={{ display: "flex", gap: "5px", alignItems: "center" }}>
              <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
                Lock expires on:
              </Typography>
              <Typography variant="body2" sx={{ color: theme.palette.text.primary }}>
                {new Date(Number(lockExpiry) * 1000).toLocaleDateString()}
              </Typography>
            </Box>
          )}
        </Box>
        <Box
          sx={{
            padding: "16px",
            borderRadius: 2,
            backgroundColor: theme.palette.background.default,
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
            <Typography
              variant="h2"
              sx={{
                color: theme.palette.text.primary,
                fontWeight: theme.typography.h2.fontWeight,
              }}
            >
              {mode === "lock" ? formatToken(vePollenAmount, 18, 4) : formatToken(sPollenUnlockAmount, 18, 4)}
            </Typography>
            <TickerPill contractAddress={outputTokenAddress} ticker={outputTokenSymbol} />
          </Box>
        </Box>
      </Box>

      {mode === "lock" && <LockDurationDropdown lockDuration={lockDuration} onDurationChange={(duration) => dispatcher({ type: "setLockDuration", payload: duration })} />}

      {mode === "unlock" && <UnlockDurationDisplay tokenType={tokenType} />}

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <Box display={needsApproval ? "block" : "none"}>
          <ValidationButton
            validationError={validationError}
            fullWidth
            variant="contained"
            onClick={() => dispatcher({ type: "approveLocking" })}
            disabled={!!validationError}
            sx={buttonStyle}
          >
            {approveButtonText}
          </ValidationButton>
        </Box>
        <Box display={needsApproval ? "none" : "block"}>
          <TransactionButton
            transaction={transaction}
            disabled={isDisabled || (mode === "unlock" && !canUnlock)}
            validationError={validationError}
            onConfirmation={() => dispatcher({ type: "confirmedTransaction" })}
            sx={buttonStyle}
          >
            {buttonText}
          </TransactionButton>
        </Box>
      </Box>
    </Box>
  );
};

const GenericLockForm = ({ tokenType }: GenericLockFormProps) => {
  return (
    <LockFormProvider tokenType={tokenType}>
      <Component tokenType={tokenType} />
    </LockFormProvider>
  );
};

export default GenericLockForm;
