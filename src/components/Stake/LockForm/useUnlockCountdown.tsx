import { atom, useAtom, useAtomValue } from "jotai";
import { useEffect } from "react";
import { timeUntilUnlockAtom, timeUntilUnlockLpAtom, canUnlockAtom, canUnlockLpAtom } from "../../../Atoms/Stake";

const dateAtom = atom(new Date());

export const useUnlockCountdown = (tokenType: "spollen" | "lp") => {
  const [, setNow] = useAtom(dateAtom);

  const isLpToken = tokenType === "lp";

  // Use the appropriate atoms based on token type
  const timeUntilUnlockData = useAtomValue(isLpToken ? timeUntilUnlockLpAtom : timeUntilUnlockAtom);
  const canUnlockData = useAtomValue(isLpToken ? canUnlockLpAtom : canUnlockAtom);

  const { data: timeData } = timeUntilUnlockData;
  const { data: canUnlock } = canUnlockData;

  const isCountingDown = timeData.remainingSeconds > 0 && !timeData.canUnlock;

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (isCountingDown) {
      timer = setInterval(() => {
        setNow(new Date());
      }, 1000);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [isCountingDown, setNow]);

  return {
    timeString: timeData.timeString,
    remainingSeconds: timeData.remainingSeconds,
    canUnlock: timeData.canUnlock || canUnlock,
    isCountingDown,
    hasPosition: timeData.hasPosition,
  };
};
