"use client";

import { ExpandMore } from "@mui/icons-material";
import { Box, Chip, MenuItem, Select, Typography, useTheme } from "@mui/material";
import { useAtomValue } from "jotai";
import { getLockingAprDataAtom } from "../../../Atoms/Stake";
import WithSuspense from "../../../providers/WithSuspense";

const lockDurations = [
  { key: 1, value: 30, multi: 1.25, label: "30 days" },
  { key: 2, value: 90, multi: 2, label: "90 days" },
  { key: 3, value: 180, multi: 3, label: "180 days" },
  { key: 4, value: 360, multi: 4, label: "1 year" },
];

interface LockDurationDropdownProps {
  lockDuration: number;
  onDurationChange: (duration: number) => void;
}

const LockDurationDropdownComponent = ({ lockDuration, onDurationChange }: LockDurationDropdownProps) => {
  const theme = useTheme();
  const { data: aprData } = useAtomValue(getLockingAprDataAtom);

  const formatApr = (apr: bigint): string => {
    return `${(Number(apr) / 100).toFixed(2)}%`;
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "6px" }}>
      <Typography variant="h4" sx={{ color: theme.palette.text.secondary }}>
        Lock Duration
      </Typography>
      <Select
        MenuProps={{
          anchorOrigin: {
            vertical: "bottom",
            horizontal: "left",
          },
          transformOrigin: {
            vertical: "top",
            horizontal: "left",
          },
        }}
        fullWidth
        value={lockDuration}
        onChange={(event) => {
          onDurationChange(Number(event.target.value));
        }}
        IconComponent={ExpandMore}
        sx={{
          backgroundColor: theme.palette.background.default,
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.divider,
          },
        }}
      >
        {lockDurations.map((info) => (
          <MenuItem key={info.key} value={info.value}>
            <Box sx={{ display: "flex", alignItems: "center", width: "100%", gap: "10px" }}>
              <Typography fontSize={16}>{info.label}</Typography>
              <Chip
                label={`${info.multi}x Multiplier`}
                sx={{
                  marginTop: "2px",
                  backgroundColor: "rgba(236, 111, 21, 0.20)",
                  color: "#ec6f15",
                  border: `1.2px solid rgba(236, 111, 21, 0.7)`,
                  padding: "5px 8.3px",
                }}
              />
              <Box sx={{ marginLeft: "auto" }}>
                <Typography variant="body2" sx={{ color: theme.palette.success.main }}>
                  {aprData?.[info.value] ? formatApr(aprData[info.value]) : "0.00%"} APR
                </Typography>
              </Box>
            </Box>
          </MenuItem>
        ))}
      </Select>
    </Box>
  );
};

const LockDurationDropdown = WithSuspense(LockDurationDropdownComponent, "paper");

export default LockDurationDropdown;
