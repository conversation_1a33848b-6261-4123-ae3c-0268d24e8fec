"use client";

import { Box, Typography, Chip, useTheme, Fade } from "@mui/material";
import { Lock } from "@mui/icons-material";
import { useUnlockCountdown } from "./useUnlockCountdown";

interface UnlockDurationDisplayProps {
  tokenType?: "spollen" | "lp";
}

const UnlockDurationDisplay: React.FC<UnlockDurationDisplayProps> = ({ tokenType = "spollen" }) => {
  const theme = useTheme();
  const { timeString, canUnlock, isCountingDown, hasPosition } = useUnlockCountdown(tokenType);

  // Don't render anything if there's no lock position
  if (!hasPosition) {
    return null;
  }

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "6px" }}>
      <Typography variant="h4" sx={{ color: theme.palette.text.secondary }}>
        Lock Duration
      </Typography>
      <Fade in={true} timeout={500}>
        <Chip
          label={
            <Box sx={{ display: "flex", gap: "5px", alignItems: "center" }}>
              {!canUnlock && <Lock sx={{ fontSize: "16px" }} />}
              <Typography
                sx={{
                  transition: isCountingDown ? "color 0.3s ease-in-out" : "none",
                  animation: isCountingDown ? "pulse 2s infinite" : "none",
                  "@keyframes pulse": {
                    "0%": { opacity: 1 },
                    "50%": { opacity: 0.7 },
                    "100%": { opacity: 1 },
                  },
                }}
              >
                {canUnlock ? "Ready to unlock" : `You can unlock in ${timeString}`}
              </Typography>
            </Box>
          }
          sx={{
            marginTop: "2px",
            backgroundColor: canUnlock ? "rgba(29, 255, 173, 0.20)" : "rgba(236, 111, 21, 0.20)",
            color: canUnlock ? theme.palette.success.main : "#ec6f15",
            border: `1.2px solid ${canUnlock ? "rgba(29, 255, 173, 0.7)" : "rgba(236, 111, 21, 0.7)"}`,
            padding: "10px 0",
            width: "100%",
            transition: "all 0.3s ease-in-out",
          }}
        />
      </Fade>
    </Box>
  );
};

export default UnlockDurationDisplay;
