import { Accordion, AccordionSummary, AccordionDetails, Box, Typography, Divider, useTheme, useMediaQuery } from "@mui/material";
import { useAccountHistory } from "./store";
import { ExpandMore } from "@mui/icons-material";
import { truncateAddress, formatToken } from "../../utils/helpers";
import TokenIcon from "../TokenIcons";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { Link } from "react-router-dom";
import { LSPShareChangeDto } from "@Beraborrowofficial/sdk";

interface DenHistoryProps {
  item: LSPShareChangeDto;
}

const Component: React.FC<DenHistoryProps> = ({ item }: DenHistoryProps) => {
  const { blockExplorer } = useAccountHistory();
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  return (
    <>
      <Accordion
        sx={{
          paddingY: "8px",
          paddingX: "0px",

          borderRadius: "16px",
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Box display="flex" justifyContent="space-between" flex={1}>
            <Box>
              <Box display="flex" alignItems="center">
                <TokenIcon key={item.transaction.id} contractAddress={BERABORROW_ADDRESSES.debtToken.contractAddress} height={isMdUp ? "30px" : "25px"} zIndex={5} />
                <Typography component="span" variant={"h1"} color={"var(--text-primary)"} pl={2}>
                  Pool {item.shareOperation.charAt(0) + item.shareOperation.toLowerCase().slice(1)}
                </Typography>
              </Box>
              <Typography variant={"h5"} color={item.shareOperation === "DEPOSIT" ? "var(--text-success)" : "var(--error-light)"} sx={{ display: "inline-block" }}>
                {item.shareOperation === "DEPOSIT" ? "Deposited" : "Withdrawn"}: {formatToken(BigInt(item.shareAmountChange ?? "0"), BERABORROW_ADDRESSES.debtToken.decimals, 2)} s
                {BERABORROW_ADDRESSES.debtToken.ticker}
              </Typography>
            </Box>

            <Box alignContent={"right"} textAlign={"right"} pr={1}></Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails sx={{ p: 1 }}>
          <>
            <Divider sx={{ backgroundColor: "var(--border-color)", mb: 1 }} />{" "}
            <Typography variant="h4" color={"var(--text-primary)"} display="flex" alignItems="center" pb={1}>
              Transaction
            </Typography>
            <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
              <Box>
                <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                  Hash
                </Typography>
              </Box>

              <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                {blockExplorer ? (
                  <Link target="_blank" to={`${blockExplorer}/tx/${item.transaction.id}`}>
                    {truncateAddress(item.transaction.id)}
                  </Link>
                ) : (
                  truncateAddress(item.transaction.id)
                )}
              </Typography>
            </Box>
            <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
              <Box>
                <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                  Time
                </Typography>
              </Box>
              <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                {new Date(item.transaction.timestamp * 1000).toLocaleString()}
              </Typography>
            </Box>
          </>
        </AccordionDetails>
      </Accordion>
    </>
  );
};
const LspHistory = Component;
export default LspHistory;
