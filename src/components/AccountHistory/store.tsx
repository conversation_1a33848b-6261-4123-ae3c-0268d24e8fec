import { useMemo } from "react";
import { useAtomValue } from "jotai";
import { denManagers<PERSON>tom, getDenChangesOfUserDens } from "../../Atoms/Den";
import { getRedemptionsByRedeemerAtom } from "../../Atoms/Redemptions";
import { useAccount } from "wagmi";
import { Address, isAddressEqual } from "viem";

import { HistoryDenFilter } from "../../@type/Account";
import { getUserLspShareChangesAtom } from "../../Atoms/StabilityPool";
import { getUserVaultChangesAtom } from "../../Atoms/Vault";
import { useParams, useNavigate } from "react-router-dom";
import { historyMonthsAtom } from "../../Atoms/Account";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { VaultHistoryDto } from "../../@type/Vault";
import { DenChangeDto, LSPShareChangeDto, RedemptionDto, TransactionDto } from "@Beraborrowofficial/sdk";

export const useAccountHistory = () => {
  const { filter, date } = useParams();
  const navigate = useNavigate();
  const { chain } = useAccount();
  const { data: _denHistory } = useAtomValue(getDenChangesOfUserDens);
  const { data: _redemptionHistory } = useAtomValue(getRedemptionsByRedeemerAtom);
  const { data: _lspHistory } = useAtomValue(getUserLspShareChangesAtom);
  const { data: _vaultOperations } = useAtomValue(getUserVaultChangesAtom);

  const historyMonths = useAtomValue(historyMonthsAtom);
  const denManagers = useAtomValue(denManagersAtom);
  const historyFilter: HistoryDenFilter = (filter as HistoryDenFilter) ?? "all";
  const blockExplorer = chain?.blockExplorers?.default?.url;

  const _vaultHistory = useMemo(() => {
    let _vaultHistory: VaultHistoryDto[] = [];

    _vaultOperations.forEach((item) => {
      if (item.changes.length !== 0) {
        let changes: VaultHistoryDto[] = item.changes;

        for (let i = 0; i < changes.length; i++) {
          if (changes[i].vaultOperation === "DEPOSIT") {
            changes[i].token = Object.entries(BERABORROW_ADDRESSES.collateralTokens).find(([asset]) => isAddressEqual(asset as Address, item.vault.asset.id));
          }
          if (changes[i].vaultOperation === "WITHDRAW") {
            changes[i].token = Object.entries(BERABORROW_ADDRESSES.collateralTokens).find(([asset]) =>
              isAddressEqual(asset as Address, changes[i].withdrawn[0].id?.split("/")[1] as Address)
            );
          }
        }
        _vaultHistory = _vaultHistory.concat(changes);
      }
    });

    return _vaultHistory;
  }, [_vaultOperations]);

  // Calculate filtered history
  const history = useMemo(() => {
    let denHistory: DenChangeDto[] = [];
    let redemptionHistory: (RedemptionDto & { transaction: TransactionDto })[] = [];
    let lspHistory: LSPShareChangeDto[] = [];
    let vaultHistory: VaultHistoryDto[] = [];

    if (historyFilter.startsWith("0x")) {
      const ticker = denManagers?.find((item) => item.contractAddress == historyFilter)?.collateralTicker;

      denHistory = _denHistory?.filter((item) => item.denManager.id === historyFilter.toLowerCase()) ?? [];
      vaultHistory = _vaultHistory?.filter((item) => ticker && item.token?.[1]?.ticker === ticker) ?? [];
    } else if (historyFilter === "dens") {
      denHistory = _denHistory ?? [];
    } else if (historyFilter === "redemptions") {
      redemptionHistory = _redemptionHistory ?? [];
    } else if (historyFilter === "pool") {
      lspHistory = _lspHistory ?? [];
    } else if (historyFilter === "vaults") {
      vaultHistory = _vaultHistory ?? [];
    } else {
      denHistory = _denHistory ?? [];
      lspHistory = _lspHistory ?? [];
      vaultHistory = _vaultHistory ?? [];
    }

    return [...denHistory, ...redemptionHistory, ...lspHistory, ...vaultHistory].sort((a, b) => a.transaction.timestamp - b.transaction.timestamp);
  }, [_denHistory, _redemptionHistory, _lspHistory, _vaultHistory, denManagers, historyFilter]);

  const dispatcher = (action: { type: "historyFilter"; payload: HistoryDenFilter } | { type: "dateFilter"; payload: string } | { type: "close" }) => {
    switch (action.type) {
      case "historyFilter":
        navigate(`/dashboard/history/${action.payload}${date ? "/" + date : ""}`);
        break;
      case "dateFilter":
        navigate(`/dashboard/history/${historyFilter}/${action.payload}`);
        break;
      default:
        break;
    }
  };

  const getDenOperationName = (denOperation: string, collateralChange: string, debtChange: string, collateralRatioAfter: string | null) => {
    switch (denOperation) {
      case "openDen":
        return "Open Den";
      case "closeDen":
        return "Close Den";
      case "adjustDen":
        if (collateralChange !== "0") {
          return collateralChange[0] === "-" ? "Withdrew" : "Deposited";
        } else {
          return debtChange[0] === "-" ? "Repay" : "Borrow";
        }
      case "accrueRewards":
        return "Earned";
      case "redeemCollateral":
        return collateralRatioAfter === null ? "Redemption Close" : "Redeemed Against";
      case "Liquidation":
        return "Liquidation Close";
      case "liquidateInNormalMode":
        return "Liquidation Close";
      case "liquidateInRecoveryMode":
        return "Liquidation Close";
      default:
        return denOperation;
    }
  };

  return { history, blockExplorer, getDenOperationName, dispatcher, historyFilter, denManagers, historyMonths, date };
};
