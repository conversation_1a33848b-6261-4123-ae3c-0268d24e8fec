import { Grid, Box, Select, MenuItem } from "@mui/material";
import WithSuspense from "../../providers/WithSuspense";
import { useAccountHistory } from "./store";

import DenHistory from "./DenHistory";
import RedemptionHistory from "./RedemptionHistory";
import LspHistory from "./LspHistory";
import VaultHistory from "./VaultHistory";
import { ExpandMore } from "@mui/icons-material";
import TokenIcon from "../TokenIcons";
import VaultApy from "../VaultApy/Component";

const Component: React.FC = () => {
  const { history, dispatcher, historyFilter, denManagers, historyMonths, date } = useAccountHistory();

  return (
    <Grid container spacing={1} key={"history"}>
      <Grid item xs={12}>
        <Box display="flex" justifyContent="space-between" alignItems="center" p={1} key={"filters"}>
          <Box>
            <Select
              MenuProps={{
                anchorOrigin: {
                  vertical: "bottom",
                  horizontal: "left",
                },
                transformOrigin: {
                  vertical: "top",
                  horizontal: "left",
                },
              }}
              fullWidth
              value={historyFilter}
              onChange={(event) => {
                dispatcher({ type: "historyFilter", payload: event.target.value as typeof historyFilter });
              }}
              IconComponent={ExpandMore}
            >
              <MenuItem key={"All"} value={"all"}>
                <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
                  {" "}
                  <Box mr={2} sx={{ lineHeight: 0, display: "flex", alignItems: "center", height: "1.3rem", width: "1.3rem", borderRadius: "50%", position: "relative" }}>
                    <img src={`/imgs/dashboard.png`} alt="" height={"100%"} width={"100%"} />
                  </Box>
                  All
                </Box>
              </MenuItem>
              <MenuItem key={"pool"} value={"pool"}>
                <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
                  <Box mr={2} sx={{ lineHeight: 0, display: "flex", alignItems: "center", height: "1.3rem", width: "1.3rem", borderRadius: "50%", position: "relative" }}>
                    <img src={`/imgs/pool.png`} alt="" height={"100%"} width={"100%"} />
                  </Box>
                  Stability Pool
                </Box>
              </MenuItem>{" "}
              <MenuItem key={"vaults"} value={"vaults"}>
                <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
                  <Box mr={2} sx={{ lineHeight: 0, display: "flex", alignItems: "center", height: "1.3rem", width: "1.3rem", borderRadius: "50%", position: "relative" }}>
                    <img src={`/imgs/vault.png`} alt="" height={"100%"} width={"100%"} />
                  </Box>
                  Vaults
                </Box>
              </MenuItem>{" "}
              <MenuItem key={"redemptions"} value={"redemptions"}>
                <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
                  {" "}
                  <Box mr={2} sx={{ lineHeight: 0, display: "flex", alignItems: "center", height: "1.3rem", width: "1.3rem", borderRadius: "50%", position: "relative" }}>
                    <img src={`/imgs/swap.png`} alt="" height={"100%"} width={"100%"} />
                  </Box>
                  Redemptions
                </Box>
              </MenuItem>{" "}
              <MenuItem key={"dens"} value={"dens"}>
                <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
                  {" "}
                  <Box mr={2} sx={{ lineHeight: 0, display: "flex", alignItems: "center", height: "1.3rem", width: "1.3rem", borderRadius: "50%", position: "relative" }}>
                    <img src={`/imgs/borrow.png`} alt="" height={"100%"} width={"100%"} />
                  </Box>
                  Dens
                </Box>
              </MenuItem>
              {denManagers
                .filter((denManager) => !denManager.wrappedCollateral)
                .map((item) => (
                  <MenuItem key={item.contractAddress} value={item.contractAddress}>
                    <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
                      <TokenIcon pr={2} height={"1.3rem"} contractAddress={item.collateralAddress} vaulted={!!item.vaultAddress} />
                      {item.collateralTicker}
                      <VaultApy address={item.contractAddress} />
                    </Box>
                  </MenuItem>
                ))}
            </Select>
          </Box>
          <Box>
            <Select
              MenuProps={{
                anchorOrigin: {
                  vertical: "bottom",
                  horizontal: "left",
                },
                transformOrigin: {
                  vertical: "top",
                  horizontal: "left",
                },
              }}
              fullWidth
              value={date ?? historyMonths[historyMonths.length - 1].date}
              onChange={(event) => {
                dispatcher({ type: "dateFilter", payload: event.target.value as typeof historyFilter });
              }}
              IconComponent={ExpandMore}
            >
              {historyMonths.map((item) => (
                <MenuItem key={item.label} value={item.date}>
                  <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>{item.label}</Box>
                </MenuItem>
              ))}
            </Select>
          </Box>
        </Box>
      </Grid>
      {history.map((item) => {
        return (
          <Grid item xs={12} display={"block"} key={item.transaction.id + ("denOperation" in item ? item.denOperation : "")}>
            <>
              {item.__typename === "DenChange" ? (
                <DenHistory item={item} />
              ) : item.__typename === "SharePositionChange" ? (
                <LspHistory item={item} />
              ) : item.__typename === "VaultPositionChange" ? (
                <VaultHistory item={item} />
              ) : item.__typename === "Redemption" ? (
                <RedemptionHistory item={item} />
              ) : (
                <></>
              )}
            </>
          </Grid>
        );
      })}
    </Grid>
  );
};
const AccountHistory = WithSuspense(Component, "paper");
export default AccountHistory;
