import { Accordion, AccordionSummary, AccordionDetails, Box, Typography, Divider, useTheme, useMediaQuery } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import { Link } from "react-router-dom";
import { Address, zeroAddress } from "viem";

import { useAccountHistory } from "./store";
import { truncateAddress, formatToken } from "../../utils/helpers";
import TokenIcon from "../TokenIcons";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { VaultHistoryDto } from "../../@type/Vault";

interface VaultHistoryProps {
  item: VaultHistoryDto;
}

const Component: React.FC<VaultHistoryProps> = ({ item }) => {
  const { blockExplorer } = useAccountHistory();
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));

  return (
    <>
      <Accordion
        sx={{
          paddingY: "8px",
          paddingX: "0px",

          borderRadius: "16px",
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Box display="flex" justifyContent="space-between" flex={1}>
            <Box>
              <Box display="flex" alignItems="center">
                <TokenIcon key={item.transaction.id} contractAddress={(item.token?.[0] as Address) ?? zeroAddress} height={isMdUp ? "30px" : "25px"} zIndex={5} />
                <Typography component="span" variant={"h1"} color={"var(--text-primary)"} pl={2}>
                  Vault {item.vaultOperation.charAt(0) + item.vaultOperation.toLowerCase().slice(1)}
                </Typography>
              </Box>
              <Typography variant={"h5"} color={item.vaultOperation === "DEPOSIT" ? "var(--text-success)" : "var(--error-light)"} sx={{ display: "inline-block" }}>
                {item.vaultOperation === "DEPOSIT" ? "Deposited" : "Withdrawn"}: {formatToken(BigInt(item.shareChange ?? "0"), BERABORROW_ADDRESSES.debtToken.decimals, 8)}{" "}
                {item.token?.[1]?.ticker}
              </Typography>
            </Box>

            <Box alignContent={"right"} textAlign={"right"} pr={1}></Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails sx={{ p: 1 }}>
          <>
            <Divider sx={{ backgroundColor: "var(--border-color)", mb: 1 }} />{" "}
            <Typography variant="h4" color={"var(--text-primary)"} display="flex" alignItems="center" pb={1}>
              Transaction
            </Typography>
            <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
              <Box>
                <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                  Hash
                </Typography>
              </Box>

              <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                {blockExplorer ? (
                  <Link target="_blank" to={`${blockExplorer}/tx/${item.transaction.id}`}>
                    {truncateAddress(item.transaction.id)}
                  </Link>
                ) : (
                  truncateAddress(item.transaction.id)
                )}
              </Typography>
            </Box>
            <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
              <Box>
                <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                  Time
                </Typography>
              </Box>
              <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                {new Date(item.transaction.timestamp * 1000).toLocaleString()}
              </Typography>
            </Box>
          </>
        </AccordionDetails>
      </Accordion>
    </>
  );
};
const VaultHistory = Component;
export default VaultHistory;
