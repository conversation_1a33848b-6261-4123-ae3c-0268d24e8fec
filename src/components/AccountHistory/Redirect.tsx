import { useEffect } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { historyStartDateAtom } from "../../Atoms/Account";
import { isAddress } from "viem";
import { useSet<PERSON>tom } from "jotai";
function isValidDateString(date: string): boolean {
  // Check if string is exactly in the format YYYY-MM-DD
  if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    return false;
  }

  // Let Date constructor or Date.parse handle actual validation
  const parsedDate = new Date(date);

  // Ensure the resulting date matches the input
  const isValidDate = !isNaN(parsedDate.getTime()); // Check if the date is valid

  return isValidDate;
}
export const useAccountHistoryRedirect = () => {
  let { filter, date } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const setFirstMonth = useSetAtom(historyStartDateAtom);

  useEffect(() => {
    if (location.pathname.includes("/dashboard/history/")) {
      if (!filter && !date) {
      } else if (!(filter && (filter === "all" || filter === "dens" || filter === "pool" || filter === "vaults" || filter === "redemptions" || isAddress(filter)))) {
        navigate(`/dashboard/history`, { replace: true });
      } else if (date && !isValidDateString(date)) {
        navigate(`/dashboard/history/${filter}`, { replace: true });
      } else if (date) {
        const dateObj = new Date(date);
        setFirstMonth(new Date(dateObj.getFullYear(), dateObj.getMonth(), 1).getTime());
      }
    } else {
      //reset start Month when leaving history page
      const today = new Date();
      setFirstMonth(new Date(today.getFullYear(), today.getMonth(), 1).getTime());
    }
  }, [location.pathname]);
};
