import { Accordion, AccordionSummary, AccordionDetails, Box, Typography, Divider, useTheme, useMediaQuery } from "@mui/material";
import { useAccountHistory } from "./store";
import { ExpandMore } from "@mui/icons-material";
import { formatBigIntPercentage, formatToken, truncateAddress } from "../../utils/helpers";
import TokenIcon from "../TokenIcons";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { Link } from "react-router-dom";
import { DenChangeDto } from "@Beraborrowofficial/sdk";
import { checksumAddress } from "viem";

interface DenHistoryProps {
  item: DenChangeDto;
}

const Component: React.FC<DenHistoryProps> = ({ item }: DenHistoryProps) => {
  const { getDenOperationName, blockExplorer } = useAccountHistory();
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  const collateralAddress = BERABORROW_ADDRESSES.denManagers[checksumAddress(item.denManager.id)]?.collateral;
  const collateral = BERABORROW_ADDRESSES.collateralTokens[collateralAddress];
  const vaulted = !!BERABORROW_ADDRESSES.denManagers[checksumAddress(item.denManager.id)]?.vault;
  return (
    <>
      {collateral && (
        <Accordion
          sx={{
            paddingY: "8px",
            paddingX: "0px",

            borderRadius: "16px",
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box display="flex" justifyContent="space-between" flex={1}>
              <Box>
                <Box display="flex" alignItems="center">
                  <TokenIcon key={item.transaction.id} contractAddress={collateralAddress} height={isMdUp ? "30px" : "25px"} mr={"-5px"} zIndex={5} vaulted={vaulted} />
                  <Typography component="span" variant={"h1"} color={"var(--text-primary)"} pl={2}>
                    {getDenOperationName(item.denOperation, item.collateralChange, item.debtChange, item.collateralRatioAfter)}
                  </Typography>
                </Box>
                <Typography variant={"h5"} color={"var(--text-secondary)"} sx={{ display: "inline-block" }}>
                  Ratio: {formatBigIntPercentage(BigInt(item.collateralRatioAfter ?? "0"), 2)}%
                </Typography>
              </Box>
              {!(item.denOperation === "redeemCollateral" || item.denOperation.includes("liquidate")) && (
                <Box alignContent={"right"} textAlign={"right"} pr={1}>
                  {item.collateralChange !== "0" && (
                    <Typography variant={"h4"} color={"var(--text-primary)"} pl={1}>
                      {formatToken(BigInt(item.collateralChange ?? "0"), BERABORROW_ADDRESSES.debtToken.decimals, 8)} {collateral.ticker}
                    </Typography>
                  )}
                  {item.debtChange !== "0" && (
                    <Typography variant={"h4"} color={"var(--text-primary)"} pl={1}>
                      {formatToken(BigInt(item.debtChange ?? "0"), BERABORROW_ADDRESSES.debtToken.decimals, 2)} {BERABORROW_ADDRESSES.debtToken.ticker}
                    </Typography>
                  )}
                </Box>
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 1 }}>
            <>
              <Divider sx={{ backgroundColor: "var(--border-color)", mb: 1 }} />{" "}
              <Typography variant="h4" color={"var(--text-primary)"} display="flex" alignItems="center" pb={1}>
                Transaction
              </Typography>
              <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                <Box>
                  <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                    Hash
                  </Typography>
                </Box>

                <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                  {blockExplorer ? (
                    <Link target="_blank" to={`${blockExplorer}/tx/${item.transaction.id}`}>
                      {truncateAddress(item.transaction.id)}
                    </Link>
                  ) : (
                    truncateAddress(item.transaction.id)
                  )}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                <Box>
                  <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                    Time
                  </Typography>
                </Box>
                <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                  {new Date(item.transaction.timestamp * 1000).toLocaleString()}
                </Typography>
              </Box>
            </>
          </AccordionDetails>
        </Accordion>
      )}
    </>
  );
};
const DenHistory = Component;
export default DenHistory;
