import React from "react";
import { Button, Typography, useMediaQuery, useTheme } from "@mui/material";
import TokenIcon from "../TokenIcons";
import { useCollateralPillStore } from "./store";
import { ExpandMore } from "@mui/icons-material";
import { Hex } from "viem";

interface CollateralPillProps {
  contractAddress: Hex;
  urlRedirect: string;
  excludeDenManagers?: boolean;
  excludeWrappedCollateral?: boolean;
  includePsm?: boolean;
  includePublicVaults?: boolean;
  showVaultTicker?: boolean;
  includeManagedVaults?: boolean;
  hideIcon?: boolean;
}
const Component: React.FC<CollateralPillProps> = ({
  contractAddress,
  urlRedirect,
  excludeWrappedCollateral,
  excludeDenManagers,
  includePsm,
  includePublicVaults,
  showVaultTicker,
  includeManagedVaults,

  hideIcon = false,
}) => {
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  const { collateralDetails, ticker, vaulted, dispatcher } = useCollateralPillStore(
    contractAddress,
    urlRedirect,
    excludeWrappedCollateral,
    excludeDenManagers,
    includePsm,
    includePublicVaults,
    showVaultTicker,
    includeManagedVaults
  );

  return (
    <>
      <Button
        onClick={() => {
          dispatcher("open");
        }}
        size="small"
        variant="contained"
        sx={{ width: "fit-content" }}
      >
        {!hideIcon && <TokenIcon contractAddress={collateralDetails.contractAddress} vaulted={vaulted} height={isMdUp ? "30px" : "25px"} vaultSize={"60%"} pr={1} />}
        <Typography variant={isMdUp ? "h4" : "h5"} color={"var(--text-primary)"} sx={{ whiteSpace: "nowrap" }} lineHeight={!hideIcon ? undefined : isMdUp ? "30px" : "25px"}>
          {ticker}
        </Typography>
        <ExpandMore />
      </Button>
    </>
  );
};

const CollateralPill = Component;
export default CollateralPill;
