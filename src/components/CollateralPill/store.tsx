import { useAtomValue } from "jotai";
import { Hex } from "viem";
import { denManagerAtom } from "../../Atoms/Den";
import { psmBondAtom } from "../../Atoms/PsmBond";
import { getCollateralDetailsAtom } from "../../Atoms/Tokens";
import { vaultDetailsAtom } from "../../Atoms/Vault";
import { useNotifications } from "../../Hooks/useNotifications";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import CollateralSelector from "../CollateralSelector/wrapper";

export const useCollateralPillStore = (
  contractAddress: Hex,
  urlRedirect: string,
  excludeWrappedCollateral?: boolean,
  excludeDenManagers?: boolean,
  includePsm?: boolean,
  includePublicVaults?: boolean,
  showVaultTicker?: boolean,
  includeManagedVaults?: boolean
) => {
  const denManagerAddr = useAtomValue(denManagerAtom);
  const psmAddr = useAtomValue(psmBondAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const vaultDetails = useAtomValue(vaultDetailsAtom);
  const { addModal } = useNotifications();
  const vaulted = includePsm && psmAddr ? false : !!BERABORROW_ADDRESSES.denManagers[denManagerAddr]?.vault;
  const ticker = showVaultTicker ? vaultDetails.ticker : (BERABORROW_ADDRESSES.collateralTokens?.[contractAddress]?.ticker ?? collateralDetails.ticker);
  const dispatcher = (action: "open") => {
    switch (action) {
      case "open":
        addModal({
          id: "DenManagerSelector",
          title: includePsm || includePublicVaults || includeManagedVaults ? "Collaterals" : "Den Management",
          Component: (
            <CollateralSelector
              urlRedirect={urlRedirect}
              excludeWrappedCollateral={excludeWrappedCollateral}
              excludeDenManagers={excludeDenManagers}
              includePsm={includePsm}
              includePublicVaults={includePublicVaults}
              includeManagedVaults={includeManagedVaults}
              isModal
            />
          ),
        });
        break;
      default:
        break;
    }
  };
  return { ticker, collateralDetails, denManagerAddr, vaulted, dispatcher };
};
