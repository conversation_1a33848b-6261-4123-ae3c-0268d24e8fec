import { useAtomValue, useSetAtom } from "jotai";
import { getCollateralDetailsAtom, collateralTypeSelectorAtom } from "../../Atoms/Tokens";
import { denManagersAtom, denManagerAtom } from "../../Atoms/Den";
import { useNavigate, useParams } from "react-router-dom";
import { useEffect } from "react";
import { vaultAtom } from "../../Atoms/Vault";

export const useDenBorrowRedirect = (borrowAction: "borrow" | "withdraw") => {
  let { ticker: tickerParam } = useParams();
  const setVaultAddr = useSetAtom(vaultAtom);
  const setCollateralTypeSelector = useSetAtom(collateralTypeSelectorAtom);

  const navigate = useNavigate();
  const denManagers = useAtomValue(denManagersAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const denManagerAddr = useAtomValue(denManagerAtom);
  const currentDenManager = denManagers.find((item) => item.contractAddress === denManagerAddr);
  const newDenManager = denManagers.find((item) => item.collateralTicker.toLowerCase() === tickerParam?.toLowerCase())?.contractAddress;
  useEffect(() => {
    if ((tickerParam && !newDenManager) || !tickerParam) {
      navigate(`/den/${borrowAction === "borrow" ? "borrow" : "manage"}/${currentDenManager?.collateralTicker || collateralDetails.ticker}`, { replace: true });
      setVaultAddr(undefined);
    }
    setCollateralTypeSelector("den");
  }, [denManagerAddr, tickerParam, newDenManager]);
};

7;
