import React from "react";
import { useDenBorrowStore } from "./store";
import { Box, Button, Chip, Divider, Grid, InputAdornment, Slider, TextField, Typography } from "@mui/material";
import { formatBigIntPercentage, formatDecimal, formatToken } from "../../utils/helpers";
import TransactionButton from "../Transaction/Transaction";
import WithSuspense from "../../providers/WithSuspense";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import StyledTextfield from "../StyledTextField";
import { Favorite, InfoOutlined } from "@mui/icons-material";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { ValidationButton } from "../ValidationButton";
import StyledTooltip from "../StyledFeeTooltip";
import BorrowRateBreakdown from "../BorrowRateBreakdown";
import PreloadComponent from "../PreloadComponent/Component";
import CollateralSelector from "../CollateralSelector/wrapper";
import CollateralPill from "../CollateralPill/component";
import WbTwilightIcon from "@mui/icons-material/WbTwilight";
import { useDenBorrowRedirect } from "./redirect";
import TokenIcon from "../TokenIcons";

const Component: React.FC<{ borrowAction?: "borrow" | "withdraw" }> = ({ borrowAction = "borrow" }) => {
  useDenBorrowRedirect(borrowAction);
  const {
    debtBalance,
    collateral,
    dispatcher,
    isManaging,
    formValues,
    formAmounts,
    validationError,
    userDen,
    needApproval,
    borrowingFee,
    mcr,
    borrowType,
    maxDebtAmount,
    collateralRatioHealth,
    isRecoveryMode,
    isDelegated,
    totalDebt,
    isNotWithdrawable,
    borrowingFeeRate,
    MINIMUM_NET_DEBT,
    liquidationReserve,
    maxLeverageNumber,
    isLeveraging,
    isVaulted,
    previewVaultedShares,
    flashLoanFee,
    flashLoanRate,
    transactionProps,
    isOogaboogaAvailable,
    isLeveragingDisabled,
    isSunsetting,
    minSafeCr,
  } = useDenBorrowStore(borrowAction);
  if ((userDen.status !== "open" && borrowType === "withdraw") || isSunsetting) {
    return (
      <Box
        sx={{
          background: "var(--background-accent)",
          width: "100%",
          alignItems: "center",
          height: "400px",
          display: "flex", // Add flex display
          justifyContent: "center", // Center horizontally
          textAlign: "center", // Center text
        }}
        p={2}
        gap={2}
        borderRadius={"16px"}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {isSunsetting ? (
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              textAlign="center"
              gap={2} // Adjust spacing between elements
            >
              <WbTwilightIcon fontSize="large" />
              <Typography variant="h2">
                {userDen.status !== "open" ? (
                  <>This Den is sunsetting and cannot be used for new positions. Please select a different Den to continue.</>
                ) : (
                  <>This Den is being sunset. Please close your position. Interest rates will continue to increase over time.</>
                )}
              </Typography>
              <CollateralPill contractAddress={collateral.contractAddress} urlRedirect={`/den/${borrowType !== "withdraw" ? "borrow" : "manage"}`} />
            </Box>
          ) : (
            <>
              <InfoOutlined />
              <Typography variant="h2">Deposit collateral and borrow to be able to withdraw from a position</Typography>
            </>
          )}
        </Box>
      </Box>
    );
  }
  return (
    <Box display="flex" flexDirection="column" height="100%" minHeight={"450px"}>
      <Grid container spacing={1}>
        <Grid item xs={12}>
          <Typography marginBottom="4px" variant="subtitle1">
            {borrowType === "withdraw" ? "Withdraw " : "Deposit"} Collateral{" "}
          </Typography>
          <StyledTextfield
            sx={{ height: "54px" }}
            value={formValues.collateral}
            disabled={isNotWithdrawable}
            onChange={(event) => dispatcher({ type: "setCollateral", payload: event.target.value })}
            error={validationError?.type === "insufficient_collateral_balance"}
            fontSize={24}
            startIcons={
              <Box display="flex" flexDirection="column" justifyContent="center" marginBottom="10px" gap="8px">
                <TokenIcon contractAddress={collateral.contractAddress} height="35px" width="35px" />
                <Typography position="absolute" left="16px" bottom="8px">
                  $
                  {formatToken(
                    ((borrowAction === "withdraw" ? formAmounts.collateral : formAmounts.getCollateralShares(previewVaultedShares)) * collateral.price) / SCALING_FACTOR,
                    borrowAction === "withdraw" ? collateral.vaultDecimals : collateral.vaultDecimals,
                    2,
                    undefined,
                    true
                  )}
                </Typography>
              </Box>
            }
            endComponent={
              <Box display="flex" position="absolute" right="8px" bottom="8px">
                <Box display="flex" justifyContent="flex-end" alignItems="center" mt={1}>
                  <Typography component="span" variant="caption" fontSize={12}>
                    {isNotWithdrawable ? (
                      <StyledTooltip
                        title={
                          <Box>
                            {collateral.wrappedCollateralTicker} is not easily unwrapable to {collateral.ticker}, thus we only only allow withdrawals of{" "}
                            {collateral.wrappedCollateralTicker}
                          </Box>
                        }
                      >
                        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                          <InfoOutlined /> No Withdrawals via {collateral.ticker}
                        </Box>
                      </StyledTooltip>
                    ) : (
                      <Box display="flex">
                        <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                          {borrowType !== "withdraw" ? "Wallet" : "Balance"}:
                        </Typography>{" "}
                        {formatToken(
                          borrowType !== "withdraw" ? collateral.balance : userDen.collateral,
                          borrowType !== "withdraw" ? collateral.decimals : collateral.vaultDecimals,
                          2,
                          undefined,
                          true
                        )}
                      </Box>
                    )}
                  </Typography>

                  <Button
                    disabled={isNotWithdrawable}
                    onClick={(event) => {
                      event.stopPropagation();

                      dispatcher({ type: "setCollateralPercent", payload: 100 });
                    }}
                    size="small"
                    variant="contained"
                    sx={{
                      opacity: isNotWithdrawable ? 0 : 0.5,
                      marginLeft: isNotWithdrawable ? 0 : 1,
                      padding: isNotWithdrawable ? "0px!important" : undefined,
                      width: isNotWithdrawable ? "1px" : undefined,
                    }}
                  >
                    Max
                  </Button>
                </Box>
              </Box>
            }
          />
        </Grid>
      </Grid>
      <Grid container pb={1} spacing={1} mt="12px">
        <Grid item xs={12}>
          <Typography marginBottom="4px" variant="subtitle1">
            {borrowType === "deposit" ? "Borrow " : borrowType === "withdraw" ? "Partially Repay " : ""} {BERABORROW_ADDRESSES.debtToken.ticker}{" "}
            {borrowType === "open" && "to be Minted"}
          </Typography>
          <StyledTextfield
            sx={{ height: "54px" }}
            value={isLeveraging ? "0" : formValues.nect}
            disabled={isLeveraging}
            onChange={(event) => dispatcher({ type: "setDebt", payload: event.target.value })}
            error={validationError?.type === "minimum_nect" || validationError?.type === "max_nect_exceeded"}
            fontSize={24}
            startIcons={
              <Box display="flex" flexDirection="column" justifyContent="center" marginBottom="10px" gap="8px">
                <TokenIcon contractAddress={BERABORROW_ADDRESSES.debtToken.contractAddress} height="35px" width="35px" />
                <Typography position="absolute" left="16px" bottom="8px">
                  <Box>
                    {!isLeveraging && (
                      <>
                        {borrowType !== "withdraw" && (
                          <Box display="flex" flexDirection="row" alignItems="center">
                            {isRecoveryMode ? (
                              <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center">
                                No Fees
                              </Typography>
                            ) : (
                              <StyledTooltip
                                title={
                                  <BorrowRateBreakdown
                                    stayOpen={true}
                                    formAmounts={formAmounts}
                                    borrowingFeeRate={borrowingFeeRate}
                                    borrowingFee={borrowingFee}
                                    totalDebt={totalDebt}
                                    isManaging={isManaging}
                                    isRecoveryMode={isRecoveryMode}
                                  />
                                }
                              >
                                <Typography variant="caption" fontSize={12} display="flex" alignItems="center">
                                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center">
                                    <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle" }} /> Fee:
                                  </Typography>
                                  {formatToken(borrowingFee, BERABORROW_ADDRESSES.debtToken.decimals, 2, undefined, true)}{" "}
                                </Typography>
                              </StyledTooltip>
                            )}
                          </Box>
                        )}
                      </>
                    )}
                  </Box>
                </Typography>
              </Box>
            }
            endComponent={
              <Box display="flex" position="absolute" right="8px" bottom="8px">
                <Box display="flex" justifyContent="flex-end" alignItems="center" mt={1}>
                  <Typography component="span" variant="caption" fontSize={12}>
                    {isLeveraging ? (
                      <StyledTooltip
                        title={
                          <Box>
                            <Typography color={"var(--text-primary)"} variant="subtitle2" fontWeight={800}>
                              What's is this?
                            </Typography>
                            During leverage, {BERABORROW_ADDRESSES.debtToken.ticker} is minted and used to boost your collateral. It won’t appear in your wallet but directly
                            supports your leveraged position.
                          </Box>
                        }
                      >
                        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                          <InfoOutlined /> {BERABORROW_ADDRESSES.debtToken.ticker} only minted for leverage
                        </Box>
                      </StyledTooltip>
                    ) : (
                      <>
                        <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                          {borrowType !== "withdraw" ? "Limit:" : "Wallet:"}
                        </Typography>
                        {formatToken(maxDebtAmount > 0n ? maxDebtAmount : 0n, BERABORROW_ADDRESSES.debtToken.decimals, 0, undefined, true)}
                      </>
                    )}
                  </Typography>

                  <Button
                    onClick={(event) => {
                      event.stopPropagation();
                      dispatcher(
                        borrowType !== "withdraw"
                          ? { type: "setRatio", payload: formatBigIntPercentage(minSafeCr, 0, true) }
                          : {
                              type: "setDebt",
                              payload: formatDecimal(
                                debtBalance > userDen.netDebt(liquidationReserve) - MINIMUM_NET_DEBT ? userDen.netDebt(liquidationReserve) - MINIMUM_NET_DEBT : debtBalance,
                                BERABORROW_ADDRESSES.debtToken.decimals,
                                8
                              ),
                            }
                      );
                    }}
                    size="small"
                    variant="contained"
                    disabled={isLeveraging}
                    sx={{
                      opacity: isLeveraging ? 0 : 0.5,
                      marginLeft: isLeveraging ? 0 : 1,
                      padding: isLeveraging ? "0px!important" : undefined,
                      width: isLeveraging ? "1px" : undefined,
                    }}
                  >
                    Max
                  </Button>
                </Box>
              </Box>
            }
          />
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography component={"span"} variant="subtitle1" display="flex" alignItems="center">
              Ratio
              <StyledTooltip
                title={
                  <Box display={"flex"} flexDirection={"column"} gap={1}>
                    The ratio of your {collateral.ticker}'s value to your NECT debt. It's vital to maintain this ratio above the minimum ratio of {formatBigIntPercentage(mcr)}% to
                    avoid liquidations
                    <a href={"https://beraborrow.gitbook.io/docs/borrowing/collateral-ratio-and-liquidation"} target="_blank">
                      Read more
                    </a>
                  </Box>
                }
              >
                <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5 }} />
              </StyledTooltip>
            </Typography>

            <Chip
              variant="filled"
              label={collateralRatioHealth === "success" ? "Good" : collateralRatioHealth === "warning" ? "Cautious" : "Risky"}
              color={collateralRatioHealth}
              size="small"
              icon={<Favorite fontSize={"small"} />}
              sx={{ fontWeight: 700, fontSize: 12, opacity: borrowType === "withdraw" && formAmounts.debt > userDen.debt ? 0 : 0.75, my: "2px" }}
            />
          </Box>
          <TextField
            fullWidth
            disabled={isLeveraging}
            size="small"
            type="number"
            variant="outlined"
            value={formValues.ratio}
            onChange={(event) => dispatcher({ type: "setRatio", payload: event.target.value })}
            error={validationError?.type === "below_MCR"}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <Typography variant="h4" color={"var(--text-primary)"}>
                    %
                  </Typography>
                </InputAdornment>
              ),
            }}
          />
          {borrowType === "open" && !isLeveraging && (
            <Box px={1}>
              <Slider
                shiftStep={1}
                value={Number(formValues.ratio)}
                valueLabelFormat={(val) => val + "%"}
                min={Number(formatBigIntPercentage(mcr, 2, true))}
                onChange={(_e, value) => {
                  dispatcher({ type: "setRatio", payload: value.toString() });
                }}
                max={400}
                size="medium"
                sx={{ mb: 0 }}
              />
            </Box>
          )}
        </Grid>

        {borrowType !== "withdraw" && !isLeveragingDisabled && (
          <Grid item xs={6}>
            <StyledTooltip
              title={
                <Box>
                  <Typography color={"var(--text-primary)"} variant="subtitle2" fontWeight={800}>
                    {isLeveragingDisabled ? "Why is Leverage disabled?" : "What's is Leverage?"}
                  </Typography>

                  {!isOogaboogaAvailable ? (
                    "Leverage is unavailable due to off chain API errors"
                  ) : collateral?.lpToken !== undefined ? (
                    "Leverage is currently unavailable for LP tokens"
                  ) : !isVaulted ? (
                    "Leverage is currently unavailable for non Vaulted tokens"
                  ) : (
                    <>
                      Leverage is the use of external capital to increase exposure to a specific asset or to amplify the position size in a trade. <br />
                      {isLeveraging && <> Flash Loan Fee: {formatToken(flashLoanFee, collateral.decimals, 2, collateral.ticker)}</>}
                      <br />
                      <a href={"https://beraborrow.gitbook.io/docs/overview/leverage-in-beraborrow"} target="_blank">
                        Read more
                      </a>
                    </>
                  )}
                </Box>
              }
            >
              <Box display="flex" alignItems="center" justifyContent={"space-between"} sx={{ marginY: "2px", opacity: isVaulted ? 1 : 0.5 }}>
                <Typography component={"span"} display="flex" alignItems="center" variant="subtitle1">
                  Leverage <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", ml: 0.5 }} />
                </Typography>
                {!!formAmounts.leverage && (
                  <Typography variant="body2" fontSize={12} display="flex" alignItems="center">
                    <Typography variant="body2" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center">
                      Fee:
                    </Typography>
                    &nbsp;{formatBigIntPercentage(flashLoanRate, 2, undefined, undefined) + "% "}
                  </Typography>
                )}
              </Box>
            </StyledTooltip>

            <TextField
              disabled
              fullWidth
              sx={{ opacity: isLeveragingDisabled ? 0.5 : 1 }}
              size="small"
              type="number"
              variant="outlined"
              value={Number(formValues.leverage || "1")}
              InputProps={{
                endAdornment: <InputAdornment position="end">X</InputAdornment>,
              }}
            />
            <Box px={1}>
              <Slider
                disabled={isLeveragingDisabled}
                sx={{ opacity: isLeveragingDisabled ? 0.5 : 1 }}
                value={formValues.leverage || 1}
                shiftStep={1}
                min={1}
                max={maxLeverageNumber}
                size="medium"
                valueLabelFormat={(val) => val + "X"}
                onChange={(_e, value) => {
                  dispatcher({ type: "setLeverage", payload: Array.isArray(value) ? value[0] : value });
                }}
                marks={true}
              />
            </Box>
          </Grid>
        )}
      </Grid>
      <Divider sx={{ mb: 2, mt: borrowAction === "withdraw" ? "26px" : 0, backgroundColor: `var(--border-color)` }} />
      <Box>
        <Box display={needApproval || !isDelegated ? "block" : "none"}>
          <ValidationButton
            validationError={validationError}
            fullWidth
            variant="contained"
            onClick={() => dispatcher({ type: isDelegated ? "approveBorrow" : "delegateBorrow" })}
            disabled={!!validationError}
          >
            {isDelegated ? "Approve and Borrow" : "Delegate, Approve and Borrow"}
            {borrowType === "deposit" && " More"}
          </ValidationButton>
        </Box>
        <Box display={!needApproval && isDelegated ? "block" : "none"}>
          <>
            <TransactionButton
              disabled={!!validationError}
              transaction={transactionProps}
              onConfirmation={() => dispatcher({ type: "confirmedDenBorrow" })}
              onError={(error) => dispatcher({ type: "errorDenBorrow", payload: error })}
              validationError={validationError}
            >
              {isManaging ? (
                <>
                  {borrowType === "withdraw"
                    ? formAmounts.collateral > 0 && formAmounts.debt > 0
                      ? "Repay & Withdraw"
                      : formAmounts.collateral > 0
                        ? "Withdraw"
                        : formAmounts.debt > 0
                          ? "Repay"
                          : "Withdraw"
                    : "Borrow"}{" "}
                  {borrowType === "deposit" && "More"}
                </>
              ) : (
                <> {borrowType === "withdraw" ? "Withdraw" : "Borrow"}</>
              )}
            </TransactionButton>
          </>
        </Box>
      </Box>
      <PreloadComponent>
        <CollateralSelector urlRedirect="/" />
      </PreloadComponent>
    </Box>
  );
};

const DenBorrow = WithSuspense(Component, "paper");
export default DenBorrow;
