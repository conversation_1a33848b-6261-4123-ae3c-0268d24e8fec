import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { useHydrateAtoms } from "jotai/utils";
import { DenTransactions } from "../../@type/Transactions";

import {
  getCollateralAllowanceAtom,
  getCollateralBalanceAtom,
  getCollateralDetailsAtom,
  getDebtBalanceAtom,
  getDebtPriceAtom,
  getCollateralLeverageAllowanceAtom,
} from "../../Atoms/Tokens";
import {
  formValuesDenBorrowAtom,
  denManagersAtom,
  formValidationsDenBorrowAtom,
  denBorrowAmountsAtom,
  getBorrowingMintFeeRateAtom,
  denBorrowTypeAtom,
  getMCRAtom,
  getDenFullDetailsAtom,
  getIsApprovedDelegateCollVaultRouterAtom,
  denManagerAtom,
  getGTCRAtom,
  //   getLeverageFlashLoanFeeAtom,
  getIsApprovedDelegateLeverageRouterAtom,
  leverageSlippageAtom,
  isDenManagerSunsetting<PERSON>tom,
  getDenManagerCollateralPrice,
} from "../../Atoms/Den";
import { previewVaultedSharesAtom } from "../../Atoms/Vault";
import { accountAtom, beraborrowConstantsAtom } from "../../Atoms/Account";
import { DEFAULT_SLIPPAGE_TOLERANCE, Den, SCALING_FACTOR, MINIMUM_BORROWING_MINT_RATE } from "@Beraborrowofficial/sdk";
import { formatBigIntPercentage, formatDecimal, formatNoDecimal, regexDecimalToken } from "../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { useNotifications } from "../../Hooks/useNotifications";
import { BaseError, Hex } from "viem";
import ApproveBorrow from "../Approvals/ApproveBorrow/component";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";
import DelegateBorrow from "../DelegateBorrow/component";
import { DenBorrowDispatcherActionType } from "./types";
import { updatePostHogEventAtom, resetPostHogEventAtom, postHogEventAtom } from "../../Atoms/Account";
// import { oogaboogaHealthAtom } from "../../Atoms/Api";

export const useDenBorrowStore = (borrowAction: "borrow" | "withdraw", noHydrate?: boolean) => {
  const { addModal } = useNotifications();
  const navigate = useNavigate();
  const { data: isSunsetting } = useAtomValue(isDenManagerSunsettingAtom);
  const { data: _collateralBalance } = useAtomValue(getCollateralBalanceAtom);
  const { data: isDelegatedCollVault } = useAtomValue(getIsApprovedDelegateCollVaultRouterAtom);
  const { data: isDelegatedLeverage } = useAtomValue(getIsApprovedDelegateLeverageRouterAtom);
  const { data: denDetails } = useAtomValue(getDenFullDetailsAtom);
  const { data: _collateralAllowance } = useAtomValue(getCollateralAllowanceAtom);
  const { data: collateralLeverageAllowance } = useAtomValue(getCollateralLeverageAllowanceAtom);
  const { data: _currentBorrowingRate } = useAtomValue(getBorrowingMintFeeRateAtom);
  const { data: mcr } = useAtomValue(getMCRAtom);
  const { data: debtBalance } = useAtomValue(getDebtBalanceAtom);
  const { data: debtPrice } = useAtomValue(getDebtPriceAtom);
  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);

  const { data: previewVaultedShares } = useAtomValue(previewVaultedSharesAtom);
  const { data: GTCR } = useAtomValue(getGTCRAtom);
  //   const { data: flashLoanRate } = useAtomValue(getLeverageFlashLoanFeeAtom);
  //   const { data: oogaboogaHealth } = useAtomValue(oogaboogaHealthAtom);
  const [formValues, setFormValues] = useAtom(formValuesDenBorrowAtom);
  const [borrowType, setBorrowType] = useAtom(denBorrowTypeAtom);
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const resetPostHogEvent = useSetAtom(resetPostHogEventAtom);
  const account = useAtomValue(accountAtom);
  const denManagers = useAtomValue(denManagersAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const denManagerAddr = useAtomValue(denManagerAtom);
  const validationError = useAtomValue(formValidationsDenBorrowAtom);
  const beraborrowConstants = useAtomValue(beraborrowConstantsAtom);
  const formAmounts = useAtomValue(denBorrowAmountsAtom);
  const leverageSlippage = useAtomValue(leverageSlippageAtom);
  const { den: userDen, densTotal, ccr } = denDetails; // price: collateralPrice
  const isRecoveryMode = GTCR <= ccr;
  const isManaging = userDen.status === "open";
  const isLeveraging = false; // formValues.leverage !== undefined;
  const liquidationReserve = beraborrowConstants.liquidationReserve;
  const MINIMUM_NET_DEBT = beraborrowConstants.minimumDebt + liquidationReserve;
  const currentBorrowingRate = isRecoveryMode ? 0n : _currentBorrowingRate;
  const flashLoanFee = 0n; //formAmounts.getFlashLoanFee(flashLoanRate);
  const flashLoanSlippage = isLeveraging ? ((formAmounts.collateral + flashLoanFee) * leverageSlippage) / SCALING_FACTOR : 0n;
  const collateralBalance = _collateralBalance; // > MIN_GAS_FEE + flashLoanFee + flashLoanSlippage ? _collateralBalance - MIN_GAS_FEE - flashLoanFee - flashLoanSlippage : 0n;
  const getBorrowFees = (debt: bigint) => {
    return (borrowType === "open" ? liquidationReserve : 0n) + (borrowType !== "withdraw" ? (debt * currentBorrowingRate) / SCALING_FACTOR : 0n); //fees
  };
  const minSafeCr = ccr > mcr ? ccr + SCALING_FACTOR / 10n : mcr + SCALING_FACTOR / 5n;
  const borrowFees = getBorrowFees(formAmounts.debt);
  const adjustVariables = borrowType === "deposit" ? formAmounts.getDepositVariables() : formAmounts.getWithdrawVariables();
  const borrowingFee = getBorrowFees(formAmounts.debt);
  const collateralAllowance = isLeveraging ? collateralLeverageAllowance : _collateralAllowance;
  const isDelegated = isLeveraging ? isDelegatedLeverage : isDelegatedCollVault;
  const leveragedDen = formAmounts.applyLeverage(formAmounts.getCollateralShares(previewVaultedShares), currentBorrowingRate, collateralPrice, debtPrice);
  const updatedDenCollateralRatio = (
    isLeveraging
      ? userDen.addCollateral(leveragedDen.getCollateralShares(previewVaultedShares)).addDebt(leveragedDen.debt + borrowingFee)
      : borrowType !== "withdraw"
        ? userDen.addCollateral(formAmounts.getCollateralShares(previewVaultedShares)).addDebt(formAmounts.debt).addDebt(borrowFees) //fees
        : userDen.subtractCollateral(formAmounts.collateral).subtractDebt(formAmounts.debt)
  ).collateralRatio(collateralPrice);
  const averageCollateralRatio = densTotal.collateralRatio(collateralPrice);
  const collateralRatioHealth: "error" | "success" | "warning" =
    updatedDenCollateralRatio < ccr ? "error" : updatedDenCollateralRatio < averageCollateralRatio ? "warning" : "success";
  const maxDebtAmount =
    borrowType !== "withdraw"
      ? userDen.calculateDebt(formAmounts.getCollateralShares(previewVaultedShares) + userDen.collateral, collateralPrice, undefined, minSafeCr, getBorrowFees) - userDen.debt
      : debtBalance - beraborrowConstants.liquidationReserve;
  const totalDebt = formAmounts.debt + (borrowType !== "withdraw" ? borrowingFee : 0n);
  const wrappedCollateralTicker = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.denManagers[BERABORROW_ADDRESSES.wrappedDenManagers[denManagerAddr]]?.collateral]
    ?.ticker as string | undefined;
  const isNotWithdrawable = borrowType === "withdraw" && collateralDetails.ticker.toLowerCase() === "honey";
  const maxLeverageNumber = Math.round(Number(formatDecimal(userDen.calculateLeverage(mcr), 18)));
  const isVaulted = !!BERABORROW_ADDRESSES.denManagers[denManagerAddr].vault;
  const isLeveragingDisabled = true; // !isVaulted || !oogaboogaHealth?.healthy || collateralDetails?.lpToken !== undefined;
  const getFormHydratedValues = () => {
    if (userDen.status === "open") {
      const currentRatio = userDen.collateralRatio(collateralPrice);
      return {
        collateral: formatDecimal(borrowAction !== "withdraw" ? collateralBalance : 0n, collateralDetails.decimals, collateralDetails.lpToken ? undefined : 0),
        nect: formatDecimal(
          userDen.calculateDebt(borrowAction !== "withdraw" ? collateralBalance : 0n, collateralPrice, previewVaultedShares, currentRatio, getBorrowFees),
          BERABORROW_ADDRESSES.debtToken.decimals,
          0,
          true
        ).replace("-", ""),
        ratio: formatBigIntPercentage(
          userDen
            .addCollateral(borrowAction !== "withdraw" ? collateralBalance : 0n)
            .addDebt(
              userDen.calculateDebt(
                userDen.getCollateralShares(previewVaultedShares, borrowAction !== "withdraw" ? collateralBalance : 0n),
                collateralPrice,
                undefined,
                currentRatio,
                getBorrowFees
              )
            )
            .collateralRatio(collateralPrice),
          2,
          true
        ),
      };
    } else {
      return {
        collateral: formatDecimal(collateralBalance, collateralDetails.vaultDecimals, collateralDetails.lpToken ? undefined : 2),
        nect: formatDecimal(
          userDen.calculateDebt(collateralBalance, collateralPrice, previewVaultedShares, minSafeCr, getBorrowFees),
          BERABORROW_ADDRESSES.debtToken.decimals,
          0,
          true
        ).replace("-", ""),
        ratio: formatBigIntPercentage(minSafeCr, 2, true),
      };
    }
  };
  useHydrateAtoms([
    [
      postHogEventAtom,
      { type: `den${borrowAction === "borrow" ? (userDen.status === "open" ? "Deposit" : "Open") : "Withdraw"}`, page_rel_path: location.pathname, denManager: denManagerAddr },
    ],
  ]);
  useEffect(() => {
    if (!noHydrate) {
      setBorrowType(borrowAction === "borrow" ? (userDen.status === "open" ? "deposit" : "open") : borrowAction);
      const hydratedValues = getFormHydratedValues();
      updatePostHogEvent({
        type: `den${isLeveraging ? "Leverage" : ""}${borrowAction === "borrow" ? (userDen.status === "open" ? "Deposit" : "Open") : "Withdraw"}`,
        page_rel_path: location.pathname,
        denManager: denManagerAddr,
        collateral_token: collateralDetails.ticker,
        qty_collateral_token: hydratedValues.collateral,
        token_borrowed: BERABORROW_ADDRESSES.debtToken.ticker,
        qty_borrowed_token: hydratedValues.nect,
        ratio: hydratedValues.ratio,
        total_fee: formatDecimal(getBorrowFees(formatNoDecimal(hydratedValues.nect, BERABORROW_ADDRESSES.debtToken.decimals)), BERABORROW_ADDRESSES.debtToken.decimals),
      });
      setFormValues(hydratedValues);
    }
  }, [borrowAction, denManagerAddr, account]);
  //   useEffect(() => {
  //     if (oogaboogaHealth && !oogaboogaHealth?.healthy) {
  //       setLeverage(1);
  //     }
  //   }, [oogaboogaHealth]);
  const dispatcher = (action: DenBorrowDispatcherActionType) => {
    switch (action.type) {
      case "changeDenManager": {
        const denManager = action.payload as Hex;
        const ticker = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.denManagers[denManager].collateral].ticker;
        navigate(`/den/${borrowType !== "withdraw" ? "borrow" : "manage"}/${ticker}`);
        break;
      }
      case "setCollateral":
        if ((formValues.leverage || 0) > 1) {
          setLeveragedCollateral(action.payload);
        } else {
          setCollateral(action.payload);
        }
        break;
      case "setDebt":
        setDebt(action.payload);
        break;
      case "setRatio":
        setRatio(action.payload);
        break;
      case "setLeverage":
        setLeverage(action.payload);
        break;
      case "approveBorrow":
        updatePostHogEvent({ type: `approveBorrow` });
        addModal({
          id: "approveBorrow",
          title: "In progress",
          Component: (
            <ApproveBorrow
              formAmounts={formAmounts}
              onConfirmation={() => {
                dispatcher({ type: "confirmedDenBorrow" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      case "delegateBorrow":
        updatePostHogEvent({ type: `delegateBorrow` });
        addModal({
          id: "delegateBorrow",
          title: "In progress",
          Component: (
            <DelegateBorrow
              formAmounts={formAmounts}
              onConfirmation={() => {
                dispatcher({ type: "approveBorrow" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      case "confirmedDenBorrow":
        resetPostHogEvent({ type: `den${borrowAction === "borrow" ? "Deposit" : "Withdraw"}`, page_rel_path: location.pathname });
        setFormValues({
          collateral: "0",
          nect: "0",
          ratio: "0",
        });
        break;
      case "errorDenBorrow":
        const error = action.payload as BaseError;
        if (error?.shortMessage?.includes("Delegate not approved")) {
          setTimeout(() => {
            dispatcher({ type: "delegateBorrow" });
          }, 1000);
        }
        break;
      case "setCollateralPercent":
        const value = formatDecimal(
          ((borrowType !== "withdraw" ? collateralBalance : userDen.collateral) * BigInt(action.payload)) / 100n,
          borrowType !== "withdraw" ? collateralDetails.decimals : collateralDetails.vaultDecimals,
          collateralDetails.lpToken ? undefined : 5,
          true
        );
        if ((formValues.leverage || 0) > 1) {
          setLeveragedCollateral(value);
        } else {
          setCollateral(value);
        }

        updatePostHogEvent({ collateral_last_clicked_input_btn: action.payload + "%", last_action: "setCollateralPercent" });
        break;
      case "setBorrowType":
        navigate("/den/" + action.payload);
        break;
      default:
        break;
    }
  };
  const setLeverage = (value: number) => {
    //check max
    if (value > maxLeverageNumber) {
      value = maxLeverageNumber;
    }
    if (value === 1) {
      //handle adjust den Ratio
      setFormValues({ ...formValues, ratio: formatBigIntPercentage(formAmounts.collateralRatio(collateralPrice), 2, true, true), leverage: undefined });
      //updatePostHog
    } else {
      const leveragedDen = formAmounts.applyLeverage(
        formAmounts.getCollateralShares(previewVaultedShares),
        currentBorrowingRate,
        collateralPrice,
        debtPrice,
        undefined,
        BigInt(value) * SCALING_FACTOR
      );
      const updatedDen = userDen.addCollateral(leveragedDen.getCollateralShares(previewVaultedShares)).addDebt(leveragedDen.debt + getBorrowFees(leveragedDen.debt));

      updatePostHogEvent({
        type: `den${"Leverage"}${borrowAction === "borrow" ? (userDen.status === "open" ? "Deposit" : "Open") : "Withdraw"}`,
        qty_collateral_token: formatDecimal(leveragedDen.collateral, collateralDetails.decimals),
        qty_borrowed_token: formatDecimal(leveragedDen.debt, BERABORROW_ADDRESSES.debtToken.decimals),
        ratio: formatBigIntPercentage(updatedDen.collateralRatio(collateralPrice), 2, true, true),
        total_fee: formatDecimal(getBorrowFees(leveragedDen.debt), BERABORROW_ADDRESSES.debtToken.decimals),
        leverage: (value || 1).toString(),
        last_action: "setLeverage",
      });
      setFormValues({
        ...formValues,
        ratio: formatBigIntPercentage(updatedDen.collateralRatio(collateralPrice), 2, true, true),
        leverage: value,
      });
    }
  };
  const setLeveragedCollateral = (value: string) => {
    value = regexDecimalToken(value, collateralDetails.decimals);
    const amount = formatNoDecimal(value, collateralDetails.decimals);
    const leveragedDen = formAmounts.applyLeverage(formAmounts.getCollateralShares(previewVaultedShares, amount), currentBorrowingRate, collateralPrice, debtPrice, amount);
    const updatedDen = userDen.addCollateral(leveragedDen.getCollateralShares(previewVaultedShares)).addDebt(leveragedDen.debt + getBorrowFees(leveragedDen.debt));
    updatePostHogEvent({
      type: `den${"Leverage"}${borrowAction === "borrow" ? (userDen.status === "open" ? "Deposit" : "Open") : "Withdraw"}`,
      qty_collateral_token: formatDecimal(leveragedDen.collateral, collateralDetails.decimals),
      qty_borrowed_token: formatDecimal(leveragedDen.debt, BERABORROW_ADDRESSES.debtToken.decimals),
      ratio: formatBigIntPercentage(updatedDen.collateralRatio(collateralPrice), 2, true, true),
      leverage: (formValues.leverage || 1).toString(),
      total_fee: formatDecimal(getBorrowFees(leveragedDen.debt), BERABORROW_ADDRESSES.debtToken.decimals),
      last_action: "setCollateral",
    });
    setFormValues({
      ...formValues,
      collateral: value,
      ratio: formatBigIntPercentage(updatedDen.collateralRatio(collateralPrice), 2, true, true),
    });
  };

  const setRatio = (value: string) => {
    value = regexDecimalToken(value, 16);
    const amount = formatNoDecimal(value, 16);
    const updatedCollateral = borrowType !== "withdraw" ? userDen.collateral + formAmounts.getCollateralShares(previewVaultedShares) : userDen.collateral - formAmounts.collateral;
    const newDebt = userDen.calculateDebt(
      updatedCollateral,
      collateralPrice,
      undefined,
      amount,
      getBorrowFees //fees
    );
    const possibleDebt = borrowType !== "withdraw" ? (newDebt > userDen.debt ? newDebt - userDen.debt : 0n) : userDen.debt > newDebt ? userDen.debt - newDebt : 0n;
    updatePostHogEvent({
      type: `den${borrowAction === "borrow" ? (userDen.status === "open" ? "Deposit" : "Open") : "Withdraw"}`,
      qty_collateral_token: formValues.collateral,
      qty_borrowed_token: formatDecimal(possibleDebt, BERABORROW_ADDRESSES.debtToken.decimals, 5),
      ratio: value,
      total_fee: formatDecimal(getBorrowFees(possibleDebt), BERABORROW_ADDRESSES.debtToken.decimals),
      last_action: "setRatio",
    });
    setFormValues({ collateral: formValues.collateral, nect: formatDecimal(possibleDebt, BERABORROW_ADDRESSES.debtToken.decimals, 5), ratio: value });
  };
  const setCollateral = (value: string) => {
    value = regexDecimalToken(value, borrowType !== "withdraw" ? collateralDetails.decimals : collateralDetails.vaultDecimals);
    const amount = formatNoDecimal(value, borrowType !== "withdraw" ? collateralDetails.decimals : collateralDetails.vaultDecimals);
    const newDen = new Den(
      amount,
      borrowType === "open" && (formValues.nect === "0" || !formValues.nect) ? userDen.calculateDebt(amount, collateralPrice, previewVaultedShares) : formAmounts.debt
    );
    const updatedDen =
      borrowType !== "withdraw"
        ? userDen.addCollateral(newDen.getCollateralShares(previewVaultedShares)).addDebt(newDen.debt).addDebt(getBorrowFees(newDen.debt)) //fees
        : userDen.subtractCollateral(newDen.collateral).subtractDebt(newDen.debt);
    const newDebt =
      borrowType === "open" && (formValues.nect === "0" || !formValues.nect) ? formatDecimal(newDen.debt, BERABORROW_ADDRESSES.debtToken.decimals, 5) : formValues.nect;
    updatePostHogEvent({
      type: `den${borrowAction === "borrow" ? (userDen.status === "open" ? "Deposit" : "Open") : "Withdraw"}`,
      qty_collateral_token: value,
      qty_borrowed_token: newDebt,
      ratio: formatBigIntPercentage(updatedDen.collateralRatio(collateralPrice), 2, true, true),
      total_fee: formatDecimal(getBorrowFees(formatNoDecimal(newDebt, BERABORROW_ADDRESSES.debtToken.decimals)), BERABORROW_ADDRESSES.debtToken.decimals),
      last_action: "setCollateral",
    });
    setFormValues({
      collateral: value,
      nect: newDebt,
      ratio: formatBigIntPercentage(updatedDen.collateralRatio(collateralPrice), 2, true, true),
    });
  };
  const setDebt = (value: string) => {
    value = regexDecimalToken(value, BERABORROW_ADDRESSES.debtToken.decimals);
    const amount = formatNoDecimal(value, BERABORROW_ADDRESSES.debtToken.decimals);
    const newDen = new Den(formAmounts.getCollateralShares(previewVaultedShares), amount);
    const updatedDen =
      borrowType !== "withdraw"
        ? userDen.addCollateral(newDen.collateral).addDebt(newDen.debt).addDebt(getBorrowFees(amount)) //fees
        : userDen.subtractCollateral(newDen.collateral).subtractDebt(newDen.debt);
    updatePostHogEvent({
      type: `den${borrowAction === "borrow" ? (userDen.status === "open" ? "Deposit" : "Open") : "Withdraw"}`,
      qty_collateral_token: formValues.collateral,
      qty_borrowed_token: value,
      ratio: formatBigIntPercentage(updatedDen.collateralRatio(collateralPrice), 2, true, true),
      total_fee: formatDecimal(getBorrowFees(formatNoDecimal(value, BERABORROW_ADDRESSES.debtToken.decimals)), BERABORROW_ADDRESSES.debtToken.decimals),
      last_action: "setDebt",
    });
    setFormValues({ collateral: formValues.collateral, nect: value, ratio: formatBigIntPercentage(updatedDen.collateralRatio(collateralPrice), 2, true, true) });
  };

  const transactionProps: DenTransactions =
    //    !!formAmounts.leverage
    // ? borrowType !== "open"
    //   ? {
    //       type: "adjustLeverage",
    //       variables: [
    //         formAmounts.leverage,
    //         formAmounts.collateral,
    //         { borrowNECT: leveragedDen.debt, depositCollateral: leveragedDen.collateral },
    //         currentBorrowingRate,
    //         leverageSlippage,
    //         // collateralDetails.contractAddress === zeroAddress,
    //       ],
    //     }
    //   : {
    //       type: "openLeverage",
    //       variables: [
    //         formAmounts.leverage,
    //         formAmounts.collateral,
    //         { borrowNECT: leveragedDen.debt, depositCollateral: leveragedDen.collateral },
    //         currentBorrowingRate,
    //         leverageSlippage,
    //         // collateralDetails.contractAddress === zeroAddress,
    //       ],
    //     }
    // :
    borrowType !== "open"
      ? {
          type: "adjustDen",
          variables: [borrowType === "deposit" ? formAmounts.getDepositVariables() : formAmounts.getWithdrawVariables(), currentBorrowingRate + MINIMUM_BORROWING_MINT_RATE],
        }
      : {
          type: "openDen",
          variables: [
            {
              depositCollateral: formAmounts.collateral,
              borrowNECT: formAmounts.debt,
            },
            currentBorrowingRate + MINIMUM_BORROWING_MINT_RATE,
          ],
        };
  return {
    denManagers,
    isNotWithdrawable,
    denManagerAddr,
    collateral: { balance: collateralBalance, price: collateralPrice, allowance: collateralAllowance, ...collateralDetails, wrappedCollateralTicker },
    userDen,
    formValues,
    formAmounts,
    validationError,
    isManaging,
    maxDebtAmount,
    needApproval: borrowType !== "withdraw" ? collateralAllowance < formAmounts.collateral + flashLoanFee + flashLoanSlippage : false,
    borrowType,
    adjustVariables,
    mcr,
    totalDebt,
    debtBalance,
    borrowingFee,
    borrowingFeeRate: currentBorrowingRate,
    maxBorrowingRate:
      _currentBorrowingRate + DEFAULT_SLIPPAGE_TOLERANCE < (SCALING_FACTOR / 100n) * 5n ? _currentBorrowingRate + DEFAULT_SLIPPAGE_TOLERANCE : (SCALING_FACTOR / 100n) * 5n,
    collateralRatioHealth,
    dispatcher,
    isRecoveryMode,
    isDelegated,
    MINIMUM_NET_DEBT,
    liquidationReserve,
    isLeveraging: false,
    maxLeverageNumber,
    isVaulted,
    previewVaultedShares,
    flashLoanFee,
    flashLoanRate: 0n,
    transactionProps,
    isOogaboogaAvailable: false, // !!oogaboogaHealth?.healthy,
    isLeveragingDisabled,
    isSunsetting,
    minSafeCr,
  };
};

export const getDenBorrowScopedAtoms = () => [formValuesDenBorrowAtom, formValidationsDenBorrowAtom];
