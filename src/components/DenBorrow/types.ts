export type DenBorrowDispatcherActionType =
  | ChangeDenManager
  | ConfirmedDenBorrow
  | SetDebtFormValue
  | SetRatioFormValue
  | SetCollateralFormValue
  | SetLeverageFormValue
  | ApproveBorrow
  | ChangeCollateralPercent
  | SetBorrowType
  | DelegateBorrow
  | ErrorDenBorrow;

type ChangeDenManager = {
  type: "changeDenManager";
  payload: string;
};
type ConfirmedDenBorrow = {
  type: "confirmedDenBorrow";
};
type ErrorDenBorrow = {
  type: "errorDenBorrow";
  payload: unknown;
};
type SetCollateralFormValue = {
  type: "setCollateral";
  payload: string;
};
type SetRatioFormValue = {
  type: "setRatio";
  payload: string;
};
type SetDebtFormValue = {
  type: "setDebt";
  payload: string;
};
type SetLeverageFormValue = {
  type: "setLeverage";
  payload: number;
};

type ChangeCollateralPercent = {
  type: "setCollateralPercent";
  payload: number;
};
type SetBorrowType = {
  type: "setBorrowType";
  payload: "open" | "deposit" | "withdraw";
};
type ApproveBorrow = {
  type: "approveBorrow";
};
type DelegateBorrow = {
  type: "delegateBorrow";
};
