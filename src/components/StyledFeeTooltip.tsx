import React from "react";
import { styled, Tooltip, tooltipClasses, TooltipProps } from "@mui/material";

const Component: React.FC<TooltipProps> = styled(({ className, ...props }: TooltipProps) => {
  return <Tooltip {...props} enterTouchDelay={0} classes={{ popper: className }} />;
})(() => ({
  [`& .${tooltipClasses.tooltip}`]: {
    padding: "16px",
    borderRadius: "16px",
    opacity: 1,
    backgroundColor: "var(--background-accent-full)",
    color: "var(--text-secondary)", // Set text color
    boxShadow: "none", // Remove box shadow
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: "transparent", // Transparent arrow
  },
}));

const StyledTooltip = Component;
export default StyledTooltip;
