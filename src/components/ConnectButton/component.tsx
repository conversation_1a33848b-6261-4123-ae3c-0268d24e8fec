import { Box, Button, ButtonProps } from "@mui/material";
import { useConnectButtonStore } from "./store";
import PowerIcon from "@mui/icons-material/Power";
import { PRODUCTION, ANON_ADDRESS } from "../../utils/constants";
import { zeroAddress } from "viem";

const ConnectButton: React.FC<ButtonProps & { children: React.ReactNode; enforce?: boolean }> = ({ enforce, children, ...buttonProps }) => {
  const { open, isConnected } = useConnectButtonStore();
  return (
    <Box>
      {isConnected || (!enforce && !PRODUCTION && ANON_ADDRESS !== zeroAddress) ? (
        <>{children}</>
      ) : (
        <>
          <Button
            fullWidth
            variant="contained"
            sx={{ alignItems: "center", px: 2, py: "18px" }}
            {...buttonProps}
            onClick={() => {
              open();
            }}
          >
            <PowerIcon />
            <Box sx={{ ml: 2 }}>Connect wallet</Box>
          </Button>
        </>
      )}
    </Box>
  );
};
export default ConnectButton;
