import React from "react";
import { Accordion, AccordionSummary, AccordionDetails, Box, Grid, Typography, useTheme, Divider, <PERSON><PERSON>, Chip } from "@mui/material";
import { useLspPositionStore } from "./store";
import { ExpandMore, InfoOutlined } from "@mui/icons-material";
import TokenPair from "../../TokenPair";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import StyledTooltip from "../../StyledFeeTooltip";
import { formatBigIntPercentage, formatToken } from "../../../utils/helpers";
import { useNavigate } from "react-router-dom";
import { SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";

interface PositionProps {
  grid?: number;
}

const Component: React.FC<PositionProps> = ({}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentDeposit, apy, poolTvl, poolShare, myTotalAssets } = useLspPositionStore();
  return (
    <Grid item xs={12} display={"block"} key="Pool">
      <>
        <Accordion
          sx={{
            paddingY: "8px",
            paddingX: "0px",

            borderRadius: "16px",
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box display="flex" justifyContent="space-between" flex={1}>
              <TokenPair
                heading={`Stability Pool`}
                token1={{
                  contractAddress: BERABORROW_ADDRESSES.debtToken.contractAddress,
                  ticker: BERABORROW_ADDRESSES.debtToken.ticker,
                  decimals: BERABORROW_ADDRESSES.debtToken.decimals,
                  value: currentDeposit.shares,
                  staked: true,
                }}
              />
              <Box alignContent={"right"} textAlign={"right"} pr={1}>
                <Typography variant={"h1"} color={"var(--text-primary)"} pl={1}>
                  ${formatToken(myTotalAssets, SCALING_FACTOR_DECIMALS, 0, undefined, true)}{" "}
                </Typography>
                {apy !== undefined && (
                  <Chip
                    variant="outlined"
                    label={formatBigIntPercentage(apy, 2) + "% APY"}
                    color={"success"}
                    size="small"
                    sx={{ fontWeight: 600, fontSize: 10, opacity: 0.75, ml: 1, mt: "2px" }}
                  />
                )}
              </Box>
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 1 }}>
            <>
              {/* {!!(rewards.emissions.length || rewards.collaterals.length) && (
                <>
                  <Divider sx={{ backgroundColor: "var(--border-color)", mb: 1 }} />{" "}
                  <Typography variant="h4" color={"var(--text-primary)"} display="flex" alignItems="center" pb={1}>
                    Historical Composition
                  </Typography>
                  {[...rewards.emissions, ...rewards.collaterals].map((item) => {
                    const collateralTicker = BERABORROW_ADDRESSES.collateralTokens[checksumAddress(item.asset.id)]?.ticker ?? item.asset.symbol;
                    const collateralAmount = (BigInt(item.balance) * BigInt(rewards.shares)) / BigInt(rewards.totalShares);
                    return (
                      <Box key={item.asset.id} display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                        <Box>
                          <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600}>
                            <TokenIcon contractAddress={checksumAddress(item.asset.id)} height={"25px"} width={"25px"} /> <Box pl={1}>{collateralTicker}</Box>
                          </Typography>
                        </Box>

                        <Typography variant="h5" fontWeight={600} color="var(--text-success)">
                          ${formatToken((collateralAmount * BigInt(item.price.price)) / SCALING_FACTOR, item.asset.decimals, 2)}
                        </Typography>
                      </Box>
                    );
                  })}
                </>
              )} */}

              <Divider sx={{ backgroundColor: "var(--border-color)", mb: 1 }} />
              <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                <Box>
                  <StyledTooltip
                    title={
                      <Box display={"flex"} flexDirection={"column"} gap={1}>
                        <Typography variant="h6" fontWeight={700} color={"var(--text-primary)"}>
                          Your share of the Stability Pool which effects amount of rewards you will receive
                        </Typography>
                      </Box>
                    }
                  >
                    <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                      <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "bottom" }} />
                      &nbsp; Pool Share
                    </Typography>
                  </StyledTooltip>
                </Box>

                <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                  {formatBigIntPercentage(poolShare, 2)}%
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                <Box>
                  <StyledTooltip
                    title={
                      <Box display={"flex"} flexDirection={"column"} gap={1}>
                        <Typography variant="h6" fontWeight={700} color={"var(--text-primary)"}>
                          Total amount of {BERABORROW_ADDRESSES.debtToken.ticker} in the Stability Pool
                        </Typography>
                      </Box>
                    }
                  >
                    <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                      <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "bottom" }} />
                      &nbsp; Pool TVL
                    </Typography>
                  </StyledTooltip>
                </Box>

                <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                  ${formatToken(poolTvl, SCALING_FACTOR_DECIMALS, 0)}
                </Typography>
              </Box>

              <Divider sx={{ backgroundColor: "var(--border-color)", my: 1 }} />
              <Button
                onClick={() => navigate(`/pool/deposit`)}
                fullWidth
                variant={"contained"}
                sx={{ backgroundColor: "#EC6F151A", color: "var(--primary-main)", borderColor: "transparent" }}
              >
                <Typography variant="h2">Deposit</Typography>
              </Button>
            </>
          </AccordionDetails>
        </Accordion>
      </>
    </Grid>
  );
};

const LspPosition = Component;
export default LspPosition;
