import { useAtomValue } from "jotai";
import { getStabilityDepositAtom, getPoolAPY<PERSON>tom, convertSharesToAssetsAtom } from "../../../Atoms/StabilityPool";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";

export const useLspPositionStore = () => {
  const { data: currentDeposit } = useAtomValue(getStabilityDepositAtom);
  const { data: apy } = useAtomValue(getPoolAPYAtom);
  const { data: shareToAssetRatio } = useAtomValue(convertSharesToAssetsAtom);

  const poolShare = currentDeposit.sharesTotalSupply === 0n ? 0n : (currentDeposit.shares * SCALING_FACTOR) / currentDeposit.sharesTotalSupply;
  const myTotalAssets = currentDeposit.convertSharesToAssets(currentDeposit.shares, shareToAssetRatio);
  const poolTvl = currentDeposit.convertSharesToAssets(currentDeposit.sharesTotalSupply, shareToAssetRatio);

  return { currentDeposit, apy, poolTvl, poolShare, myTotalAssets };
};
export const getPositionScopedAtoms = () => [];
