import React from "react";
import { Accordion, AccordionSummary, AccordionDetails, Box, Grid, Typography, useTheme, Divider, Button, Chip, CircularProgress } from "@mui/material";
import { Hex } from "viem";
import withScopedProvider from "../../../providers/WithScopedProvider";
import { getPositionScopedAtoms, usePositionStore } from "./store";
import { ExpandMore } from "@mui/icons-material";
import TokenPair from "../../TokenPair";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import { HealthFactorBar } from "../../HealthFactorBar/HealthFactorBar";
import { formatBigIntPercentage, formatToken } from "../../../utils/helpers";
import { useNavigate } from "react-router-dom";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import VaultCompositionScoped from "../../VaultComposition/ScopedComponent";
interface PositionProps {
  denManagerAddress: Hex;
  grid?: number;
}

const Component: React.FC<PositionProps> = ({ denManagerAddress }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { collateral, userDen, isManaging, currentBorrowingRate, interestRates, apy, vaulted, vaultDeposit } = usePositionStore(denManagerAddress);

  return (
    <Grid item xs={12} display={isManaging ? "block" : "none"}>
      <>
        <Accordion
          sx={{
            paddingY: "8px",
            paddingX: "0px",

            borderRadius: "16px",
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box display="flex" justifyContent="space-between" flex={1}>
              <TokenPair
                token1={{
                  ticker: collateral.ticker,
                  contractAddress: collateral.contractAddress,
                  decimals: collateral.vaultDecimals,
                  value: userDen.collateral,
                  vaulted: !!vaulted,
                }}
                token2={{
                  contractAddress: BERABORROW_ADDRESSES.debtToken.contractAddress,
                  ticker: BERABORROW_ADDRESSES.debtToken.ticker,
                  decimals: BERABORROW_ADDRESSES.debtToken.decimals,
                  value: userDen.debt,
                }}
              />
              <Box alignContent={"right"} textAlign={"right"} pr={1}>
                <Typography variant={"h1"} color={"var(--text-primary)"} pl={1}>
                  ${formatToken((userDen.collateral * collateral.price) / SCALING_FACTOR, collateral.vaultDecimals, 0, undefined, true)}{" "}
                </Typography>
                {!!apy && (
                  <Chip
                    variant="outlined"
                    label={formatBigIntPercentage(apy, 2) + "% APY"}
                    color={"success"}
                    size="small"
                    sx={{ fontWeight: 600, fontSize: 10, opacity: 0.75, ml: 1, mt: "2px" }}
                  />
                )}
              </Box>
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 1 }}>
            <>
              <Divider sx={{ backgroundColor: "var(--border-color)", mb: 1 }} />
              <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                <Box>
                  <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                    Interest rate
                  </Typography>
                </Box>
                {interestRates !== undefined ? (
                  <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                    {formatBigIntPercentage(BigInt(interestRates), 2, true, true, 4)}%
                  </Typography>
                ) : (
                  <>
                    <CircularProgress size={"14px"} color="inherit" />
                  </>
                )}
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                <Box>
                  <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                    Borrowing Rate
                  </Typography>
                </Box>

                <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                  {formatBigIntPercentage(currentBorrowingRate, 0)}%
                </Typography>
              </Box>

              <Divider sx={{ backgroundColor: "var(--border-color)", my: 1 }} />
              {!!vaulted && (
                <Box mt={1.5}>
                  <Typography variant="h2">{collateral.ticker + " Vault"}</Typography>

                  <VaultCompositionScoped
                    denManagerAddress={denManagerAddress}
                    shares={userDen.collateral}
                    collPrice={collateral.price}
                    removeWithdrawFee={vaultDeposit.withdrawFee}
                  />
                </Box>
              )}
              <Divider sx={{ backgroundColor: "var(--border-color)", my: 1 }} />

              <HealthFactorBar />
              <Divider sx={{ backgroundColor: "var(--border-color)", my: 1 }} />
              <Button
                onClick={() => navigate(`/den/borrow/${collateral.ticker}`)}
                fullWidth
                variant={"contained"}
                sx={{ backgroundColor: "#EC6F151A", color: "var(--primary-main)", borderColor: "transparent" }}
              >
                <Typography variant="h2">Manage</Typography>
              </Button>
            </>
          </AccordionDetails>
        </Accordion>
      </>
    </Grid>
  );
};

const Position = withScopedProvider(Component, getPositionScopedAtoms());
export default Position;
