import { useAtomValue } from "jotai";
import { collateralTokenAtom, collateralTypeSelectorAtom, getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { denManagerAtom, _denManagerAddrAtom, getDenManagerCollateralPrice, getDenInterestRateAtom, getBorrowingMintFeeRateAtom, getDenFullDetailsAtom } from "../../../Atoms/Den";
import { Hex, zeroAddress } from "viem";
import { useHydrateAtoms } from "jotai/utils";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";
import { getVaultApyAtom, getVaultDepositAtom, vaultAtom } from "../../../Atoms/Vault";
import { queryClientAtom } from "jotai-tanstack-query";
import { useAccount } from "wagmi";
import { useQueryClient } from "@tanstack/react-query";
import { accountAtom } from "../../../Atoms/Account";

export const usePositionStore = (denManagerAddr: Hex) => {
  const queryClient = useQueryClient();
  const { address } = useAccount();
  useHydrateAtoms([
    [denManagerAtom, denManagerAddr],
    [queryClientAtom, queryClient],
    [accountAtom, address ?? zeroAddress],
    [collateralTypeSelectorAtom, "den"],
  ]);

  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);
  const { data: apy } = useAtomValue(getVaultApyAtom);
  const { data: currentBorrowingRate } = useAtomValue(getBorrowingMintFeeRateAtom);
  const { data: interestRates } = useAtomValue(getDenInterestRateAtom);
  const { data: denDetails } = useAtomValue(getDenFullDetailsAtom);
  const { data: vaultDeposit } = useAtomValue(getVaultDepositAtom);

  const { den: userDen } = denDetails;
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const isManaging = userDen.status === "open";

  const vaulted = !!BERABORROW_ADDRESSES.denManagers[denManagerAddr].vault;
  return {
    apy,
    collateral: { price: collateralPrice, ...collateralDetails },
    isManaging,
    userDen,
    currentBorrowingRate,
    interestRates,
    vaulted,
    vaultDeposit,
  };
};
export const getPositionScopedAtoms = () => [
  getCollateralDetailsAtom,
  collateralTokenAtom,
  getVaultDepositAtom,
  vaultAtom,
  collateralTypeSelectorAtom,
  denManagerAtom,
  _denManagerAddrAtom,
  queryClientAtom,
];
