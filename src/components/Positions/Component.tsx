import React from "react";
import { Grid } from "@mui/material";

import { useAtomValue } from "jotai";
import { denManagersAtom } from "../../Atoms/Den";
import Position from "./Position/Component";
import LspPosition from "./LspPosition/Component";
import WithSuspense from "../../providers/WithSuspense";
import { publicVaultsAtom } from "../../Atoms/Vault";
import VaultPosition from "./VaultPosition/Component";

const Component: React.FC = () => {
  const denManagers = useAtomValue(denManagersAtom);
  const vaults = useAtomValue(publicVaultsAtom);

  return (
    <Grid container pb={2} gap={1}>
      <LspPosition key={"LSP"} />
      {vaults.map((vault) => {
        return <VaultPosition key={vault.contractAddress} contractAddress={vault.contractAddress} grid={6} />;
      })}
      {denManagers
        .filter((denManager) => !denManager.wrappedCollateral)
        .map((denManager) => {
          return <Position key={denManager.contractAddress} denManagerAddress={denManager.contractAddress} grid={6} />;
        })}
    </Grid>
  );
};

const Positions = WithSuspense(Component, "paper");
export default Positions;
