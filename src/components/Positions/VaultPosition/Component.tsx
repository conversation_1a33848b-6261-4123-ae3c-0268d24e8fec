import React from "react";
import { Accordion, AccordionSummary, AccordionDetails, Box, Grid, Typography, useTheme, Di<PERSON>r, <PERSON><PERSON>, Chip } from "@mui/material";
import { getPositionScopedAtoms, useVaultPositionStore } from "./store";
import { ExpandMore, InfoOutlined } from "@mui/icons-material";
import TokenPair from "../../TokenPair";
import StyledTooltip from "../../StyledFeeTooltip";
import { formatBigIntPercentage, formatToken } from "../../../utils/helpers";
import { useNavigate } from "react-router-dom";
import { SCALING_FACTOR, SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import { Hex } from "viem";
import withScopedProvider from "../../../providers/WithScopedProvider";
import VaultCompositionScoped from "../../VaultComposition/ScopedComponent";

interface PositionProps {
  grid?: number;
  contractAddress: Hex;
}

const Component: React.FC<PositionProps> = ({ contractAddress }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentDeposit, apy, vaultTvl, vaultShare, price, vaultDetails, collateralDetails, collPrice } = useVaultPositionStore(contractAddress);
  return (
    <Grid item xs={12} display={!!vaultShare ? "block" : "none"} key={contractAddress}>
      <>
        <Accordion
          sx={{
            paddingY: "8px",
            paddingX: "0px",

            borderRadius: "16px",
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box display="flex" justifyContent="space-between" flex={1}>
              <TokenPair
                heading={collateralDetails.ticker + " Vault"}
                token1={{
                  contractAddress: contractAddress,
                  ticker: "shares",
                  decimals: vaultDetails.decimals,
                  value: currentDeposit.shares,
                }}
              />
              <Box alignContent={"right"} textAlign={"right"} pr={1}>
                <Typography variant={"h1"} color={"var(--text-primary)"} pl={1} sx={{ opacity: price ? 1 : 0 }}>
                  ${formatToken((currentDeposit.shares * price) / SCALING_FACTOR, SCALING_FACTOR_DECIMALS, 0)}{" "}
                </Typography>

                {apy !== undefined && (
                  <Chip
                    variant="outlined"
                    label={formatBigIntPercentage(apy, 2) + "% APY"}
                    color={"success"}
                    size="small"
                    sx={{ fontWeight: 600, fontSize: 10, opacity: 0.75, ml: 1, mt: "2px" }}
                  />
                )}
              </Box>
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 1 }}>
            <>
              <Divider sx={{ backgroundColor: "var(--border-color)", mb: 1 }} />
              <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                <Box>
                  <StyledTooltip
                    title={
                      <Box display={"flex"} flexDirection={"column"} gap={1}>
                        <Typography variant="h6" fontWeight={700} color={"var(--text-primary)"}>
                          Your share of the Vault which effects amount of rewards you will receive
                        </Typography>
                      </Box>
                    }
                  >
                    <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                      <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "bottom" }} />
                      &nbsp; Vault Share
                    </Typography>
                  </StyledTooltip>
                </Box>

                <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                  {formatBigIntPercentage(vaultShare, 2)}%
                </Typography>
              </Box>
              {!!price && (
                <Box display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                  <Box>
                    <StyledTooltip
                      title={
                        <Box display={"flex"} flexDirection={"column"} gap={1}>
                          <Typography variant="h6" fontWeight={700} color={"var(--text-primary)"}>
                            Total amount of {vaultDetails.ticker} in the Vault
                          </Typography>
                        </Box>
                      }
                    >
                      <Typography variant="h5" color={"var(--text-secondary)"} component="span" display="flex" alignItems="center">
                        <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "bottom" }} />
                        &nbsp; Vault TVL
                      </Typography>
                    </StyledTooltip>
                  </Box>

                  <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                    ${formatToken(vaultTvl, SCALING_FACTOR_DECIMALS, 0)}
                  </Typography>
                </Box>
              )}
              <Divider sx={{ backgroundColor: "var(--border-color)", my: 1 }} />
              <VaultCompositionScoped
                shares={currentDeposit.shares}
                denManagerAddress={contractAddress}
                vaultAddress={contractAddress}
                collPrice={collPrice}
                removeWithdrawFee={currentDeposit.withdrawFee}
              />
              <Divider sx={{ backgroundColor: "var(--border-color)", my: 1 }} />
              <Button
                onClick={() => navigate(`/vault/deposit/${collateralDetails.ticker}`)}
                fullWidth
                variant={"contained"}
                sx={{ backgroundColor: "#EC6F151A", color: "var(--primary-main)", borderColor: "transparent" }}
              >
                <Typography variant="h2">Deposit</Typography>
              </Button>
            </>
          </AccordionDetails>
        </Accordion>
      </>
    </Grid>
  );
};
const VaultPosition = withScopedProvider(Component, getPositionScopedAtoms());
export default VaultPosition;
