import { useAtomValue } from "jotai";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { useQueryClient } from "@tanstack/react-query";
import { queryClientAtom } from "jotai-tanstack-query";
import { Hex } from "viem";
import {
  getInfraredVaultPriceAtom,
  getVaultApyAtom,
  previewRedeemUnderlyingAtom,
  getVaultDepositAtom,
  _vaultAddrAtom,
  vaultAtom,
  vaultDetailsAtom,
  getInfraredTokenPriceAtom,
} from "../../../Atoms/Vault";
import { _denManagerAddrAtom, denManagerAtom } from "../../../Atoms/Den";
import { collateralTokenAtom, collateralTypeSelectorAtom, getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import { useHydrateAtoms } from "jotai/utils";
import { accountAtom } from "../../../Atoms/Account";
import { useAccount } from "wagmi";
import { ANON_ADDRESS } from "../../../utils/constants";

export const useVaultPositionStore = (contractAddress: Hex) => {
  const queryClient = useQueryClient();
  const { address } = useAccount();
  useHydrateAtoms([
    [queryClientAtom, queryClient],
    [accountAtom, address ?? ANON_ADDRESS],
    [vaultAtom, contractAddress],
    [collateralTypeSelectorAtom, "vault"],
  ]);
  const { data: currentDeposit } = useAtomValue(getVaultDepositAtom);
  const { data: apy } = useAtomValue(getVaultApyAtom);
  const { data: _price } = useAtomValue(getInfraredVaultPriceAtom);
  const { data: collPrice } = useAtomValue(getInfraredTokenPriceAtom);
  const price = currentDeposit.price || _price;
  const vaultDetails = useAtomValue(vaultDetailsAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);

  const myTotalAssets = (currentDeposit.shares * currentDeposit.shareToAssetRatio) / SCALING_FACTOR;
  const vaultShare = currentDeposit.totalSupply === 0n ? 0n : (currentDeposit.shares * SCALING_FACTOR) / currentDeposit.totalSupply;
  const vaultTvl = (currentDeposit.totalSupply * price) / SCALING_FACTOR;
  return { currentDeposit, apy, vaultTvl, vaultShare, myTotalAssets, price, vaultDetails, collateralDetails, collPrice };
};
export const getPositionScopedAtoms = () => [
  getInfraredVaultPriceAtom,
  getCollateralDetailsAtom,
  collateralTokenAtom,
  vaultDetailsAtom,
  accountAtom,
  getVaultApyAtom,
  previewRedeemUnderlyingAtom,
  getVaultDepositAtom,
  _vaultAddrAtom,
  vaultAtom,
  collateralTypeSelectorAtom,
  denManagerAtom,
  _denManagerAddrAtom,
  queryClientAtom,
];
