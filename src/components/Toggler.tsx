import React from "react";

import { Box, Container, Tab, Tabs, useMediaQuery, useTheme } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import { useAtomValue } from "jotai";
import { getCollateralTokensAtom } from "../Atoms/Tokens";

interface TogglerProps {
  togglers: { label: string; link?: string; customLink?: string }[];
  fullWidth?: boolean;
  pb?: number;
  paddingLine?: string;
}

const Toggler: React.FC<TogglerProps> = ({ togglers, paddingLine, fullWidth = true, pb }) => {
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  paddingLine = paddingLine ?? (isMdUp ? "10px" : "8px");
  const navigate = useNavigate();
  const location = useLocation();
  const collateralTokens = useAtomValue(getCollateralTokensAtom);
  const tickers = collateralTokens
    .map((item) => (item.lpToken ? [item.ticker.toLowerCase(), item.lpToken.provider + "-" + item.ticker.toLowerCase()] : item.ticker.toLowerCase()))
    .flat();
  const tickerPattern = tickers
    .sort((a, b) => b.length - a.length) // longer first to prevent partial matches
    .join("|");
  const regex = new RegExp(`\\/(${tickerPattern})$`, "i");
  const _pathname = location.pathname.toLowerCase().replace(regex, "");
  const pathname = _pathname.includes("/dashboard/history/") ? "/dashboard/history" : _pathname;
  const handleTabChange = (_event: React.SyntheticEvent, value: string) => {
    if (value && value !== pathname) {
      navigate(value);
    }
  };
  return (
    <Box sx={{ pb: pb ?? 2 }}>
      <Container
        sx={{
          width: "unset",
          display: "flex",
          justifyContent: "space-evenly",
          alignItems: "center",
          borderBottom: `1px solid ${theme.palette.background.default}`,
          marginLeft: "-" + paddingLine,
          marginRight: "-" + paddingLine,
          paddingLeft: paddingLine,
          paddingRight: paddingLine,
        }}
        disableGutters={fullWidth}
      >
        <Tabs variant="fullWidth" value={pathname} onChange={handleTabChange}>
          {togglers.map((item) => {
            return <Tab key={item.label} value={item.link} label={item.label} disableRipple sx={{ pt: 0 }} />;
          })}
        </Tabs>
      </Container>
    </Box>
  );
};

export default Toggler;
