export type DenRedeemDispatcherActionType = ChangeDenManager | ConfirmedDenRedeem | SetDebtFormValue | SetRatioFormValue | SetCollateralFormValue | SetDebtPercent;

type ChangeDenManager = {
  type: "changeDenManager";
  payload: string;
};
type ConfirmedDenRedeem = {
  type: "confirmedDenRedeem";
};

type SetCollateralFormValue = {
  type: "setCollateral";
  payload: string;
};
type SetRatioFormValue = {
  type: "setRatio";
  payload: string;
};
type SetDebtFormValue = {
  type: "setDebt";
  payload: string;
};
type SetDebtPercent = {
  type: "setDebtPercent";
  payload: number;
};
