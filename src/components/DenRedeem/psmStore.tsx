import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { getCollateralDetailsAtom, getDebtBalanceAtom, getDebtPriceAtom, getCollateralPrice } from "../../Atoms/Tokens";
import { denRedeemAmountsAtom, formValidationsDenRedeemAtom, formValuesDenRedeemAtom } from "../../Atoms/Redemptions";
import { formatDecimal, formatNoDecimal, regexDecimalToken } from "../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { Hex } from "viem";
import { useEffect } from "react";
import { SCALING_FACTOR, SCALING_FACTOR_BP } from "@Beraborrowofficial/sdk";
import { useLocation, useNavigate } from "react-router-dom";
import { updatePostHogEventAtom, resetPostHogEventAtom, accountAtom } from "../../Atoms/Account";
import { DenRedeemDispatcherActionType } from "./types";
import { formValidationsPsmRedeemAtom, getFeePsmAtom, getMaxPsmRedeemAtom, psmBondAtom } from "../../Atoms/PsmBond";

export const usePsmRedeemStore = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { data: debtBalance } = useAtomValue(getDebtBalanceAtom);
  const { data: collateralPrice } = useAtomValue(getCollateralPrice);
  const { data: maxAmount } = useAtomValue(getMaxPsmRedeemAtom);
  const { data: _redemptionFeeRate } = useAtomValue(getFeePsmAtom);
  const { data: debtPrice } = useAtomValue(getDebtPriceAtom);
  const account = useAtomValue(accountAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const psmBondAddr = useAtomValue(psmBondAtom);
  const validationError = useAtomValue(formValidationsPsmRedeemAtom);
  const [formValues, setFormValues] = useAtom(formValuesDenRedeemAtom);
  const formAmounts = useAtomValue(denRedeemAmountsAtom);
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const resetPostHogEvent = useSetAtom(resetPostHogEventAtom);
  if (debtPrice === undefined) throw Error("debt Price not found");
  const redemptionFeeRate = _redemptionFeeRate;
  const maxRedemptionFeeRate = ((redemptionFeeRate + redemptionFeeRate / 10n) * SCALING_FACTOR_BP) / SCALING_FACTOR;
  const redemptionFee = (redemptionFeeRate * formAmounts.collateral) / SCALING_FACTOR;
  const isAbovePeg = debtPrice >= SCALING_FACTOR;
  const isAbovePegWithFees = (debtPrice * (SCALING_FACTOR + redemptionFeeRate)) / SCALING_FACTOR >= (SCALING_FACTOR / 1000n) * 1005n;
  const isAbovePegForPsmOnly = debtPrice >= (SCALING_FACTOR / 1000n) * 995n;
  useEffect(() => {
    setFormValues(
      formAmounts.calculateCollateral(debtBalance, collateralPrice, SCALING_FACTOR) > maxAmount
        ? {
            collateral: formatDecimal(maxAmount, collateralDetails.decimals, 2),
            nect: formatDecimal(formAmounts.calculateDebt(maxAmount, collateralPrice, SCALING_FACTOR, SCALING_FACTOR), BERABORROW_ADDRESSES.debtToken.decimals, 2),
          }
        : {
            collateral: formatDecimal(formAmounts.calculateCollateral(debtBalance, collateralPrice, SCALING_FACTOR, SCALING_FACTOR), collateralDetails.decimals, 2),
            nect: formatDecimal(debtBalance, BERABORROW_ADDRESSES.debtToken.decimals, 2),
          }
    );
    updatePostHogEvent({
      type: "redeem",
      page_rel_path: location.pathname,
      denManager: psmBondAddr || "undefined",
      token_swapped: "NECT",
      qty_swapped_token: formValues.nect,
      token_redeemed: collateralDetails.ticker,
      qty_redeemed_token: formValues.collateral,
      total_fee: formatDecimal((redemptionFeeRate * formAmounts.collateral) / SCALING_FACTOR, collateralDetails.decimals),
    });
  }, [psmBondAddr, account]);

  const dispatcher = (action: DenRedeemDispatcherActionType) => {
    switch (action.type) {
      case "changeDenManager": {
        const denManager = action.payload as Hex;
        const ticker = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.denManagers[denManager].collateral].ticker;
        navigate(`/redeem/${ticker}`);
        break;
      }
      case "setCollateral":
        setCollateral(action.payload);
        break;
      case "setDebt":
        setDebt(action.payload);
        break;
      case "confirmedDenRedeem":
        resetPostHogEvent({ type: "redeem", page_rel_path: location.pathname });
        setFormValues({
          collateral: "0",
          nect: "0",
        });
        break;
      case "setDebtPercent":
        updatePostHogEvent({ last_clicked_input_btn: action.payload + "%" });
        const value = formatDecimal((debtBalance * BigInt(action.payload)) / 100n, BERABORROW_ADDRESSES.debtToken.decimals, 5);
        setDebt(value);
        break;
      default:
        break;
    }
  };

  const setCollateral = (value: string) => {
    value = regexDecimalToken(value, collateralDetails.decimals);
    const amount = formatNoDecimal(value, collateralDetails.decimals);
    formAmounts.setCollateral(amount);
    const debt = formAmounts.calculateDebt(amount, collateralPrice, SCALING_FACTOR, SCALING_FACTOR);
    formAmounts.setDebt(debt);
    setFormValues({ collateral: value, nect: formatDecimal(debt, BERABORROW_ADDRESSES.debtToken.decimals, 5) });
    updatePostHogEvent({
      token_swapped: "NECT",
      qty_swapped_token: formatDecimal(debt, BERABORROW_ADDRESSES.debtToken.decimals, 5),
      token_redeemed: collateralDetails.ticker,
      qty_redeemed_token: value,
      total_fee: formatDecimal((redemptionFeeRate * formatNoDecimal(value, collateralDetails.decimals)) / SCALING_FACTOR, collateralDetails.decimals),
    });
  };
  const setDebt = (value: string) => {
    value = regexDecimalToken(value, BERABORROW_ADDRESSES.debtToken.decimals);
    const amount = formatNoDecimal(value, BERABORROW_ADDRESSES.debtToken.decimals);
    formAmounts.setDebt(amount);
    const collateral = formAmounts.calculateCollateral(amount, collateralPrice, SCALING_FACTOR, SCALING_FACTOR);

    setFormValues({ collateral: formatDecimal(collateral, collateralDetails.decimals, 5), nect: value });
    updatePostHogEvent({
      token_swapped: "NECT",
      qty_swapped_token: value,
      token_redeemed: collateralDetails.ticker,
      qty_redeemed_token: formatDecimal(collateral, collateralDetails.decimals),
      total_fee: formatDecimal((redemptionFeeRate * collateral) / SCALING_FACTOR, collateralDetails.decimals),
    });
  };

  return {
    maxRedemptionFeeRate,
    redemptionFeeRate,
    redemptionFee,
    formAmounts,
    debtBalance,
    collateral: { price: collateralPrice, ...collateralDetails },
    formValues,
    validationError,
    dispatcher,
    isAbovePeg,
    isAbovePegWithFees,
    redeemType: "psm",
    isAbovePegForPsmOnly,
  };
};

export const getDenRedeemScopedAtoms = () => [formValuesDenRedeemAtom, formValidationsDenRedeemAtom];
