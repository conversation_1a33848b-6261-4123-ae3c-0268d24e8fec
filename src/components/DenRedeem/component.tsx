import React from "react";
import { Box, Chip, Divider, Grid, Typography } from "@mui/material";
import { formatToken } from "../../utils/helpers";
import TransactionButton from "../Transaction/Transaction";
import WithSuspense from "../../providers/WithSuspense";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import StyledTextfield from "../StyledTextField";
import TickerPill from "../TickerPill";
import { InfoOutlined } from "@mui/icons-material";
import { AmountSelector } from "../AmountSelector";
import RedeemFeeBreakdown from "../RedeemFeeBreakdown";
import StyledTooltip from "../StyledFeeTooltip";
import CollateralPill from "../CollateralPill/component";
import { useDenRedeemStoreSelector } from "./storeSelector";

const Component: React.FC = () => {
  const {
    formAmounts,
    collateral,
    dispatcher,
    formValues,
    validationError,
    debtBalance,
    redemptionFee,
    redemptionFeeRate,
    maxRedemptionFeeRate,
    isAbovePeg,
    isAbovePegWithFees,
    isAbovePegForPsmOnly,
    redeemType,
  } = useDenRedeemStoreSelector();
  return (
    <>
      <Grid container pb={3} spacing={2}>
        <Grid item xs={12} lg={12}>
          <Typography variant="subtitle1">{BERABORROW_ADDRESSES.debtToken.ticker} to be Swapped</Typography>
          <StyledTextfield
            value={formValues.nect}
            onChange={(event) => dispatcher({ type: "setDebt", payload: event.target.value })}
            error={validationError?.type === "insufficient_nect_balance"}
            endComponent={
              <Box sx={{ display: "flex", flexDirection: "column", alignItems: "end" }}>
                <TickerPill contractAddress={BERABORROW_ADDRESSES.debtToken.contractAddress} ticker={BERABORROW_ADDRESSES.debtToken.ticker} />
                <AmountSelector handleItemClick={(percent) => dispatcher({ type: "setDebtPercent", payload: percent })} mt={1} ml={"auto"} />
              </Box>
            }
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="caption" fontSize={12}>
                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                    Wallet:
                  </Typography>{" "}
                  {formatToken(debtBalance, BERABORROW_ADDRESSES.debtToken.decimals, 2, undefined, true)}{" "}
                </Typography>
              </Box>
            }
          />
        </Grid>
      </Grid>
      <Grid container pb={1} spacing={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1">{collateral.ticker} to be Redeemed</Typography>
          <StyledTextfield
            fullWidth
            value={formatToken(formAmounts.collateral - redemptionFee, redeemType === "psm" ? collateral.decimals : collateral.vaultDecimals)}
            disabled
            onChange={(event) => dispatcher({ type: "setCollateral", payload: event.target.value })}
            error={validationError?.type === "insufficient_collateral_balance"}
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <StyledTooltip
                  title={
                    <RedeemFeeBreakdown stayOpen={true} formAmounts={formAmounts} collateral={collateral} redemptionFeeRate={redemptionFeeRate} redemptionFee={redemptionFee} />
                  }
                >
                  <Typography variant="caption" fontSize={12} display="flex" alignItems="center">
                    <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center">
                      <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle" }} /> Fees:
                    </Typography>{" "}
                    {formatToken(redemptionFee, redeemType === "psm" ? collateral.decimals : collateral.vaultDecimals, 5, undefined, true)}{" "}
                  </Typography>
                </StyledTooltip>
              </Box>
            }
            endComponent={
              isAbovePegForPsmOnly && redeemType === "psm" ? (
                <TickerPill contractAddress={collateral.contractAddress} ticker={collateral.ticker} />
              ) : (
                <CollateralPill
                  contractAddress={collateral.contractAddress}
                  urlRedirect={"/redeem"}
                  excludeWrappedCollateral
                  excludeDenManagers={isAbovePegForPsmOnly}
                  includePsm
                />
              )
            }
          />
        </Grid>
      </Grid>
      {isAbovePegWithFees && !isAbovePeg && (
        <Box pt={1}>
          <Chip
            sx={{
              p: 1,
              fontWeight: 600,
              lineHeight: 1.5,
              fontSize: 12,
              height: "auto",
              "& .MuiChip-label": {
                display: "block",
                whiteSpace: "normal",
              },
            }}
            variant="filled"
            color={"warning"}
            size="small"
            label="Currently if you perform a redemption the NECT price + fees you would get less than if you just swapped NECT on Ooga booga.
"
          />
        </Box>
      )}
      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />
      <TransactionButton
        disabled={!debtBalance || !!validationError || isAbovePeg || (isAbovePegForPsmOnly && redeemType !== "psm")}
        transaction={
          redeemType === "psm"
            ? { type: "redeemPsm", variables: [collateral.contractAddress, formAmounts.debt, maxRedemptionFeeRate] }
            : { type: "redeemDebt", variables: [formAmounts.debt, maxRedemptionFeeRate] }
        }
        onConfirmation={() => dispatcher({ type: "confirmedDenRedeem" })}
        validationError={validationError}
      >
        {isAbovePeg
          ? `Redemptions Closed while ${BERABORROW_ADDRESSES.debtToken.ticker} above Peg`
          : isAbovePegForPsmOnly && redeemType !== "psm"
            ? "Redemptions only available for Honey"
            : "Redeem"}
        {/* */}
      </TransactionButton>
    </>
  );
};

const DenRedeem = WithSuspense(Component, "paper");
export default DenRedeem;
