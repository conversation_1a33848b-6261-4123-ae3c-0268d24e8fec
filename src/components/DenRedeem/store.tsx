import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { getCollateralDetailsAtom, getDebtBalanceAtom, getDebtPriceAtom } from "../../Atoms/Tokens";
import { getDenManagerCollateralPrice, getDensTotalsAtom, denManagerAtom } from "../../Atoms/Den";
import { denRedeemAmountsAtom, formValidationsDenRedeemAtom, formValuesDenRedeemAtom, getRedeemFeeRateAtom } from "../../Atoms/Redemptions";
import { formatDecimal, formatNoDecimal, regexDecimalToken } from "../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { Hex } from "viem";
import { useEffect } from "react";
import { DEFAULT_SLIPPAGE_TOLERANCE, SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { useLocation, useNavigate } from "react-router-dom";
import { updatePostHogEventAtom, resetPostHogEventAtom, accountAtom } from "../../Atoms/Account";
import { DenRedeemDispatcherActionType } from "./types";

export const useDenRedeemStore = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { data: debtBalance } = useAtomValue(getDebtBalanceAtom);
  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);
  const { data: densTotals } = useAtomValue(getDensTotalsAtom);
  const { data: _redemptionFeeRate } = useAtomValue(getRedeemFeeRateAtom);
  const { data: debtPrice } = useAtomValue(getDebtPriceAtom);
  const account = useAtomValue(accountAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const denManagerAddr = useAtomValue(denManagerAtom);
  const validationError = useAtomValue(formValidationsDenRedeemAtom);
  const [formValues, setFormValues] = useAtom(formValuesDenRedeemAtom);
  const formAmounts = useAtomValue(denRedeemAmountsAtom);
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const resetPostHogEvent = useSetAtom(resetPostHogEventAtom);
  if (debtPrice === undefined) throw Error("debt Price not found");
  const redemptionFeeRate = _redemptionFeeRate;
  const maxRedemptionFeeRate =
    redemptionFeeRate + 2n * DEFAULT_SLIPPAGE_TOLERANCE > (SCALING_FACTOR / 100n) * 5n ? (SCALING_FACTOR / 100n) * 5n : redemptionFeeRate + 2n * DEFAULT_SLIPPAGE_TOLERANCE;
  const redemptionFee = (redemptionFeeRate * formAmounts.collateral) / SCALING_FACTOR;
  const isAbovePeg = debtPrice >= SCALING_FACTOR;
  const isAbovePegWithFees = (debtPrice * (SCALING_FACTOR + redemptionFeeRate)) / SCALING_FACTOR >= (SCALING_FACTOR / 1000n) * 1005n;
  const isAbovePegForPsmOnly = debtPrice >= (SCALING_FACTOR / 1000n) * 995n;

  useEffect(() => {
    setFormValues(
      densTotals.calculateCollateral(debtBalance, collateralPrice, SCALING_FACTOR, SCALING_FACTOR) > densTotals.collateral
        ? {
            collateral: formatDecimal(densTotals.collateral, collateralDetails.vaultDecimals, 2),
            nect: formatDecimal(densTotals.calculateDebt(densTotals.collateral, collateralPrice, SCALING_FACTOR, SCALING_FACTOR), BERABORROW_ADDRESSES.debtToken.decimals, 2),
          }
        : {
            collateral: formatDecimal(densTotals.calculateCollateral(debtBalance, collateralPrice, SCALING_FACTOR, SCALING_FACTOR), collateralDetails.vaultDecimals, 2),
            nect: formatDecimal(debtBalance, BERABORROW_ADDRESSES.debtToken.decimals, 0),
          }
    );
    updatePostHogEvent({
      type: "redeem",
      page_rel_path: location.pathname,
      denManager: denManagerAddr,
      token_swapped: "NECT",
      qty_swapped_token: formValues.nect,
      token_redeemed: collateralDetails.ticker,
      qty_redeemed_token: formValues.collateral,
      total_fee: formatDecimal((redemptionFeeRate * formAmounts.collateral) / SCALING_FACTOR, collateralDetails.vaultDecimals),
    });
  }, [denManagerAddr, account]);

  const dispatcher = (action: DenRedeemDispatcherActionType) => {
    switch (action.type) {
      case "changeDenManager": {
        const denManager = action.payload as Hex;
        const ticker = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.denManagers[denManager].collateral].ticker;
        navigate(`/redeem/${ticker}`);
        break;
      }
      case "setCollateral":
        setCollateral(action.payload);
        break;
      case "setDebt":
        setDebt(action.payload);
        break;
      case "confirmedDenRedeem":
        resetPostHogEvent({ type: "redeem", page_rel_path: location.pathname });
        setFormValues({
          collateral: "0",
          nect: "0",
        });
        break;
      case "setDebtPercent":
        updatePostHogEvent({ last_clicked_input_btn: action.payload + "%" });
        const value = formatDecimal((debtBalance * BigInt(action.payload)) / 100n, BERABORROW_ADDRESSES.debtToken.decimals, 5);
        setDebt(value);
        break;
      default:
        break;
    }
  };

  const setCollateral = (value: string) => {
    value = regexDecimalToken(value, collateralDetails.vaultDecimals);
    const amount = formatNoDecimal(value, collateralDetails.vaultDecimals);
    formAmounts.setCollateral(amount);
    const debt = formAmounts.calculateDebt(amount, collateralPrice, SCALING_FACTOR, SCALING_FACTOR);
    formAmounts.setDebt(debt);
    setFormValues({ collateral: value, nect: formatDecimal(debt, BERABORROW_ADDRESSES.debtToken.decimals, 5) });
    updatePostHogEvent({
      token_swapped: "NECT",
      qty_swapped_token: formatDecimal(debt, BERABORROW_ADDRESSES.debtToken.decimals, 5),
      token_redeemed: collateralDetails.ticker,
      qty_redeemed_token: value,
      total_fee: formatDecimal((redemptionFeeRate * formatNoDecimal(value, collateralDetails.vaultDecimals)) / SCALING_FACTOR, collateralDetails.vaultDecimals),
    });
  };
  const setDebt = (value: string) => {
    value = regexDecimalToken(value, BERABORROW_ADDRESSES.debtToken.decimals);
    const amount = formatNoDecimal(value, BERABORROW_ADDRESSES.debtToken.decimals);
    formAmounts.setDebt(amount);
    const collateral = formAmounts.calculateCollateral(amount, collateralPrice, SCALING_FACTOR, SCALING_FACTOR);

    setFormValues({ collateral: formatDecimal(collateral, collateralDetails.vaultDecimals, 5), nect: value });
    updatePostHogEvent({
      token_swapped: "NECT",
      qty_swapped_token: value,
      token_redeemed: collateralDetails.ticker,
      qty_redeemed_token: formatDecimal(collateral, collateralDetails.vaultDecimals),
      total_fee: formatDecimal((redemptionFeeRate * collateral) / SCALING_FACTOR, collateralDetails.vaultDecimals),
    });
  };

  return {
    maxRedemptionFeeRate,
    redemptionFeeRate,
    redemptionFee,
    formAmounts,
    debtBalance,
    collateral: { price: collateralPrice, ...collateralDetails },
    formValues,
    validationError,
    dispatcher,
    isAbovePeg,
    isAbovePegWithFees,
    isAbovePegForPsmOnly,
    redeemType: "den",
  };
};

export const getDenRedeemScopedAtoms = () => [formValuesDenRedeemAtom, formValidationsDenRedeemAtom];
