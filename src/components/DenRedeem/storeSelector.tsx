import { useAtomValue, useAtom, useSetAtom } from "jotai";

import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { useNavigate, useParams } from "react-router-dom";
import { denManagerAtom, denManagersAtom } from "../../Atoms/Den";
import { getCollateralDetailsAtom, getCollateralTokensAtom, collateralTypeSelectorAtom } from "../../Atoms/Tokens";
import { useEffect } from "react";
import { useDenRedeemStore } from "./store";
import { usePsmRedeemStore } from "./psmStore";
import { psmBondCollateralsAtom, psmBondAtom } from "../../Atoms/PsmBond";
import { zeroAddress } from "viem";

export const useDenRedeemStoreSelector = () => {
  const navigate = useNavigate();
  const { ticker: tickerParam } = useParams();
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const denManagerAddr = useAtomValue(denManagerAtom);
  const psmBondCollaterals = useAtomValue(psmBondCollateralsAtom);
  const denManagers = useAtomValue(denManagersAtom);
  const Collaterals = useAtomValue(getCollateralTokensAtom);
  const [psmBondAddr, setPsmBondAddr] = useAtom(psmBondAtom);
  const setCollateralTypeSelector = useSetAtom(collateralTypeSelectorAtom);

  const newDenManager = denManagers.find((item) => item.collateralTicker.toLowerCase() === tickerParam?.toLowerCase())?.contractAddress;
  const underlyingDenManagerAddr = newDenManager ? BERABORROW_ADDRESSES.wrappedDenManagers[newDenManager] : undefined;
  const collateral = Collaterals.find((item) => item.ticker.toLowerCase() === tickerParam?.toLowerCase())?.contractAddress;
  const isPSM = psmBondCollaterals.includes(collateral || zeroAddress);

  useEffect(() => {
    if (!isPSM && newDenManager) {
      //disabled all denManagers
      if (underlyingDenManagerAddr) {
        //Only use underlying Collaterals
        const wrappedCollateralTicker = denManagers.find((item) => item.contractAddress === underlyingDenManagerAddr)?.collateralTicker;
        if (wrappedCollateralTicker) navigate(`/redeem/${wrappedCollateralTicker}`, { replace: true });
      } else if ((tickerParam && !newDenManager) || !tickerParam) {
        navigate(`/redeem/${collateralDetails.ticker}`, { replace: true });
      }
    } else {
      if (!psmBondAddr || psmBondAddr !== collateral) {
        setPsmBondAddr(isPSM ? collateral : psmBondCollaterals[0]);
        navigate(`/redeem/${BERABORROW_ADDRESSES.collateralTokens[isPSM ? collateral || psmBondCollaterals[0] : psmBondCollaterals[0]].ticker}`, { replace: true });
      }
    }
    setCollateralTypeSelector("psm");

    return () => {
      if (psmBondAddr) {
        setPsmBondAddr(undefined);
        setCollateralTypeSelector("den");
      }
    };
  }, [denManagerAddr, newDenManager, psmBondAddr, tickerParam, collateralDetails]);

  return psmBondCollaterals.includes(collateral || zeroAddress) ? usePsmRedeemStore() : useDenRedeemStore();
};
