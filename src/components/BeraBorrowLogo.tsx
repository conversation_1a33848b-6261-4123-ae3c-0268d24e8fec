import { Box, Typography } from "@mui/material";
import React from "react";
import { Link } from "react-router-dom";

type BeraBorrowLogoProps = React.ComponentProps<typeof Box> & {
  height?: number | string;
  label?: string;
};

const BeraBorrowLogo: React.FC<BeraBorrowLogoProps> = ({ height = "56px", label, ...boxProps }) => (
  <Link to="/">
    <Box sx={{ lineHeight: 0, display: "flex", alignItems: "center" }} {...boxProps}>
      <Box sx={{ height: height, width: height }}>
        <img src="/beraborrow-logo.png" height={"100%"} width={"100%"} />
      </Box>
      {label && (
        <Typography
          variant="h3"
          color={"var(--text-primary)"}
          sx={{
            display: { xs: "none", sm: "flex" },
            fontWeight: 700,
            fontSize: 24,
            pl: { lg: 4, xs: 2 },
            lineHeight: "inherit",
            alignItems: "center",
          }}
        >
          {label}
        </Typography>
      )}
    </Box>
  </Link>
);
export default BeraBorrowLogo;
