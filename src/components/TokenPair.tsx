import React from "react";
import { Box, Typography, useMediaQuery, useTheme } from "@mui/material";
import TokenIcon from "./TokenIcons";
import { formatToken } from "../utils/helpers";
import { Hex } from "viem";
interface TokenPairProps {
  token1: { contractAddress: Hex; ticker: string; decimals: number; value?: bigint; vaulted?: boolean; staked?: boolean };
  token2?: { contractAddress: Hex; ticker: string; decimals: number; value?: bigint; vaulted?: boolean; staked?: boolean };
  token3?: { contractAddress: Hex; ticker: string; decimals: number; value?: bigint; vaulted?: boolean; staked?: boolean };
  heading?: string;
}

const Component: React.FC<TokenPairProps> = ({ token1, token2, token3, heading }) => {
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  return (
    <>
      <Box>
        <Box display="flex" alignItems="center">
          <TokenIcon contractAddress={token1.contractAddress} height={isMdUp ? "30px" : "25px"} mr={"-5px"} zIndex={5} vaulted={token1.vaulted} />
          {token2 && <TokenIcon contractAddress={token2.contractAddress} height={isMdUp ? "30px" : "25px"} ml={"-5px"} zIndex={4} vaulted={token2.vaulted} />}
          {token3 && <TokenIcon contractAddress={token3.contractAddress} height={isMdUp ? "30px" : "25px"} ml={"-5px"} zIndex={4} vaulted={token3.vaulted} />}
          <Typography component="span" variant={"h1"} color={"var(--text-primary)"} pl={1}>
            {heading ? heading : (token1.staked ? "s" : "") + token1.ticker} {!heading && token2 && <>• {(token2.staked ? "s" : "") + token2.ticker}</>}
          </Typography>
        </Box>
        <Typography variant={"h5"} color={"var(--text-secondary)"} sx={{ display: "inline-block" }}>
          {token1.value !== undefined && formatToken(token1.value, token1.decimals, 1)} {(token1.staked ? "s" : "") + token1.ticker}
          {token2 && (
            <>
              {" "}
              • {token2.value !== undefined && formatToken(token2.value, token2.decimals, 1)} {(token2.staked ? "s" : "") + token2.ticker}
            </>
          )}
        </Typography>
      </Box>
    </>
  );
};

const TokenPair = Component;
export default TokenPair;
