import React, { ReactNode } from "react";
import { Accordion, AccordionDetails, AccordionSummary, Typography, Box } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import VaultApy from "./VaultApy/Component";
import { Hex } from "viem";
import TokenIcon from "./TokenIcons";
import { BERABORROW_ADDRESSES } from "../utils/constants";

type StyledAccordionProps = React.ComponentProps<typeof Accordion> & {
  hideIcon?: boolean;
  title: string;
  children: ReactNode;
  denManagerAddr?: Hex;
  collateralAddress?: Hex;

  onClickSummary?: () => void;
};

const StyledAccordion: React.FC<StyledAccordionProps> = ({ hideIcon, title, denManagerAddr, children, onClickSummary, collateralAddress, ...accordionProps }) => {
  const contractAddress = collateralAddress ? collateralAddress : denManagerAddr ? BERABORROW_ADDRESSES.denManagers[denManagerAddr].collateral : undefined;

  const isVaulted = denManagerAddr ? !!BERABORROW_ADDRESSES.denManagers[denManagerAddr].vault : false;

  return (
    <>
      <Accordion
        sx={{
          padding: "16px",
          borderRadius: "16px",
          backgroundColor: "var(--background-accent)",
        }}
        {...accordionProps}
      >
        <AccordionSummary
          onClick={onClickSummary}
          expandIcon={hideIcon === true ? undefined : <ExpandMore />}
          sx={
            hideIcon === true
              ? {
                  cursor: "default", // Set cursor to default
                  "& .MuiAccordionSummary-content": {
                    cursor: "default", // Ensure the content also has the default cursor
                  },
                  "& .MuiAccordionSummary-expandIconWrapper": {
                    cursor: "default", // Keep the pointer cursor on the expand icon if needed
                  },
                }
              : {}
          }
        >
          <Box display="flex" justifyContent="space-between" width={"100%"}>
            <Box display="flex" alignItems={"center"}>
              {contractAddress && <TokenIcon contractAddress={contractAddress} vaulted={isVaulted} height={"25px"} pr={1} />}
              <Typography variant="h4" textAlign={"left"}>
                {title}
              </Typography>
            </Box>
            <Box mr={1}>{!!denManagerAddr && <VaultApy address={denManagerAddr} />}</Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails sx={{ padding: "8px 16px" }}>{children}</AccordionDetails>
      </Accordion>
    </>
  );
};
export default StyledAccordion;
