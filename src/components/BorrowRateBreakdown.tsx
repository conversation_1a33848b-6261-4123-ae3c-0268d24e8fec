import { Accordion, AccordionDetails, AccordionSummary, Box, Divider, Typography, useTheme } from "@mui/material";
import { formatBigIntPercentage, formatToken } from "../utils/helpers";
import { BERABORROW_ADDRESSES } from "../utils/constants";
import { ExpandMore } from "@mui/icons-material";
import { Den } from "@Beraborrowofficial/sdk";
import { useAtomValue } from "jotai";
import { beraborrowConstantsAtom } from "../Atoms/Account";

type BorrowRateBreakdownProps = {
  formAmounts: Den;
  borrowingFeeRate: bigint;
  borrowingFee: bigint;
  totalDebt: bigint;
  isManaging: boolean;
  isRecoveryMode: boolean;
  stayOpen?: boolean;
};
const BorrowRateBreakdown: React.FC<BorrowRateBreakdownProps> = ({ stayOpen, formAmounts, borrowingFeeRate, borrowingFee, totalDebt, isManaging, isRecoveryMode }) => {
  const theme = useTheme();
  const liquidationReserve = useAtomValue(beraborrowConstantsAtom).liquidationReserve;

  return (
    <>
      <Accordion
        defaultExpanded
        expanded={stayOpen === true ? true : undefined}
        sx={{
          padding: "16px",
          borderRadius: "16px",
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <AccordionSummary
          expandIcon={stayOpen === true ? undefined : <ExpandMore />}
          sx={
            stayOpen === true
              ? {
                  cursor: "default", // Set cursor to default
                  "& .MuiAccordionSummary-content": {
                    cursor: "default", // Ensure the content also has the default cursor
                  },
                  "& .MuiAccordionSummary-expandIconWrapper": {
                    cursor: "default", // Keep the pointer cursor on the expand icon if needed
                  },
                }
              : {}
          }
        >
          <Typography variant="h4" textAlign={"left"}>
            Fee Breakdown
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          {" "}
          <Box display="flex" justifyContent="space-between" mb={1}>
            <Typography variant="subtitle2" color={"var(--text-secondary)"}>
              Net debt
            </Typography>
            <Typography variant="subtitle2">
              <strong>{formatToken(formAmounts.debt, BERABORROW_ADDRESSES.debtToken.decimals, 2, BERABORROW_ADDRESSES.debtToken.ticker)} </strong>
            </Typography>
          </Box>
          <Box display="flex" justifyContent="space-between" mb={1}>
            <Typography variant="subtitle2" color={"var(--text-secondary)"}>
              Borrowing fee (<strong>{formatBigIntPercentage(borrowingFeeRate, 3)}%</strong>)
            </Typography>
            <Typography variant="subtitle2">
              <strong style={{ whiteSpace: "nowrap" }}>
                {borrowingFee ? formatToken(borrowingFee - (isManaging || isRecoveryMode ? 0n : liquidationReserve), BERABORROW_ADDRESSES.debtToken.decimals, 2) : "0"}{" "}
                {BERABORROW_ADDRESSES.debtToken.ticker}
              </strong>
            </Typography>
          </Box>
          <Box display="flex" justifyContent="space-between" mb={2}>
            <Typography variant="subtitle2" color={"var(--text-secondary)"}>
              Liquidation reserve
            </Typography>
            <Typography variant="subtitle2">
              <strong>{isManaging ? "0" : formatToken(liquidationReserve, BERABORROW_ADDRESSES.debtToken.decimals, undefined, BERABORROW_ADDRESSES.debtToken.ticker)}</strong>
            </Typography>
          </Box>
          <Divider sx={{ mb: 2, backgroundColor: `var(--text-secondary)` }} />
          <Box display="flex" justifyContent="space-between">
            <Typography variant="subtitle2" color={"var(--text-secondary)"}>
              Total Debt
            </Typography>
            <Typography variant="subtitle2">
              <strong>{formatToken(totalDebt, BERABORROW_ADDRESSES.debtToken.decimals, 2, BERABORROW_ADDRESSES.debtToken.ticker)}</strong>
            </Typography>
          </Box>
        </AccordionDetails>
      </Accordion>
    </>
  );
};
export default BorrowRateBreakdown;
