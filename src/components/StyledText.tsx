import React, { ReactNode } from "react";
import { InputAdornment, TextField } from "@mui/material";

type StyledTextProps = React.ComponentProps<typeof TextField> & { endAdornment?: ReactNode; startAdornment?: ReactNode };
const Component: React.FC<StyledTextProps> = ({ endAdornment, startAdornment, error, ...textFieldProps }) => {
  return (
    <>
      <TextField
        disabled
        fullWidth
        type="text"
        variant="outlined"
        InputProps={{
          readOnly: true,
          endAdornment: endAdornment ? <InputAdornment position="end">{endAdornment}</InputAdornment> : undefined,
          startAdornment: startAdornment ? <InputAdornment position="start">{startAdornment}</InputAdornment> : undefined,
        }}
        {...textFieldProps}
      />
    </>
  );
};

const StyledText = Component;
export default StyledText;
