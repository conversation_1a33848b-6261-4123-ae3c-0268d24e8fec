import { Box, BoxProps, Button, CircularProgress, SxProps, Theme } from "@mui/material";

import { useTransaction } from "../../Hooks/useTransaction";
import { ReactNode, useEffect } from "react";
import { FormError } from "../../@type";
import StyledTooltip from "../StyledFeeTooltip";
import { InfoOutlined } from "@mui/icons-material";
import theme from "../../theme/theme";
import { TransactionProps } from "../../@type/Transactions";
import ConnectButton from "../ConnectButton/component";
// import { useSetAtom } from "jotai";
// import { simulateStateAtom } from "../../Atoms/Transaction";
const TooltipContent = (props: BoxProps) => {
  const { children, ...boxProps } = props;
  return (
    <Box
      {...boxProps}
      sx={{
        padding: "16px",
        borderRadius: "16px",
        backgroundColor: theme.palette.background.paper,
      }}
    >
      {children}
    </Box>
  );
};

const Component = ({
  children,
  disabled,
  onConfirmation,
  onError,
  onClick,
  validationError,
  sx,
  transaction,
  variant = "contained",
}: {
  transaction: TransactionProps;
  sx?: SxProps<Theme>;
  variant?: "contained" | "outlined";
  disabled?: boolean;
  onClick?: () => void;
  onConfirmation?: () => void;
  onError?: (error: unknown) => void;
  children?: ReactNode;
  validationError?: FormError | undefined;
}) => {
  const { submit, isPending, isConfirming, isBlocked, isConfirmed, error } = useTransaction(transaction);
  //   const setSimulateState = useSetAtom(simulateStateAtom);
  useEffect(() => {
    if (isConfirmed) {
      onConfirmation?.();
    }
  }, [isConfirmed]);

  useEffect(() => {
    if (error && onError) {
      setTimeout(() => {});
      //   setSimulateState({ type: "idle" });
      onError(error);
    }
  }, [error]);

  return (
    <Box>
      <ConnectButton sx={{ borderRadius: 4, py: 1.5, fontSize: "18px", ...sx }}>
        <StyledTooltip
          title={
            validationError &&
            (validationError.description || validationError.link) && (
              <TooltipContent display={"flex"} flexDirection={"column"} gap={1}>
                {validationError?.description}
                {validationError?.link && (
                  <a href={validationError?.link} target="_blank">
                    Read more
                  </a>
                )}
              </TooltipContent>
            )
          }
        >
          <span>
            <Button
              disabled={isConfirming || isPending || disabled || isBlocked}
              fullWidth
              variant={variant}
              onClick={() => {
                onClick ? onClick() : submit();
              }}
              sx={{ borderRadius: 4, py: 1.5, fontSize: "18px", ...sx }}
            >
              {isPending ? (
                <>
                  <CircularProgress color="secondary" size={25} sx={{ mr: 2 }} />
                  Transaction in Progress
                </>
              ) : isConfirming || isBlocked ? (
                <>
                  <CircularProgress color="secondary" size={25} sx={{ mr: 2 }} />
                  Confirming
                </>
              ) : validationError ? (
                <>
                  {validationError.description && <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", mr: 0.5 }} />}
                  {validationError.shortMessage ?? children ?? "Submit"}
                </>
              ) : (
                (children ?? "Submit")
              )}
            </Button>
          </span>
        </StyledTooltip>
      </ConnectButton>
    </Box>
  );
};
const TransactionButton = Component;

export default TransactionButton;
