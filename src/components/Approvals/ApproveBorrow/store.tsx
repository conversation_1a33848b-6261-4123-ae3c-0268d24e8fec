import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { getCollateralAllowanceAtom, getCollateralDetailsAtom } from "../../../Atoms/Tokens";
import {
  denBorrowTypeAtom,
  getDenManagerCollateralPrice,
  denManagerAtom,
  getBorrowingMintFeeRateAtom,
  //   getLeverageFlashLoanFeeAtom,
  leverageSlippageAtom,
} from "../../../Atoms/Den";
import { Den, MINIMUM_BORROWING_MINT_RATE, SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { useTransaction } from "../../../Hooks/useTransaction";
import { useEffect, useRef, useState } from "react";
import { useNotifications } from "../../../Hooks/useNotifications";
import { DenTransactions } from "../../../@type/Transactions";
import { PostHogEvent } from "../../../@type/Account";
import { posthog } from "posthog-js";
import { updatePostHogEvent<PERSON>tom, postHogEventAtom, simpleModeAtom, accountAtom } from "../../../Atoms/Account";
// import { previewVaultedSharesAtom } from "../../../Atoms/Vault";

export const useApproveBorrowStore = (formAmounts: Den, onConfirmation?: () => void) => {
  const { data: collateralAllowance } = useAtomValue(getCollateralAllowanceAtom);
  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);
  const { data: currentBorrowingRate } = useAtomValue(getBorrowingMintFeeRateAtom);
  //   const { data: flashLoanRate } = useAtomValue(getLeverageFlashLoanFeeAtom);
  //   const { data: previewVaultedShares } = useAtomValue(previewVaultedSharesAtom);
  //   const { data: debtPrice } = useAtomValue(getDebtPriceAtom);

  const leverageSlippage = useAtomValue(leverageSlippageAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const denManagerAddr = useAtomValue(denManagerAtom);
  const isLeveraging = !!formAmounts.leverage;
  const flashLoanFee = 0n; //isLeveraging ? formAmounts.getFlashLoanFee(flashLoanRate) : 0n;
  const flashLoanSlippage = isLeveraging ? ((formAmounts.collateral + flashLoanFee) * leverageSlippage) / SCALING_FACTOR : 0n;
  const approvalAmount = formAmounts.collateral + flashLoanFee + flashLoanSlippage - collateralAllowance;
  const hasSubmittedApprovalRef = useRef(false);
  const hasSubmittedRef = useRef(false);
  const { removeModal } = useNotifications();
  const collateralAmount = formAmounts.collateral + flashLoanFee + flashLoanSlippage;
  const borrowType = useAtomValue(denBorrowTypeAtom);
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const postHogEvent = useAtomValue(postHogEventAtom);
  const simpleMode = useAtomValue(simpleModeAtom);
  const account = useAtomValue(accountAtom);
  //   const leveragedDen = formAmounts.applyLeverage(formAmounts.getCollateralShares(previewVaultedShares), currentBorrowingRate, collateralPrice, debtPrice);

  const [step, setStep] = useState(1);
  const transaction: DenTransactions =
    //   !!formAmounts.leverage
    //     ? borrowType !== "open"
    //       ? {
    //           type: "adjustLeverage",
    //           variables: [
    //             formAmounts.leverage,
    //             formAmounts.collateral,
    //             { borrowNECT: leveragedDen.debt, depositCollateral: leveragedDen.collateral },
    //             currentBorrowingRate,
    //             leverageSlippage,
    //             // collateralDetails.contractAddress === zeroAddress,
    //           ],
    //         }
    //       : {
    //           type: "openLeverage",
    //           variables: [
    //             formAmounts.leverage,
    //             formAmounts.collateral,
    //             { borrowNECT: leveragedDen.debt, depositCollateral: leveragedDen.collateral },
    //             currentBorrowingRate,
    //             leverageSlippage,
    //             // collateralDetails.contractAddress === zeroAddress,
    //           ],
    //         }
    //     :
    borrowType !== "open"
      ? {
          type: "adjustDen",
          variables: [borrowType === "deposit" ? formAmounts.getDepositVariables() : formAmounts.getWithdrawVariables(), currentBorrowingRate + MINIMUM_BORROWING_MINT_RATE],
        }
      : {
          type: "openDen",
          variables: [
            {
              depositCollateral: formAmounts.collateral,
              borrowNECT: formAmounts.debt,
            },
            currentBorrowingRate + MINIMUM_BORROWING_MINT_RATE,
          ],
        };

  const {
    submit: submitApproval,
    isConfirming: isConfirmingApproval,
    isPending: isPendingApproval,
    isIdle: isIdleApproval,
    isConfirmed: isConfirmedApproval,
    error: errorApproval,
  } = useTransaction({ type: "approveCollateral", variables: [collateralAmount, isLeveraging] }, true);
  const { submit, isConfirming, isPending, isIdle, isConfirmed, error: errorContract } = useTransaction(transaction, true);
  const error = errorApproval || errorContract;
  error && console.error(error);

  useEffect(() => {
    const newStep = isConfirming ? 4 : isConfirmingApproval ? 2 : isConfirmedApproval ? 3 : 1;
    if (newStep > step) {
      setStep(newStep);
    }
  }, [isConfirming, isConfirmingApproval, isConfirmedApproval]);
  useEffect(() => {
    if (!hasSubmittedApprovalRef.current) {
      submitApproval();
      hasSubmittedApprovalRef.current = true;
    } else if (!hasSubmittedRef.current && isConfirmedApproval) {
      updatePostHogEvent({ type: `den${borrowType.charAt(0).toUpperCase() + borrowType.slice(1)}` });
      submit(`den${borrowType.charAt(0).toUpperCase() + borrowType.slice(1)}` as PostHogEvent["type"]);
      hasSubmittedRef.current = true;
    }
  }, [isConfirmedApproval]);

  useEffect(() => {
    if (errorApproval || errorContract || isConfirmed) {
      isConfirmed && onConfirmation?.();
      if (postHogEvent) {
        const { type: postHogEventType, ...postHogEventDetails } = postHogEvent;
        const display_mode = simpleMode ? "simple" : "den";
        const wallet_id = account?.toString();
        posthog.capture(postHogEventType + "Error", { errorMessage: "Modal Closed", wallet_id, display_mode, ...postHogEventDetails });
      }
      removeModal("approveBorrow");
    }
  }, [errorApproval || errorContract || isConfirmed]);

  return {
    collateral: { price: collateralPrice, ...collateralDetails, approvalAmount },
    denManagerAddr,
    isApproval: !!approvalAmount,
    isConfirming,
    isPending,
    isIdle,
    isIdleApproval,
    isPendingApproval,
    isConfirmingApproval,
    isConfirmedApproval,
    error,
    step,
  };
};

export const getApproveBorrowScopedAtoms = () => [];
