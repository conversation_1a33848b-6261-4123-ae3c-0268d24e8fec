import { Box, CircularProgress, Divider, Grid, IconButton, Typography, useTheme } from "@mui/material";
import { useApproveBorrowStore } from "./store";
import { Done, SwapHorizontalCircleSharp } from "@mui/icons-material";
import { Den } from "@Beraborrowofficial/sdk";
import { formatToken } from "../../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";

const ApproveBorrow: React.FC<{ formAmounts: Den; onConfirmation?: () => void }> = ({ formAmounts, onConfirmation }) => {
  const { collateral, step } = useApproveBorrowStore(formAmounts, onConfirmation);
  const theme = useTheme();
  return (
    <Box
      sx={{
        [theme.breakpoints.up("lg")]: {
          padding: "32px 64px",
        },
      }}
    >
      <Grid container alignItems="center" justifyContent="space-evenly" pb={3}>
        <Grid item xs={12} md={5}>
          <Typography variant="h6" style={{ color: "#f0f0f0" }}>
            Collateral
          </Typography>
          <Typography variant="h6" style={{ color: "#f0f0f0" }}>
            {formatToken(formAmounts.collateral, collateral.decimals, 8, collateral.ticker)}
          </Typography>
        </Grid>
        <Grid item xs={12} md={2}>
          <IconButton>
            <SwapHorizontalCircleSharp style={{ color: "#f0f0f0" }} />
          </IconButton>
        </Grid>
        <Grid item xs={12} md={5}>
          <Typography variant="h6" style={{ color: "#f0f0f0" }}>
            Debt
          </Typography>
          <Typography variant="h6" style={{ color: "#f0f0f0" }}>
            {formAmounts.leverage ? "" : formatToken(formAmounts.debt, BERABORROW_ADDRESSES.debtToken.decimals, 8, BERABORROW_ADDRESSES.debtToken.ticker)}
          </Typography>
        </Grid>
      </Grid>

      <Divider sx={{ mb: 2, backgroundColor: `var(--text-primary)` }} />

      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>Approve {collateral.ticker} deposit</Typography>
        {step === 1 ? <CircularProgress size={16} color="inherit" /> : <Done color="success" />}
      </Box>
      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>Signing Message</Typography>
        {step < 2 ? <></> : step === 2 ? <CircularProgress size={16} color="inherit" /> : <Done color="success" />}
      </Box>
      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>Confirm Transaction in wallet </Typography>
        {step < 3 ? <></> : step === 3 ? <CircularProgress size={16} color="inherit" /> : <Done color="success" />}
      </Box>
      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>Signing Message</Typography>
        {step < 4 ? <></> : step === 4 ? <CircularProgress size={16} color="inherit" /> : <Done color="success" />}
      </Box>
    </Box>
  );
};
export default ApproveBorrow;
