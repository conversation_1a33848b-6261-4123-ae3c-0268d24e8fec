import { useAtomValue, useSetAtom } from "jotai";
import { useTransaction } from "../../../Hooks/useTransaction";
import { useEffect, useRef, useState } from "react";
import { useNotifications } from "../../../Hooks/useNotifications";
import { posthog } from "posthog-js";
import { updatePostHogEventAtom, postHogEventAtom, simpleModeAtom, accountAtom } from "../../../Atoms/Account";
import { managedVaultDepositAmountAtom } from "../../../Atoms/Boyco";

export const useApproveVaultStore = (onConfirmation?: () => void) => {
  const depositAmount = useAtomValue(managedVaultDepositAmountAtom);
  const hasSubmittedApprovalRef = useRef(false);
  const hasSubmittedRef = useRef(false);
  const { removeModal } = useNotifications();
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const postHogEvent = useAtomValue(postHogEventAtom);
  const simpleMode = useAtomValue(simpleModeAtom);
  const account = useAtomValue(accountAtom);
  const [step, setStep] = useState(1);
  const {
    submit: submitApproval,
    isConfirming: isConfirmingApproval,
    isPending: isPendingApproval,
    isIdle: isIdleApproval,
    isConfirmed: isConfirmedApproval,
    error: errorApproval,
  } = useTransaction({ type: "approveManagedVault", variables: [depositAmount] }, true);
  const {
    submit,
    isConfirming,
    isPending,
    isIdle,
    isConfirmed,
    error: errorContract,
  } = useTransaction(
    {
      type: "depositManagedVault",
      variables: [depositAmount],
    },
    true
  );
  const error = errorApproval || errorContract;
  error && console.error(error);

  useEffect(() => {
    const newStep = isConfirming ? 4 : isConfirmingApproval ? 2 : isConfirmedApproval ? 3 : 1;
    if (newStep > step) {
      setStep(newStep);
    }
  }, [isConfirming, isConfirmingApproval, isConfirmedApproval]);
  useEffect(() => {
    if (!hasSubmittedApprovalRef.current) {
      submitApproval();
      hasSubmittedApprovalRef.current = true;
    } else if (!hasSubmittedRef.current && isConfirmedApproval) {
      updatePostHogEvent({ type: `vaultManagedDeposit` });
      submit("managedVaultDeposit");
      hasSubmittedRef.current = true;
    }
  }, [isConfirmedApproval]);

  useEffect(() => {
    if (errorApproval || errorContract || isConfirmed) {
      isConfirmed && onConfirmation?.();
      if (postHogEvent) {
        const { type: postHogEventType, ...postHogEventDetails } = postHogEvent;
        const display_mode = simpleMode ? "simple" : "den";
        const wallet_id = account?.toString();
        posthog.capture(postHogEventType + "Error", { errorMessage: "Modal Closed", wallet_id, display_mode, ...postHogEventDetails });
      }
      removeModal("ApproveManagedVault");
    }
  }, [errorApproval || errorContract || isConfirmed]);

  return {
    isApproval: !!depositAmount,
    isConfirming,
    isPending,
    isIdle,
    isIdleApproval,
    isPendingApproval,
    isConfirmingApproval,
    isConfirmedApproval,
    error,
    step,
  };
};
