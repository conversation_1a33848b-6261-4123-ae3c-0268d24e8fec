import { useAtomValue, useSetAtom } from "jotai";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { useTransaction } from "../../../Hooks/useTransaction";
import { useEffect, useRef, useState } from "react";
import { useNotifications } from "../../../Hooks/useNotifications";
import { posthog } from "posthog-js";
import { updatePostHogEventAtom, postHogEventAtom, simpleModeAtom, accountAtom } from "../../../Atoms/Account";
import { getVaultDepositAtom, vaultDepositAmountAtom } from "../../../Atoms/Vault";

export const useApproveVaultStore = (onConfirmation?: () => void) => {
  const { data: currentDeposit } = useAtomValue(getVaultDepositAtom);
  const depositAmount = useAtomValue(vaultDepositAmountAtom);
  const depositShares = (depositAmount * currentDeposit.collateralToShareRatio) / SCALING_FACTOR;
  const depositAssets = (depositShares * currentDeposit.shareToAssetRatio) / SCALING_FACTOR;

  const hasSubmittedApprovalRef = useRef(false);
  const hasSubmittedRef = useRef(false);
  const { removeModal } = useNotifications();
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const postHogEvent = useAtomValue(postHogEventAtom);
  const simpleMode = useAtomValue(simpleModeAtom);
  const account = useAtomValue(accountAtom);
  const [step, setStep] = useState(1);
  const {
    submit: submitApproval,
    isConfirming: isConfirmingApproval,
    isPending: isPendingApproval,
    isIdle: isIdleApproval,
    isConfirmed: isConfirmedApproval,
    error: errorApproval,
  } = useTransaction({ type: "approveVault", variables: [depositAmount] }, true);
  const {
    submit,
    isConfirming,
    isPending,
    isIdle,
    isConfirmed,
    error: errorContract,
  } = useTransaction(
    {
      type: "depositVault",
      variables: [depositAssets],
    },
    true
  );
  const error = errorApproval || errorContract;
  error && console.error(error);

  useEffect(() => {
    const newStep = isConfirming ? 4 : isConfirmingApproval ? 2 : isConfirmedApproval ? 3 : 1;
    if (newStep > step) {
      setStep(newStep);
    }
  }, [isConfirming, isConfirmingApproval, isConfirmedApproval]);
  useEffect(() => {
    if (!hasSubmittedApprovalRef.current) {
      submitApproval();
      hasSubmittedApprovalRef.current = true;
    } else if (!hasSubmittedRef.current && isConfirmedApproval) {
      updatePostHogEvent({ type: `vaultDeposit` });
      submit("vaultDeposit");
      hasSubmittedRef.current = true;
    }
  }, [isConfirmedApproval]);

  useEffect(() => {
    if (errorApproval || errorContract || isConfirmed) {
      isConfirmed && onConfirmation?.();
      if (postHogEvent) {
        const { type: postHogEventType, ...postHogEventDetails } = postHogEvent;
        const display_mode = simpleMode ? "simple" : "den";
        const wallet_id = account?.toString();
        posthog.capture(postHogEventType + "Error", { errorMessage: "Modal Closed", wallet_id, display_mode, ...postHogEventDetails });
      }
      removeModal("ApproveVault");
    }
  }, [errorApproval || errorContract || isConfirmed]);

  return {
    isApproval: !!depositAmount,
    isConfirming,
    isPending,
    isIdle,
    isIdleApproval,
    isPendingApproval,
    isConfirmingApproval,
    isConfirmedApproval,
    error,
    step,
  };
};
