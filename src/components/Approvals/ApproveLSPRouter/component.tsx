import { Box, CircularProgress, Divider, Typography, useTheme } from "@mui/material";
import { Done } from "@mui/icons-material";
import { useApproveLSPRouterStore } from "./store";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";

const ApproveLSPRouter: React.FC<{ onConfirmation?: () => void }> = ({ onConfirmation }) => {
  const { step } = useApproveLSPRouterStore(onConfirmation);
  const theme = useTheme();
  return (
    <Box
      sx={{
        [theme.breakpoints.up("lg")]: {
          padding: "32px 64px",
        },
      }}
    >
      <Divider sx={{ mb: 2, backgroundColor: `var(--text-primary)` }} />

      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>Approve s{BERABORROW_ADDRESSES.debtToken.ticker} withdrawal</Typography>
        {step === 1 ? <CircularProgress size={16} color="inherit" /> : <Done color="success" />}
      </Box>
      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>Signing Message</Typography>
        {step < 2 ? <></> : step === 2 ? <CircularProgress size={16} color="inherit" /> : <Done color="success" />}
      </Box>
      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>Confirm Transaction in wallet </Typography>
        {step < 3 ? <></> : step === 3 ? <CircularProgress size={16} color="inherit" /> : <Done color="success" />}
      </Box>
      <Box display="flex" justifyContent="space-between" mb={1}>
        <Typography>Signing Message</Typography>
        {step < 4 ? <></> : step === 4 ? <CircularProgress size={16} color="inherit" /> : <Done color="success" />}
      </Box>
    </Box>
  );
};
export default ApproveLSPRouter;
