import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { useTransaction } from "../../../Hooks/useTransaction";
import { useEffect, useRef, useState } from "react";
import { useNotifications } from "../../../Hooks/useNotifications";
import { posthog } from "posthog-js";
import { updatePostHogEventAtom, postHogEventAtom, simpleModeAtom, accountAtom } from "../../../Atoms/Account";
import {
  convertSharesToAssetsAtom,
  stabilityPoolWithdrawAmountAtom,
  getStabilityDepositAtom,
  //   preferredUnderlyingTokensAtom,
  //   getLspAllowanceAtom,
  //   poolAssetSelectedAtom,
  //   getRedeemPreferredUnderlyingToAnyAtom,
} from "../../../Atoms/StabilityPool";
import { BERABORROW_ADDRESSES } from "../../../utils/constants";

export const useApproveLSPRouterStore = (onConfirmation?: () => void) => {
  //   const { data: lspAllowance } = useAtomValue(getLspAllowanceAtom);
  const { data: shareToAssetRatio } = useAtomValue(convertSharesToAssetsAtom);
  const { data: currentDeposit } = useAtomValue(getStabilityDepositAtom);
  //   const preferredUnderlyingTokens = useAtomValue(preferredUnderlyingTokensAtom);
  //   const { data: redeemedAssets } = useAtomValue(getRedeemPreferredUnderlyingToAnyAtom);
  const withdrawAmount = useAtomValue(stabilityPoolWithdrawAmountAtom);
  //   const poolAssetSelected = useAtomValue(poolAssetSelectedAtom);
  const myAssets = currentDeposit.convertSharesToAssets(withdrawAmount, shareToAssetRatio);
  //   const preferredUnderlyingContractAddresses = preferredUnderlyingTokens?.map((item) => item.contractAddress) ?? [];
  const debtPrice = currentDeposit.assets.find((item) => item.contractAddress === BERABORROW_ADDRESSES.debtToken.contractAddress)?.price;
  if (debtPrice === undefined) throw Error("debt Price not found");
  const withdrawalDebtValue = (myAssets * SCALING_FACTOR) / debtPrice;
  const approvalAmount = withdrawAmount;
  const hasSubmittedApprovalRef = useRef(false);
  const hasSubmittedRef = useRef(false);
  const { removeModal } = useNotifications();
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const postHogEvent = useAtomValue(postHogEventAtom);
  const simpleMode = useAtomValue(simpleModeAtom);
  const account = useAtomValue(accountAtom);
  const [step, setStep] = useState(1);
  //   const redeemedAssetsLength = (redeemedAssets ?? currentDeposit.getUnderlyingAssets()).filter((item) => !!item.userBalance).length;
  const {
    submit: submitApproval,
    isConfirming: isConfirmingApproval,
    isPending: isPendingApproval,
    isIdle: isIdleApproval,
    isConfirmed: isConfirmedApproval,
    error: errorApproval,
  } = useTransaction({ type: "approveLsp", variables: [approvalAmount] }, true);
  const {
    submit,
    isConfirming,
    isPending,
    isIdle,
    isConfirmed,
    error: errorContract,
  } = useTransaction(
    // poolAssetSelected && poolAssetSelected !== BERABORROW_ADDRESSES.debtToken.contractAddress
    //   ? {
    //       type: "redeemPreferredUnderlying",
    //       variables: [withdrawAmount, withdrawalDebtValue, preferredUnderlyingContractAddresses, redeemedAssetsLength, DEFAULT_SLIPPAGE_TOLERANCE],
    //     }
    //   :
    {
      type: "withdrawDebt",
      variables: [
        withdrawAmount,
        withdrawalDebtValue,
        currentDeposit.assets.filter((item) => item.balance).map((item) => item.contractAddress),
        currentDeposit.assets.filter((item) => item.balance).map((item) => currentDeposit.getMyShare(item.balance, withdrawAmount)),
        BigInt(currentDeposit.getUnderlyingAssets().filter((item) => item.balance).length),
      ],
    },
    true
  );
  const error = errorApproval || errorContract;
  error && console.error(error);

  useEffect(() => {
    const newStep = isConfirming ? 4 : isConfirmingApproval ? 2 : isConfirmedApproval ? 3 : 1;
    if (newStep > step) {
      setStep(newStep);
    }
  }, [isConfirming, isConfirmingApproval, isConfirmedApproval]);
  useEffect(() => {
    if (!hasSubmittedApprovalRef.current) {
      submitApproval();
      hasSubmittedApprovalRef.current = true;
    } else if (!hasSubmittedRef.current && isConfirmedApproval) {
      updatePostHogEvent({ type: `poolWithdraw` });
      submit("poolWithdraw");
      hasSubmittedRef.current = true;
    }
  }, [isConfirmedApproval]);

  useEffect(() => {
    if (errorApproval || errorContract || isConfirmed) {
      isConfirmed && onConfirmation?.();
      if (postHogEvent) {
        const { type: postHogEventType, ...postHogEventDetails } = postHogEvent;
        const display_mode = simpleMode ? "simple" : "den";
        const wallet_id = account?.toString();
        posthog.capture(postHogEventType + "Error", { errorMessage: "Modal Closed", wallet_id, display_mode, ...postHogEventDetails });
      }
      removeModal("ApproveLSPRouter");
    }
  }, [errorApproval || errorContract || isConfirmed]);

  return {
    isApproval: !!approvalAmount,
    isConfirming,
    isPending,
    isIdle,
    isIdleApproval,
    isPendingApproval,
    isConfirmingApproval,
    isConfirmedApproval,
    error,
    step,
  };
};
