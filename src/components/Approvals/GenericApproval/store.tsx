import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useTransaction } from "../../../Hooks/useTransaction";
import { useEffect, useRef, useState } from "react";
import { useNotifications } from "../../../Hooks/useNotifications";
import { posthog } from "posthog-js";
import { updatePostHogEventAtom, postHogEventAtom, simpleModeAtom, accountAtom, beraborrowAtom } from "../../../Atoms/Account";
import { TransactionProps } from "../../../@type/Transactions";

export interface ApprovalConfig {
  modalId: string;
  approvalTransactionType: TransactionProps["type"];
  mainTransactionType: TransactionProps["type"];
  postHogApprovalEvent: string;
  postHogMainEvent: string;
  approvalLabel: string;
  mainLabel: string;
}

export const useGenericApprovalStore = (amount: bigint, config: ApprovalConfig, onConfirmation?: () => void, additionalVariables?: any[]) => {
  const hasSubmittedApprovalRef = useRef(false);
  const hasSubmittedRef = useRef(false);
  const { removeModal } = useNotifications();
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const postHogEvent = useAtomValue(postHogEventAtom);
  const simpleMode = useAtomValue(simpleModeAtom);
  const account = useAtomValue(accountAtom);
  const beraborrow = useAtomValue(beraborrowAtom);
  const [step, setStep] = useState(1);

  const getApprovalContractAddress = () => {
    if (config.modalId === "ApproveSPollenLocking") {
      return beraborrow.sPollenVotingEscrow.contractAddress;
    }
    if (config.modalId === "ApproveLpPollenLocking") {
      return beraborrow.lpVotingEscrow.contractAddress;
    }
    if (config.modalId === "ApprovePollenWrapping") {
      return beraborrow.sPollenToken.contractAddress;
    }
    if (config.modalId === "ApproveSPollenStaking") {
      return beraborrow.sPollenStaking.contractAddress;
    }
    if (config.modalId === "ApproveLpPollenStaking") {
      return beraborrow.lpPollenStaking.contractAddress;
    }
    return undefined;
  };

  const approvalContractAddress = getApprovalContractAddress();
  const approvalVariables = approvalContractAddress ? [amount, approvalContractAddress] : [amount];

  const {
    submit: submitApproval,
    isConfirming: isConfirmingApproval,
    isPending: isPendingApproval,
    isIdle: isIdleApproval,
    isConfirmed: isConfirmedApproval,
    error: errorApproval,
  } = useTransaction({ type: config.approvalTransactionType, variables: approvalVariables } as TransactionProps, true);

  const mainTransactionVariables = additionalVariables ? [amount, ...additionalVariables] : [amount];

  const {
    submit,
    isConfirming,
    isPending,
    isIdle,
    isConfirmed,
    error: errorContract,
  } = useTransaction(
    {
      type: config.mainTransactionType,
      variables: mainTransactionVariables,
    } as TransactionProps,
    true
  );

  const error = errorApproval ?? errorContract;
  error && console.error(error);

  useEffect(() => {
    const newStep = isConfirming ? 4 : isConfirmingApproval ? 2 : isConfirmedApproval ? 3 : 1;
    if (newStep > step) {
      setStep(newStep);
    }
  }, [isConfirming, isConfirmingApproval, isConfirmedApproval]);

  useEffect(() => {
    if (!hasSubmittedApprovalRef.current) {
      submitApproval();
      hasSubmittedApprovalRef.current = true;
    } else if (!hasSubmittedRef.current && isConfirmedApproval) {
      updatePostHogEvent({ type: config.postHogMainEvent });
      submit();
      hasSubmittedRef.current = true;
    }
  }, [isConfirmedApproval, config.postHogMainEvent]);

  useEffect(() => {
    if (errorApproval || errorContract || isConfirmed) {
      isConfirmed && onConfirmation?.();
      if (postHogEvent) {
        const { type: postHogEventType, ...postHogEventDetails } = postHogEvent;
        const display_mode = simpleMode ? "simple" : "den";
        const wallet_id = account?.toString();
        posthog.capture(postHogEventType + "Error", { errorMessage: "Modal Closed", wallet_id, display_mode, ...postHogEventDetails });
      }
      removeModal(config.modalId);
    }
  }, [errorApproval, errorContract, isConfirmed, config.modalId]);

  return {
    isApproval: !!amount,
    isConfirming,
    isPending,
    isIdle,
    isIdleApproval,
    isPendingApproval,
    isConfirmingApproval,
    isConfirmed,
    isConfirmedApproval,
    error,
    step,
    config,
  };
};
