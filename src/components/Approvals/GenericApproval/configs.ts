import { ApprovalConfig } from "./store";

export const APPROVAL_CONFIGS: Record<string, ApprovalConfig> = {
  pollenWrapping: {
    modalId: "ApprovePollenWrapping",
    approvalTransactionType: "approveCollateral",
    mainTransactionType: "wrapPollen",
    postHogApprovalEvent: "approvePollenWrapping",
    postHogMainEvent: "pollenWrapping",
    approvalLabel: "Approve POLLEN Wrapping",
    mainLabel: "Wrap POLLEN",
  },
  sPollenStaking: {
    modalId: "ApproveSPollenStaking",
    approvalTransactionType: "approveCollateral",
    mainTransactionType: "stakePollen",
    postHogApprovalEvent: "approveSPollenStaking",
    postHogMainEvent: "sPollenStaking",
    approvalLabel: "Approve sPOLLEN Staking",
    mainLabel: "Stake sPOLLEN",
  },
  lpPollenStaking: {
    modalId: "ApproveLpPollenStaking",
    approvalTransactionType: "approveCollateral",
    mainTransactionType: "stakeLpPollen",
    postHogApprovalEvent: "approveLpPollenStaking",
    postHogMainEvent: "lpPollenStaking",
    approvalLabel: "Approve BERA-POLLEN Staking",
    mainLabel: "Stake BERA-POLLEN",
  },
  sPollenLocking: {
    modalId: "ApproveSPollenLocking",
    approvalTransactionType: "approveCollateral",
    mainTransactionType: "increaseLockPosition",
    postHogApprovalEvent: "approveSPollenLocking",
    postHogMainEvent: "sPollenLocking",
    approvalLabel: "Approve sPOLLEN Locking",
    mainLabel: "Lock sPOLLEN",
  },
  lpPollenLocking: {
    modalId: "ApproveLpPollenLocking",
    approvalTransactionType: "approveCollateral",
    mainTransactionType: "increaseLpLockPosition",
    postHogApprovalEvent: "approveLpPollenLocking",
    postHogMainEvent: "lpPollenLocking",
    approvalLabel: "Approve BERA-POLLEN Locking",
    mainLabel: "Lock BERA-POLLEN",
  },
};
