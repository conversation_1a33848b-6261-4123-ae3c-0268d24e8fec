import { useAtomValue } from "jotai";
import { useEffect, useState } from "react";
import { lpLiquidStakeAmountAtom } from "../../../Atoms/Stake";
import { useTransaction } from "../../../Hooks/useTransaction";

export const useApproveLpPollenStakingStore = () => {
  const [step, setStep] = useState(1);
  const stakeAmount = useAtomValue(lpLiquidStakeAmountAtom);

  const {
    submit: submitApproval,
    isConfirming: isConfirmingApproval,
    isPending: isPendingApproval,
    isIdle: isIdleApproval,
    isConfirmed: isConfirmedApproval,
    error: errorApproval,
  } = useTransaction({ type: "approveCollateral", variables: [stakeAmount, false] }, true);
  const {
    submit,
    isConfirming,
    isPending,
    isIdle,
    isConfirmed,
    error: errorContract,
  } = useTransaction(
    {
      type: "stakeLpPollen",
      variables: [stakeAmount],
    },
    true
  );
  const error = errorApproval ?? errorContract;
  error && console.error(error);

  useEffect(() => {
    const newStep = isConfirming ? 4 : isConfirmingApproval ? 2 : isConfirmedApproval ? 3 : 1;
    if (newStep > step) {
      setStep(newStep);
    }
  }, [isConfirming, isConfirmingApproval, isConfirmedApproval]);

  const handleApproval = () => {
    submitApproval();
  };

  const handleStake = () => {
    submit();
  };

  return {
    step,
    handleApproval,
    handleStake,
    isConfirmedApproval,
    isConfirmed,
    error,
    isPendingApproval,
    isPending,
    isIdleApproval,
    isIdle,
  };
};
