import { Box, Button, Typography, useTheme } from "@mui/material";
import { useApproveLpPollenStakingStore } from "./store";

const ApproveLpPollenStaking = () => {
  const theme = useTheme();
  const { step, handleApproval, handleStake, isConfirmed, error, isPendingApproval, isPending } = useApproveLpPollenStakingStore();

  const steps = [
    { id: 1, label: "Approve BERA-POLLEN", action: handleApproval, pending: isPendingApproval },
    { id: 2, label: "Confirming approval...", action: null, pending: false },
    { id: 3, label: "Stake BERA-POLLEN", action: handleStake, pending: isPending },
    { id: 4, label: "Confirming stake...", action: null, pending: false },
  ];

  const currentStep = steps.find((s) => s.id === step);

  if (error) {
    return (
      <Box sx={{ p: 2, textAlign: "center" }}>
        <Typography color="error" variant="body2">
          Error: Transaction failed
        </Typography>
      </Box>
    );
  }

  if (isConfirmed) {
    return (
      <Box sx={{ p: 2, textAlign: "center" }}>
        <Typography color="success.main" variant="body2">
          BERA-POLLEN staked successfully!
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" sx={{ mb: 2, textAlign: "center" }}>
        Stake BERA-POLLEN
      </Typography>

      <Box sx={{ mb: 2 }}>
        {steps.map((stepItem) => (
          <Box
            key={stepItem.id}
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 1,
              opacity: stepItem.id <= step ? 1 : 0.5,
            }}
          >
            <Box
              sx={{
                width: 24,
                height: 24,
                borderRadius: "50%",
                backgroundColor: stepItem.id < step ? theme.palette.success.main : stepItem.id === step ? theme.palette.primary.main : theme.palette.grey[300],
                color: "white",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "12px",
                fontWeight: "bold",
                mr: 2,
              }}
            >
              {stepItem.id < step ? "✓" : stepItem.id}
            </Box>
            <Typography variant="body2" sx={{ flex: 1 }}>
              {stepItem.label}
            </Typography>
          </Box>
        ))}
      </Box>

      {currentStep?.action && (
        <Button
          fullWidth
          variant="contained"
          onClick={currentStep.action}
          disabled={currentStep.pending}
          sx={{
            borderRadius: 4,
            py: 1.5,
          }}
        >
          {currentStep.pending ? "Processing..." : currentStep.label}
        </Button>
      )}
    </Box>
  );
};

export default ApproveLpPollenStaking;
