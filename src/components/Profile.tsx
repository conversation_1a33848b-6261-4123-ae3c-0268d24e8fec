import { Box, Button, Grid, IconButton, Typo<PERSON> } from "@mui/material";
import { useAccount, useDisconnect } from "wagmi";
import { truncateAddress } from "../utils/helpers";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import CheckIcon from "@mui/icons-material/Check";
import LoginIcon from "@mui/icons-material/Login";
import { useState } from "react";

import { useNotifications } from "../Hooks/useNotifications";
import { Avatar } from "./Avatar";

const Profile: React.FC = () => {
  const { disconnect } = useDisconnect();
  const { address } = useAccount();
  const [copied, setCopied] = useState<boolean>(false);
  const { removeModal } = useNotifications();
  const copyAddress = () => {
    if (!address) return;
    navigator.clipboard
      .writeText(address)
      .then(() => {
        setCopied(true);
        setTimeout(() => {
          setCopied(false);
        }, 2000);
      })
      .catch((err) => {
        console.error("Failed to copy text: ", err);
      });
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
      <Box sx={{ background: "var(--background-accent)", width: "100%", alignItems: "center" }} p={2} display={"flex"} gap={2} flexDirection={"column"} borderRadius={"20px"}>
        <Avatar width={72} height={72} />
        <Typography fontSize={22} onClick={() => copyAddress()} sx={{ cursor: "pointer" }}>
          {truncateAddress(address, 4)}
          {copied ? (
            <IconButton sx={{ color: "white" }}>
              <CheckIcon />
            </IconButton>
          ) : (
            <IconButton onClick={() => copyAddress()} sx={{ color: "white" }}>
              <ContentCopyIcon />
            </IconButton>
          )}
        </Typography>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Button
            onClick={() => {
              disconnect();
              removeModal("show-profile");
            }}
            sx={{ color: "var(--text-secondary)", width: "100%", borderRadius: "20px", background: "var(--background-accent)", py: 2 }}
          >
            Disconnect
            <IconButton onClick={() => copyAddress()} sx={{ color: "var(--text-secondary)" }}>
              <LoginIcon />
            </IconButton>
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
};
export default Profile;
