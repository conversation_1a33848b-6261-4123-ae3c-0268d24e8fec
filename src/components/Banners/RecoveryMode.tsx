import { Box, Typography } from "@mui/material";
import { isRecoveryModeAtom } from "../../Atoms/Den";
import WithSuspense from "../../providers/WithSuspense";
import { Link } from "react-router-dom";
import { useAtomValue } from "jotai";

const Component = () => {
  const { data: isRecoveryMode } = useAtomValue(isRecoveryModeAtom);

  return (
    <Box
      sx={{
        display: isRecoveryMode ? "flex" : "none",
        textAlign: "center",
        overflow: "hidden",
        width: "100%",
        backgroundImage: "var(--error-gradient)",
        p: { xs: 1 },
        justifyContent: "center",
        alignItems: "center", // Vertically centers the content
      }}
    >
      {" "}
      <img src="/imgs/recoveryMode.svg" height={"24px"} />
      <Typography variant="h5" sx={{ fontWeight: { md: 700 }, verticalAlign: "middle", px: 1 }}>
        Recovery mode activated
      </Typography>
      <Link target="_blank" to={"https://beraborrow.gitbook.io/docs/borrowing/recovery-mode"}>
        <Typography variant="h5" sx={{ fontWeight: { md: 700 }, verticalAlign: "middle" }} color={"var(--text-primary)"}>
          Read More
        </Typography>
      </Link>
    </Box>
  );
};

export const RecoverModeBanner = WithSuspense(Component);
