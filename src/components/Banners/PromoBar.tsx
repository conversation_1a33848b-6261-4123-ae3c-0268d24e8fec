import { Box, Link, Typography, useTheme, useMediaQuery } from "@mui/material";

const PromoText = () => (
  <Typography variant="h4" fontWeight={600} color="white" sx={{ whiteSpace: "nowrap" }}>
    The Berachain Prime Vault with Euler is live - $40k in rewards from: Euler x eOracle x Beraborrow x Burrbear x ZeroLend - &nbsp;
    <Link
      href="https://app.euler.finance/vault/******************************************?network=berachain"
      target="_blank"
      rel="noopener noreferrer"
      sx={{ color: "white", fontWeight: 700 }}
    >
      Deposit HERE
    </Link>
  </Typography>
);

export const PromoBar = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Box
      sx={{
        width: "100%",
        overflow: "hidden",
        backgroundColor: "#EC6F15",
        py: isMobile ? 0.75 : 1,
      }}
    >
      {isMobile ? (
        <Box
          sx={{
            display: "flex",
            width: "max-content",
            animation: "scroll-left 20s linear infinite",
            "@keyframes scroll-left": {
              "0%": { transform: "translateX(0)" },
              "100%": { transform: "translateX(-50%)" },
            },
          }}
        >
          <Box sx={{ display: "flex", gap: 8, pr: 4 }}>
            <PromoText />
            <PromoText />
          </Box>
        </Box>
      ) : (
        <Box sx={{ display: "flex", justifyContent: "center" }}>
          <PromoText />
        </Box>
      )}
    </Box>
  );
};
