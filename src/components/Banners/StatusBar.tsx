import { Box, Typography } from "@mui/material";
import { useAtomValue } from "jotai";
import { getDebtTotalSupplyAtom } from "../../Atoms/Tokens";
import { formatCurrency, formatDecimal } from "../../utils/helpers";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import WithSuspense from "../../providers/WithSuspense";
import { defillamaTVLAtom } from "../../Atoms/System";
import { getLspTvlAtom } from "../../Atoms/StabilityPool";

const StatusBarTextItem = ({ label, value }: { label: string; value: string }) => {
  return (
    <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
      <Typography sx={{ color: "white", fontSize: 12, pl: { xs: 1, md: 0 } }}>{label}</Typography>
      <Typography
        sx={{
          color: "white",
          fontSize: 12,
          background: "#D55002",
          borderRadius: 3,
          p: 1,
        }}
      >
        {value}
      </Typography>
    </Box>
  );
};

const Component = () => {
  const { data: totalDebtSupply } = useAtomValue(getDebtTotalSupplyAtom);
  const { data: defillamaTVL } = useAtomValue(defillamaTVLAtom);
  const { data: lspTvl } = useAtomValue(getLspTvlAtom);

  const items = [
    {
      label: "TVL",
      value: formatCurrency(defillamaTVL),
    },
    {
      label: "Minted NECT",
      value: formatCurrency(formatDecimal(totalDebtSupply, BERABORROW_ADDRESSES.debtToken.decimals)),
    },
    {
      label: "Pool's TVL",
      value: formatCurrency(formatDecimal(lspTvl, BERABORROW_ADDRESSES.debtToken.decimals)),
    },
  ];

  return (
    <Box
      sx={{
        overflow: "hidden",
        width: "100%",
        background: "#EC6F15",
        whiteSpace: "nowrap",
        p: { xs: 1, lg: 0 },
      }}
    >
      <Box
        sx={{
          display: "flex",
          width: "fit-content",
          animation: {
            xs: "scroll-left 25s linear infinite",
            sm: "scroll-left 25s linear infinite",
            md: "none",
          },

          "@keyframes scroll-left": {
            "0%": { transform: "translateX(0)" },
            "100%": { transform: "translateX(-50%)" },
          },
        }}
      >
        <Box sx={{ display: "flex", gap: 3 }}>
          {items.map((item) => (
            <StatusBarTextItem label={item.label} value={item.value} key={item.label} />
          ))}
        </Box>
        <Box sx={{ display: { xs: "flex", md: "none" }, gap: 3, pl: 1 }}>
          {items.map((item) => (
            <StatusBarTextItem label={item.label} value={item.value} key={item.label + "-copy"} />
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export const StatusBar = WithSuspense(Component);
