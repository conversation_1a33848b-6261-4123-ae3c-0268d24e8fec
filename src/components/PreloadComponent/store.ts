import { useEffect } from "react";
import { preloadCompSetter<PERSON>tom, preloadCompAtom } from "../../Atoms/System";
import { useSetAtom, useAtomValue } from "jotai";

export const usePreloadComponentStore = (delay: number) => {
  const setter = useSetAtom(preloadCompSetterAtom);
  const display = useAtomValue(preloadCompAtom);

  useEffect(() => {
    setter(delay);
  }, []);
  return { display };
};

export const getPreloadComponentScopedAtoms = () => [preloadCompSetterAtom, preloadCompAtom];
