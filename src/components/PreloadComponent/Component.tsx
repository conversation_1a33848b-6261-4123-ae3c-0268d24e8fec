import { FC, ReactNode, Suspense } from "react";
import { getPreloadComponentScopedAtoms, usePreloadComponentStore } from "./store";
import withScopedProvider from "../../providers/WithScopedProvider";
import { Box } from "@mui/material";

const PreloadComponent: FC<{ children: ReactNode; delay?: number }> = ({ children, delay = 3000 }) => {
  const { display } = usePreloadComponentStore(delay);
  return (
    <Box
      sx={{
        position: "absolute",
        top: "-9999px",
        left: "-9999px",
        width: "1px",
        height: "1px",
        opacity: 0,
        pointerEvents: "none",
      }}
    >
      {display && <Suspense fallback={null}>{children}</Suspense>}
    </Box>
  );
};

export default withScopedProvider(PreloadComponent, getPreloadComponentScopedAtoms());
