import React, { ReactNode, useRef, useEffect } from "react";
import { Box, TextField, useTheme } from "@mui/material";

type StyledTextfieldProps = React.ComponentProps<typeof TextField> & { endComponent?: ReactNode; startComponent?: ReactNode; fontSize?: number | string; startIcons?: ReactNode };
const Component: React.FC<StyledTextfieldProps> = ({ endComponent, startComponent, error, fontSize, startIcons, ...textFieldProps }) => {
  const theme = useTheme();
  const textFieldRef = useRef<HTMLInputElement>(null);
  const handleClick = () => {
    if (textFieldRef.current && !textFieldProps.disabled) {
      textFieldRef.current.focus();
    }
  };
  useEffect(() => {
    const input = textFieldRef.current;
    if (input) {
      const preventScroll = (event: WheelEvent) => event.preventDefault();

      // Add non-passive event listener manually
      input.addEventListener("wheel", preventScroll, { passive: false });

      // Cleanup: remove the event listener on component unmount
      return () => {
        input.removeEventListener("wheel", preventScroll);
      };
    }
  }, []);
  return (
    <>
      <Box
        display="flex"
        flexDirection="row"
        justifyContent={"space-between"}
        onClick={handleClick}
        sx={{
          position: "relative",
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "16px",
          },
          borderRadius: "12px",
          border: `1px solid ${error ? theme.palette.error.main : theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper,
          cursor: textFieldProps.disabled ? undefined : "text",
          "&:hover": textFieldProps.disabled
            ? undefined
            : {
                borderColor: error ? theme.palette.error.main : theme.palette.text.primary,
              },
          "&:focus-within": textFieldProps.disabled
            ? undefined
            : {
                boxShadow: `0 0 0 1px ${error ? theme.palette.error.main : theme.palette.primary.main}`,
                borderColor: error ? theme.palette.error.main : theme.palette.primary.main,
              },
        }}
      >
        <Box display="flex" flexDirection="column" alignItems="start" flexGrow={1}>
          <Box display="flex" width={"100%"} flexDirection={startIcons ? "row" : "column"}>
            {startIcons && (
              <Box mt="auto" mr="8px">
                {startIcons}
              </Box>
            )}
            <TextField
              fullWidth
              type="number"
              variant="standard"
              inputRef={textFieldRef} // Attach the ref to the TextField
              inputProps={{
                style: {
                  textAlign: "left",
                  fontSize: fontSize || 24,
                  color: textFieldProps.disabled ? "var(--text-secondary)" : undefined,
                  WebkitTextFillColor: textFieldProps.disabled ? "var(--text-secondary)" : undefined,
                },
              }}
              InputProps={{
                disableUnderline: true,
              }}
              {...textFieldProps}
            />
          </Box>
          {startComponent && <Box mt="auto">{startComponent}</Box>}
        </Box>
        {endComponent && (
          <Box display="flex" alignItems="center" justifyContent="center">
            {endComponent}
          </Box>
        )}
      </Box>
    </>
  );
};

const StyledTextfield = Component;
export default StyledTextfield;
