import WithSuspense from "../../providers/WithSuspense";
import WithdrawOnlyTableView from "./WithdrawOnlyTableView";

const WithdrawTable: React.FC = () => {
  return (
    <>
      <>
        <WithdrawOnlyTableView
          key={"0x583Cc8a82B55A96a9dED97f5353397c85ee8b60E"}
          contractAddress={"0x583Cc8a82B55A96a9dED97f5353397c85ee8b60E"}
          collateralAddress="0x93F4d0ab6a8B4271f4a28Db399b5E30612D21116"
        />
        <WithdrawOnlyTableView
          key={"0xc6cff91fae96ac93de25a735d2877614522cbc02"}
          contractAddress={"0xc6cff91fae96ac93de25a735d2877614522cbc02"}
          collateralAddress="0x9B2316cfe980515de7430F1c4E831B89a5921137"
        />{" "}
        <WithdrawOnlyTableView
          key={"0xdf3F6ABbA9Cb5bA375ffeC89bF246800b4Aed3eC"}
          contractAddress={"0xdf3F6ABbA9Cb5bA375ffeC89bF246800b4Aed3eC"}
          collateralAddress="0xf6718b2701D4a6498eF77D7c152b2137Ab28b8A3"
        />
        <WithdrawOnlyTableView
          key={"0x77A0F11926FaaeBe041D723F5B20069FCB4C4c4A"}
          contractAddress={"0x77A0F11926FaaeBe041D723F5B20069FCB4C4c4A"}
          collateralAddress="0xE946Dd7d03F6F5C440F68c84808Ca88d26475FC5"
        />
        <WithdrawOnlyTableView
          key={"0x3A6E624C162133D318476863A5f28E50bcedc9c3"}
          contractAddress={"0x3A6E624C162133D318476863A5f28E50bcedc9c3"}
          collateralAddress="0xdCB3D91555385DaE23e6B966b5626aa7A75Be940"
        />
      </>
    </>
  );
};
export default WithSuspense(WithdrawTable, "paper");
