import { Box, Typography, Button, useMediaQuery, useTheme } from "@mui/material";
import { formatBigIntPercentage, formatTokenWithMillionsOrBillions } from "../../utils/helpers";
import TokenIcon from "../TokenIcons";
import WithScopedProvider from "../../providers/WithScopedProvider";
import { Hex } from "viem";
import { useManagedVaultWithdrawStore } from "./withdrawStore";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import Modal from "../Modal/Modal";

const WithdrawOnlyTableView: React.FC<{ contractAddress: Hex; collateralAddress: Hex }> = ({ contractAddress, collateralAddress }) => {
  const collateralDetails = BERABORROW_ADDRESSES.collateralTokens[collateralAddress];
  const { dispatcher, apy, collateralRatio, userBalance, vaultTvl, modals } = useManagedVaultWithdrawStore(contractAddress);
  const theme = useTheme();

  const isSmUp = useMediaQuery(theme.breakpoints.up("sm"));
  return (
    <>
      {" "}
      {modals.map((modal) => (
        <Modal key={modal.id} {...modal} />
      ))}{" "}
      <Button
        onClick={() => {
          dispatcher({ type: "withdraw", payload: contractAddress });
        }}
        fullWidth
        variant="contained"
        size="small"
        sx={{
          display: !userBalance ? "none" : "inherit",
          minWidth: "800px",
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "16px",
          },
          borderRadius: isSmUp ? "12px" : "18px",
          border: "2px solid var(--border-dark)",
          backgroundColor: theme.palette.background.paper,
          "&:hover": {
            border: "2px solid var(--border-dark)",
            backgroundColor: theme.palette.common.black,
            opacity: 0.9,
          },
          "&.Mui-disabled": {
            border: "2px solid var(--border-dark)",
            backgroundColor: "var(--border-dark)",
          },
        }}
      >
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "repeat(6, 2fr)",
          }}
          alignItems="center"
          width="100%"
        >
          <Box display="flex" alignItems="center">
            <TokenIcon contractAddress={collateralAddress} height="35px" width="35px" />
            <Box pl={2} textAlign={"left"}>
              <Typography variant="h4" fontWeight={800} color="var(--text-primary)">
                {collateralDetails.ticker}
              </Typography>
            </Box>
          </Box>
          {/* Left Side: Token Icon and Text */}
          <Box display="flex" alignItems="center" justifyContent="center">
            {apy > 1000000000000n && (
              <Typography component="span" color={"var(--text-success)"}>
                {apy ? formatBigIntPercentage(apy, 2) + "%" : "-"}
              </Typography>
            )}
          </Box>

          <Box display="flex" alignItems="center" justifyContent="center">
            <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
              ${formatTokenWithMillionsOrBillions(vaultTvl, collateralDetails.decimals, 3)}
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" justifyContent="center">
            <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
              {collateralRatio ? `${formatBigIntPercentage(collateralRatio, 0)}%` : "-"}
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" justifyContent="center">
            <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
              -
            </Typography>
          </Box>

          <Box display="flex" alignItems="center" justifyContent="center">
            <Typography mt={0.3} component={"span"} color={"var(--text-primary)"}>
              {`$${formatTokenWithMillionsOrBillions(userBalance, collateralDetails.decimals, 2)}`}
            </Typography>
          </Box>
        </Box>
      </Button>
    </>
  );
};
export default WithScopedProvider(WithdrawOnlyTableView);
