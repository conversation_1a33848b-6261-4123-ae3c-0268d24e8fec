import { useAtomValue } from "jotai";
import { _denManagerAddrAtom } from "../../Atoms/Den";
import { useHydrateAtoms } from "jotai/utils";
import { useQueryClient } from "@tanstack/react-query";
import { queryClientAtom } from "jotai-tanstack-query";
import { Hex } from "viem";
import { useNotifications } from "../../Hooks/useNotifications";
import { _vaultAddrAtom } from "../../Atoms/Vault";
import { accountAtom } from "../../Atoms/Account";

import { useAccount } from "wagmi";
import { boycoVaultAtom, boycoVaultCollateralAtom, getBoycoCollateralBalanceAtom, getBoycoCollateralPriceAtom, getBoycoUserBalanceAtom } from "../../Atoms/Boyco";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { modalsAtom } from "../../Atoms/Notifications";
import { ANON_ADDRESS } from "../../utils/constants";
import { Typography } from "@mui/material";
import TransactionButton from "../Transaction/Transaction";

export const useManagedVaultWithdrawStore = (managedVaultAddr: Hex) => {
  const { removeModal, addModal } = useNotifications();
  const modals = useAtomValue(modalsAtom);
  const queryClient = useQueryClient();
  const { address } = useAccount();
  useHydrateAtoms([
    [queryClientAtom, queryClient],
    [accountAtom, address ?? ANON_ADDRESS],
    [boycoVaultAtom, managedVaultAddr],
  ]);
  const { data: collateralPrice } = useAtomValue(getBoycoCollateralPriceAtom);
  const { data: collateralBalance } = useAtomValue(getBoycoCollateralBalanceAtom);
  const { data: deposit } = useAtomValue(getBoycoUserBalanceAtom);

  const vaultTvl = (collateralBalance * collateralPrice) / SCALING_FACTOR;
  const userBalance = (deposit * collateralPrice) / SCALING_FACTOR;
  const dispatcher = (action: { type: "withdraw"; payload: Hex } | { type: "withdrew" }) => {
    switch (action.type) {
      case "withdraw": {
        addModal({
          id: "acceptWithdraw",
          title: "Withdraw Boyco Deposit?",
          Component: (
            <>
              <Typography pb={1}>Please proceed with withdrawal of your Boyco assets, please note this is not a managed vault and can only be withdrawn</Typography>
              <TransactionButton
                onConfirmation={() => {
                  dispatcher({ type: "withdrew" });
                }}
                transaction={{
                  type: "withdrawBoyco",
                  variables: [managedVaultAddr, deposit],
                }}
                variant="outlined"
              >
                Withdraw
              </TransactionButton>
            </>
          ),
        });
        break;
      }
      case "withdrew": {
        removeModal("acceptWithdraw");
        break;
      }
      default:
        break;
    }
  };

  return {
    dispatcher,
    apy: 0n,
    collateralRatio: 0n,
    userBalance,
    deposit,
    vaultTvl,
    modals,
  };
};

export const getManagedVaultSelectorScopedAtoms = () => [
  boycoVaultAtom,
  getBoycoCollateralPriceAtom,
  getBoycoCollateralBalanceAtom,
  getBoycoUserBalanceAtom,
  boycoVaultCollateralAtom,
];
