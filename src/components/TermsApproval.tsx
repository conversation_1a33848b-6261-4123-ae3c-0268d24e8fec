import React, { useState } from "react";
import { <PERSON>, Button, Link, Typography } from "@mui/material";
import { useNotifications } from "../Hooks/useNotifications";
import { useSetAtom } from "jotai";
import { acceptedAtom, consentAtom } from "../Atoms/System";
import { TESTNET } from "../utils/constants";
import posthog from "posthog-js";

const Component: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const toggleOpen = () => setIsOpen((prev) => !prev);

  const setAccepted = useSetAtom(acceptedAtom);
  const setConsent = useSetAtom(consentAtom);
  const { removeModal } = useNotifications();
  const handleAccept = (nonEssential: boolean) => {
    removeModal("tcAccept");
    setAccepted(true);
    setConsent(nonEssential);
    if (typeof window !== "undefined" && window.dataLayer) {
      window.dataLayer.push({
        event: "update_consent",
        consent: {
          ad_storage: nonEssential ? "granted" : "denied",
          analytics_storage: nonEssential ? "granted" : "denied",
          functionality_storage: "granted",
          personalization_storage: nonEssential ? "granted" : "denied",
          security_storage: "granted",
        },
      });
    }
    if (nonEssential) {
      posthog.opt_in_capturing();
    }
  };

  return (
    <Box sx={{ textAlign: "left" }}>
      <Typography>
        {TESTNET ? (
          <>
            This application operates within a <strong>testnet environment</strong>, which has been created exclusively for testing and development purposes. By using the
            application, you acknowledge and agree to be bound by this disclaimer.
          </>
        ) : (
          <>
            This application operates on the <strong>mainnet</strong>, utilizing real digital assets and cryptocurrencies. By using this application, you acknowledge and agree to
            be bound by this disclaimer and accept the risks associated with decentralized finance (DeFi) and blockchain-based transactions.
          </>
        )}
      </Typography>

      {/* Faded Text with Read More */}
      <Box
        sx={{
          position: "relative",
          maxHeight: isOpen ? "none" : "3.6em", // Show only ~2 lines when collapsed
          overflow: "hidden",
          WebkitMaskImage: isOpen ? "none" : "linear-gradient(to bottom, rgba(0,0,0,1) 50%, rgba(0,0,0,0) 100%)",
          maskImage: isOpen ? "none" : "linear-gradient(to bottom, rgba(0,0,0,1) 50%, rgba(0,0,0,0) 100%)",
          transition: "max-height 0.3s ease",
        }}
      >
        {TESTNET ? <TestnetText /> : <MainnetText />}
      </Box>

      <Button variant="text" onClick={toggleOpen}>
        <Typography>Read {isOpen ? "Less" : "More"}</Typography>
      </Button>

      <Box textAlign="center" mt={2} display="flex" justifyContent="space-around" alignItems="center">
        <Button variant="contained" onClick={() => handleAccept(true)}>
          <Box>Accept All</Box>
        </Button>
        <Button variant="outlined" onClick={() => handleAccept(false)}>
          <Box>Accept Essential Only</Box>
        </Button>
      </Box>
    </Box>
  );
};

const TermsApproval = Component;
export default TermsApproval;

const MainnetText: React.FC = () => {
  return (
    <>
      <Typography fontWeight={"bold"} py={1}>
        Risk of Loss
      </Typography>
      <Typography>
        All transactions conducted through this application are real, irreversible, and final. Digital assets used within this application hold real-world value and can be lost
        permanently due to human error, smart contract vulnerabilities, network failures, or other unforeseen circumstances. You are solely responsible for managing your private
        keys, wallet security, and transaction decisions.
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        No Guarantees, No Warranties
      </Typography>
      <Typography>
        This application is provided "as is" and "as available", without any warranties or guarantees, whether express, implied, statutory, or otherwise. This includes but is not
        limited to warranties of merchantability, fitness for a particular purpose, non-infringement, accuracy, or reliability. The developers, maintainers, and contributors of
        this application do not assume liability for losses, damages, or disruptions caused by the use or misuse of this platform.
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        Security & Smart Contract Risks
      </Typography>
      <Typography>
        Despite rigorous security measures, smart contracts may contain vulnerabilities that could be exploited, resulting in the loss of funds. By interacting with this
        application, you acknowledge and accept that blockchain transactions carry inherent risks, including but not limited to:
      </Typography>
      <Typography>Contract exploits or bugs</Typography>
      <Typography> Impermanent loss or market volatility </Typography>
      <Typography> Malicious third-party attacks </Typography>
      <Typography> Regulatory changes affecting your jurisdiction </Typography>
      <Typography fontWeight={"bold"} py={1}>
        No Financial Advice
      </Typography>
      <Typography>
        This application does not provide financial, legal, or tax advice. You should conduct your own research, assess your risk tolerance, and consult a qualified professional
        before engaging in any transactions. Participation in decentralised finance involves significant risk, and you may lose part or all of your funds.
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        Regulatory & Compliance Responsibility
      </Typography>
      <Typography>
        You are solely responsible for ensuring that your use of this application complies with all relevant laws, regulations, and guidelines in your jurisdiction. The developers,
        maintainers, and associated parties disclaim any responsibility for legal or regulatory consequences arising from your use of this platform.
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        Updates & Changes
      </Typography>
      <Typography>
        This disclaimer and the terms of use for this application may be updated at any time without prior notice. By continuing to use the application, you agree to be bound by
        any such changes.
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        Limitation of Liability
      </Typography>
      <Typography>
        To the maximum extent permitted by law, in no event shall the developers, maintainers, contributors, or any affiliated parties be liable for any direct, indirect,
        incidental, consequential, or punitive damages arising from or related to your use of this application.
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        Legal Recourse Waiver
      </Typography>
      <Typography>
        By signing this, you expressly agree that you have no legal recourse against Beraborrow or its operating companies for any losses, damages, or liabilities incurred through
        your use of this application. You fully acknowledge and accept that all risks associated with this platform are solely your own responsibility. By accessing and using this
        application, you confirm that you fully understand and accept the risks involved and take full responsibility for your actions on this platform.
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        Cookies Policy
      </Typography>
      <Typography>
        This application uses cookies to enhance user experience, analyse platform performance, and provide essential functionalities. Cookies are small text files stored on your
        device that help us improve platform security and usability. By using this application, you consent to the use of cookies in accordance with this policy.
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        Types of Cookies Used
      </Typography>
      <Typography>
        <b>Essential Cookies:</b> Required for core functionalities such as authentication and security.{" "}
      </Typography>
      <Typography>
        <b> Analytics Cookies:</b> Help us understand user behaviour and improve platform performance.
      </Typography>
      <Typography>
        <b>Preference Cookies:</b> Store user settings and preferences for a more personalised experience.{" "}
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        Managing Cookies
      </Typography>
      <Typography>
        By clicking <b>Accept All</b> you agree to make use of all cookies including non-essential such as Analytics Cookie
      </Typography>
      <Typography>
        By clicking <b>Accept Essential Only</b> you agree to make use of essential Cookies only
      </Typography>

      <Typography fontWeight={"bold"} py={1}>
        Updates to Terms
      </Typography>
      <Typography>
        We reserve the right to modify terms from time to time. If we make any changes to these terms, we will change the "UPDATED" date below and will post the updated terms on
        this page.
        <Typography>
          If you have any questions or concerns regarding these Terms, please contact at{" "}
          <Link sx={{ color: "var(--text-primary)" }} href="mailto:<EMAIL>">
            <EMAIL>.
          </Link>
        </Typography>
      </Typography>
      <Typography fontWeight={"bold"} py={1}>
        UPDATED: 03 March 2025
      </Typography>
    </>
  );
};

const TestnetText: React.FC = () => {
  return (
    <>
      <Typography>
        All tokens and assets are simulated and have no real-world value. These tokens cannot be exchanged for real currency or any tangible assets, and all transactions and
        balances are exclusively for testing purposes.
      </Typography>
      <Typography>
        As this is experimental software, the application is provided on an "as is" and “as available” basis, and entirely at your own risk, and no (express or implied, statutory
        or otherwise) representations, guarantees or warranties regarding are given, including but not limited as to merchantability or satisfactory quality, performance, accuracy,
        reliability, or suitability/fitness for any particular purpose. To the extent permitted by law, any and all implied terms are excluded. The application is under active
        development, and it may contain bugs, errors, or other issues that could result in unexpected behavior.
      </Typography>
      <Typography>
        By using this application, you acknowledge and accept the risk of potential loss of data, tokens, or other assets within the testnet environment. The developers and
        maintainers of this application are not liable for any loss—whether direct, indirect, incidental, or consequential—that may arise from your use of the platform.
      </Typography>
      <Typography>
        This application does not provide financial advice and is not intended to facilitate real-world financial transactions. All activities conducted here are strictly for
        testing and educational purposes within the testnet environment.
      </Typography>
      <Typography>
        You are responsible for ensuring that your use of this application complies with all relevant laws, regulations, and guidelines in your jurisdiction. Your use of the
        application is entirely at your own risk. The developers, maintainers, and their respective directors, employees, contractors, service providers or agents, and other
        associated parties are not liable for any damages or losses incurred.
      </Typography>
      <Typography>
        Please note that this disclaimer, along with the application, may be updated at any time without prior notice. By continuing to use the application, you agree to be bound
        by any such changes.
      </Typography>
    </>
  );
};
