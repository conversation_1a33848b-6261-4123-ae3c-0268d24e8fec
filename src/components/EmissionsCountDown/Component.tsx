import { Typography, Box } from "@mui/material";
import WithSuspense from "../../providers/WithSuspense";
import StyledTooltip from "../StyledFeeTooltip";
import { useEmissionsCountDown } from "./store";
import { InfoOutlined } from "@mui/icons-material";

const Component: React.FC = () => {
  const { isPending, remainingSeconds } = useEmissionsCountDown();
  return (
    <Typography component={"span"} variant="subtitle2" color={"var(--text-secondary)"} mb={1.5} display={isPending ? "flex" : "none"} alignItems="center">
      <StyledTooltip
        title={
          <Box display={"flex"} flexDirection={"column"} gap={1}>
            Please wait for emissions to complete before withdrawing, to avoid slippage
          </Box>
        }
      >
        <InfoOutlined sx={{ fontSize: "inherit", lineHeight: "inherit", verticalAlign: "middle", mr: 0.5 }} />
      </StyledTooltip>
      Until Emissions Completed: {remainingSeconds} seconds
    </Typography>
  );
};
const EmissionsCountDown = WithSuspense(Component);
export default EmissionsCountDown;
