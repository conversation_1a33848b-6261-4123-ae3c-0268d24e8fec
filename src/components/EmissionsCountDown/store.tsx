import { atom, useAtom, useAtomValue } from "jotai";
import { getPendingGainTimestampAtom, getStabilityDepositAtom } from "../../Atoms/StabilityPool";
import { useEffect } from "react";
import { blockNumberAtom } from "../../Atoms/System";
const dateAtom = atom(new Date());

export const useEmissionsCountDown = () => {
  const { data: pendingGains, refetch } = useAtomValue(getPendingGainTimestampAtom); //refresh onBlock change
  const { refetch: refetchDeposit } = useAtomValue(getStabilityDepositAtom); //refresh onBlock change

  const blockNumber = useAtomValue(blockNumberAtom);
  const [now, setNow] = useAtom(dateAtom);
  const maxPendingTimestamp = Number(pendingGains.reduce((max, time) => (time > max ? time : max), BigInt(Math.floor(now.getTime() / 1000))));
  const remainingSeconds = Math.floor(Math.max(maxPendingTimestamp - now.getTime() / 1000, 0) < 30 ? Math.max(maxPendingTimestamp - now.getTime() / 1000, 0) : 0);
  const isPending = !!remainingSeconds;
  useEffect(() => {
    const timer = setInterval(() => {
      setNow(new Date());
    }, 1000);
    if (!isPending) {
      refetchDeposit();
      clearInterval(timer);
    }
    return () => {
      clearInterval(timer);
    };
  }, [isPending]);

  useEffect(() => {
    if (blockNumber) {
      refetch();
    }
  }, [blockNumber]);

  return { isPending, remainingSeconds };
};
