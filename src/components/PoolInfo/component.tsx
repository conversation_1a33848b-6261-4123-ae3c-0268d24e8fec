import React from "react";
import { usePoolDepositStore } from "./store";
import { Box, Grid, Typography, useTheme, Divider, useMediaQuery, Link } from "@mui/material";
import { formatBigIntPercentage, formatToken, formatTokenWithMillionsOrBillions } from "../../utils/helpers";
import { SCALING_FACTOR, SCALING_FACTOR_BP_DECIMALS, SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import ArrowOutwardRoundedIcon from "@mui/icons-material/ArrowOutwardRounded";
import WithSuspense from "../../providers/WithSuspense";
import { BERABORROW_ADDRESSES } from "../../utils/constants";

import TokenIcon from "../TokenIcons";

const Component: React.FC = () => {
  const { currentDeposit, apy, poolShare, poolTvl, myAssets, assets, shares, exitFee, entryFee, vaultApy } = usePoolDepositStore();
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));

  return (
    <Box pt={isMdUp ? "10px" : "8px"} px={2}>
      <Typography pt={"6px"} variant="h1">
        Current Position
      </Typography>
      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />
      <Box>
        <Box
          sx={{
            mb: 2,
            backgroundColor: "var(--primary-orange-20, rgba(236, 111, 21, 0.20))",
            backdropFilter: {
              xs: "none", // No blur on mobile
            },
            p: 1,
            px: 2,
            [theme.breakpoints.down("md")]: {
              paddingX: "8px",
            },
            borderRadius: 5,
          }}
        >
          <Link href="/vault/deposit/sNECT">
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                textDecoration: "none",
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              <Box
                sx={{
                  height: "25px",
                  width: "25px",
                  borderRadius: "50%",
                  overflow: "hidden",
                  mr: 1,
                }}
              >
                <img src={`/icons/snect.png`} alt="?" width="100%" height="100%" />
              </Box>
              <Typography variant="h6" fontWeight={500} sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                Auto-compound your sNECT to earn {formatBigIntPercentage(vaultApy)}% APY <ArrowOutwardRoundedIcon sx={{ fontSize: "inherit", verticalAlign: "middle" }} />
              </Typography>
            </Box>
          </Link>
        </Box>
      </Box>
      <Grid container pb={0.5} spacing={0.5}>
        {!!apy && (
          <Grid item xs={4}>
            <Box
              sx={{
                padding: "8px",
                "@media (min-width:600px)": {
                  padding: "16px",
                },
                borderRadius: "12px",
                border: `1px solid ${theme.palette.background.default}`,
                backgroundColor: theme.palette.background.paper,
              }}
            >
              <Typography variant="subtitle1">s{BERABORROW_ADDRESSES.debtToken.ticker} APY</Typography>

              <Typography variant="h3" color={"var(--text-success)"}>
                {formatBigIntPercentage(apy)} %
              </Typography>
            </Box>
          </Grid>
        )}
        <Grid item xs={apy ? 4 : 6}>
          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <Typography variant="subtitle1">Pool TVL</Typography>
            <Typography variant="h3" color={"var(--text-primary)"}>
              $ {formatTokenWithMillionsOrBillions(poolTvl, SCALING_FACTOR_DECIMALS, 2)}
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={apy ? 4 : 6}>
          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <Typography variant="subtitle1">Pool Share</Typography>

            <Typography variant="h3" color={"var(--text-primary)"}>
              {formatBigIntPercentage(poolShare, 2, undefined, undefined, undefined, 4)} %
            </Typography>
          </Box>
        </Grid>
      </Grid>

      <Grid container pb={1} spacing={0.5}>
        <Grid item xs={6}>
          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            {" "}
            <Typography variant="body2" fontSize={12} display="flex" alignItems="center">
              <Typography variant="body2" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center" pr={0.5}>
                Deposit Fee:
              </Typography>
              {formatBigIntPercentage(entryFee, 2, undefined, undefined, SCALING_FACTOR_BP_DECIMALS) + "%"}
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={6}>
          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <Typography variant="body2" fontSize={12} display="flex" alignItems="center">
              <Typography variant="body2" color={"var(--text-secondary)"} component="span" fontSize={12} display="flex" alignItems="center" pr={0.5}>
                Withdraw Fee:{" "}
              </Typography>
              {formatBigIntPercentage(exitFee, 2, undefined, undefined, SCALING_FACTOR_BP_DECIMALS) + "%"}
            </Typography>
          </Box>
        </Grid>
      </Grid>

      <Typography variant="subtitle1">My Composition</Typography>
      <Box
        sx={{
          padding: "8px",
          "@media (min-width:600px)": {
            padding: "16px",
          },
          borderRadius: "12px",
          border: `1px solid ${theme.palette.background.default}`,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        {assets.map((item) => {
          return (
            <Box key={item.contractAddress}>
              {!!item.balance && (
                <Box key={item.contractAddress} display="flex" justifyContent="space-between" alignItems="center" pb={1}>
                  <Box>
                    <Typography variant="h5" component="span" display="flex" alignItems="center" fontWeight={600}>
                      <TokenIcon contractAddress={item.contractAddress} height={"25px"} width={"25px"} />{" "}
                      <Box pl={1}>{formatToken(currentDeposit.getMyShare(item.balance, shares), item.decimals, 2, item.ticker)}</Box>
                    </Typography>
                  </Box>
                  <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
                    ${formatToken((currentDeposit.getMyShare(item.balance, shares) * item.price) / SCALING_FACTOR, item.decimals, 2)}
                  </Typography>
                </Box>
              )}
            </Box>
          );
        })}
        <Divider sx={{ my: 1, backgroundColor: `var(--border-color)` }} />
        <Box key={"Total"} display="flex" justifyContent="space-between" alignItems="center" pb={1}>
          <Box></Box>
          <Typography variant="h5" fontWeight={600} color="var(--text-primary)">
            ${formatToken(myAssets, SCALING_FACTOR_DECIMALS, 2)}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

const PoolInfo = WithSuspense(Component, "paper");
export default PoolInfo;
