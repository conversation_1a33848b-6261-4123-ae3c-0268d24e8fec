import { useAtomValue, useSetAtom } from "jotai";
import { useHydrateAtoms } from "jotai/utils";
import { getStabilityDepositAtom, getPoolAPYAtom, convertSharesToAssetsAtom } from "../../Atoms/StabilityPool";
import { SCALING_FACTOR } from "@Beraborrowofficial/sdk";
import { BERABORROW_ADDRESSES, PRODUCTION } from "../../utils/constants";
import { accountAtom } from "../../Atoms/Account";
import { Hex, zeroAddress } from "viem";
import { vaultAtom, getVaultApyAtom } from "../../Atoms/Vault";
import { useEffect } from "react";
import { collateralTypeSelectorAtom } from "../../Atoms/Tokens";
import { useLocation } from "react-router-dom";

export const usePoolDepositStore = () => {
  useHydrateAtoms([
    [vaultAtom, Object.entries(BERABORROW_ADDRESSES.vaults).find((item) => item[1].ticker === "BB.SNECT")?.[0] as Hex | undefined],
    [collateralTypeSelectorAtom, "vault"],
  ]);
  const location = useLocation();
  const setCollateralTypeSelector = useSetAtom(collateralTypeSelectorAtom);
  const setVaultAddr = useSetAtom(vaultAtom);
  const account = useAtomValue(accountAtom);
  const { data: vaultApy } = useAtomValue(getVaultApyAtom);
  const { data: currentDeposit } = useAtomValue(getStabilityDepositAtom);
  const { data: apy } = useAtomValue(getPoolAPYAtom);
  const { data: shareToAssetRatio } = useAtomValue(convertSharesToAssetsAtom);
  const { exitFee, entryFee } = currentDeposit;
  const poolShare = currentDeposit.sharesTotalSupply === 0n ? 0n : (currentDeposit.shares * SCALING_FACTOR) / currentDeposit.sharesTotalSupply;
  const poolTvl = currentDeposit.convertSharesToAssets(currentDeposit.sharesTotalSupply, shareToAssetRatio);
  const myAssets = currentDeposit.convertSharesToAssets(currentDeposit.shares, shareToAssetRatio);
  const shares = !PRODUCTION && (!account || account === zeroAddress) ? currentDeposit.sharesTotalSupply : currentDeposit.shares;
  const assets = currentDeposit
    .getUnderlyingAssets()
    .map((item) => {
      const collateral =
        BERABORROW_ADDRESSES.collateralTokens[item.contractAddress] ??
        (item.contractAddress === BERABORROW_ADDRESSES.debtToken.contractAddress
          ? BERABORROW_ADDRESSES.debtToken
          : item.contractAddress === BERABORROW_ADDRESSES.pollenToken.contractAddress
            ? BERABORROW_ADDRESSES.pollenToken
            : undefined);
      return { ...item, ...collateral };
    })
    .filter((item) => item); //incase of undefined

  useEffect(() => {
    return () => {
      if (!location.pathname.startsWith("/pool")) {
        setVaultAddr(undefined);
        setCollateralTypeSelector("den");
      }
    };
  }, [location.pathname]);

  return {
    entryFee,
    exitFee,
    vaultApy,
    currentDeposit,
    assets,
    apy,
    poolShare,
    poolTvl,
    myAssets,
    shares,
  };
};
