import { useAtomValue, useAtom } from "jotai";
import { getCollateralDetailsAtom, getDebtBalanceAtom } from "../../Atoms/Tokens";
import { getDenFullDetailsAtom, denManagerAtom, getDenManagerCollateralPrice } from "../../Atoms/Den";

import { useNotifications } from "../../Hooks/useNotifications";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import { formatDecimal, formatBigIntPercentage } from "../../utils/helpers";
import { useEffect, useState } from "react";
import { postHogEventAtom, beraborrowConstantsAtom } from "../../Atoms/Account";

export const useClosePositionStore = () => {
  const { data: denDetails } = useAtomValue(getDenFullDetailsAtom);
  const { data: debtBalance } = useAtomValue(getDebtBalanceAtom);
  const { data: collateralPrice } = useAtomValue(getDenManagerCollateralPrice);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const denManagerAddr = useAtomValue(denManagerAtom);
  const [postHogEvent, setPostHogEvent] = useAtom(postHogEventAtom);
  const [postHogEventState, _setPostHogEventState] = useState(postHogEvent);
  const { removeModal } = useNotifications();
  const liquidationReserve = useAtomValue(beraborrowConstantsAtom).liquidationReserve;
  const { den: userDen } = denDetails;

  useEffect(() => {
    setPostHogEvent({
      type: "denClose",
      denManager: denManagerAddr,
      token_borrowed: BERABORROW_ADDRESSES.debtToken.ticker,
      qty_borrowed_token: formatDecimal(userDen.debt, BERABORROW_ADDRESSES.debtToken.decimals),
      collateral_token: collateralDetails.ticker,
      qty_collateral_token: formatDecimal(userDen.collateral, collateralDetails.vaultDecimals),
      ratio: formatBigIntPercentage(userDen.collateralRatio(collateralPrice)),
    });
    return () => {
      setPostHogEvent(postHogEventState);
    };
  }, []);
  const dispatcher = (type: "close" | "dismiss") => {
    switch (type) {
      case "close":
        break;
      case "dismiss":
        removeModal("close-denBorrow");
        break;

      default:
        break;
    }
  };
  const collateralTicker = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.denManagers[denManagerAddr].collateral].ticker;
  const wrappedCollateralTicker = BERABORROW_ADDRESSES.collateralTokens[BERABORROW_ADDRESSES.denManagers[BERABORROW_ADDRESSES.wrappedDenManagers[denManagerAddr]]?.collateral]
    ?.ticker as string | undefined;

  return {
    collateral: {
      ...collateralDetails,
      ticker: collateralTicker ?? collateralDetails.ticker,
      wrappedCollateralTicker: wrappedCollateralTicker,
    },
    dispatcher,
    userDen,
    debtBalance,
    liquidationReserve,
  };
};
export const getClosePositionScopedAtoms = () => [];
