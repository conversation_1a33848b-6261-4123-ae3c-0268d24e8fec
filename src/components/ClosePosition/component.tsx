import { Box, Grid, Typography } from "@mui/material";
import { formatToken } from "../../utils/helpers";
import { useClosePositionStore } from "./store";
import TransactionButton from "../Transaction/Transaction";
import { BERABORROW_ADDRESSES } from "../../utils/constants";

const ClosePosition: React.FC = () => {
  const { collateral, userDen, debtBalance, dispatcher, liquidationReserve } = useClosePositionStore();
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
      <Box sx={{ background: "var(--background-accent)" }} p={2} display={"flex"} gap={2} flexDirection={"column"} borderRadius={6}>
        <Box display="flex" justifyContent="space-between">
          <Typography color={"var(--text-secondary)"} fontSize={14}>
            Amount to Repay
          </Typography>
          <Typography>
            <strong>{formatToken(userDen.netDebt(liquidationReserve), BERABORROW_ADDRESSES.debtToken.decimals, undefined, BERABORROW_ADDRESSES.debtToken.ticker)} </strong>
          </Typography>
        </Box>
        <Box display="flex" justifyContent="space-between">
          <Typography color={"var(--text-secondary)"} fontSize={14}>
            Amount to Receive
          </Typography>
          <Typography>
            <strong>{formatToken(userDen.collateral, collateral.vaultDecimals, 8, collateral.wrappedCollateralTicker ?? collateral.ticker)} </strong>
          </Typography>
        </Box>
        {collateral.wrappedCollateralTicker && (
          <Box display="flex" justifyContent="center">
            <Typography color={"var(--text-secondary)"} fontSize={14}>
              Please Note you will receive {collateral.wrappedCollateralTicker}
            </Typography>
          </Box>
        )}
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TransactionButton
            validationError={
              debtBalance < userDen.netDebt(liquidationReserve)
                ? {
                    type: "insuffiecent_nect",
                    shortMessage:
                      formatToken(userDen.netDebt(liquidationReserve) - debtBalance, BERABORROW_ADDRESSES.debtToken.decimals, 5, BERABORROW_ADDRESSES.debtToken.ticker) +
                      " additionally needed to close",
                    description: "Mint more NECT via Manage Den or purchase more",
                    link: "https://app.oogabooga.io/?fromToken=HONEY&toToken=NECT",
                  }
                : undefined
            }
            disabled={debtBalance < userDen.netDebt(liquidationReserve)}
            transaction={{ type: "closeDen", variables: [] }}
            onConfirmation={() => {
              dispatcher("dismiss");
            }}
          >
            <Typography fontSize={20} p={2}>
              Close Position
            </Typography>
          </TransactionButton>
        </Grid>
      </Grid>
    </Box>
  );
};
export default ClosePosition;
