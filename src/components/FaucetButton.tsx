import { <PERSON><PERSON>, <PERSON> } from "@mui/material";
import { TESTNET } from "../utils/constants";

export const FaucetButton = () => {
  if (!TESTNET) {
    return <></>;
  }
  return (
    <>
      {TESTNET && (
        <Link href="https://bartio.faucet.berachain.com/" target="_blank">
          <Button
            sx={{
              border: "1px solid var(--border-color)",
              borderRadius: 5,
              background: "var(--background-default)",
              px: { xs: "8px", sm: 2 },
              py: { xs: "8px", sm: 2 },
              minWidth: 0,
            }}
          >
            <img src="/imgs/faucet.png" width={24} height={24} />
          </Button>
        </Link>
      )}
    </>
  );
};
