import { Switch, Typography, Box, SwitchProps, styled, Stack } from "@mui/material";
import { useAtom } from "jotai";
import { simpleModeAtom } from "../Atoms/Account";

const CustomSwitch = styled((props: SwitchProps) => <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />)(({ theme }) => ({
  width: 42,
  height: 26,
  padding: 0,

  "& .MuiSwitch-switchBase": {
    padding: 0,
    margin: 2,
    transitionDuration: "300ms",
    "&.Mui-checked": {
      transform: "translateX(16px)",
      color: "#fff",
      "& + .MuiSwitch-track": {
        backgroundColor: "#1E1916",
        opacity: 1,
        border: 0,
      },
      "&.Mui-disabled + .MuiSwitch-track": {
        opacity: 0.5,
      },
    },

    "&.Mui-disabled + .MuiSwitch-track": {
      opacity: theme.palette.mode === "light" ? 0.7 : 0.3,
    },
  },
  "& .<PERSON>iSwitch-thumb": {
    boxSizing: "border-box",
    width: 22,
    height: 22,
  },
  "& .MuiSwitch-track": {
    borderRadius: 26 / 2,
    backgroundColor: "#1E1916",
    opacity: 1,
    transition: theme.transitions.create(["background-color"], {
      duration: 500,
    }),
  },
}));

function ModeSwitch() {
  const [simpleMode, setSimpleMode] = useAtom(simpleModeAtom);
  const handleToggle = () => {
    setSimpleMode(!simpleMode);
  };

  return (
    <Box display="flex" alignItems="center">
      <Stack
        direction="row"
        spacing={1}
        alignItems="center"
        sx={{
          marginLeft: 1,
          background: "var(--background-default)",
          borderRadius: 18,
          padding: "8px 16px",
          border: "1px solid var(--border-color)",
          backdropFilter: { xs: "none", lg: "blur(5px)" },
        }}
      >
        <Typography sx={{ fontSize: 14 }}>Simple</Typography>
        <CustomSwitch
          checked={!simpleMode}
          onChange={handleToggle}
          name="modeSwitch"
          color="default"
          inputProps={{ "aria-label": "Mode Switch" }}
          style={{ color: !simpleMode ? "#EC6F15" : "#EC6F15", opacity: 1 }}
        />
        <Typography sx={{ fontSize: 14 }}>Den</Typography>
      </Stack>
    </Box>
  );
}

export default ModeSwitch;
