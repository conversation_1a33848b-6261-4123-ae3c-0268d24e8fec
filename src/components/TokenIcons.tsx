import { Box } from "@mui/material";
import React from "react";
import { Hex } from "viem";
import { BERABORROW_ADDRESSES } from "../utils/constants";
import { Token } from "@Beraborrowofficial/sdk";

const getIconFileName = (ticker: string, staked?: boolean): string => {
  const normalizedTicker = ticker.toLowerCase().replace(/^bb/, "");

  if (normalizedTicker === "mpollen" || normalizedTicker === "pollen") {
    return staked ? "spollen.png" : "pollen.png";
  }

  if (normalizedTicker === "spollen") {
    return "spollen.png";
  }

  if (normalizedTicker === "vepollen") {
    return "vepollen.png";
  }

  return `${staked ? "s" : ""}${normalizedTicker}.png`;
};

type BeraBorrowLogoProps = React.ComponentProps<typeof Box> & {
  height?: number | string;
  showVaultedAsset?: boolean;
  vaulted?: boolean;
  vaultIcon?: string;
  vaultSize?: string;
  contractAddress: Hex;
  staked?: boolean;
};

const TokenIcon: React.FC<BeraBorrowLogoProps> = ({
  height = "40px",
  width,
  contractAddress,
  showVaultedAsset,
  vaulted,
  staked,
  vaultSize = "60%",
  vaultIcon = "vaulted.png",
  ...boxProps
}) => {
  const vaultedAsset = BERABORROW_ADDRESSES.vaults[contractAddress];
  const addresses = BERABORROW_ADDRESSES as typeof BERABORROW_ADDRESSES & { sPollenToken: { contractAddress: `0x${string}`; ticker: string; decimals: number } };

  const collateral: Omit<Token, "contractAddress"> =
    (showVaultedAsset ? vaultedAsset : BERABORROW_ADDRESSES.collateralTokens[vaultedAsset?.collateral]) ??
    BERABORROW_ADDRESSES.collateralTokens[contractAddress] ??
    (contractAddress === BERABORROW_ADDRESSES.debtToken.contractAddress
      ? BERABORROW_ADDRESSES.debtToken
      : contractAddress === BERABORROW_ADDRESSES.pollenToken.contractAddress
        ? BERABORROW_ADDRESSES.pollenToken
        : contractAddress === addresses.sPollenToken.contractAddress
          ? addresses.sPollenToken
          : contractAddress === BERABORROW_ADDRESSES.sPollenVotingEscrow
            ? { ticker: "vePOLLEN", decimals: 18 }
            : contractAddress === BERABORROW_ADDRESSES.stabilityPool
              ? { ...BERABORROW_ADDRESSES.debtToken, ticker: "s" + BERABORROW_ADDRESSES.debtToken.ticker }
              : { ticker: "beraborrow", decimals: 18 });

  let token1: Omit<Token, "contractAddress"> | undefined,
    token2: Omit<Token, "contractAddress"> | undefined,
    token3: Omit<Token, "contractAddress"> | undefined,
    lpProvider: string | undefined;
  const { lpToken } = collateral || {};
  if (lpToken?.token1 && lpToken?.token2 && lpToken?.provider) {
    //@ts-expect-error token3 is new
    const { token1: t1, token2: t2, token3: t3, provider } = lpToken;
    token1 = BERABORROW_ADDRESSES.collateralTokens[t1];
    token2 = BERABORROW_ADDRESSES.collateralTokens[t2];
    token3 = BERABORROW_ADDRESSES.collateralTokens[t3];
    lpProvider = provider;
  }
  return (
    <Box sx={{ lineHeight: 0, display: "flex", alignItems: "center", marginY: "3px" }} maxWidth={"100%"} {...boxProps}>
      <Box
        sx={{
          height: height,
          width: width ?? height,
          borderRadius: "50%",
          position: "relative",
          display: "flex", // Add flex to align images side by side
          justifyContent: "space-between", // Ensure images are spaced correctly
          alignItems: "center", // Align vertically
        }}
      >
        {!!lpProvider && (
          <Box
            width={vaultSize}
            height={vaultSize}
            sx={{
              zIndex: 20,
              position: "absolute",
              left: "0%",
              top: "0px",
              transform: `translate(-40%, -40%)`,
            }}
          >
            <img src={`/icons/${lpProvider.toLowerCase()}.${lpProvider === "Kodiak" ? "png" : "svg"}`} height="100%" style={{ borderRadius: "50%" }} />
          </Box>
        )}
        {(!!vaultedAsset || vaulted) && (
          <Box
            width={vaultIcon === "vaulted.png" ? vaultSize : "30%"}
            height={vaultIcon === "vaulted.png" ? vaultSize : "30%"}
            sx={{
              zIndex: 20,
              position: "absolute",
              right: "-1%",
              top: "0px",
              transform: `translate(${vaultIcon === "vaulted.png" ? "40%" : "-40%"}, -40%)`,
            }}
          >
            <img src={"/icons/" + vaultIcon} height="100%" style={{ borderRadius: vaultIcon === "vaulted.png" ? "50%" : undefined }} />
          </Box>
        )}
        {token1 && token2 ? (
          <Box component={"span"} sx={{ display: "flex" }}>
            <img src={`/icons/${getIconFileName(token1.ticker)}`} alt="?" height={"100%"} width={token3 ? "60%" : "66.66%"} />
            <img
              src={`/icons/${getIconFileName(token2.ticker)}`}
              alt="?"
              height={"100%"}
              width={token3 ? "60%" : "66.66%"}
              style={{ marginLeft: token3 ? "-30%" : "-33%" }} // Adjust to overlap slightly
            />
            {!!token3 && (
              <img
                src={`/icons/${getIconFileName(token3.ticker)}`}
                alt="?"
                height={"100%"}
                width={token3 ? "60%" : "66.66%"}
                style={{ marginLeft: token3 ? "-30%" : "-33%" }} // Adjust to overlap slightly
              />
            )}
          </Box>
        ) : (
          <img src={`/icons/${getIconFileName(collateral.ticker, staked)}`} alt="?" height={"100%"} width={"100%"} />
        )}
      </Box>
    </Box>
  );
};
export default TokenIcon;
