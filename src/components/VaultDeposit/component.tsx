import React from "react";
import { useVaultDepositStore } from "./store";
import { Box, Divider, Grid, Typography, useTheme } from "@mui/material";
import { formatToken } from "../../utils/helpers";
import TransactionButton from "../Transaction/Transaction";
import WithSuspense from "../../providers/WithSuspense";
import StyledTextfield from "../StyledTextField";
import { AmountSelector } from "../AmountSelector";
import VaultComposition from "../VaultComposition/Component";
import { ValidationButton } from "../ValidationButton";
import { BERABORROW_ADDRESSES } from "../../utils/constants";
import TokenIcon from "../TokenIcons";

const Component: React.FC = () => {
  const theme = useTheme();
  const { collateralBalance, validationError, formValue, dispatcher, depositAssets, depositShares, collateralDetails, vaultAddr, needApproval, currentDeposit, collPrice } =
    useVaultDepositStore();
  return (
    <>
      <Grid container pb={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1" mb={0.5}>
            Deposit {collateralDetails.ticker.toUpperCase()}
          </Typography>
          <StyledTextfield
            value={formValue}
            error={validationError?.type === "insufficient_balance"}
            onChange={(e) => dispatcher({ type: "setVaultDeposit", payload: e.target.value })}
            startIcons={<TokenIcon contractAddress={BERABORROW_ADDRESSES.vaults[vaultAddr].collateral} height="35px" width="35px" />}
            endComponent={
              <Box display="flex" position="absolute" right="8px" bottom="8px">
                <AmountSelector handleItemClick={(percent) => dispatcher({ type: "setVaultDepositPercentage", payload: percent })} mt={1} ml={"auto"} />
              </Box>
            }
            startComponent={
              <Box display="flex" flexDirection="row" alignItems="center">
                <Typography variant="caption" fontSize={12}>
                  <Typography variant="caption" color={"var(--text-secondary)"} component="span" fontSize={12}>
                    Balance:
                  </Typography>{" "}
                  {formatToken(collateralBalance, collateralDetails.decimals, 2)}
                </Typography>
              </Box>
            }
          />
        </Grid>
      </Grid>
      {!!currentDeposit.totalSupply && (
        <>
          <Typography variant="subtitle1" mb={0.5}>
            Potential Composition{" "}
          </Typography>

          <Box
            sx={{
              padding: "8px",
              "@media (min-width:600px)": {
                padding: "16px",
              },
              borderRadius: "12px",
              border: `1px solid ${theme.palette.background.default}`,
              backgroundColor: theme.palette.background.paper,
            }}
          >
            <VaultComposition denManagerAddress={vaultAddr} vaultAddress={vaultAddr} shares={depositShares} collPrice={collPrice} removeWithdrawFee={currentDeposit.withdrawFee} />
          </Box>
        </>
      )}

      <Divider sx={{ my: 2, backgroundColor: `var(--border-color)` }} />
      <Box display={needApproval ? "block" : "none"}>
        <ValidationButton validationError={validationError} fullWidth variant="contained" onClick={() => dispatcher({ type: "approveVault" })} disabled={!!validationError}>
          Approve and Deposit
        </ValidationButton>
      </Box>
      <Box display={needApproval ? "none" : "block"}>
        <TransactionButton
          disabled={!!validationError || !collateralBalance}
          transaction={{ type: "depositVault", variables: [depositAssets] }}
          onConfirmation={() => dispatcher({ type: "confirmedVaultDeposit" })}
          validationError={validationError}
        >
          Deposit
        </TransactionButton>
      </Box>
    </>
  );
};

const VaultDeposit = WithSuspense(Component, "paper");
export default VaultDeposit;
