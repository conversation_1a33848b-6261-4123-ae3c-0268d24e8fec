import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { getCollateralBalanceAtom, getCollateralDetailsAtom } from "../../Atoms/Tokens";
import { formatBigIntPercentage, formatDecimal, regexDecimalToken } from "../../utils/helpers";
import { useHydrateAtoms } from "jotai/utils";
import { SCALING_FACTOR, SCALING_FACTOR_DECIMALS } from "@Beraborrowofficial/sdk";
import { useLocation } from "react-router-dom";
import { postHogEventAtom, updatePostHogEventAtom, resetPostHogEventAtom } from "../../Atoms/Account";
import { VaultDepositDispatcherActionType } from "./types";
import {
  getVaultDepositAtom,
  getVaultApyAtom,
  vaultDetailsAtom,
  vaultDepositAmountAtom,
  formValuesVaultDepositAtom,
  _vaultAddr<PERSON>tom,
  formValidationsVaultDeposit<PERSON>tom,
  getCollateralVaultAllowance<PERSON>tom,
  getInfraredVaultPriceAtom,
  getInfraredTokenPriceAtom,
} from "../../Atoms/Vault";
import { useNotifications } from "../../Hooks/useNotifications";
import ApproveVault from "../Approvals/ApproveVault/component";
import { useEffect } from "react";
import { QUERY_CLIENT } from "../../utils/constants";
export const useVaultDepositStore = () => {
  const location = useLocation();
  const { data: collateralBalance } = useAtomValue(getCollateralBalanceAtom);
  const { data: currentDeposit } = useAtomValue(getVaultDepositAtom);
  const { data: _price } = useAtomValue(getInfraredVaultPriceAtom);
  const { data: collPrice } = useAtomValue(getInfraredTokenPriceAtom);
  const price = currentDeposit.price || _price;
  const { data: apy } = useAtomValue(getVaultApyAtom);
  const { data: allowance } = useAtomValue(getCollateralVaultAllowanceAtom);
  const vaultDetails = useAtomValue(vaultDetailsAtom);
  const collateralDetails = useAtomValue(getCollateralDetailsAtom);
  const vaultAddr = useAtomValue(_vaultAddrAtom);

  const { addModal, isModalPresent } = useNotifications();
  const updatePostHogEvent = useSetAtom(updatePostHogEventAtom);
  const resetPostHogEvent = useSetAtom(resetPostHogEventAtom);
  const depositAmount = useAtomValue(vaultDepositAmountAtom);
  const [formValue, setFormValue] = useAtom(formValuesVaultDepositAtom);
  const validationError = useAtomValue(formValidationsVaultDepositAtom);
  const vaultTvl = (currentDeposit.totalSupply * price) / SCALING_FACTOR;
  const depositShares = (depositAmount * currentDeposit.collateralToShareRatio) / SCALING_FACTOR;
  const depositAssets = (depositShares * currentDeposit.shareToAssetRatio) / SCALING_FACTOR;
  useHydrateAtoms([
    [formValuesVaultDepositAtom, formatDecimal(collateralBalance, vaultDetails.decimals, 8)],
    [
      postHogEventAtom,
      {
        type: "vaultDeposit",
        page_rel_path: location.pathname,
        token_deposited: vaultDetails.ticker,
        qty_deposited_token: formatDecimal(collateralBalance, vaultDetails.decimals),
        current_apy: formatBigIntPercentage(apy),
        current_shares: formatDecimal(currentDeposit.shares, vaultDetails.decimals),
        current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
      },
    ],
  ]);

  useEffect(() => {
    setFormValue(collateralBalance ? formatDecimal(collateralBalance, vaultDetails.decimals, 8) : "0");
  }, [location.pathname]);
  const dispatcher = (action: VaultDepositDispatcherActionType) => {
    switch (action.type) {
      case "setVaultDeposit":
        updatePostHogEvent({
          token_deposited: vaultDetails.ticker,
          qty_deposited_token: regexDecimalToken(action.payload, vaultDetails.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, vaultDetails.decimals),
          current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
        });
        setFormValue(regexDecimalToken(action.payload, vaultDetails.decimals));

        break;
      case "confirmedVaultDeposit":
        if (!isModalPresent("ApproveVault")) {
          resetPostHogEvent({ type: "vaultDeposit", page_rel_path: location.pathname });
          QUERY_CLIENT.invalidateQueries().then(() => {
            setFormValue("0");
          });
        }

        break;
      case "setVaultDepositPercentage":
        const value = formatDecimal((collateralBalance * BigInt(action.payload)) / 100n, vaultDetails.decimals, action.payload === 100 ? undefined : 8, true);
        updatePostHogEvent({
          last_clicked_input_btn: action.payload + "%",
          token_deposited: vaultDetails.ticker,
          qty_deposited_token: regexDecimalToken(value, vaultDetails.decimals),
          current_apy: formatBigIntPercentage(apy),
          current_shares: formatDecimal(currentDeposit.shares, vaultDetails.decimals),
          current_vault_tvl: formatDecimal(vaultTvl, SCALING_FACTOR_DECIMALS),
        });

        setFormValue(regexDecimalToken(value, vaultDetails.decimals));
        break;
      case "approveVault":
        updatePostHogEvent({ type: `approveVault` });
        addModal({
          id: "ApproveVault",
          title: "Approve " + collateralDetails.ticker + " Deposit",
          Component: (
            <ApproveVault
              onConfirmation={() => {
                dispatcher({ type: "confirmedVaultDeposit" });
              }}
            />
          ),
          maxWidth: "800px",
        });
        break;
      default:
        break;
    }
  };
  return {
    collateralBalance,
    currentDeposit,
    validationError,
    formValue,
    depositAmount,
    dispatcher,
    depositAssets,
    depositShares,
    vaultDetails,
    collateralDetails,
    vaultAddr,
    price: price,
    needApproval: allowance < depositAmount,
    collPrice,
  };
};

export const getVaultDepositScopedAtoms = () => [formValidationsVaultDepositAtom, formValuesVaultDepositAtom, vaultDepositAmountAtom];
