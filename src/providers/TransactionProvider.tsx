import React, { createContext, useContext } from "react";
import { transactionStateListenerAtom } from "../Atoms/Transaction";
import { useAtom } from "jotai";
import { useWriteContract } from "wagmi";

// Define the shape of the context value
interface TransactionContextType {
  writeContract: ReturnType<typeof useWriteContract>["writeContract"];
  data: ReturnType<typeof useWriteContract>["data"];
  error: ReturnType<typeof useWriteContract>["error"];
  status: ReturnType<typeof useWriteContract>["status"];
  reset: ReturnType<typeof useWriteContract>["reset"];
}

// Create the context
const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

// Provider component
export const TransactionProvider = ({ children }: { children: React.ReactNode }) => {
  const { writeContract, data, error, status, reset } = useWriteContract();
  useAtom(transactionStateListenerAtom); // Listen to atom changes

  return <TransactionContext.Provider value={{ writeContract, data, error, status, reset }}>{children}</TransactionContext.Provider>;
};
export const useWriteContext = () => {
  const context = useContext(TransactionContext);

  if (!context) {
    throw new Error("useTransactionContext must be used within a TransactionProvider");
  }

  return context;
};
