import { useAtomValue } from "jotai";
import { modalsAtom, notificationEvent<PERSON>tom, toasts<PERSON>tom } from "../Atoms/Notifications";
import { useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/system";
import { truncateAddress } from "../utils/helpers";
import { ToastProps, NotificationTypes } from "../@type/Notifications";

export const useNotificationsProvider = () => {
  const modals = useAtomValue(modalsAtom);
  const toasts = useAtomValue(toastsAtom);
  const notification = useAtomValue(notificationEventAtom);
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));
  return { notification: notificationEventHandler(notification, isMdUp), modals, toasts };
};

const notificationEventHandler = (notification: NotificationTypes | undefined, isMdUp: boolean): ToastProps | undefined => {
  switch (notification?.type) {
    case "confirmed":
      return { id: "notification", message: "Confirmed: " + (isMdUp ? notification.payload : truncateAddress(notification.payload)), severity: "success" };
    case "broadcast":
      return { id: "notification", message: "Broadcast: " + (isMdUp ? notification.payload : truncateAddress(notification.payload)) };
    case "error":
      return { id: "notification", message: notification.payload, severity: "error" };
    default:
      return undefined;
  }
};
