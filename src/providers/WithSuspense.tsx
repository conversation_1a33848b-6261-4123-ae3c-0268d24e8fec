import { FC, Suspense } from "react";
import AppLoader from "../components/Loaders/AppLoader";
import PaperLoader from "../components/Loaders/PaperLoader";

const WithSuspense = <P extends object>(Component: FC<P>, loaderType?: "app" | "paper") => {
  const WrappedComponent: FC<P> = (props) => {
    const loader = loaderType === "app" ? <AppLoader /> : loaderType === "paper" ? <PaperLoader /> : undefined;
    return (
      <Suspense fallback={loader}>
        <Component {...props} />
      </Suspense>
    );
  };

  return WrappedComponent;
};

export default WithSuspense;
