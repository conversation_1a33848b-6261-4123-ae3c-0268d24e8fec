import React from "react";

import Modal from "../components/Modal/Modal";
import Toast from "../components/Toast/Toast";
import { useNotificationsProvider } from "./NotificationStore";

export const NotificationProvider = ({ children }: { children: React.ReactNode }) => {
  const { notification, modals, toasts } = useNotificationsProvider();
  return (
    <>
      <>
        {modals.map((modal) => (
          <Modal key={modal.id} {...modal} />
        ))}
      </>
      <>
        {toasts.map((toast) => (
          <Toast key={toast.id} {...toast} />
        ))}
        {notification && <Toast key={notification.id} {...notification} />}
      </>
      {children}
    </>
  );
};
