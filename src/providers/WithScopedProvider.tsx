import { FC } from "react";
import { Atom, Provider } from "jotai";
import { ScopeProvider } from "jotai-scope";

const withScopedProvider = <P extends object>(Component: FC<P>, atoms?: Atom<unknown>[]) => {
  const WrappedComponent: FC<P> = (props) => {
    return (
      <>
        {atoms?.length ? (
          <ScopeProvider atoms={atoms}>
            <Component {...(props as P)} />
          </ScopeProvider>
        ) : atoms === undefined ? (
          <Provider>
            <Component {...(props as P)} />
          </Provider>
        ) : (
          <Component {...(props as P)} />
        )}
      </>
    );
  };

  return WrappedComponent;
};

export default withScopedProvider;
