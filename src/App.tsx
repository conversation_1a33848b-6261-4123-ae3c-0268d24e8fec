import theme from "./theme/theme";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ThemeProvider } from "@mui/material";
import { WagmiProvider } from "wagmi";
import { QueryClientProvider } from "@tanstack/react-query";
import { PRODUCTION, QUERY_CLIENT, SETUP_APPKIT, WAGMI_ADAPTOR, GTM_ID } from "./utils/constants";
import BeraBorrowAccount from "./components/BeraBorrowAccount/BeraBorrowAccount";
import { Routes } from "./Routes/routes";
import { Provider } from "jotai";
import AppLoader from "./components/Loaders/AppLoader";
import { Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";
import AppError from "./components/AppError";
import TagManager from "react-gtm-module";
import { PostHogProvider } from "posthog-js/react";
import posthog from "posthog-js";
import { init, reactRouterV6BrowserTracingIntegration } from "@sentry/react";
import { createRoutes<PERSON>rom<PERSON>hildren, matchRoutes, useLocation, useNavigationType } from "react-router-dom";
import React from "react";

function App() {
  TagManager.initialize({ gtmId: GTM_ID });
  init({
    dsn: "https://<EMAIL>/****************",
    integrations: [
      reactRouterV6BrowserTracingIntegration({
        useEffect: React.useEffect,
        useLocation,
        useNavigationType,
        createRoutesFromChildren,
        matchRoutes,
      }),
    ],
    enabled: !window.location.href.startsWith("http://localhost"),
    environment: PRODUCTION ? "production" : "development",
    tracesSampleRate: 1.0,
    // tracePropagationTargets: ["localhost", /^https:\/\/yourserver\.io\/api/],
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
  });
  SETUP_APPKIT();
  return (
    <>
      <ThemeProvider theme={theme}>
        <CssBaseline>
          <ErrorBoundary FallbackComponent={AppError}>
            <WagmiProvider config={WAGMI_ADAPTOR.wagmiConfig}>
              <QueryClientProvider client={QUERY_CLIENT}>
                <PostHogProvider client={posthog}>
                  <Provider>
                    <Suspense fallback={<AppLoader />}>
                      <BeraBorrowAccount>
                        <Routes />
                      </BeraBorrowAccount>
                    </Suspense>
                  </Provider>
                </PostHogProvider>
              </QueryClientProvider>
            </WagmiProvider>
          </ErrorBoundary>
        </CssBaseline>
      </ThemeProvider>
    </>
  );
}

export default App;
