import { Hex } from "viem";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { requireAddress } from "../../utils";
import PollenStakingAbi from "../../abi/PollenStakingAbi";

export abstract class BasePollenStaking {
  protected readonly connection: BeraBorrowConnection;
  abstract readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
  }

  /**
   * Stakes token in POLLEN buyback distribution staking
   * @param address The depositor of shares
   * @param amount The amount of shares to deposit
   */
  async stake(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: PollenStakingAbi,
      functionName: "stake",
      args: [amount],
      account: address,
      ...overrides,
    });
  }

  /**
   * Unstakes token from POLLEN buyback distribution staking
   * @param address The depositor of shares
   * @param amount The amount of shares to deposit
   */
  async unstake(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: PollenStakingAbi,
      functionName: "withdraw",
      args: [amount],
      account: address,
      ...overrides,
    });
  }

  /**
   * Unstakes token and claims pending rewards
   * @param address The depositor of shares
   */
  async unstakeAndClaim(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: PollenStakingAbi,
      functionName: "exit",
      args: [],
      account: address,
      ...overrides,
    });
  }

  /**
   * Claims POLLEN buyback distribution rewards
   * @param address The depositor of shares
   */
  async claimRewards(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: PollenStakingAbi,
      functionName: "getReward",
      args: [],
      account: address,
      ...overrides,
    });
  }

  /**
   * Checks the balance of the address in the POLLEN buyback distribution staking
   * @param address The depositor of shares
   */
  async getBalance(address?: Hex, overrides?: TransactionOptions): Promise<bigint> {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: PollenStakingAbi,
      functionName: "balanceOf",
      args: [address],
      account: address,
      ...overrides,
    });
  }

  /**
   * Checks the pending user rewards for all reward tokens
   * @param address The address to check the balance of
   * @returns All pending user rewards (array of [rewardToken, amount])
   */
  async getRewardsForUser(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: PollenStakingAbi,
      functionName: "getAllRewardsForUser",
      args: [address],
      account: address,
      ...overrides,
    });
  }
}
