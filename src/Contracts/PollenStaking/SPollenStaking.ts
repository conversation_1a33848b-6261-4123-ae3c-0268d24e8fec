import { Hex } from "viem";
import { BeraBorrowConnection } from "../../types";
import { BasePollenStaking } from "./BasePollenStaking";
import { assertAddress } from "../../utils";

export class SPollenStaking extends BasePollenStaking {
  readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection) {
    super(connection);
    this.contractAddress = this.connection.addresses.sPollenStaking;
    assertAddress(this.contractAddress);
  }
}
