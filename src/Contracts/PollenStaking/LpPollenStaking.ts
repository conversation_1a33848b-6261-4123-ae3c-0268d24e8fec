import { Hex } from "viem";
import { BeraBorrowConnection } from "../../types";
import { BasePollenStaking } from "./BasePollenStaking";
import { assertAddress } from "../../utils";

export class LpPollenStaking extends BasePollenStaking {
  readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection) {
    super(connection);
    this.contractAddress = this.connection.addresses.lpPollenStaking;
    assertAddress(this.contractAddress);
  }
}
