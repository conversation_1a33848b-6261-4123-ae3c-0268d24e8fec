import PriceFeedAbi from "../../abi/PriceFeedAbi";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { Hex } from "viem";
import { assertAddress } from "../../utils";

export class PriceFeed {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.contractAddress = this.connection.addresses.priceFeed;
    assertAddress(this.contractAddress);
  }

  async fetchPrice(tokenAddress: Hex, overrides?: TransactionOptions) {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: PriceFeedAbi,
      functionName: "fetchPrice",
      args: [tokenAddress],
      ...overrides,
    });
  }
}
