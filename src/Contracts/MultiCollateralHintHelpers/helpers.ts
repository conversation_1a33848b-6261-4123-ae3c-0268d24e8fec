import { assertCheck } from "../../utils";

const maxNumberOfTrialsAtOnce = 2500;
// Maximum number of trials to perform in a single getApproxHint() call. If the number of trials
// required to get a statistically "good" hint is larger than this, the search for the hint will
// be broken up into multiple getApproxHint() calls.
//
// This should be low enough to work with popular public Ethereum providers like Infura without
// triggering any fair use limits.
export function* generateTrials(totalNumberOfTrials: number) {
  assertCheck(Number.isInteger(totalNumberOfTrials) && totalNumberOfTrials > 0, "Invalid Integer Number");

  while (totalNumberOfTrials) {
    const numberOfTrials = Math.min(totalNumberOfTrials, maxNumberOfTrialsAtOnce);
    yield numberOfTrials;

    totalNumberOfTrials -= numberOfTrials;
  }
}
// This returns a random integer between 0 and Number.MAX_SAFE_INTEGER
export const randomBigInt = () => BigInt(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));
