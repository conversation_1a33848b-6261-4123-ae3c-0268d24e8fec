import MultiCollateralHintHelpersAbi from "../../abi/MultiCollateralHintHelpersAbi";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { generateTrials, randomBigInt } from "./helpers";
import { SortedDens } from "../SortedDens/Contract";
import { assertAddress, requireAddress } from "../../utils";
import { Hex, zeroAddress } from "viem";
import Den<PERSON>anagerAbi from "../../abi/DenManagerAbi";

export class MultiCollateralHintHelpers {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: `0x${string}`;

  constructor(connection: BeraBorrowConnection, protocol: Hex) {
    this.connection = connection;
    this.contractAddress = this.connection.addresses.protocols[protocol]["multiCollateralHintHelpers"];
    assertAddress(this.contractAddress);
  }
  /**
   *Find out address and amount hints on the expected Redemption
   *
   * @param dmAddr Den Manager Address
   * @param amount Amount
   * @param price Collateral USD Price
   * @param redeemMaxIterations Max interactions for redemption
   *
   * @returns Details about the expected Redemption
   */
  async findRedemptionHints(
    dmAddr: `0x${string}`,
    amount: bigint,
    price: bigint,
    redeemMaxIterations: bigint,
    overrides?: TransactionOptions
  ): Promise<
    [
      truncatedAmount: bigint,
      firstRedemptionHint: `0x${string}`,
      partialRedemptionUpperHint: `0x${string}`,
      partialRedemptionLowerHint: `0x${string}`,
      partialRedemptionHintNICR: bigint,
    ]
  > {
    const address = requireAddress(this.connection, overrides);
    const [firstRedemptionHint, partialRedemptionHintNICR, truncatedDebtAmount] = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: MultiCollateralHintHelpersAbi,
      functionName: "getRedemptionHints",
      args: [dmAddr, amount, price, redeemMaxIterations],
      ...overrides,
      account: address,
    });

    const [partialRedemptionUpperHint, partialRedemptionLowerHint] =
      partialRedemptionHintNICR === BigInt(0)
        ? [zeroAddress, zeroAddress]
        : await this.findHintsForNominalCollateralRatio(
            dmAddr,
            partialRedemptionHintNICR // XXX: if we knew the partially redeemed Den's address, we'd pass it here
          );
    return [truncatedDebtAmount, firstRedemptionHint, partialRedemptionUpperHint, partialRedemptionLowerHint, partialRedemptionHintNICR];
  }

  /**
   *@interal
   */
  async _collectApproxHint(
    latestRandomSeed: bigint,
    results: { diff: bigint; hintAddress: `0x${string}` }[],
    numberOfTrials: number,
    dmAddr: `0x${string}`,
    nominalCollateralRatio: bigint,
    address?: `0x${string}`,
    overrides?: TransactionOptions
  ): Promise<{
    latestRandomSeed: bigint;
    results: {
      diff: bigint;
      hintAddress: `0x${string}`;
    }[];
  }> {
    address ??= requireAddress(this.connection, overrides);
    const [hintAddress, diff, latestRandomSeedNew] = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: MultiCollateralHintHelpersAbi,
      functionName: "getApproxHint",
      args: [dmAddr, nominalCollateralRatio, BigInt(numberOfTrials), latestRandomSeed],
      ...overrides,
      account: address,
    });

    return {
      latestRandomSeed: latestRandomSeedNew,
      results: [...results, { hintAddress, diff }],
    };
  }

  /**
	 * finds the before and after location Addresses in sorted Den list where the Den is expected to be placed
	 * @param dmAddr Den Manager Address
	 * @param nominalCollateralRatio  the expected nominal Collateral Ratio 

	 * @returns 
	 */
  async findHintsForNominalCollateralRatio(
    dmAddr: `0x${string}`,
    nominalCollateralRatio: bigint,
    address?: `0x${string}`,
    overrides?: TransactionOptions
  ): Promise<[`0x${string}`, `0x${string}`]> {
    const numberOfDens = await this.connection.publicClient.readContract({
      address: dmAddr,
      abi: DenManagerAbi,
      functionName: "getDenOwnersCount",
      args: [],
      ...overrides,
      account: address,
    });
    // const numberOfDens = await denManager.getTotalDens();

    if (!numberOfDens) {
      return [zeroAddress, zeroAddress];
    }

    const totalNumberOfTrials = Math.ceil(15 * Math.sqrt(Number(numberOfDens.toString())));

    const [firstTrials, ...restOfTrials] = generateTrials(totalNumberOfTrials);

    const { results } = await restOfTrials.reduce(
      (p, numberOfTrials) => p.then((state) => this._collectApproxHint(state.latestRandomSeed, state.results, numberOfTrials, dmAddr, nominalCollateralRatio, address, overrides)),
      this._collectApproxHint(randomBigInt(), [], firstTrials, dmAddr, nominalCollateralRatio, address, overrides)
    );

    const { hintAddress } = results.reduce((a, b) => (a.diff < b.diff ? a : b));
    const sortedDens = new SortedDens(this.connection, dmAddr);
    return sortedDens.findInsertPosition(hintAddress, nominalCollateralRatio, address);
  }
}
