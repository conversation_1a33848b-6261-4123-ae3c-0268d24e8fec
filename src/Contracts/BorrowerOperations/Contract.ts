import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { DEFAULT_SLIPPAGE_TOLERANCE } from "../../constants";
import { DenAdjustmentParams, DenCreationParams } from "../../Den/types";
import { Den, _normalizeDenAdjustment, _normalizeDenCreation } from "../../Den/Den";
import BorrowerOperationsAbi from "../../abi/BorrowerOperationsAbi";
import { DenManager } from "../DenManager/Contract";
import { Collateral } from "../Collateral/Contract";
import { MultiCollateralHintHelpers } from "../MultiCollateralHintHelpers/Contract";
import { addGasForBaseRateUpdate, addGasForPotentialListTraversal } from "../../gasHelpers";
import { assertAddress, requireAddress } from "../../utils";
import { Hex } from "viem";

export class BorrowerOperations {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  private readonly protocol: Hex;
  private readonly multiCollateralHintHelpers: MultiCollateralHintHelpers;

  constructor(connection: BeraBorrowConnection, protocol: Hex) {
    this.connection = connection;
    this.protocol = protocol;
    this.contractAddress = this.connection.addresses.protocols[this.protocol]["borrowerOperations"];
    this.multiCollateralHintHelpers = new MultiCollateralHintHelpers(this.connection, this.protocol);
    assertAddress(this.contractAddress);
    assertAddress(this.protocol);
  }
  /**
   *  * Open a new Den by depositing collateral and borrowing Debt.
   * @param dmAddr Den Manager Address
   * @param borrower Borrowers Address
   * @param params DenCreationParameters
   * @param maxBorrowingRate BorrowingOperationOptionalParameters
   */
  async openDen(dmAddr: Hex, borrower: Hex, params: DenCreationParams<bigint>, maxBorrowingRate: bigint, overrides?: TransactionOptions) {
    const address = requireAddress(this.connection, overrides);
    const normalizedParams = _normalizeDenCreation(params);
    const { depositCollateral, borrowNECT } = normalizedParams;
    const collateralAddr = this.connection.addresses.denManagers[dmAddr].collateral;
    const collateralToken = new Collateral(
      this.connection,
      collateralAddr,
      this.connection.addresses.collateralTokens[collateralAddr].ticker,
      this.connection.addresses.collateralTokens[collateralAddr].decimals
    );

    const currentBorrowingRate = maxBorrowingRate - DEFAULT_SLIPPAGE_TOLERANCE;
    const newDen = Den.create(normalizedParams, this.connection.liquidationReserve, currentBorrowingRate);
    const [collateralAllowance, hints] = await Promise.all([
      collateralToken.allowance(borrower, this.contractAddress),
      this.multiCollateralHintHelpers.findHintsForNominalCollateralRatio(dmAddr, newDen._nominalCollateralRatio, borrower),
    ]);

    if (collateralAllowance && depositCollateral && collateralAllowance < depositCollateral) {
      throw Error("ERC20: insufficient-allowance");
    }
    if (overrides?.gas === undefined) {
      const decayedBorrowingRate = maxBorrowingRate;
      const decayedDen = Den.create(normalizedParams, this.connection.liquidationReserve, decayedBorrowingRate);
      const denRecreate = Den.recreate(decayedDen, this.connection.liquidationReserve, currentBorrowingRate);
      if (denRecreate === undefined) {
        throw new Error(`Den's unable to recreate `);
      }

      const gas = await this.connection.publicClient.estimateContractGas({
        address: this.contractAddress,
        abi: BorrowerOperationsAbi,
        functionName: "openDen",
        args: [dmAddr, borrower, maxBorrowingRate, depositCollateral, borrowNECT, ...hints],
        type: "eip1559",
        ...overrides,
        account: address,
      });

      const gasLimit = addGasForBaseRateUpdate(addGasForPotentialListTraversal(gas));

      overrides = { ...overrides, gas: gasLimit };
    }
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: BorrowerOperationsAbi,
      functionName: "openDen",
      args: [dmAddr, borrower, maxBorrowingRate, depositCollateral, borrowNECT, ...hints],
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }
  /**
   * Adjust an existing Den by depositing/withdrawing collateral and/or borrowing Debt.
   * @param dmAddr Den Manager Address
   * @param isDebtIncrease if the new Nect will be minted
   * @param params DenCreationParameters
   * @param maxBorrowingRate Max borrowing rate
   */
  async adjustDen(dmAddr: Hex, address: Hex, isDebtIncrease: boolean, params: DenAdjustmentParams<bigint>, maxBorrowingRate: bigint, overrides?: TransactionOptions) {
    const normalizedParams = _normalizeDenAdjustment(params);
    const { depositCollateral, withdrawCollateral, borrowNECT, repayNECT } = normalizedParams;
    const denManager = new DenManager(this.connection, dmAddr);
    const collateralAddr = this.connection.addresses.denManagers[dmAddr].collateral;
    const collateralToken = new Collateral(
      this.connection,
      collateralAddr,
      this.connection.addresses.collateralTokens[collateralAddr].ticker,
      this.connection.addresses.collateralTokens[collateralAddr].decimals
    );

    const [den, collateralAllowance] = await Promise.all([denManager.getDen(address), depositCollateral && collateralToken.allowance(address, this.contractAddress)]);
    if (collateralAllowance && depositCollateral && collateralAllowance < depositCollateral) {
      throw Error("ERC20: insufficient-allowance");
    }

    const currentBorrowingRate = maxBorrowingRate - DEFAULT_SLIPPAGE_TOLERANCE;
    const adjustedDen = den.adjust(normalizedParams, currentBorrowingRate);
    let hints = await this.multiCollateralHintHelpers.findHintsForNominalCollateralRatio(dmAddr, adjustedDen._nominalCollateralRatio, address);

    if (overrides?.gas === undefined) {
      const decayedBorrowingRate = maxBorrowingRate;

      const decayedDen = den.adjust(normalizedParams, decayedBorrowingRate);

      const denAdjustment = den.adjustTo(decayedDen, this.connection.liquidationReserve, currentBorrowingRate);

      if (denAdjustment === undefined) {
        throw new Error(`Den's adjustment is Invalid `);
      }

      const gas = await this.connection.publicClient.estimateContractGas({
        address: this.contractAddress,
        abi: BorrowerOperationsAbi,
        functionName: "adjustDen",
        args: [dmAddr, address, maxBorrowingRate, depositCollateral ?? 0n, withdrawCollateral ?? 0n, borrowNECT ?? repayNECT ?? 0n, isDebtIncrease, ...hints],
        type: "eip1559",
        ...overrides,
        account: address,
      });
      overrides = { ...overrides, gas: addGasForPotentialListTraversal(gas) };
    }
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: BorrowerOperationsAbi,
      functionName: "adjustDen",
      args: [dmAddr, address, maxBorrowingRate, depositCollateral ?? 0n, withdrawCollateral ?? 0n, (borrowNECT || repayNECT) ?? 0n, isDebtIncrease, ...hints],
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }
  /**
   * Closes a Den by repaying all outstanding Debt and receiving the Collateral in return
   */
  async closeDen(dmAddr: Hex, address: Hex, overrides?: TransactionOptions) {
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: BorrowerOperationsAbi,
      functionName: "closeDen",
      args: [dmAddr, address],
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }

  /**
   * Get TCR
   */
  async getTCR(): Promise<bigint> {
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BorrowerOperationsAbi,
      functionName: "getTCR",
      args: [],
    });
  }
  async getGlobalSystemBalances(): Promise<readonly [totalPricedCollateral: bigint, totalDebt: bigint]> {
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BorrowerOperationsAbi,
      functionName: "getGlobalSystemBalances",
      args: [],
    });
  }
  async getBalances(): Promise<{ collateral: bigint; debt: bigint; price: bigint }[]> {
    const res = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BorrowerOperationsAbi,
      functionName: "fetchBalances",
      args: [],
    });
    return res.collaterals.map((_item, i) => {
      return { collateral: res.collaterals[i], debt: res.debts[i], price: res.prices[i] };
    });
  }
  async isApprovedDelegate(delegatedAddress: Hex, address: Hex, overrides?: TransactionOptions) {
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BorrowerOperationsAbi,
      functionName: "isApprovedDelegate",
      args: [address, delegatedAddress],
      ...overrides,
    });
  }
  async setDelegateApproval(delegatedAddress: Hex, approval: boolean, overrides?: TransactionOptions) {
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: BorrowerOperationsAbi,
      functionName: "setDelegateApproval",
      args: [delegatedAddress, approval],
      type: "eip1559",
      ...overrides,
    });
  }
}
