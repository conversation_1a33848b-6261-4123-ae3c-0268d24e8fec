import P<PERSON><PERSON><PERSON><PERSON>Abi from "../../abi/POLLENTokenAbi";
import { assertAddress, requireAddress } from "../../utils";
import { BeraBorrowConnection, TransactionOptions } from "../../types";

export class Pollen {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: `0x${string}`;
  readonly ticker: string;
  readonly decimals: number;
  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.contractAddress = this.connection.addresses.pollenToken.contractAddress;
    this.ticker = this.connection.addresses.pollenToken.ticker;
    this.decimals = this.connection.addresses.pollenToken.decimals;
    assertAddress(this.contractAddress);
  }

  /**
   * Get the amount of POLLEN held by an address.
   *
   * @param address - Address whose balance should be retrieved.
   */
  async getBalance(address?: `0x${string}`, overrides?: TransactionOptions): Promise<bigint> {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: POLLENTokenAbi,
      functionName: "balanceOf",
      args: [address],
      ...overrides,
      account: address,
    });
  }
  /**
   * transfer Pollen tokens to an address.
   *
   * @param receiver - Address of receipient.
   * @param amount - Amount of POLLEN to send.
   *
   */
  async transfer(receiver: `0x${string}`, amount: bigint, address?: `0x${string}`, overrides?: TransactionOptions) {
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: POLLENTokenAbi,
      functionName: "transfer",
      args: [receiver, amount],
      account: address,
      type: "eip1559",
      ...overrides,
    });
  }
}
