import iBGTVault<PERSON>bi from "../../abi/iBGTVaultAbi";
import { assertAddress, requireAddress } from "../../utils";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { SCALING_FACTOR } from "../../constants";
import { Hex } from "viem";
import { CollVaultRouter } from "../CollVaultRouter/Contract";
import { SubgraphClient } from "../../subgraph";
import IERC20Abi from "../../abi/IERC20Abi";

export class IbgtVault {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  readonly collateralAddress: Hex;
  readonly ibgtAddress: Hex;
  readonly apyType: "none" | "assets" | "ibgt" = "ibgt";

  private readonly collVaultRouter: CollVaultRouter;
  private readonly subgraphClient: SubgraphClient;

  constructor(connection: BeraBorrowConnection, contractAddress: Hex) {
    this.connection = connection;
    this.collVaultRouter = new CollVaultRouter(this.connection);
    this.contractAddress = contractAddress;
    this.ibgtAddress = Object.entries(this.connection.addresses.collateralTokens).find((item) => item[1].ticker.toLocaleLowerCase() === "ibgt")?.[0] as Hex;
    this.collateralAddress = this.ibgtAddress;
    this.subgraphClient = new SubgraphClient(this.connection);
    assertAddress(this.contractAddress);
    assertAddress(this.collateralAddress);
  }
  async getVaultDeposit(includePrice = false, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    if (includePrice) {
      const [shares, totalSupply, withdrawFee, shareToAssetRatio, collateralToShareRatio, allowance, price] = await this.connection.publicClient.multicall({
        contracts: [
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "balanceOf", args: [address] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "totalSupply", args: [] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "getWithdrawFee", args: [] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "convertToAssets", args: [SCALING_FACTOR] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "previewDeposit", args: [SCALING_FACTOR] },
          { address: this.collateralAddress, abi: IERC20Abi, functionName: "allowance", args: [address, this.contractAddress] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "fetchPrice", args: [] },
        ],
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
        account: address,
        ...overrides,
      });
      return { shares, totalSupply, price, withdrawFee, shareToAssetRatio, collateralToShareRatio, allowance };
    } else {
      const [shares, totalSupply, withdrawFee, shareToAssetRatio, collateralToShareRatio, allowance] = await this.connection.publicClient.multicall({
        contracts: [
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "balanceOf", args: [address] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "totalSupply", args: [] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "getWithdrawFee", args: [] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "convertToAssets", args: [SCALING_FACTOR] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "previewDeposit", args: [SCALING_FACTOR] },
          { address: this.collateralAddress, abi: IERC20Abi, functionName: "allowance", args: [address, this.contractAddress] },
        ],
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
        account: address,
        ...overrides,
      });
      return { shares, totalSupply, price: 0n, withdrawFee, shareToAssetRatio, collateralToShareRatio, allowance };
    }
  }

  /**
   * Get the amount of Shares held by an address.
   *
   * @param address - Address whose balance should be retrieved.
   */
  async getBalance(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "balanceOf",
      args: [address],
      ...overrides,
      account: address,
    });
  }

  async getWithdrawFee(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "getWithdrawFee",
      args: [],
      ...overrides,
      account: address,
    });
  }
  /**
   * transfer Shares  to an address.
   *
   * @param receiver - Address of receipient.
   * @param amount - Amount of Shares to send.
   *
   */
  async transfer(receiver: Hex, amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "transfer",
      args: [receiver, amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Gets the spending Allowance of the contract
   * @param address
   * @param spender Contract Address of Spender
   * @returns spending Allowance
   */
  async allowance(address: Hex, spender: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "allowance",
      args: [address, spender],
      account: address,
      ...overrides,
    });
  }

  /**
   * Get Token Value in the Vault.
   * @param assetAddress Asset address
   * @param address User address
   */
  async getPrice(assetAddress: Hex, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    const price = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "getPrice",
      args: [assetAddress],
      account: address,
      ...overrides,
    });
    return price;
  }
  /**
   * Get Preview of Shares amount as asset
   * @param amount Amount to deposit
   */
  async convertToAssets(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "convertToAssets",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount as asset
   * @param amount Amount to deposit
   */
  async convertToShares(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "convertToShares",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount after deposit
   * @param amount Amount to deposit
   */
  async previewDeposit(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "previewDeposit",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount after withdrawal
   * @param amount Amount to withdraw
   */
  async previewWithdraw(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "previewWithdraw",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * total debt supply.
   */
  async totalSupply() {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "totalSupply",
      args: [],
    });
  }
  /**
   * Approves the spending Allowance of the contract
   * @param amount Amount to spend
   * @param spender Contract Address of Spender
   */
  async approve(amount: bigint, spender: Hex, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "approve",
      args: [spender, amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Deposit into the CollVault
   * @param amount Amount to deposit
   * @param address address
   */
  async deposit(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "deposit",
      args: [amount, address],
      account: address,
      ...overrides,
    });
  }
  /**
   * Redeem shares in the CollVault
   * @param amount Amount to deposit
   * @param address address
   */
  async redeem(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: iBGTVaultAbi,
      functionName: "redeem",
      args: [amount, address, address],
      account: address,
      ...overrides,
    });
  }

  /**
   * Preview the composition of the Vault
   */
  async previewRedeemUnderlying(shares: bigint, includePrice = true, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    const assets = await this.collVaultRouter.previewRedeemUnderlying(this.contractAddress, address, shares);
    if (!includePrice) {
      return assets
        .map((item) => {
          return { ...item, price: 0n };
        })
        .sort((a, b) => {
          const valueA = a.balance;
          const valueB = b.balance;
          if (valueA > valueB) return -1;
          if (valueA < valueB) return 1;
          return a.contractAddress.localeCompare(b.contractAddress);
        });
    }
    //@ts-expect-error not deep array
    const prices = (await this.connection.publicClient.multicall({
      contracts: assets.map((item) => {
        return { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "getPrice", args: [item.contractAddress] };
      }),
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
      account: address,
      ...overrides,
    })) as bigint[];
    return assets
      .map((item, index) => {
        return { ...item, price: prices[index] };
      })
      .sort((a, b) => {
        const valueA = a.balance * a.price;
        const valueB = b.balance * b.price;
        if (valueA > valueB) return -1;
        if (valueA < valueB) return 1;
        return a.contractAddress.localeCompare(b.contractAddress);
      });
  }

  /**
   * Get APY.
   * r = T / t * ln(Q'/Q)
   * r: APY
   * T: 1 year
   * t: 1 week
   * Q: Initial share price
   * Q`: Current share price
   */
  async getContractAPY(currentBlockNumber?: bigint) {
    if (this.apyType === "none") return 0n;
    if (!currentBlockNumber) {
      currentBlockNumber = await this.connection.publicClient.getBlockNumber();
    }
    const BLOCKS_IN_WEEK = (7 * 24 * 60 * 60) / this.connection.averageBlockTime; //  "averageBlockTime": 2,
    //check if the startblock for contract is newer than one week ago then use startblock not 1 week ago
    const blockNumberOneWeekAgo =
      currentBlockNumber - BigInt(BLOCKS_IN_WEEK) < BigInt(this.connection.addresses.vaults[this.contractAddress].startBlock)
        ? BigInt(this.connection.addresses.vaults[this.contractAddress].startBlock)
        : currentBlockNumber - BigInt(BLOCKS_IN_WEEK);

    const [blockOneWeekAgo, [currentTotalSupply, currentTVL], [oneWeekAgoTotalSupply, oneWeekAgoTVL]] = await Promise.all([
      this.connection.publicClient.getBlock({ blockNumber: blockNumberOneWeekAgo }),
      this.connection.publicClient.multicall({
        contracts: [
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "totalSupply", args: [] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "getBalance", args: [this.ibgtAddress] },
        ],
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
      }),
      this.connection.publicClient.multicall({
        contracts: [
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "totalSupply", args: [] },
          { address: this.contractAddress, abi: iBGTVaultAbi, functionName: "getBalance", args: [this.ibgtAddress] },
        ],
        blockNumber: blockNumberOneWeekAgo,
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
      }),
    ]);

    const realOneWeekAgo = Date.now() / 1000 - Number(blockOneWeekAgo.timestamp);
    const sharePriceOneWeekAgo = oneWeekAgoTotalSupply == 0n ? 0n : (oneWeekAgoTVL * SCALING_FACTOR) / oneWeekAgoTotalSupply;
    const sharePriceNow = currentTotalSupply == 0n ? 0n : (currentTVL * SCALING_FACTOR) / currentTotalSupply;
    const SECONDS_IN_YEAR = 31536000;
    const apy = sharePriceOneWeekAgo == 0n ? 0 : (SECONDS_IN_YEAR / realOneWeekAgo) * Math.log(Number(sharePriceNow) / Number(sharePriceOneWeekAgo));

    return BigInt((apy * Number(SCALING_FACTOR)).toFixed(0));
  }

  /**
   * Get APY.
   * r = T / t * ln(Q'/Q)
   * r: APY
   * T: 1 year
   * t: 1 week
   * Q: Initial share price
   * Q`: Current share price
   */
  async getApy(from: number, to: number): Promise<bigint> {
    if (this.apyType === "none") return 0n;
    try {
      const [start, end] = await Promise.all([
        this.subgraphClient.getVaultSharePriceHistory(this.contractAddress, from, to - 86400, 96, "asc"),
        this.subgraphClient.getVaultSharePriceHistory(this.contractAddress, to - 86400, to, 96, "desc"),
      ]);
      if (start === undefined || end === undefined) {
        throw new Error("subgraph Error");
      }
      const startSharePrice = start.vaultSnapshot
        .filter((item) => item.totalSupply)
        .reduce(
          (prev, item) => {
            return {
              sharePrice: prev.sharePrice + (BigInt(item.totalSupply) === 0n ? 0n : (BigInt(item.iBGT) * SCALING_FACTOR) / BigInt(item.totalSupply)),
              timestamp: prev.timestamp + Number(item.timestamp),
            };
          },
          { sharePrice: 0n, timestamp: 0 }
        );
      const endSharePrice = end.vaultSnapshot
        .filter((item) => item.totalSupply)
        .reduce(
          (prev, item) => {
            return {
              sharePrice: prev.sharePrice + (BigInt(item.totalSupply) === 0n ? 0n : (BigInt(item.iBGT) * SCALING_FACTOR) / BigInt(item.totalSupply)),
              timestamp: prev.timestamp + Number(item.timestamp),
            };
          },
          { sharePrice: 0n, timestamp: 0 }
        );

      const real30DaysAgo = endSharePrice.timestamp / end.vaultSnapshot.length - startSharePrice.timestamp / start.vaultSnapshot.length || 1;
      const sharePrice30DaysAgo = startSharePrice.sharePrice / BigInt(start.vaultSnapshot.length);
      const sharePriceNow = endSharePrice.sharePrice / BigInt(end.vaultSnapshot.length);
      const SECONDS_IN_YEAR = 31536000;
      const apy = sharePrice30DaysAgo == 0n ? 0 : (SECONDS_IN_YEAR / real30DaysAgo) * Math.log(Number(sharePriceNow) / Number(sharePrice30DaysAgo));

      return BigInt((apy * Number(SCALING_FACTOR)).toFixed(0));
    } catch (error) {
      console.error(error);
      throw new Error("subgraph Error");
    }
  }
}
