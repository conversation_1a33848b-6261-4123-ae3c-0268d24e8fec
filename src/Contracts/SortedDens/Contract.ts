import { BeraBorrowConnection, TransactionOptions } from "../../types";
import SortedDensAbi from "../../abi/SortedDensAbi";
import { assertAddress, requireAddress } from "../../utils";
import { zeroAddress } from "viem";

export class SortedDens {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: `0x${string}`;

  constructor(connection: BeraBorrowConnection, denManager: `0x${string}`) {
    this.connection = connection;
    this.contractAddress = this.connection.addresses.denManagers[denManager].sortedDens;
    assertAddress(this.contractAddress);
  }
  /**
   * finds position in the Sorted Dens to finds position in the Sorted Dens to insert the new den based off nominalCollateralRatio
   *
   * @param hintAddress - hint address to start at
   * @param nominalCollateralRatio - Collateral Ratio of the Den
   *
   * @returns the 2 addresses where between the Den will be likely placed
   */
  async findInsertPosition(
    hintAddress: `0x${string}`,
    nominalCollateralRatio: bigint,
    address?: `0x${string}`,
    overrides?: TransactionOptions
  ): Promise<[`0x${string}`, `0x${string}`]> {
    address ??= requireAddress(this.connection, overrides);

    const [prevHint, nextHint] = await Promise.all([this.getPreviousAddress(hintAddress), this.getNextAddress(hintAddress)]);
    let [prev, next] = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: SortedDensAbi,
      functionName: "findInsertPosition",
      args: [nominalCollateralRatio, prevHint, nextHint],
      ...overrides,
      account: address,
    });

    if (address) {
      // In the case of reinsertion, the address of the Den being reinserted is not a usable hint,
      // because it is deleted from the list before the reinsertion.
      // "Jump over" the Den to get the proper hint.
      if (prev === address) {
        prev = await this.getPreviousAddress(prev);
      } else if (next === address) {
        next = await this.getNextAddress(next);
      }
    }
    // Don't use `address(0)` as hint as it can result in huge gas cost.
    if (prev === zeroAddress) {
      prev = next;
    } else if (next === zeroAddress) {
      next = prev;
    }

    return [prev, next];
  }
  /**
   * finds previous position in the Sorted Dens
   *
   * @param currAddress -  address to look from
   *
   * @returns the previous address
   */
  async getPreviousAddress(currAddress: `0x${string}`, address?: `0x${string}`, overrides?: TransactionOptions): Promise<`0x${string}`> {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: SortedDensAbi,
      functionName: "getPrev",
      args: [currAddress],
      ...overrides,
      account: address,
    });
  }
  /**
   * finds next position in the Sorted Dens
   *
   * @param currAddress -  address to look from
   *
   * @returns the next address
   */
  async getNextAddress(currAddress: `0x${string}`, address?: `0x${string}`, overrides?: TransactionOptions): Promise<`0x${string}`> {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: SortedDensAbi,
      functionName: "getNext",
      args: [currAddress],
      ...overrides,
      account: address,
    });
  }
}
