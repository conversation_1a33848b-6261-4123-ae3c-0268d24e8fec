import BaseCollateralVaultAbi from "../../abi/BaseCollateralVaultAbi";
import { assertAddress, requireAddress } from "../../utils";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { SCALING_FACTOR, SECONDS_IN_YEAR } from "../../constants";
import { Hex } from "viem";
import { CollVaultRouter } from "../CollVaultRouter/Contract";
import { SubgraphClient } from "../../subgraph";
import IERC20Abi from "../../abi/IERC20Abi";

export class CollateralVault {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  readonly collateralAddress: Hex;
  readonly ibgtAddress: Hex | undefined;
  readonly apyType: "none" | "assets" | "ibgt" = "none";

  private readonly collVaultRouter: CollVaultRouter;
  private readonly subgraphClient: SubgraphClient;

  constructor(connection: BeraBorrowConnection, contractAddress: Hex) {
    this.connection = connection;
    this.collVaultRouter = new CollVaultRouter(this.connection);
    this.contractAddress = contractAddress;
    this.collateralAddress = this.connection.addresses.vaults[contractAddress].collateral;
    this.ibgtAddress = Object.entries(this.connection.addresses.collateralTokens).find((item) => item[1].ticker.toLocaleLowerCase() === "ibgt")?.[0] as Hex;
    this.apyType = this.connection.addresses.vaults[this.contractAddress].apyType ?? "none";
    if (this.ibgtAddress === undefined && this.apyType === "ibgt") this.apyType = "assets";
    this.subgraphClient = new SubgraphClient(this.connection);
    assertAddress(this.contractAddress);
    assertAddress(this.collateralAddress);
  }
  async getVaultDeposit(includePrice = false, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    if (includePrice) {
      const [shares, totalSupply, withdrawFee, shareToAssetRatio, collateralToShareRatio, allowance, price] = await this.connection.publicClient.multicall({
        contracts: [
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "balanceOf", args: [address] },
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "totalSupply", args: [] },
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "getWithdrawFee", args: [] },
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "convertToAssets", args: [SCALING_FACTOR] },
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "previewDeposit", args: [SCALING_FACTOR] },
          { address: this.collateralAddress, abi: IERC20Abi, functionName: "allowance", args: [address, this.contractAddress] },
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "fetchPrice", args: [] },
        ],
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
        account: address,
        ...overrides,
      });
      return { shares, totalSupply, price, withdrawFee, shareToAssetRatio, collateralToShareRatio, allowance };
    } else {
      const [shares, totalSupply, withdrawFee, shareToAssetRatio, collateralToShareRatio, allowance] = await this.connection.publicClient.multicall({
        contracts: [
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "balanceOf", args: [address] },
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "totalSupply", args: [] },
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "getWithdrawFee", args: [] },
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "convertToAssets", args: [SCALING_FACTOR] },
          { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "previewDeposit", args: [SCALING_FACTOR] },
          { address: this.collateralAddress, abi: IERC20Abi, functionName: "allowance", args: [address, this.contractAddress] },
        ],
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
        account: address,
        ...overrides,
      });
      return { shares, totalSupply, price: 0n, withdrawFee, shareToAssetRatio, collateralToShareRatio, allowance };
    }
  }

  /**
   * Get the amount of Shares held by an address.
   *
   * @param address - Address whose balance should be retrieved.
   */
  async getBalance(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "balanceOf",
      args: [address],
      ...overrides,
      account: address,
    });
  }

  async getWithdrawFee(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "getWithdrawFee",
      args: [],
      ...overrides,
      account: address,
    });
  }
  /**
   * transfer Shares  to an address.
   *
   * @param receiver - Address of receipient.
   * @param amount - Amount of Shares to send.
   *
   */
  async transfer(receiver: Hex, amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "transfer",
      args: [receiver, amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Gets the spending Allowance of the contract
   * @param address
   * @param spender Contract Address of Spender
   * @returns spending Allowance
   */
  async allowance(address: Hex, spender: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "allowance",
      args: [address, spender],
      account: address,
      ...overrides,
    });
  }

  /**
   * Get Token Value in the Vault.
   * @param assetAddress Asset address
   * @param address User address
   */
  async getPrice(assetAddress: Hex, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    const price = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "getPrice",
      args: [assetAddress],
      account: address,
      ...overrides,
    });
    return price;
  }
  /**
   * Get Preview of Shares amount as asset
   * @param amount Amount to deposit
   */
  async convertToAssets(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "convertToAssets",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount as asset
   * @param amount Amount to deposit
   */
  async convertToShares(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "convertToShares",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount after deposit
   * @param amount Amount to deposit
   */
  async previewDeposit(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "previewDeposit",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount after deposit
   * @param amount Amount to deposit
   */
  async previewRedeem(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "previewRedeem",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount after withdrawal
   * @param amount Amount to withdraw
   */
  async previewWithdraw(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "previewWithdraw",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * total debt supply.
   */
  async totalSupply() {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "totalSupply",
      args: [],
    });
  }
  /**
   * Approves the spending Allowance of the contract
   * @param amount Amount to spend
   * @param spender Contract Address of Spender
   */
  async approve(amount: bigint, spender: Hex, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "approve",
      args: [spender, amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Deposit into the CollVault
   * @param amount Amount to deposit
   * @param address address
   */
  async deposit(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "deposit",
      args: [amount, address],
      account: address,
      ...overrides,
    });
  }
  /**
   * Redeem shares in the CollVault
   * @param amount Amount to deposit
   * @param address address
   */
  async redeem(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "redeem",
      args: [amount, address, address],
      account: address,
      ...overrides,
    });
  }

  /**
   * Preview the composition of the Vault
   */
  async previewRedeemUnderlying(shares: bigint, includePrice = true, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    const assets = await this.collVaultRouter.previewRedeemUnderlying(this.contractAddress, address, shares, overrides);
    if (!includePrice) {
      return assets
        .map((item) => {
          return { ...item, price: 0n };
        })
        .sort((a, b) => {
          const valueA = a.balance;
          const valueB = b.balance;
          if (valueA > valueB) return -1;
          if (valueA < valueB) return 1;
          return a.contractAddress.localeCompare(b.contractAddress);
        });
    }
    //@ts-expect-error not deep
    const prices = (await this.connection.publicClient.multicall({
      contracts: assets.map((item) => {
        return { address: this.contractAddress, abi: BaseCollateralVaultAbi, functionName: "getPrice", args: [item.contractAddress] };
      }),
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
      account: address,
      ...overrides,
    })) as bigint[];
    return assets
      .map((item, index) => {
        return { ...item, price: prices[index] };
      })
      .sort((a, b) => {
        const valueA = a.balance * a.price;
        const valueB = b.balance * b.price;
        if (valueA > valueB) return -1;
        if (valueA < valueB) return 1;
        return a.contractAddress.localeCompare(b.contractAddress);
      });
  }

  /**
   * Calculate APY for a specific contract using annual compound interest formula
   * This method matches how most DeFi competitors calculate APY
   * @param currentBlockNumber Current block number (optional)
   * @param options APY calculation options
   * @returns Calculated APY as a bigint
   */
  async getContractAPY(currentBlockNumber?: bigint, blockLookBack = 9000n, debug = false): Promise<bigint> {
    if (this.apyType === "none") return 0n;
    if (!currentBlockNumber) {
      currentBlockNumber = await this.connection.publicClient.getBlockNumber();
    }

    try {
      const vaultStartBlock = BigInt(this.connection.addresses.vaults[this.contractAddress]?.startBlock || 0);

      // Simplified approach: Just look at the last 9,000 blocks
      // This stays under the 10,000 block limit and focuses on recent rebalances
      const RECENT_BLOCKS = blockLookBack;
      const MAX_BLOCK_RANGE = 10000n;
      let fromBlock = currentBlockNumber > RECENT_BLOCKS ? currentBlockNumber - RECENT_BLOCKS : 0n;

      if (vaultStartBlock > 0n && vaultStartBlock > fromBlock) {
        fromBlock = vaultStartBlock;
      }

      if (debug) {
        console.log(`[DEBUG] Fetching rebalance events for ${this.contractAddress} from block ${fromBlock} to ${currentBlockNumber}`);
      }

      // Prepare all promises
      type RebalanceEvent = { blockNumber: bigint };

      const promises: Promise<RebalanceEvent[]>[] = [];

      for (let startBlock = fromBlock; startBlock <= currentBlockNumber; startBlock += MAX_BLOCK_RANGE) {
        const toBlock = startBlock + MAX_BLOCK_RANGE - 1n <= currentBlockNumber ? startBlock + MAX_BLOCK_RANGE - 1n : currentBlockNumber;
        if (debug) {
          console.log(`[DEBUG] Preparing request from ${startBlock} to ${toBlock}`);
        }
        const events = this.connection.publicClient.getContractEvents({
          address: this.contractAddress,
          abi: BaseCollateralVaultAbi,
          eventName: "Rebalance",
          fromBlock: startBlock,
          toBlock: toBlock,
        }) as Promise<RebalanceEvent[]>;
        promises.push(events);
      }

      // Fire all requests concurrently
      const results = await Promise.all(promises);

      // Merge all results
      const rebalanceEvents = results.flat();

      // Sort events by block number (newest first) and take the most recent 8
      const sortedEvents = rebalanceEvents.sort((a, b) => Number(b.blockNumber) - Number(a.blockNumber)).slice(0, 8);

      if (debug) {
        console.log(`[DEBUG] Found ${sortedEvents.length} rebalance events for ${this.contractAddress} in the last ${Number(currentBlockNumber - fromBlock)} blocks`);
      }

      // If we have at least 2 rebalance events, we can calculate APY
      if (sortedEvents.length >= 2) {
        const apyResults: number[] = [];
        //@ts-expect-error  timestamps directly from events - they have a blockTimestamp property as hex string
        const timestamps = sortedEvents.map((event) => parseInt(event.blockTimestamp as string, 16));

        // Get price per share at each rebalance event block
        const pricePerSharePromises = sortedEvents.map((event) =>
          this.connection.publicClient.multicall({
            contracts: [
              {
                address: this.contractAddress,
                abi: BaseCollateralVaultAbi,
                functionName: "getBalance",
                args: [this.collateralAddress],
              },
              {
                address: this.contractAddress,
                abi: BaseCollateralVaultAbi,
                functionName: "totalSupply",
              },
            ],
            multicallAddress: this.connection.addresses.multicall,
            allowFailure: false,
            blockNumber: event.blockNumber,
          })
        );
        const pricePerShares = (await Promise.all(pricePerSharePromises)).map((item) => (item[1] === 0n ? 0n : (item[0] * SCALING_FACTOR) / item[1]));

        // Look at sequential event pairs (0-1, 1-2, 2-3, etc.) to track chronological progression
        // This better represents the actual growth pattern of the vault
        for (let i = 0; i < sortedEvents.length - 1; i++) {
          // Compare current event with the next one (which is earlier in time since they're sorted newest first)
          const currentPricePerShare = pricePerShares[i];
          const pastPricePerShare = pricePerShares[i + 1];

          // Calculate exact time difference in seconds using event timestamps directly
          const timeDiffSeconds = timestamps[i] - timestamps[i + 1];

          // Skip if time difference is too small or price per share is 0
          if (timeDiffSeconds < 300 || pastPricePerShare === 0n) {
            continue;
          }

          // Calculate price per share growth
          const pricePerShareGrowth = Number(currentPricePerShare) / Number(pastPricePerShare);

          // Skip if price per share hasn't changed or decreased
          if (pricePerShareGrowth <= 1) {
            continue;
          }

          // Calculate annualized APY using the standard formula
          // APY = ((Final / Initial) ^ (365 / days_elapsed)) - 1
          const periodInYears = timeDiffSeconds / SECONDS_IN_YEAR;
          const annualizedAPY = Math.pow(pricePerShareGrowth, 1 / periodInYears) - 1;

          if (debug) {
            console.log(
              `[DEBUG] Sequential Events ${i}-${i + 1}: TimeSpan=${(timeDiffSeconds / 86400).toFixed(1)} days, Growth=${pricePerShareGrowth.toFixed(6)}x, APY=${(annualizedAPY * 100).toFixed(2)}%`
            );
          }

          // Only filter out negative APYs, no cap on high values
          const apyPercentage = annualizedAPY * 100;

          if (apyPercentage > 0) {
            apyResults.push(apyPercentage);
          }
        }

        // If we have valid APY results, calculate the average of sequential APYs
        // This represents the overall trend of the vault's performance
        if (apyResults.length > 0) {
          // Simple average of all sequential APYs
          const averageApyPercentage = this._calculateAverageAPY(apyResults); // apyResults.reduce((sum, apy) => sum + apy, 0) / apyResults.length;

          if (debug) {
            console.log(`[DEBUG] Final APY (average of ${apyResults.length} sequential event pairs): ${averageApyPercentage.toFixed(2)}%`);
          }

          // Convert to bigint with proper scaling and return
          return BigInt(Math.floor((averageApyPercentage / 100) * Number(SCALING_FACTOR)));
        }
      }

      if (debug) {
        console.log(`[DEBUG] Insufficient rebalance events or valid APYs, falling back to standard calculation`);
      }
      return 0n;
    } catch (error) {
      console.error(`Error fetching rebalance events for ${this.contractAddress}:`, error);
      return 0n;
    }
  }
  async getSlotAPY(type: "HOURLY" | "DAILY" | "WEEKLY" = "DAILY", timeFrame = 1, minObservations = 3): Promise<bigint> {
    if (this.apyType === "none") return 0n;
    const vault = await this.subgraphClient.getVaultSlotByType(this.contractAddress, timeFrame, type, minObservations);

    if (vault?.vaultSlot[0] === undefined) return 0n;
    const currentSlot = vault.vaultSlot[0];
    const prevSlot = vault.vaultSlot[vault.vaultSlot.length - 1];
    const prevTotalSupply = BigInt(prevSlot.startTotalSupply);
    const prevBalance = BigInt(prevSlot.startBalance);
    const prevTime = Number(prevSlot.startTime);
    const currentTotalSupply = BigInt(currentSlot.lastTotalSupply);
    const currentBalance = BigInt(currentSlot.lastBalance);
    const currentTime = Number(timeFrame === 1 ? currentSlot.lastTime : BigInt(currentSlot.cumTime) / BigInt(currentSlot.observations));
    if (currentTotalSupply === 0n || prevTotalSupply === 0n) return 0n;

    try {
      const currentPricePerShare = (currentBalance * SCALING_FACTOR) / currentTotalSupply;
      const prevPricePerShare = (prevBalance * SCALING_FACTOR) / prevTotalSupply;
      const timeDiffSeconds = currentTime - prevTime;
      const periodInYears = timeDiffSeconds / SECONDS_IN_YEAR;

      if (prevPricePerShare === 0n) return 0n;
      // Calculate price per share growth
      const pricePerShareGrowth = (currentPricePerShare * SCALING_FACTOR) / prevPricePerShare;
      // APY = ((Final / Initial) ^ (365 / days_elapsed)) - 1
      const annualizedAPY = Math.pow(Number(pricePerShareGrowth) / Number(SCALING_FACTOR), 1 / periodInYears) - 1;
      return BigInt(Math.floor(annualizedAPY * Number(SCALING_FACTOR)));
    } catch (error) {
      console.error(`Error fetching vaultSlot  for ${this.contractAddress}:`, error);
      return 0n;
    }
  }

  async getAPY(from: number, to: number, limit: number = 300, type: "REBALANCE" | "EVENT" | "TIMED" = "REBALANCE"): Promise<bigint> {
    if (this.apyType === "none") return 0n;
    const vaultSnapshots = await this.subgraphClient.getVaultSharePriceHistoryByType(this.contractAddress, from, to, limit, "desc", type);
    const pricePerShares = vaultSnapshots?.vaultSnapshot.filter((item) => !!item.sharePrice);
    const apyResults: number[] = [];
    if (pricePerShares === undefined || pricePerShares.length <= 2) {
      return 0n;
    }
    try {
      for (let i = 0; i < pricePerShares.length - 1; i++) {
        // Compare current event with the next one (which is earlier in time since they're sorted newest first)
        const currentPricePerShare = Number(pricePerShares[i].sharePrice);
        const pastPricePerShare = Number(pricePerShares[i + 1].sharePrice);

        // Calculate exact time difference in seconds using event timestamps directly
        const timeDiffSeconds = Number(pricePerShares[i].timestamp) - Number(pricePerShares[i + 1].timestamp);

        // Skip if time difference is too small or price per share is 0

        if (timeDiffSeconds < 300 || pastPricePerShare === 0) {
          continue;
        }

        // Calculate price per share growth
        const pricePerShareGrowth = Number(currentPricePerShare) / pastPricePerShare;
        // Skip if price per share hasn't changed or decreased
        if (pricePerShareGrowth <= 1) {
          continue;
        }

        // Calculate annualized APY using the standard formula
        // APY = ((Final / Initial) ^ (365 / days_elapsed)) - 1
        const periodInYears = timeDiffSeconds / SECONDS_IN_YEAR;
        const annualizedAPY = Math.pow(pricePerShareGrowth, 1 / periodInYears) - 1;

        // Only filter out negative APYs, no cap on high values
        const apyPercentage = annualizedAPY * 100;

        if (apyPercentage > 0) {
          apyResults.push(apyPercentage);
        }
      }

      // If we have valid APY results, calculate the average of sequential APYs
      // This represents the overall trend of the vault's performance
      if (apyResults.length > 0) {
        // Simple average of all sequential APYs
        const averageApyPercentage = this._calculateAverageAPY(apyResults); // apyResults.reduce((sum, apy) => sum + apy, 0) / apyResults.length;
        // Convert to bigint with proper scaling and return
        return BigInt(Math.floor((averageApyPercentage / 100) * Number(SCALING_FACTOR)));
      }
      return 0n;
    } catch (error) {
      console.error(`Error fetching rebalance events for ${this.contractAddress}:`, error);
      return 0n;
    }
  }
  _calculateAverageAPY(apyResults: number[], factor = 20): number {
    if (apyResults.length === 0) return 0;

    // Step 1: Sort the array
    const sortedAPY = [...apyResults].sort((a, b) => a - b);

    // Step 2: Calculate quartiles
    const q1 = sortedAPY[Math.floor(sortedAPY.length * 0.25)];
    const q3 = sortedAPY[Math.floor(sortedAPY.length * 0.75)];
    const iqr = q3 - q1;

    // Step 3: Determine bounds for outliers
    const lowerBound = q1 - iqr * factor;
    const upperBound = q3 + iqr * factor;

    // Step 4: Filter values within bounds
    const filteredAPY = sortedAPY.filter((apy) => apy >= lowerBound && apy <= upperBound);

    // Step 5: Calculate average
    const averageAPY = filteredAPY.reduce((sum, apy) => sum + apy, 0) / filteredAPY.length;

    return averageAPY;
  }
}
