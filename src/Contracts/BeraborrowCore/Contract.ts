import <PERSON><PERSON>orrowCore<PERSON>bi from "../../abi/<PERSON><PERSON>orrow<PERSON>ore<PERSON>bi";
import { SCALING_FACTOR, SCALING_FACTOR_BP } from "../../constants";
import { BeraBorrowConnection } from "../../types";
import { Hex } from "viem";
import { assertAddress, requireAddress } from "../../utils";

export class BeraborrowCore {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  private readonly protocol: Hex;

  constructor(connection: BeraBorrowConnection, protocol?: Hex) {
    this.connection = connection;
    this.protocol = protocol ?? this.connection.addresses.defaultProtocol;
    this.contractAddress = this.connection.addresses.protocols[this.protocol].beraborrowCore;
    assertAddress(this.contractAddress);
    assertAddress(this.protocol);
  }
  async getPeripheryFlashLoanFee(address: Hex) {
    if (this.connection.startBlock === 2836771) {
      return (SCALING_FACTOR / 10000n) * 5n; //0.05%
    }
    const fee = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BeraborrowCoreAbi,
      functionName: "getPeripheryFlashLoanFee",
      args: [address],
      account: address,
    });
    return (BigInt(fee) * SCALING_FACTOR) / SCALING_FACTOR_BP;
  }
  async getCCR() {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BeraborrowCoreAbi,
      functionName: "CCR",
      args: [],
    });
  }
  async getLspEntryFee(address?: Hex) {
    address ??= requireAddress(this.connection);
    const fee = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BeraborrowCoreAbi,
      functionName: "getLspEntryFee",
      args: [address],
      account: address,
    });

    return BigInt(fee);
  }
  async getLspExitFee(address?: Hex) {
    address ??= requireAddress(this.connection);
    const fee = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: BeraborrowCoreAbi,
      functionName: "getLspExitFee",
      args: [address],
      account: address,
    });

    return BigInt(fee);
  }
}
