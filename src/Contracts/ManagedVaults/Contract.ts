import ManagedLeveragedVaultAbi from "../../abi/ManagedLeveragedVaultAbi";
import { assertAddress, requireAddress } from "../../utils";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { DEFAULT_SLIPPAGE_TOLERANCE, SCALING_FACTOR } from "../../constants";
import { Hex } from "viem";
import { SubgraphClient } from "../../subgraph";
import IERC20Abi from "../../abi/IERC20Abi";
import ManagedLeveragedVaultGettersAbi from "../../abi/ManagedLeveragedVaultGettersAbi";
import { MultiCollateralHintHelpers } from "../MultiCollateralHintHelpers/Contract";
import BaseCollateralVaultAbi from "../../abi/BaseCollateralVaultAbi";
import { Den } from "../../Den/Den";
import DenManagerAbi from "../../abi/DenManagerAbi";
import LiquidStabilityPoolAbi from "../../abi/LiquidStabilityPoolAbi";
import Price<PERSON>eedAbi from "../../abi/PriceFeedAbi";

export class ManagedVault {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  readonly getterAddress: Hex;
  readonly denManagerAddress: Hex;
  readonly collateralAddress: Hex;
  readonly vaultAddress: Hex;

  readonly protocol: Hex;
  private readonly multiCollateralHintHelpers: MultiCollateralHintHelpers;
  private readonly subgraphClient: SubgraphClient;

  constructor(connection: BeraBorrowConnection, contractAddress: Hex) {
    this.connection = connection;
    this.contractAddress = contractAddress;
    this.denManagerAddress = this.connection.addresses.managedVaults[contractAddress].denManager;
    this.collateralAddress = this.connection.addresses.managedVaults[contractAddress].collateral;
    this.vaultAddress = this.connection.addresses.denManagers[this.denManagerAddress].vault;
    this.getterAddress = this.connection.addresses.managedVaults[contractAddress].getterAddress;
    this.protocol = this.connection.addresses.denManagers[this.denManagerAddress].protocol ?? this.connection.addresses.defaultProtocol;
    this.multiCollateralHintHelpers = new MultiCollateralHintHelpers(this.connection, this.protocol);

    this.subgraphClient = new SubgraphClient(this.connection);
    assertAddress(this.contractAddress);
    assertAddress(this.getterAddress);
  }
  async getVaultDeposit(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    const sNectVaultAddress = Object.entries(this.connection.addresses.vaults).find((item) => item[1].ticker.toLowerCase() === "bb.snect")?.[0] as Hex;
    const scaling = this.connection.addresses.collateralTokens[this.collateralAddress].decimals;
    const scaleFactor = 10n ** BigInt(scaling); // bigint scaling factor
    const [shares, totalSupply, sharePrice, details, allowance, collPrice, collVaultPrice, nectPrice, sNectNectPrice, sNect, bbsNectSupply, sNectInVault] =
      await this.connection.publicClient.multicall({
        contracts: [
          { address: this.contractAddress, abi: ManagedLeveragedVaultAbi, functionName: "balanceOf", args: [address] },
          { address: this.contractAddress, abi: ManagedLeveragedVaultAbi, functionName: "totalSupply", args: [] },
          { address: this.contractAddress, abi: ManagedLeveragedVaultAbi, functionName: "convertToAssets", args: [scaleFactor] },
          { address: this.getterAddress, abi: ManagedLeveragedVaultGettersAbi, functionName: "getParameters", args: [] },
          { address: this.collateralAddress, abi: IERC20Abi, functionName: "allowance", args: [address, this.contractAddress] },
          { address: this.contractAddress, abi: ManagedLeveragedVaultAbi, functionName: "getPrice", args: [this.collateralAddress] },
          { address: this.connection.addresses.priceFeed, abi: PriceFeedAbi, functionName: "fetchPrice", args: [this.vaultAddress] },
          { address: this.connection.addresses.priceFeed, abi: PriceFeedAbi, functionName: "fetchPrice", args: [this.connection.addresses.debtToken.contractAddress] },
          { address: this.connection.addresses.stabilityPool, abi: LiquidStabilityPoolAbi, functionName: "convertToAssets", args: [SCALING_FACTOR] },
          { address: this.connection.addresses.stabilityPool, abi: LiquidStabilityPoolAbi, functionName: "balanceOf", args: [this.contractAddress] },
          { address: sNectVaultAddress, abi: BaseCollateralVaultAbi, functionName: "totalSupply", args: [] },
          { address: sNectVaultAddress, abi: BaseCollateralVaultAbi, functionName: "getBalance", args: [this.connection.addresses.stabilityPool] },
        ],
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
        account: address,
        ...overrides,
      });
    const vaultPrice = (sharePrice * collPrice) / scaleFactor;
    const sNectPrice = (sNectNectPrice * nectPrice) / SCALING_FACTOR;
    const bbsNectPrice = bbsNectSupply == 0n ? 0n : (sNectInVault * sNectPrice) / bbsNectSupply;
    return { shares, details, totalSupply, price: 1n, allowance, sNect, sNectPrice, bbsNectPrice, nectPrice, collPrice, collVaultPrice, vaultPrice };
  }

  getApys(params: {
    collApy: bigint;
    sNectApy: bigint;
    bbsNectApy: bigint;
    debtInterest: bigint;
    collAmount: bigint;
    debtAmount: bigint;
    sNectAmount: bigint;
    bbsNectAmount: bigint;
    collPrice: bigint;
    debtPrice: bigint;
    sNectPrice: bigint;
    bbsNectPrice: bigint;
  }) {
    const { collApy, sNectApy, bbsNectApy, collAmount, sNectAmount, bbsNectAmount, collPrice, sNectPrice, bbsNectPrice, debtAmount, debtPrice, debtInterest } = params;
    const effectiveDebtInterestRate = (debtInterest * this.exposureRatio(collAmount, collPrice, debtAmount, debtPrice)) / SCALING_FACTOR;
    const effectiveSNectApy = (sNectApy * this.exposureRatio(collAmount, collPrice, sNectAmount, sNectPrice)) / SCALING_FACTOR;
    const effectiveBbsNectApy = (bbsNectApy * this.exposureRatio(collAmount, collPrice, bbsNectAmount, bbsNectPrice)) / SCALING_FACTOR;
    const apy = collApy + effectiveSNectApy + effectiveBbsNectApy - effectiveDebtInterestRate;
    return { apy, collApy, effectiveBbsNectApy, effectiveDebtInterestRate, effectiveSNectApy };
  }
  /**
   *
   * @param collAmount collateral Amount of deposit
   * @param collPrice price of collateral
   * @param exposureAmount exposureToken amount
   * @param exposurePrice price of Exposure token
   * @returns ratio of exposure to collateral value
   */
  private exposureRatio(collAmount: bigint, collPrice: bigint, exposureAmount: bigint, exposurePrice: bigint): bigint {
    if (collAmount === 0n) return 0n;
    if (collPrice === 0n) return 0n;
    if (exposureAmount === 0n) return 0n;
    if (exposurePrice === 0n) return 0n;
    const collateralValue = (collAmount * collPrice) / SCALING_FACTOR;
    const exposureValue = (exposureAmount * exposurePrice) / SCALING_FACTOR;
    if (collateralValue === 0n) return 0n;
    return (exposureValue * SCALING_FACTOR) / collateralValue;
  }

  async getEpochDetails(timestamp?: bigint, address?: Hex, overrides?: TransactionOptions) {
    timestamp ??= BigInt(Math.floor(Date.now() / 1000));
    address ??= requireAddress(this.connection, overrides);
    try {
      const epoch = await this.getWithdrawalRequestEpoch(timestamp, address);
      const CutOffStartTimestamp = await this.getCutOffStartTimestamp(epoch, address);
      return { epoch, CutOffStartTimestamp, timestamp };
    } catch (error) {
      console.error(error);
      return { epoch: 0n, CutOffStartTimestamp: 0n, timestamp };
    }
  }
  async getReportBalanceOf(epoch: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    //previewRedeem
    return await this.connection.publicClient.readContract({
      address: this.getterAddress,
      abi: ManagedLeveragedVaultGettersAbi,
      functionName: "getReportBalanceOf",
      args: [epoch, address],
      account: address,
      ...overrides,
    });
  }
  async getWithdrawalRequestEpoch(timestamp: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "getWithdrawalRequestEpoch",
      args: [timestamp],
      ...overrides,
      account: address,
    });
  }

  async getCutOffStartTimestamp(epoch: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "getCutOffStartTimestamp",
      args: [epoch],
      ...overrides,
      account: address,
    });
  }

  /**
   * Get the amount of Shares held by an address.
   *
   * @param address - Address whose balance should be retrieved.
   */
  async getBalance(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "balanceOf",
      args: [address],
      ...overrides,
      account: address,
    });
  }

  /**
   * transfer Shares  to an address.
   *
   * @param receiver - Address of receipient.
   * @param amount - Amount of Shares to send.
   *
   */
  async transfer(receiver: Hex, amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "transfer",
      args: [receiver, amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Gets the spending Allowance of the contract
   * @param address
   * @param spender Contract Address of Spender
   * @returns spending Allowance
   */
  async allowance(address: Hex, spender: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "allowance",
      args: [address, spender],
      account: address,
      ...overrides,
    });
  }

  /**
   * Get Token Value in the Vault.
   * @param assetAddress Asset address
   * @param address User address
   */
  async getPrice(assetAddress: Hex, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    const price = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "getPrice",
      args: [assetAddress],
      account: address,
      ...overrides,
    });
    return price;
  }
  /**
   * Get Preview of Shares amount as asset
   * @param amount Amount to deposit
   */
  async convertToAssets(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "convertToAssets",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount as asset
   * @param amount Amount to deposit
   */
  async convertToShares(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "convertToShares",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount after deposit
   * @param amount Amount to deposit
   */
  async previewPreState(amount: bigint, address: Hex, overrides?: TransactionOptions) {
    const [shares, collateralShares, [debt, coll]] = await this.connection.publicClient.multicall({
      contracts: [
        { address: this.contractAddress, abi: ManagedLeveragedVaultAbi, functionName: "previewDeposit", args: [amount] },
        { address: this.vaultAddress, abi: BaseCollateralVaultAbi, functionName: "previewDeposit", args: [amount] },
        { address: this.denManagerAddress, abi: DenManagerAbi, functionName: "getEntireDebtAndColl", args: [address] },
      ],
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
      account: address,
      ...overrides,
    });

    return { shares, collateralShares, debt, coll };
  }
  /**
   * Get Preview of Shares amount after deposit
   * @param amount Amount to deposit
   */
  async previewDeposit(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "previewDeposit",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Get Preview of Shares amount after deposit
   * @param amount Amount to deposit
   */
  async previewRedeem(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "previewRedeem",
      args: [amount],
      account: address,
      ...overrides,
    });
  }

  /**
   * total debt supply.
   */
  async totalSupply() {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "totalSupply",
      args: [],
    });
  }
  /**
   * Approves the spending Allowance of the contract
   * @param amount Amount to spend
   * @param spender Contract Address of Spender
   */
  async approve(amount: bigint, spender: Hex, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "approve",
      args: [spender, amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Deposit into the CollVault
   * @param amount Amount to deposit
   * @param address address
   */
  async deposit(amount: bigint, address?: Hex, slippage?: bigint, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    slippage ??= DEFAULT_SLIPPAGE_TOLERANCE;

    const { shares, collateralShares, debt, coll } = await this.previewPreState(amount, address);
    const den = new Den(coll, debt);
    const adjustedDen = den.adjust({ depositCollateral: collateralShares }, 0n);
    const [lowerHint, upperHint] = await this.multiCollateralHintHelpers.findHintsForNominalCollateralRatio(this.denManagerAddress, adjustedDen._nominalCollateralRatio, address);
    const minSharesOut = shares - (shares * slippage) / SCALING_FACTOR;
    const minCollVaultShares = collateralShares - (collateralShares * slippage) / SCALING_FACTOR;

    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "deposit",
      args: [amount, address, { lowerHint, upperHint, minSharesOut, minCollVaultShares }],
      account: address,
      ...overrides,
    });
  }
  async redeemIntent(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "redeemIntent",
      args: [amount, address, address],
      account: address,
      ...overrides,
    });
  }
  async cancelWithdrawalIntent(epoch: bigint, amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "cancelWithdrawalIntent",
      args: [epoch, amount, address],
      account: address,
      ...overrides,
    });
  }
  async withdrawFromEpoch(epoch: bigint, minAmount: bigint, swapper: Hex, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    //previewRedeem
    const payload = "" as Hex;
    // const swapper = this.connection.addresses.ibgtVault; //TODO: add swapper address
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultAbi,
      functionName: "withdrawFromEpoch",
      args: [epoch, address, { swapper, payload, minRebalanceOut: 0n }],
      account: address,
      ...overrides,
    });
  }
}
