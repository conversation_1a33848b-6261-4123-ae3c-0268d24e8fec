import { Hex } from "viem";
import { BeraBorrowConnection } from "../../types";
import { BaseVeFeeDistributor } from "./BaseVeFeeDistributor";
import { assertAddress } from "../../utils";

export class <PERSON>ollenVeFeeDistributor extends BaseVeFeeDistributor {
  readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection) {
    super(connection);
    this.contractAddress = this.connection.addresses.sPollenVeFeeDistributor;
    assertAddress(this.contractAddress);
  }
}
