import { Hex } from "viem";
import { BeraBorrowConnection } from "../../types";
import { BaseVeFeeDistributor } from "./BaseVeFeeDistributor";
import { assertAddress } from "../../utils";

export class LpVeFeeDistributor extends BaseVeFeeDistributor {
  readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection) {
    super(connection);
    this.contractAddress = this.connection.addresses.lpVeFeeDistributor;
    assertAddress(this.contractAddress);
  }
}
