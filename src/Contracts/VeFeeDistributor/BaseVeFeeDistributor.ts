import { Hex } from "viem";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { requireAddress } from "../../utils";
import VePollenFeeDistributorAbi from "../../abi/VePollenFeeDistributorAbi";

export abstract class BaseVeFeeDistributor {
  protected readonly connection: BeraBorrowConnection;
  abstract readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
  }

  /**
   * Claims rewards from the fee distributor
   * @param address User address
   * @returns The reward token amount
   */
  async claimReward(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: VePollenFeeDistributorAbi,
      functionName: "claimReward",
      args: [],
      account: address,
      ...overrides,
    });
  }

  /**
   * Gets all active fund sources
   * @returns Array of source addresses
   */
  async getAllActiveSources(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: VePollenFeeDistributorAbi,
      functionName: "getAllActiveSources",
      args: [],
      account: address,
      ...overrides,
    });
  }

  /**
   * Gets the reward token address
   * @returns Reward token address
   */
  async getRewardToken(address?: Hex, overrides?: TransactionOptions): Promise<Hex> {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: VePollenFeeDistributorAbi,
      functionName: "token",
      args: [],
      account: address,
      ...overrides,
    });
  }
}
