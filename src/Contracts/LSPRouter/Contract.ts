import { Hex } from "viem";
import <PERSON><PERSON><PERSON>outer<PERSON><PERSON> from "../../abi/LSPRouterAbi";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { DEFAULT_SLIPPAGE_TOLERANCE, SCALING_FACTOR } from "../../constants";
import { assertAddress, requireAddress } from "../../utils";
import { OogaBoogaServices } from "../../Services/OogaBooga/services";
import { StabilityDeposit, TokenBalance } from "../StabilityPool/StabilityDeposit";
import { StabilityPool } from "../StabilityPool/Contract";

export class LSPRouter {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  readonly OogaBoogaApi: OogaBoogaServices;
  readonly StabilityPool: StabilityPool;
  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.contractAddress = this.connection.addresses.LSPRouter;
    this.OogaBoogaApi = new OogaBoogaServices(this.connection);
    this.StabilityPool = new StabilityPool(this.connection);
    assertAddress(this.contractAddress);
  }
  /**
   *
   * @param shares sNECT shares
   * @param assetsWithdrawn Assets value of shares
   * @param assets contract address of wrapped tokens
   * @param assetsBalance  balance of wrapped tokens
   * @param unwrappedLength length of unwrapped tokens
   * @param unwrap to unwrap the tokens
   * @param slippage slippage amount
   * @param address
   * @param overrides
   * @returns simulated contract
   */
  async redeem(
    shares: bigint,
    assetsWithdrawn: bigint,
    assets: Hex[],
    assetsBalance: bigint[],
    unwrappedLength = 0n,
    unwrap = true,
    slippage?: bigint,
    address?: Hex,
    overrides?: TransactionOptions
  ) {
    address ??= requireAddress(this.connection);
    slippage ??= DEFAULT_SLIPPAGE_TOLERANCE;
    const minAssetsWithdrawn = assetsWithdrawn - (assetsWithdrawn * slippage) / SCALING_FACTOR;
    const minUnderlyingWithdrawn = assetsBalance.map((item) => item - (item * slippage) / SCALING_FACTOR);
    const params = {
      shares,
      receiver: address,
      minAssetsWithdrawn,
      minUnderlyingWithdrawn,
      receivedTokensLengthHint: unwrappedLength,
      tokensToClaim: assets,
      unwrap,
    };
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: LSPRouterAbi,
      functionName: "redeem",
      args: [params],
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }
  async previewRedeemPreferredUnderlying(shares: bigint, assets: Hex[], unwrap = true, address?: Hex, overrides?: TransactionOptions) {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: LSPRouterAbi,
      functionName: "previewRedeemPreferredUnderlying",
      args: [shares, assets, unwrap],
      ...overrides,
      account: address,
    });
  }
  async redeemPreferredUnderlying(
    shares: bigint,
    assetsWithdrawn: bigint,
    assets: Hex[],
    redeemedTokensLength: number,
    slippage?: bigint,
    address?: Hex,
    overrides?: TransactionOptions
  ) {
    address ??= requireAddress(this.connection);
    slippage ??= DEFAULT_SLIPPAGE_TOLERANCE;
    const underlyingWithdrawn = await this.previewRedeemPreferredUnderlying(shares, assets, false);
    const minAssetsWithdrawn = assetsWithdrawn - (assetsWithdrawn * slippage) / SCALING_FACTOR;
    const minUnderlyingWithdrawn = assets.map((item) => {
      const balanceIndex = underlyingWithdrawn[1].findIndex((underlyingWithdrawn) => underlyingWithdrawn === item);
      return balanceIndex === -1 || this.connection.addresses.vaults[item] === undefined
        ? 0n
        : underlyingWithdrawn[2][balanceIndex] - (underlyingWithdrawn[2][balanceIndex] * slippage) / SCALING_FACTOR;
    });
    const params = {
      shares,
      preferredUnderlyingTokens: assets,
      receiver: address,
      _owner: address,
      minAssetsWithdrawn,
      minUnderlyingWithdrawn,
      unwrap: true,
      redeemedTokensLength,
    };

    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: LSPRouterAbi,
      functionName: "redeemPreferredUnderlying",
      args: [params],
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }
  private async _redeemPreferredUnderlyingToAny(
    targetToken: Hex,
    shares: bigint,
    assetsWithdrawn: bigint,
    assets: TokenBalance[],
    zap: boolean,
    redeemedTokensLength: number,
    slippage?: bigint,
    address?: Hex
  ): Promise<
    | {
        expectedTokens: Omit<TokenBalance, "underlyingAssets">[];
        type: "redeemPreferredUnderlying";
        params: {
          shares: bigint;
          preferredUnderlyingTokens: `0x${string}`[];
          receiver: `0x${string}`;
          _owner: `0x${string}`;
          minAssetsWithdrawn: bigint;
          minUnderlyingWithdrawn: bigint[];
          unwrap: boolean;
          redeemedTokensLength: number;
        };
      }
    | {
        expectedTokens: Omit<TokenBalance, "underlyingAssets">[];
        type: "redeemPreferredUnderlyingToOne";
        params: {
          shares: bigint;
          targetToken: Hex;
          preferredUnderlyingTokens: `0x${string}`[];
          receiver: `0x${string}`;
          _owner: `0x${string}`;
          minAssetsWithdrawn: bigint;
          minUnderlyingWithdrawn: bigint[];
          unwrap: boolean;
          redeemedTokensLength: number;
          pathDefinitions: Hex[];
          quoteAmounts: bigint[];
          minTargetTokenAmount: bigint;
          minOutputs: bigint[];
          referralCode: number;
          executor: Hex;
        };
      }
  > {
    address ??= requireAddress(this.connection);
    slippage ??= DEFAULT_SLIPPAGE_TOLERANCE;
    const [underlyingWithdrawn, debtPrice] = await Promise.all([
      this.previewRedeemPreferredUnderlying(
        shares,
        assets.map((item) => item.contractAddress),
        false
      ),
      this.StabilityPool.getPrice(this.connection.addresses.debtToken.contractAddress),
    ]);

    const minAssetsWithdrawn = assetsWithdrawn - (assetsWithdrawn * slippage) / SCALING_FACTOR;
    const minUnderlyingWithdrawn = assets.map((item) => {
      const balanceIndex = underlyingWithdrawn[1].findIndex((underlyingWithdrawn) => underlyingWithdrawn === item.contractAddress);
      return balanceIndex === -1 || this.connection.addresses.vaults[item.contractAddress] === undefined
        ? 0n
        : underlyingWithdrawn[2][balanceIndex] - (underlyingWithdrawn[2][balanceIndex] * slippage) / SCALING_FACTOR;
    });
    const wrappedTokens = assets.map((asset) => {
      const userBalanceIndex = underlyingWithdrawn[1].findIndex((redeemedAsset) => redeemedAsset === asset.contractAddress);
      return { ...asset, userBalance: userBalanceIndex !== -1 ? (underlyingWithdrawn[2][userBalanceIndex] * SCALING_FACTOR) / debtPrice : 0n };
    });
    const deposit = new StabilityDeposit(0n, 0n, wrappedTokens, 0n, 0n);
    const unwrappedTokens = deposit.getUnderlyingAssets(wrappedTokens);
    // const swappingTokens = unwrappedTokens.filter(
    //   (item) => (item.userBalance ?? 0n) > 1000n && item.contractAddress !== targetToken && !this.connection.addresses.collateralTokens[item.contractAddress]?.lpToken
    // );

    // if (swappingTokens.length === 0 || !zap || !!this.connection.addresses.collateralTokens[targetToken]?.lpToken) {
    const params = {
      shares,
      preferredUnderlyingTokens: assets.map((item) => item.contractAddress),
      receiver: address,
      _owner: address,
      minAssetsWithdrawn,
      minUnderlyingWithdrawn,
      unwrap: true,
      redeemedTokensLength,
    };

    return { type: "redeemPreferredUnderlying", params, expectedTokens: unwrappedTokens };
    // }
    // } else {
    //   const swaps = await Promise.all(
    //     swappingTokens.map(async (item) => {
    //       return await this.OogaBoogaApi.getSwap(item.contractAddress, targetToken, this.contractAddress, (item.userBalance as bigint).toString());
    //     })
    //   );

    //   const executor = swaps?.[0].data?.routerParams?.executor;
    //   const referralCode = swaps?.[0].data?.routerParams?.referralCode.toString();
    //   const error = swaps.find((item) => item.data === null) as { data: null; error: string } | undefined;
    //   const noWay = swaps.find((item) => item.data?.status !== "Success");

    //   if (error?.error) {
    //     const errorMessage: string = typeof error?.error === "string" ? error.error : "Swaps not possible";
    //     throw new Error(errorMessage);
    //   } else if (noWay) {
    //     throw new Error("Swaps not possible");
    //   } else if (!referralCode || !executor) {
    //     throw new Error("No Swaps available");
    //   }
    //   const pathDefinitions = swaps.map((item) => item.data?.routerParams.pathDefinition as Hex);
    //   const quoteAmounts = swaps.map((item) => BigInt(item.data?.routerParams.swapTokenInfo.outputQuote || "0"));
    //   const minOutputs = swaps.map((item) => BigInt(item.data?.routerParams.swapTokenInfo.outputMin || "0"));

    //   const expectedOutput = swaps.map((item) => BigInt(item.data?.assumedAmountOut ?? "0"));
    //   const targetTokenAmount =
    //     (unwrappedTokens.find((item) => item.contractAddress === targetToken)?.userBalance ?? 0n) +
    //     expectedOutput.reduce((prev, curr) => {
    //       return prev + curr;
    //     }, 0n);
    //   const minTargetTokenAmount = targetTokenAmount - (targetTokenAmount * slippage) / SCALING_FACTOR;
    //   const expectedTokens = unwrappedTokens.map((item) => {
    //     if (item.contractAddress === targetToken) {
    //       return { ...item, userBalance: targetTokenAmount };
    //     }
    //     const swappedToken = swaps.find((swap) => swap.data && item.contractAddress === swap.data?.tokens[swap.data?.tokenFrom].address);
    //     if (swappedToken) {
    //       return { ...item, userBalance: 0n };
    //     } else {
    //       return item;
    //     }
    //   });

    //   return {
    //     params: {
    //       shares,
    //       targetToken,
    //       preferredUnderlyingTokens: assets.map((item) => item.contractAddress),
    //       receiver: address,
    //       _owner: address,
    //       minAssetsWithdrawn,
    //       minUnderlyingWithdrawn,
    //       unwrap: true,
    //       redeemedTokensLength,
    //       pathDefinitions,
    //       quoteAmounts,
    //       minTargetTokenAmount,
    //       minOutputs,
    //       referralCode: parseInt(referralCode),
    //       executor,
    //     },
    //     expectedTokens,
    //     type: "redeemPreferredUnderlyingToOne",
    //   };
    // }
  }

  async previewRedeemPreferredUnderlyingToAny(
    targetToken: Hex,
    shares: bigint,
    assetsWithdrawn: bigint,
    assets: TokenBalance[],
    zap: boolean,
    redeemedTokensLength: number,
    slippage?: bigint,
    address?: Hex
  ): Promise<Omit<TokenBalance, "underlyingAssets">[]> {
    address ??= requireAddress(this.connection);
    slippage ??= DEFAULT_SLIPPAGE_TOLERANCE;
    const { expectedTokens } = await this._redeemPreferredUnderlyingToAny(targetToken, shares, assetsWithdrawn, assets, zap, redeemedTokensLength, slippage, address);
    return expectedTokens;
  }

  async redeemPreferredUnderlyingToAny(
    targetToken: Hex,
    shares: bigint,
    assetsWithdrawn: bigint,
    assets: TokenBalance[],
    zap: boolean,
    redeemedTokensLength: number,
    slippage?: bigint,
    address?: Hex,
    overrides?: TransactionOptions
  ) {
    address ??= requireAddress(this.connection);
    slippage ??= DEFAULT_SLIPPAGE_TOLERANCE;
    const preview = await this._redeemPreferredUnderlyingToAny(targetToken, shares, assetsWithdrawn, assets, zap, redeemedTokensLength, slippage, address);
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: LSPRouterAbi,
      functionName: preview.type,
      args: [preview.params],
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }
}
