import CollVaultRouterAbi from "../../abi/CollVaultRouterAbi";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { Hex } from "viem";
import { DenAdjustmentParams, DenCreationParams } from "../../Den/types";
import { _normalizeDenAdjustment, _normalizeDenCreation, Den } from "../../Den/Den";
import { addGasForBaseRateUpdate, addGasForPotentialListTraversal } from "../../gasHelpers";
import { DEFAULT_SLIPPAGE_TOLERANCE } from "../../constants";
import { MultiCollateralHintHelpers } from "../MultiCollateralHintHelpers/Contract";
import { Collateral } from "../Collateral/Contract";
import { DenManager } from "../DenManager/Contract";
import { DenManagerGetters } from "../DenManagerGetters/Contract";
import { assertAddress, requireAddress } from "../../utils";
import BaseCollateralVaultAbi from "../../abi/BaseCollateralVaultAbi";

export class CollVaultRouter {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  private readonly denManagerGetters: DenManagerGetters;
  private readonly multiCollateralHintHelpers: MultiCollateralHintHelpers;

  private readonly protocol: Hex;
  constructor(connection: BeraBorrowConnection, protocol?: Hex) {
    this.connection = connection;
    this.protocol = protocol ?? this.connection.addresses.defaultProtocol;
    this.contractAddress = this.connection.addresses.protocols[this.protocol].collVaultRouter;
    this.denManagerGetters = new DenManagerGetters(this.connection, this.protocol);
    this.multiCollateralHintHelpers = new MultiCollateralHintHelpers(this.connection, this.protocol);
    assertAddress(this.contractAddress);
    assertAddress(this.protocol);
  }
  private async _previewDeposit(amount: bigint, vaultAddress: Hex, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: vaultAddress,
      abi: BaseCollateralVaultAbi,
      functionName: "previewDeposit",
      args: [amount],
      account: address,
      ...overrides,
    });
  }
  /**
   * Open den with Vault
   *
   */
  async openDenVault(
    dmAddr: Hex,
    collVault: Hex,
    address: Hex,
    params: DenCreationParams<bigint>,
    maxBorrowingRate: bigint,
    minSharesMinted: bigint,
    native: boolean,
    wrapperHookAddress?: Hex,
    overrides?: TransactionOptions
  ) {
    const normalizedParams = _normalizeDenCreation(params);
    const { depositCollateral, borrowNECT } = normalizedParams;

    const collateralAddr = this.connection.addresses.denManagers[dmAddr].collateral;
    const collateralToken = new Collateral(
      this.connection,
      collateralAddr,
      this.connection.addresses.collateralTokens[collateralAddr].ticker,
      this.connection.addresses.collateralTokens[collateralAddr].decimals
    );
    const allowanceAddress = wrapperHookAddress ?? this.contractAddress;
    const [collateralAllowance, _collIndex, collateralShares] = await Promise.all([
      collateralToken.allowance(address, allowanceAddress),
      this.denManagerGetters.getCollVaultIndex(collVault),
      this._previewDeposit(depositCollateral, collVault),
    ]);
    if (_collIndex === -1) {
      throw Error("Incorrect Collateral Address");
    }

    if (collateralAllowance && depositCollateral && collateralAllowance < depositCollateral) {
      throw Error("ERC20: insufficient-allowance");
    }

    const currentBorrowingRate = maxBorrowingRate - DEFAULT_SLIPPAGE_TOLERANCE;
    const newDen = Den.create({ depositCollateral: collateralShares, borrowNECT }, this.connection.liquidationReserve, currentBorrowingRate);
    const multiCollateralHintHelpers = new MultiCollateralHintHelpers(this.connection, this.protocol);
    const hints = await multiCollateralHintHelpers.findHintsForNominalCollateralRatio(dmAddr, newDen._nominalCollateralRatio, address);
    const _preDeposit = "" as Hex;
    const OpenDenVaultParams = {
      denManager: dmAddr,
      collVault: collVault,
      _maxFeePercentage: maxBorrowingRate,
      _debtAmount: borrowNECT,
      _collAssetToDeposit: depositCollateral,
      _upperHint: hints[0],
      _lowerHint: hints[1],
      _minSharesMinted: minSharesMinted,
      _collIndex: BigInt(_collIndex),
      _preDeposit: _preDeposit, // Bytes as a hex string
    };
    if (overrides?.gas === undefined) {
      const denRecreate = Den.recreate(newDen, this.connection.liquidationReserve, currentBorrowingRate);
      if (denRecreate === undefined) {
        throw new Error(`Den's unable to recreate `);
      }

      const gas = await this.connection.publicClient.estimateContractGas({
        address: this.contractAddress,
        abi: CollVaultRouterAbi,
        functionName: "openDenVault",
        args: [OpenDenVaultParams],
        type: "eip1559",
        ...overrides,
        account: address,
        value: native ? (depositCollateral ?? 0n) : undefined,
      });

      const gasLimit = addGasForBaseRateUpdate(addGasForPotentialListTraversal(gas));

      overrides = { ...overrides, gas: gasLimit };
    }
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: CollVaultRouterAbi,
      functionName: "openDenVault",
      args: [OpenDenVaultParams],
      type: "eip1559",
      ...overrides,
      account: address,
      value: native ? (depositCollateral ?? 0n) : undefined,
    });
  }

  /**
   * Adjust an existing Den by depositing/withdrawing collateral and/or borrowing Debt.
   * @param dmAddr Den Manager Address
   * @param collVault Address of the Collaterals Vault
   * @param isDebtIncrease if the new Nect will be minted
   * @param params DenCreationParameters
   * @param maxBorrowingRate Max borrowing rate
   */
  async adjustDenVault(
    dmAddr: Hex,
    collVault: Hex,
    address: Hex,
    isDebtIncrease: boolean,
    params: DenAdjustmentParams<bigint>,
    maxBorrowingRate: bigint,
    minSharesMinted: bigint,
    minSharesBurned: bigint,
    native: boolean,
    wrapperHookAddress?: Hex,
    overrides?: TransactionOptions
  ) {
    const normalizedParams = _normalizeDenAdjustment(params);
    const { depositCollateral, withdrawCollateral, borrowNECT, repayNECT } = normalizedParams;
    const denManager = new DenManager(this.connection, dmAddr);

    const collateralAddr = this.connection.addresses.denManagers[dmAddr].collateral;
    const collateralToken = new Collateral(
      this.connection,
      collateralAddr,
      this.connection.addresses.collateralTokens[collateralAddr].ticker,
      this.connection.addresses.collateralTokens[collateralAddr].decimals
    );
    const allowanceAddress = wrapperHookAddress ?? this.contractAddress;

    const [den, collateralAllowance, _collIndex, collateralShares] = await Promise.all([
      denManager.getDen(address),
      depositCollateral && collateralToken.allowance(address, allowanceAddress),
      this.denManagerGetters.getCollVaultIndex(collVault),
      depositCollateral ? this._previewDeposit(depositCollateral, collVault) : undefined,
    ]);
    if (_collIndex === -1) {
      throw Error("Incorrect Collateral Address");
    }
    if (collateralAllowance && depositCollateral && collateralAllowance < depositCollateral) {
      throw Error("ERC20: insufficient-allowance");
    }
    const currentBorrowingRate = maxBorrowingRate - DEFAULT_SLIPPAGE_TOLERANCE;
    const adjustedDen = den.adjust(depositCollateral && collateralShares ? { ...normalizedParams, depositCollateral: collateralShares } : normalizedParams, currentBorrowingRate);
    let hints = await this.multiCollateralHintHelpers.findHintsForNominalCollateralRatio(dmAddr, adjustedDen._nominalCollateralRatio, address);
    const _preDeposit = "" as Hex;

    if (overrides?.gas === undefined) {
      const decayedBorrowingRate = maxBorrowingRate;

      const decayedDen = den.adjust(normalizedParams, decayedBorrowingRate);

      const denAdjustment = den.adjustTo(decayedDen, this.connection.liquidationReserve, currentBorrowingRate);

      if (denAdjustment === undefined) {
        throw new Error(`Den's adjustment is Invalid `);
      }

      const paramsGas = {
        denManager: dmAddr, // Address of the den manager
        collVault: collVault, // Address of the collateral vault
        _maxFeePercentage: BigInt(maxBorrowingRate), // Max borrowing rate as uint256
        _collAssetToDeposit: depositCollateral ?? 0n, // Amount of collateral to deposit, or 0n if not provided
        _collWithdrawal: withdrawCollateral ?? 0n, // Amount of collateral to withdraw, or 0n if not provided
        _debtChange: borrowNECT ?? repayNECT ?? 0n, // Debt change (borrow or repay)
        _isDebtIncrease: isDebtIncrease, // Boolean for whether debt is increasing
        _upperHint: hints[0], // Upper hint address from hints array
        _lowerHint: hints[1], // Lower hint address from hints array
        unwrap: true, // Set unwrap to true
        _minSharesMinted: BigInt(minSharesMinted), // Minimum shares to be minted as uint256
        _minAssetsWithdrawn: BigInt(minSharesBurned), // Minimum assets to be withdrawn (shares burned) as uint256
        _collIndex: BigInt(_collIndex), // Collateral index as uint256
        _preDeposit,
      };

      const gas = await this.connection.publicClient.estimateContractGas({
        address: this.contractAddress,
        abi: CollVaultRouterAbi,
        functionName: "adjustDenVault",
        args: [paramsGas],
        type: "eip1559",
        ...overrides,
        account: address,
        value: native ? (depositCollateral ?? 0n) : undefined,
      });
      overrides = { ...overrides, gas: addGasForPotentialListTraversal(gas) };
    }
    const paramsSimulate = {
      denManager: dmAddr, // Address of the den manager
      collVault: collVault, // Address of the collateral vault
      _maxFeePercentage: BigInt(maxBorrowingRate), // Max borrowing rate as uint256
      _collAssetToDeposit: depositCollateral ?? 0n, // Amount of collateral to deposit, or 0n if not provided
      _collWithdrawal: withdrawCollateral ?? 0n, // Amount of collateral to withdraw, or 0n if not provided
      _debtChange: borrowNECT ?? repayNECT ?? 0n, // Debt change (borrow or repay)
      _isDebtIncrease: isDebtIncrease, // Boolean for whether debt is increasing
      _upperHint: hints[0], // Upper hint address from hints array
      _lowerHint: hints[1], // Lower hint address from hints array
      unwrap: true, // Set unwrap to true
      _minSharesMinted: BigInt(minSharesMinted), // Minimum shares to be minted as uint256
      _minAssetsWithdrawn: BigInt(minSharesBurned), // Minimum assets to be withdrawn (shares burned) as uint256
      _collIndex: BigInt(_collIndex), // Collateral index as uint256
      _preDeposit,
    };
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: CollVaultRouterAbi,
      functionName: "adjustDenVault",
      args: [paramsSimulate],
      type: "eip1559",
      ...overrides,
      account: address,
      value: native ? (depositCollateral ?? 0n) : undefined,
    });
  }

  async closeDen(dmAddr: Hex, collVault: Hex, address: Hex, minAssetsWithdrawn: bigint, overrides?: TransactionOptions) {
    const _collIndex = await this.denManagerGetters.getCollVaultIndex(collVault);
    if (_collIndex === -1) {
      throw Error("Incorrect Collateral Address");
    }
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: CollVaultRouterAbi,
      functionName: "closeDenVault",
      args: [dmAddr, collVault, minAssetsWithdrawn, BigInt(_collIndex), true],
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }

  async claimCollateralSurplus(dmAddr: Hex, collVault: Hex, address: Hex, minAssetsWithdrawn: bigint, overrides?: TransactionOptions) {
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: CollVaultRouterAbi,
      functionName: "claimCollateralRouter",
      args: [dmAddr, collVault, address, minAssetsWithdrawn],
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }

  async redeemCollateralRouter(
    dmAddr: Hex,
    collVault: Hex,
    address: Hex,
    _debtAmount: bigint,
    minSharesBurned: bigint,
    minAssetsWithdrawn: bigint,
    _firstRedemptionHint: Hex,
    _upperPartialRedemptionHint: Hex,
    _lowerPartialRedemptionHint: Hex,
    _partialRedemptionHintNICR: bigint,
    _maxIterations: bigint,
    _maxFeePercentage: bigint,
    overrides?: TransactionOptions
  ) {
    const _collIndex = await this.denManagerGetters.getCollVaultIndex(collVault);
    if (_collIndex === -1) {
      throw Error("Incorrect Collateral Address");
    }
    const paramsSimulate = {
      denManager: dmAddr,
      collVault: collVault,
      _debtAmount: _debtAmount,
      _firstRedemptionHint: _firstRedemptionHint,
      _upperPartialRedemptionHint: _upperPartialRedemptionHint,
      _lowerPartialRedemptionHint: _lowerPartialRedemptionHint,
      _partialRedemptionHintNICR: _partialRedemptionHintNICR,
      _maxIterations: _maxIterations,
      _maxFeePercentage: _maxFeePercentage,
      _minSharesWithdrawn: minSharesBurned,
      minAssetsWithdrawn: minAssetsWithdrawn,
      collIndex: BigInt(_collIndex),
      unwrap: true,
    };
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: CollVaultRouterAbi,
      functionName: "redeemCollateralVault",
      args: [paramsSimulate],
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }

  async previewRedeemUnderlying(collVault: Hex, address: Hex, shares: bigint, overrides?: TransactionOptions): Promise<{ contractAddress: Hex; balance: bigint }[]> {
    const assets = await this._previewRedeemUnderlying(collVault, address, shares, overrides);
    const totalAssets = await Promise.all(
      assets[0].map(async (item, index) => {
        if (this.connection.addresses.vaults[item]) {
          const unwrappedAssets = await this.previewRedeemUnderlying(item, address, assets[1][index], overrides);
          return unwrappedAssets;
        }
        return { contractAddress: item, balance: assets[1][index] };
      })
    );
    const mergedAssets = new Map<Hex, bigint>();
    totalAssets.flat().forEach(({ contractAddress, balance }) => {
      if (mergedAssets.has(contractAddress)) {
        mergedAssets.set(contractAddress, mergedAssets.get(contractAddress)! + balance);
      } else {
        mergedAssets.set(contractAddress, balance);
      }
    });
    return Array.from(mergedAssets, ([contractAddress, balance]) => ({ contractAddress, balance }));
  }

  private async _previewRedeemUnderlying(collVault: Hex, address: Hex, shares: bigint, overrides?: TransactionOptions) {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: CollVaultRouterAbi,
      functionName: "previewRedeemUnderlying",
      args: [collVault, shares],
      ...overrides,
      account: address,
    });
  }
}
