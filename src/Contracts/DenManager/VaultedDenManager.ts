import { Hex, SimulateContractReturnType } from "viem";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { DenManager } from "./Contract";
import { CollateralVault } from "../CollateralVaults/Contract";
import { CollVaultRouter } from "../CollVaultRouter/Contract";
import { SCALING_FACTOR } from "../../constants";
import { requireAddress } from "../../utils";
import DenManagerAbi from "../../abi/DenManagerAbi";
import { addGasForBaseRateUpdate } from "../../gasHelpers";

export class VaultedDenManager extends DenManager {
  private vault: CollateralVault;
  private collVaultRouter: CollVaultRouter;

  constructor(connection: BeraBorrowConnection, contractAddress: Hex, vaultAddress: Hex, protocol?: Hex) {
    super(connection, contractAddress, protocol);
    this.vault = new CollateralVault(connection, vaultAddress);
    this.collVaultRouter = new CollVaultRouter(connection);
  }

  /**
   * Get CollateralVault class instance
   */
  getCollateralVault(): CollateralVault {
    return this.vault;
  }
  /**
   * claimable any Surplus Collateral that is claimable
   *
   */
  override async claimCollateralSurplusBalances(address?: Hex, overrides?: TransactionOptions): Promise<SimulateContractReturnType> {
    address = requireAddress(this.connection, overrides);
    const amount = await this.getCollateralSurplusBalances();
    const sharesBurned = await this.vault.previewDeposit(amount);
    const minSharesBurned = sharesBurned - (sharesBurned * (SCALING_FACTOR / 20n)) / SCALING_FACTOR;
    return this.collVaultRouter.claimCollateralSurplus(
      this.contractAddress,
      this.vault.contractAddress,
      address,
      minSharesBurned,
      overrides
    ) as unknown as Promise<SimulateContractReturnType>;
  }

  override async getCollateralSurplusBalances(address?: Hex, overrides?: TransactionOptions): Promise<bigint> {
    address = requireAddress(this.connection, overrides);
    const balance = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "surplusBalances",
      args: [address],
      ...overrides,
      account: address,
    });
    if (balance === 0n) {
      return 0n;
    }
    return this.vault.convertToAssets(balance, address);
  }

  /**
   * Redeem Debt to Collateral Token of Den Manager at face value.
   *
   * @param amount - Amount of Debt to be redeemed.
   * @param maxRedemptionRate - Maximum acceptable
   *
   * @remarks
   * If `maxRedemptionRate` is omitted, the current redemption rate (based on `amount`) plus 0.1%
   * is used as maximum acceptable rate.
   */
  override async redeemDebt(amount: bigint, maxRedemptionRate: bigint, address?: Hex, overrides?: TransactionOptions): Promise<SimulateContractReturnType> {
    address ??= requireAddress(this.connection);
    const MAX_REDEEM_ITERATIONS = 10000n;
    const attemptedNECTAmount = amount;
    const price = await this.fetchPrice();
    const [truncatedAmount, firstRedemptionHint, ...partialHints] = await this.multiCollateralHintHelpers.findRedemptionHints(
      this.contractAddress,
      attemptedNECTAmount,
      price,
      MAX_REDEEM_ITERATIONS,
      overrides
    );
    maxRedemptionRate = maxRedemptionRate > (SCALING_FACTOR / 100n) * 5n ? (SCALING_FACTOR / 100n) * 5n : maxRedemptionRate;
    if (truncatedAmount === 0n) {
      throw new Error(`redeemDebt: amount too low to redeem `);
    }
    if (price === 0n) {
      throw new Error("Invalid collateral price");
    }
    const grossShares = (truncatedAmount * SCALING_FACTOR) / price;
    const netShares = grossShares - (grossShares * maxRedemptionRate) / SCALING_FACTOR;
    const minSharesBurned = netShares - (netShares * (SCALING_FACTOR / 100n) * 5n) / SCALING_FACTOR;
    const minAssetsWithdrawn = await this.vault.convertToAssets(minSharesBurned);

    let simulate = (await this.collVaultRouter.redeemCollateralRouter(
      this.contractAddress,
      this.vault.contractAddress,
      address,
      truncatedAmount,
      minSharesBurned,
      minAssetsWithdrawn,
      firstRedemptionHint,
      ...partialHints,
      MAX_REDEEM_ITERATIONS,
      maxRedemptionRate,
      overrides
    )) as unknown as SimulateContractReturnType;

    if (simulate.request.gas) {
      simulate.request.gas = addGasForBaseRateUpdate(simulate.request.gas);
    }
    return simulate;
  }
}
