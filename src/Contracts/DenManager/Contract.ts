import DenManagerAbi from "../../abi/DenManagerAbi";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { userDenStatusFrom } from "../../Den/helpers";
import { BackendDenStatus } from "../../Den/types";
import { MultiCollateralHintHelpers } from "../MultiCollateralHintHelpers/Contract";
import { addGasForBaseRateUpdate } from "../../gasHelpers";
import { assertAddress, requireAddress } from "../../utils";
import { Den, DenWithPendingRedistribution, UserDen } from "../../Den/Den";
import { SortedDens } from "../SortedDens/Contract";
import { Hex, SimulateContractReturnType } from "viem";
import BeraborrowCoreAbi from "../../abi/BeraborrowCoreAbi";

export class DenManager {
  protected readonly connection: BeraBorrowConnection;
  private readonly sortedDens: SortedDens;
  readonly contractAddress: Hex;
  protected readonly multiCollateralHintHelpers: MultiCollateralHintHelpers;
  readonly protocol: Hex;

  constructor(connection: BeraBorrowConnection, contractAddress: Hex, protocol?: Hex) {
    this.connection = connection;
    this.protocol = protocol ?? this.connection.addresses.defaultProtocol;
    this.contractAddress = contractAddress;
    this.sortedDens = new SortedDens(connection, contractAddress);
    this.multiCollateralHintHelpers = new MultiCollateralHintHelpers(this.connection, this.protocol);
    assertAddress(this.contractAddress);
    assertAddress(this.protocol);
  }

  /**
   * Get sortedDen class instance
   */
  getSortedDens(): SortedDens {
    return this.sortedDens;
  }
  /**
   * Gets all the details for the den in a single multicall
   * @param address user address
   * @param overrides
   * @returns the users den, totals for the denManager and collateral price
   */
  async getDenDetails(address: Hex, overrides?: TransactionOptions) {
    const [_den, status, _systemBalances, mcr, ccr] = await this.connection.publicClient.multicall({
      contracts: [
        { address: this.contractAddress, abi: DenManagerAbi, functionName: "getEntireDebtAndColl", args: [address] },
        { address: this.contractAddress, abi: DenManagerAbi, functionName: "getDenStatus", args: [address] },
        { address: this.contractAddress, abi: DenManagerAbi, functionName: "getEntireSystemBalances", args: [] },
        { address: this.contractAddress, abi: DenManagerAbi, functionName: "MCR", args: [] },
        { address: this.connection.addresses.protocols[this.protocol].beraborrowCore, abi: BeraborrowCoreAbi, functionName: "CCR" },
      ],
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
      account: address,
      ...overrides,
    });
    const [totalColl, totalDebt, price] = _systemBalances;
    const [debt, coll] = _den;
    const statusNum = Number(status.toString()) as BackendDenStatus;
    const densTotal = new Den(totalColl, totalDebt);
    const den = statusNum === BackendDenStatus.active ? new UserDen(address, userDenStatusFrom(statusNum), coll, debt) : new UserDen(address, userDenStatusFrom(statusNum));

    return { den, densTotal, price, mcr, ccr };
  }
  async getDenBeforeRedistribution(address?: Hex, overrides?: TransactionOptions): Promise<DenWithPendingRedistribution> {
    address = requireAddress(this.connection, overrides);
    const [den, snapshot] = await Promise.all([
      this.connection.publicClient.readContract({
        address: this.contractAddress,
        abi: DenManagerAbi,
        functionName: "Dens",
        args: [address],
        ...overrides,
        account: address,
      }),
      this.connection.publicClient.readContract({
        address: this.contractAddress,
        abi: DenManagerAbi,
        functionName: "rewardSnapshots",
        args: [address],
        ...overrides,
        account: address,
      }),
    ]);

    const [snapshotCollateral, snapshotDebt] = snapshot;
    const [debt, coll, stake, status] = den;
    const statusNum = Number(status.toString()) as BackendDenStatus;
    if (statusNum === BackendDenStatus.active) {
      return new DenWithPendingRedistribution(address, userDenStatusFrom(statusNum), coll, debt, stake, new Den(snapshotCollateral, snapshotDebt));
    } else {
      return new DenWithPendingRedistribution(address, userDenStatusFrom(statusNum));
    }
  }
  /**
   * Get the current state of a Den.
   *
   * @param address - Address that owns the Den.
   */
  async getDen(address?: Hex, overrides?: TransactionOptions): Promise<Den> {
    address = address ?? requireAddress(this.connection, overrides);
    const [coll, debt] = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "getDenCollAndDebt",
      args: [address],
      ...overrides,
      account: address,
    });

    return new Den(coll, debt);
  }

  /**
   * Get the total amount of collateral and debt in the BeraBorrow system.
   */
  async getTotal(overrides?: TransactionOptions): Promise<Den> {
    const address = requireAddress(this.connection, overrides);

    const [totalCollateral, totalDebt] = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "getEntireSystemBalances",
      args: [],
      ...overrides,
      account: address,
    });

    return new Den(totalCollateral, totalDebt);
  }

  /**
   * Get any Surplus Collateral that is claimable
   *
   */
  async getCollateralSurplusBalances(address?: Hex, overrides?: TransactionOptions): Promise<bigint> {
    address = requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "surplusBalances",
      args: [address],
      ...overrides,
      account: address,
    });
  }
  /**
   * claimable any Surplus Collateral that is claimable
   *
   */
  async claimCollateralSurplusBalances(address?: Hex, overrides?: TransactionOptions): Promise<SimulateContractReturnType> {
    address = requireAddress(this.connection, overrides);
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "claimCollateral",
      args: [address, address],
      type: "eip1559",
      ...overrides,
      account: address,
    }) as unknown as Promise<SimulateContractReturnType>;
  }
  async getInterestRate(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "interestRate",
      args: [],
      ...overrides,
      account: address,
    });
  }
  /**
   * Get the total amount Dens .
   */
  async getTotalDens(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "getDenOwnersCount",
      args: [],
      ...overrides,
      account: address,
    });
  }
  async fetchPrice(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "fetchPrice",
      args: [],
      ...overrides,
      account: address,
    });
  }
  /**
   * Checks if Redeem is in bootstrap Period
   */
  redeemBootstrapPeriod() {
    const currentTimeStamp = new Date().getTime() / 1000;
    const endTimeStamp = this.connection.deploymentTimestamp + this.connection.bootstrapPeriod;
    return { redeemable: currentTimeStamp < endTimeStamp, redeemTimeStamp: endTimeStamp };
  }

  /**
   * Redeem Debt to Collateral Token of Den Manager at face value.
   *
   * @param amount - Amount of Debt to be redeemed.
   * @param maxRedemptionRate - Maximum acceptable
   *
   * @remarks
   * If `maxRedemptionRate` is omitted, the current redemption rate (based on `amount`) plus 0.1%
   * is used as maximum acceptable rate.
   */
  async redeemDebt(amount: bigint, maxRedemptionRate: bigint, address?: Hex, overrides?: TransactionOptions): Promise<SimulateContractReturnType> {
    address ??= requireAddress(this.connection);
    const MAX_REDEEM_ITERATIONS = 10000n;
    const attemptedNECTAmount = amount;
    const price = await this.fetchPrice();
    const [truncatedAmount, firstRedemptionHint, ...partialHints] = await this.multiCollateralHintHelpers.findRedemptionHints(
      this.contractAddress,
      attemptedNECTAmount,
      price,
      MAX_REDEEM_ITERATIONS,
      overrides
    );

    if (truncatedAmount === 0n) {
      throw new Error(`redeemDebt: amount too low to redeem `);
    }

    let simulate = (await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "redeemCollateral",
      args: [truncatedAmount, firstRedemptionHint, ...partialHints, MAX_REDEEM_ITERATIONS, maxRedemptionRate],
      type: "eip1559",
      ...overrides,
      account: address,
    })) as unknown as SimulateContractReturnType;
    if (simulate.request.gas) {
      simulate.request.gas = addGasForBaseRateUpdate(simulate.request.gas);
    }
    return simulate;
  }

  /**
	 * gets the Collateral token Address

	 * @returns Collateral Token Address
	 */
  async getCollateralToken(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "collateralToken",
      args: [],
      ...overrides,
      account: address,
    });
  }

  /**
   * @notice Get borrowing Mint fee rate
   * @return current borrowing Mint fee rate
   */
  async getBorrowingMintFeeRate(overrides?: TransactionOptions) {
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "getBorrowingRateWithDecay",
      args: [],
      ...overrides,
    });
  }

  /**
   * @notice Get redemption fee rate
   * @return current redemption fee rate
   */
  async getRedemptionFeeRate(overrides?: TransactionOptions) {
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "getRedemptionRate",
      args: [],
      ...overrides,
    });
  }
  /**
   * @notice Get redemption fee rate
   * @return current redemption fee rate
   */
  async getRedemptionFeeRateWithDecay(overrides?: TransactionOptions) {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "getRedemptionRateWithDecay",
      args: [],
      ...overrides,
    });
  }
  async getMaxDebt(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "maxSystemDebt",
      args: [],
      ...overrides,
      account: address,
    });
  }

  async getDefaultedDebt(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "defaultedDebt",
      args: [],
      ...overrides,
      account: address,
    });
  }
  /**
   * @notice Get function for sunsetting
   * @returns bool
   */
  async isSunsetting() {
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "sunsetting",
      args: [],
    });
  }

  /**
   * @notice Get den status
   * @returns Status enum
   */
  async getDenStatus(borrower: Hex): Promise<BackendDenStatus> {
    const res = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "getDenStatus",
      args: [borrower],
    });
    return Number(res);
  }

  /**
   * @notice If den is paused or not
   * @returns bool
   */
  async isPaused(): Promise<boolean> {
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "paused",
      args: [],
    });
  }

  /**
   * MCR
   */
  async getMCR(): Promise<bigint> {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerAbi,
      functionName: "MCR",
      args: [],
    });
  }
}
