import { Hex } from "viem";
import <PERSON><PERSON>en<PERSON><PERSON> from "../../abi/<PERSON>ollenAbi";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { assertAddress, requireAddress } from "../../utils";

export class SPollen {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: `0x${string}`;
  readonly ticker: string;
  readonly decimals: number;
  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.contractAddress = this.connection.addresses.sPollenToken.contractAddress;
    this.ticker = this.connection.addresses.sPollenToken.ticker;
    this.decimals = this.connection.addresses.sPollenToken.decimals;
    assertAddress(this.contractAddress);
  }

  /**
   * Deposit (wrap) POLLEN tokens to receive sPOLLEN
   *
   * @param amount - Amount of POLLEN tokens to deposit
   * @param address - Address that will receive the sPOLLEN tokens
   * @returns Simulation result
   */
  async deposit(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: SPollenAbi,
      functionName: "depositFor",
      args: [address, amount],
      account: address,
      ...overrides,
    });
  }

  /**
   * Withdraw (unwrap) sPOLLEN tokens to receive POLLEN
   *
   * @param amount - Amount of sPOLLEN tokens to withdraw
   * @param address - Address that will receive the POLLEN tokens
   * @returns Simulation result
   */
  async withdraw(amount: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: SPollenAbi,
      functionName: "withdrawTo",
      args: [address, amount],
      account: address,
      ...overrides,
    });
  }

  /**
   * Get the amount of sPOLLEN held by an address.
   *
   * @param address - Address whose balance should be retrieved.
   * @returns The balance of sPOLLEN for the address
   */
  async getBalance(address?: Hex, overrides?: TransactionOptions): Promise<bigint> {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: SPollenAbi,
      functionName: "balanceOf",
      args: [address],
      ...overrides,
      account: address,
    });
  }

  /**
   * Get the total supply of sPOLLEN tokens.
   *
   * @returns The total supply of sPOLLEN tokens
   */
  async totalSupply(overrides?: TransactionOptions): Promise<bigint> {
    const address = requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: SPollenAbi,
      functionName: "totalSupply",
      args: [],
      ...overrides,
      account: address,
    });
  }
}
