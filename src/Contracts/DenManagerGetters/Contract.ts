import { BeraBorrowConnection, ContractCall, TransactionOptions } from "../../types";
import DenManagerGettersAbi from "../../abi/DenManagerGettersAbi";
import { assertAddress, requireAddress } from "../../utils";
import { SCALING_FACTOR } from "../../constants";
import DenManagerAbi from "../../abi/DenManagerAbi";
import { Hex } from "viem";

export class DenManagerGetters {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection, protocol: Hex) {
    this.connection = connection;
    this.contractAddress = this.connection.addresses.protocols[protocol]["denManagerGetters"];
    assertAddress(this.contractAddress);
  }
  /**
   *  Get all Collaterals and their associated Den Managers
   * @returns an Array of Collateral Addresses and an Array or associated Den Managers Addresses
   */
  async _getAllCollateralsAndDenManagers(address?: Hex, overrides?: TransactionOptions): Promise<readonly { collateral: Hex; denManagers: readonly Hex[] }[]> {
    address ??= requireAddress(this.connection);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DenManagerGettersAbi,
      functionName: "getAllCollateralsAndDenManagers",
      args: [],
      ...overrides,
      account: address,
    });
  }
  async getCollVaultIndex(collVault: Hex) {
    const CollateralsAndDenManagers = await this._getAllCollateralsAndDenManagers();
    return CollateralsAndDenManagers.findIndex((item) => item.collateral === collVault);
  }

  async getTotalDens(address?: Hex, overrides?: TransactionOptions) {
    address = requireAddress(this.connection);
    const CollateralsAndDenManagers = await this._getAllCollateralsAndDenManagers();
    const denManagers: Hex[] = [];
    CollateralsAndDenManagers.map((curr) => denManagers.push(...curr.denManagers.filter((item) => this.connection.addresses.denManagers?.[item] !== undefined)));
    const contractCalls: ContractCall[] = denManagers.flatMap((item) => {
      return [{ address: item, abi: DenManagerAbi, functionName: "getDenOwnersCount", args: [], ...overrides }];
    });
    const result = (await this.connection.publicClient.multicall({
      contracts: contractCalls,
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
    })) as unknown as Array<bigint>;
    let counter = 0n;
    for (let index = 0; index < result.length; index++) {
      counter += result[index];
    }
    return counter;
  }

  /**
   *  Get all denManagers which are in recovery mode
   * @returns Array of den manager address
   */
  async getTotalDensTVL(address?: Hex, overrides?: TransactionOptions) {
    address = requireAddress(this.connection);
    const CollateralsAndDenManagers = await this._getAllCollateralsAndDenManagers();
    const denManagers: Hex[] = [];
    CollateralsAndDenManagers.map((curr) => denManagers.push(...curr.denManagers.filter((item) => this.connection.addresses.denManagers?.[item] !== undefined)));
    const contractCalls: ContractCall[] = denManagers.flatMap((item) => {
      return [
        { address: item, abi: DenManagerAbi, functionName: "getTotalActiveCollateral", args: [], ...overrides },
        { address: item, abi: DenManagerAbi, functionName: "fetchPrice", args: [], ...overrides },
      ];
    });
    const result = (await this.connection.publicClient.multicall({
      contracts: contractCalls,
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
    })) as unknown as Array<bigint>;
    let totalCollateralFiat = 0n;
    for (let index = 0; index < result.length / 2; index++) {
      let coll = result[index * 2];
      let price = result[index * 2 + 1];
      totalCollateralFiat += (coll * price) / SCALING_FACTOR;
    }
    return totalCollateralFiat;
  }
  /**
   *  Get tvls of user
   * @param address user address
   * @returns tvl bigint
   */

  async getTVLOfUserDens(address?: Hex, overrides?: TransactionOptions) {
    address = requireAddress(this.connection);
    const CollateralsAndDenManagers = await this._getAllCollateralsAndDenManagers();
    const denManagers: Hex[] = [];
    CollateralsAndDenManagers.map((curr) => denManagers.push(...curr.denManagers.filter((item) => this.connection.addresses.denManagers?.[item] !== undefined)));

    const contractCalls: ContractCall[] = denManagers.flatMap((item) => {
      return [
        { address: item, abi: DenManagerAbi, functionName: "getDenCollAndDebt", args: [address], ...overrides },
        { address: item, abi: DenManagerAbi, functionName: "fetchPrice", args: [], ...overrides },
      ];
    });
    const result = (await this.connection.publicClient.multicall({
      contracts: contractCalls,
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
    })) as unknown as Array<[bigint, bigint] | bigint>;
    let tvl = 0n;
    for (let index = 0; index < result.length / 2; index++) {
      let coll = result[index * 2];
      coll = Array.isArray(coll) ? (coll?.[0] ?? 0n) : 0n;
      let price = result[index * 2 + 1];
      price = typeof price === "bigint" ? price : 0n;

      tvl += (coll * price) / SCALING_FACTOR;
    }
    return tvl;
  }
}
