import DebtToken from "../../abi/DebtTokenAbi";
import { assertAddress, requireAddress } from "../../utils";
import { BeraBorrowConnection, TransactionOptions } from "../../types";

export class Debt {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: `0x${string}`;
  readonly ticker: string;
  readonly decimals: number;
  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.contractAddress = this.connection.addresses["debtToken"].contractAddress;
    this.ticker = this.connection.addresses.debtToken.ticker;
    this.decimals = this.connection.addresses.debtToken.decimals;
    assertAddress(this.contractAddress);
  }
  /**
   * Get the amount of Nect held by an address.
   *
   * @param address - Address whose balance should be retrieved.
   */
  async getBalance(address?: `0x${string}`, overrides?: TransactionOptions): Promise<bigint> {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DebtToken,
      functionName: "balanceOf",
      args: [address],
      ...overrides,
      account: address,
    });
  }
  /**
   * transfer Debt tokens to an address.
   *
   * @param receiver - Address of receipient.
   * @param amount - Amount of POLLEN to send.
   *
   */
  async transfer(receiver: `0x${string}`, amount: bigint, address?: `0x${string}`, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: DebtToken,
      functionName: "transfer",
      args: [receiver, amount],
      account: address,
      type: "eip1559",
      ...overrides,
    });
  }

  /**
   * total debt supply.
   */
  async totalSupply() {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: DebtToken,
      functionName: "totalSupply",
      args: [],
    });
  }
}
