import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { Hex } from "viem";
import PermissionlessPSMAbi from "../../abi/PermissionlessPSMAbi";
import { assertAddress } from "../../utils";

export class PsmBond {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  readonly collaterals: Hex[];

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.contractAddress = this.contractAddress = this.connection.addresses.psmBond;
    this.collaterals = this.connection.addresses.psmBondCollaterals;
    assertAddress(this.contractAddress);
    this.collaterals.map((item) => assertAddress(item));
  }
  async redeem(tokenAddress: Hex, amount: bigint, maxFeePercentage: bigint, address: Hex, overrides?: TransactionOptions) {
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: PermissionlessPSMAbi,
      functionName: "redeem",
      args: [tokenAddress, amount, address, Number(maxFeePercentage)],
      account: address,
      type: "eip1559",
      ...overrides,
    });
  }
  async previewRedeem(tokenAddress: Hex, amount: bigint, maxFeePercentage: bigint, overrides?: TransactionOptions) {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: PermissionlessPSMAbi,
      functionName: "previewRedeem",
      args: [tokenAddress, amount, Number(maxFeePercentage)],
      ...overrides,
    });
  }

  async maxRedeem(tokenAddress: Hex, overrides?: TransactionOptions) {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: PermissionlessPSMAbi,
      functionName: "nectMinted",
      args: [tokenAddress],
      ...overrides,
    });
  }

  async isPaused(overrides?: TransactionOptions) {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: PermissionlessPSMAbi,
      functionName: "paused",
      args: [],
      ...overrides,
    });
  }

  async getFee(overrides?: TransactionOptions) {
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: PermissionlessPSMAbi,
      functionName: "DEFAULT_FEE",
      args: [],
      ...overrides,
    });
  }
}
