import { BeraBorrowConnection, TransactionOptions } from "../../types";
import LiquidStabilityPoolAbi from "../../abi/LiquidStabilityPoolAbi";
import LSPGettersAbi from "../../abi/LSPGettersAbi";

import { StabilityDeposit } from "./StabilityDeposit";
import { assertAddress, requireAddress } from "../../utils";
import { SCALING_FACTOR, SECONDS_IN_YEAR } from "../../constants";
import { Hex } from "viem";
import { CollVaultRouter } from "../CollVaultRouter/Contract";
import { CollateralVault } from "../CollateralVaults/Contract";
import { SubgraphClient } from "../../subgraph";
import BeraborrowCoreAbi from "../../abi/BeraborrowCoreAbi";

export class StabilityPool {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  readonly getterAddress: Hex;
  private readonly collVaultRouter: CollVaultRouter;
  private readonly subgraphClient: SubgraphClient;
  private extraAssets: readonly Hex[] = [];
  private collaterals: readonly Hex[] = [];
  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
    this.contractAddress = this.connection.addresses.stabilityPool;
    this.getterAddress = this.connection.addresses.LSPGetters;
    this.collVaultRouter = new CollVaultRouter(this.connection);
    this.subgraphClient = new SubgraphClient(this.connection);
    assertAddress(this.contractAddress);
    assertAddress(this.getterAddress);
  }
  /**
   * Get the current state of a Stability Deposit.
   *
   * @param address - Address that owns the Stability Deposit.
   */
  async getStabilityDeposit(address?: Hex, overrides?: TransactionOptions): Promise<StabilityDeposit> {
    address ??= requireAddress(this.connection, overrides);

    const [sharesOwn, sharesTotalSupply, totalExtraAssets, totalCollaterals, totalDebtBalance, debtPrice, entryFee, exitFee] = await this.connection.publicClient.multicall({
      contracts: [
        { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "balanceOf", args: [address] },
        { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "totalSupply", args: [] },
        { address: this.getterAddress, abi: LSPGettersAbi, functionName: "extraAssets", args: [] },
        { address: this.getterAddress, abi: LSPGettersAbi, functionName: "collateralTokens", args: [] },
        { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "getTotalDebtTokenDeposits", args: [] },
        { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "getPrice", args: [this.connection.addresses.debtToken.contractAddress] },
        {
          address: this.connection.addresses.protocols[this.connection.addresses.defaultProtocol].beraborrowCore,
          abi: BeraborrowCoreAbi,
          functionName: "getLspEntryFee",
          args: [address],
        },
        {
          address: this.connection.addresses.protocols[this.connection.addresses.defaultProtocol].beraborrowCore,
          abi: BeraborrowCoreAbi,
          functionName: "getLspExitFee",
          args: [address],
        },
      ],
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
      account: address,
      ...overrides,
    });
    this.collaterals = totalCollaterals;
    this.extraAssets = totalExtraAssets;

    const totalAssetsBalances = (
      await Promise.all(
        [...totalExtraAssets, ...totalCollaterals].map(async (tokenAddress) => {
          const [[balance, price]] = await Promise.all([
            this.connection.publicClient.multicall({
              contracts: [
                { address: this.getterAddress, abi: LSPGettersAbi, functionName: "getTokenVirtualBalance", args: [tokenAddress] },
                { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "getPrice", args: [tokenAddress] },
              ],
              multicallAddress: this.connection.addresses.multicall,
              allowFailure: false,
              account: address,
              ...overrides,
            }),
          ]);
          //TODO: for redeemToPrefered we need all the zero balances but contract reverts for total supply = zero
          const underlyingAssets = await (this.connection.addresses.vaults[tokenAddress] && balance
            ? this.collVaultRouter.previewRedeemUnderlying(tokenAddress, address, SCALING_FACTOR)
            : undefined);

          return {
            contractAddress: tokenAddress,
            balance,
            price,
            underlyingAssets: underlyingAssets?.map((item) => {
              return { ...item, balance: (item.balance * balance) / SCALING_FACTOR };
            }),
          };
        })
      )
    ).sort((a, b) => {
      const valueA = a.balance * a.price;
      const valueB = b.balance * b.price;
      if (valueA > valueB) return -1;
      if (valueA < valueB) return 1;
      return a.contractAddress.localeCompare(b.contractAddress);
    });
    let pricesHashMap: { [key: Hex]: bigint } = { [this.connection.addresses.debtToken.contractAddress]: debtPrice };
    totalAssetsBalances.map((item) => {
      pricesHashMap[item.contractAddress] = item.price;
    });
    const totalAssetsBalancesPrice = await Promise.all(
      [
        { contractAddress: this.connection.addresses.debtToken.contractAddress, balance: totalDebtBalance, price: debtPrice, underlyingAssets: undefined },
        ...totalAssetsBalances,
      ].map(async (item) => {
        if (item.underlyingAssets) {
          const underlyingAssetsPrice = await Promise.all(
            item.underlyingAssets.map(async (underlying) => {
              let price = pricesHashMap[underlying.contractAddress];
              if (!price) {
                const collVault = new CollateralVault(this.connection, item.contractAddress);
                price = await collVault.getPrice(underlying.contractAddress, address);
                pricesHashMap[underlying.contractAddress] = price;
              }
              return { ...underlying, price };
            })
          );
          return { ...item, underlyingAssets: underlyingAssetsPrice };
        } else {
          return { ...item, underlyingAssets: undefined };
        }
      })
    );
    if (sharesTotalSupply == 0n) {
      return new StabilityDeposit(0n, 0n, [], BigInt(entryFee), BigInt(exitFee));
    }
    return new StabilityDeposit(sharesOwn, sharesTotalSupply, totalAssetsBalancesPrice, BigInt(entryFee), BigInt(exitFee));
  }

  /**
   *
   */
  async getCollateralTokens(address?: Hex, overrides?: TransactionOptions) {
    return await this.connection.publicClient.readContract({
      address: this.getterAddress,
      abi: LSPGettersAbi,
      functionName: "collateralTokens",
      args: [],
      account: address,
      ...overrides,
    });
  }
  async getPrice(assetAddress: Hex, address?: Hex, overrides?: TransactionOptions) {
    const price = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "getPrice",
      args: [assetAddress],
      account: address,
      ...overrides,
    });
    return price;
  }
  async getTvl(address?: Hex, overrides?: TransactionOptions) {
    const [debtPrice, totalAssets] = await this.connection.publicClient.multicall({
      contracts: [
        { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "getPrice", args: [this.connection.addresses.debtToken.contractAddress] },
        { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "totalAssets", args: [] },
      ],
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
      account: address,
      ...overrides,
    });

    const tvl = (debtPrice * totalAssets) / SCALING_FACTOR;
    return tvl;
  }
  async getPendingGainTimestamp(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    if (!this.collaterals || !this.extraAssets) {
      const [collaterals, extraAssets] = await this.connection.publicClient.multicall({
        contracts: [
          { address: this.getterAddress, abi: LSPGettersAbi, functionName: "collateralTokens", args: [] },
          { address: this.getterAddress, abi: LSPGettersAbi, functionName: "extraAssets", args: [] },
        ],
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
      });
      this.collaterals = collaterals;
      this.extraAssets = extraAssets;
    }

    const tokens = [...this.collaterals, ...this.extraAssets, this.connection.addresses.debtToken.contractAddress];
    const pendingTimestampsRaw = await this.connection.publicClient.multicall({
      contracts: tokens.map((address) => ({ address: this.getterAddress, abi: LSPGettersAbi, functionName: "getFullProfitUnlockTimestamp", args: [address] })),
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
      account: address,
      ...overrides,
    });
    // Ensure we only map valid hex strings to BigInt
    const pendingTimestamps: bigint[] = pendingTimestampsRaw.map((ts) => {
      if (typeof ts === "number") return BigInt(ts);
      if (typeof ts === "string") return BigInt(ts);
      if (typeof ts === "bigint") return ts;

      throw new Error("Unexpected return type from multicall");
    });

    return pendingTimestamps;
  }

  /**
   * Get the remaining POLLEN that will be collectively rewarded to stability depositors.
   */
  async getRemainingStabilityPoolPOLLENReward(overrides?: TransactionOptions): Promise<bigint> {
    const address = requireAddress(this.connection, overrides);
    const [totalPollenBalance, sharesOwn, sharesTotalSupply] = await this.connection.publicClient.multicall({
      contracts: [
        { address: this.getterAddress, abi: LSPGettersAbi, functionName: "getTokenVirtualBalance", args: [this.connection.addresses.pollenToken.contractAddress] },
        { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "balanceOf", args: [address] },
        { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "totalSupply", args: [] },
      ],
      multicallAddress: this.connection.addresses.multicall,
      allowFailure: false,
      account: address,
      ...overrides,
    });

    const pollenRewards = (sharesOwn * totalPollenBalance) / (sharesTotalSupply || 1n);
    return pollenRewards;
  }

  /**
   * Get the total amount of NECT currently deposited in the Stability Pool.
   */
  async getDebtInStabilityPool(overrides?: TransactionOptions): Promise<bigint> {
    const address = requireAddress(this.connection, overrides);
    const totalDebts = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "getTotalDebtTokenDeposits",
      args: [],
      account: address,
      ...overrides,
    });

    return totalDebts;
  }

  /**
   * Make a new Stability Deposit, or top up existing one.
   *
   * @param amount - Amount of Debt to add to new or existing deposit.
   *
   * As a side-effect, the transaction will also pay out an existing Stability Deposit's
   * collateral gain and  POLLEN reward.
   *
   */
  async depositDebtInStabilityPool(amount: bigint, overrides?: TransactionOptions) {
    const address = requireAddress(this.connection, overrides);
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "deposit",
      args: [amount, address],
      account: address,
      type: "eip1559",
      ...overrides,
    });
  }
  /**
	 * Withdraw Debt from Stability Deposit.
	 *
	 * @param amount - Amount of Debt to withdraw.

	 *
	 * @remarks
	 * As a side-effect, the transaction will also pay out the Stability Deposit's
	 * collateral gain and  POLLEN reward.
	 */
  async withdrawDebtFromStabilityPool(amount: bigint, overrides?: TransactionOptions) {
    const address = requireAddress(this.connection, overrides);
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "withdraw",
      args: [amount, address, address],
      account: address,
      type: "eip1559",
      ...overrides,
    });
  }
  /**
	 * redeem staked Debt from Stability Deposit.
	 *
	 * @param amount - Amount of staked debt to redeem.

	 *
	 */
  async redeemDebtFromStabilityPool(amount: bigint, overrides?: TransactionOptions) {
    const address = requireAddress(this.connection, overrides);
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "redeem",
      args: [amount, address, address],
      account: address,
      type: "eip1559",
      ...overrides,
    });
  }

  /**
   * Get the max withdraw amount for user
   */
  async getMaxWithdraw(user: Hex, overrides?: TransactionOptions): Promise<bigint> {
    const maxWithdraw = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "maxWithdraw",
      args: [user],
      ...overrides,
    });

    return maxWithdraw;
  }
  async convertSharesToAssets(amount: bigint, overrides?: TransactionOptions): Promise<bigint> {
    const maxWithdraw = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "convertToAssets",
      args: [amount],
      ...overrides,
    });

    return maxWithdraw;
  }
  async previewDeposit(amount: bigint, overrides?: TransactionOptions): Promise<bigint> {
    const maxWithdraw = await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "previewDeposit",
      args: [amount],
      ...overrides,
    });

    return maxWithdraw;
  }
  /**
   * Gets the spending Allowance of the contract
   * @param address
   * @param spender Contract Address of Spender
   * @returns spending Allowance
   */
  async allowance(address: `0x${string}`, spender: `0x${string}`, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "allowance",
      args: [address, spender],
      account: address,
      ...overrides,
    });
  }
  /**
   * Approves the spending Allowance of the contract
   * @param amount Amount to spend
   * @param spender Contract Address of Spender
   */
  async approve(amount: bigint, spender: `0x${string}`, address?: `0x${string}`, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: LiquidStabilityPoolAbi,
      functionName: "approve",
      args: [spender, amount],
      account: address,
      type: "eip1559",
      ...overrides,
    });
  }
  /**
   * Get APY.
   * r = T / t * ln(Q'/Q)
   * r: APY
   * T: 1 year
   * t: 1 week
   * Q: Initial share price
   * Q`: Current share price
   */
  async getContractAPY(currentBlockNumber?: bigint) {
    if (!currentBlockNumber) {
      currentBlockNumber = await this.connection.publicClient.getBlockNumber();
    }
    const BLOCKS_IN_WEEK = (7 * 24 * 60 * 60) / this.connection.averageBlockTime;
    const originalBlockNumber = BigInt(this.connection.startBlock);
    const blockNumberOneWeekAgo = currentBlockNumber - BigInt(BLOCKS_IN_WEEK) < originalBlockNumber ? originalBlockNumber : currentBlockNumber - BigInt(BLOCKS_IN_WEEK);
    const [currentBlock, blockInPast, [currentPricePerShare, currentDebtPrice], [pastPricePerShare, pastDebtPrice]] = await Promise.all([
      this.connection.publicClient.getBlock(),
      this.connection.publicClient.getBlock({ blockNumber: blockNumberOneWeekAgo }),
      this.connection.publicClient.multicall({
        contracts: [
          { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "convertToAssets", args: [SCALING_FACTOR] },
          { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "getPrice", args: [this.connection.addresses.debtToken.contractAddress] },
        ],
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
      }),
      this.connection.publicClient.multicall({
        contracts: [
          { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "convertToAssets", args: [SCALING_FACTOR] },
          { address: this.contractAddress, abi: LiquidStabilityPoolAbi, functionName: "getPrice", args: [this.connection.addresses.debtToken.contractAddress] },
        ],
        blockNumber: blockNumberOneWeekAgo,
        multicallAddress: this.connection.addresses.multicall,
        allowFailure: false,
      }),
    ]);
    const timeDiffSeconds = Number(currentBlock.timestamp - blockInPast.timestamp);
    if (timeDiffSeconds < 300) {
      return 0n;
    }
    if (pastPricePerShare === 0n) return 0n;

    const pricePerShareGrowth = Number((currentPricePerShare * currentDebtPrice) / SCALING_FACTOR) / Number((pastPricePerShare * pastDebtPrice) / SCALING_FACTOR);

    if (pricePerShareGrowth <= 1) return 0n;

    const periodInYears = timeDiffSeconds / SECONDS_IN_YEAR;
    const annualizedAPY = Math.pow(pricePerShareGrowth, 1 / periodInYears) - 1;

    return BigInt(Math.floor(annualizedAPY * Number(SCALING_FACTOR)));
  }
  /**
   * Get APY.
   * r = T / t * ln(Q'/Q)
   * r: APY
   * T: 1 year
   * t: 1 week
   * Q: Initial share price
   * Q`: Current share price
   */
  async getApy(from: number, to: number): Promise<bigint> {
    try {
      const [start, end] = await Promise.all([
        this.subgraphClient.getSharePoolSharePriceHistory(from, to - 86400, 96, "asc"),
        this.subgraphClient.getSharePoolSharePriceHistory(to - 86400, to, 96, "desc"),
      ]);
      if (start === undefined || end === undefined) {
        throw new Error("subgraph Error");
      }
      const startSharePrice = start.snapshots
        .filter((item) => item.sharePrice)
        .reduce(
          (prev, item) => {
            return { sharePrice: prev.sharePrice + Number(item.sharePrice), timestamp: prev.timestamp + Number(item.timestamp) };
          },
          { sharePrice: 0, timestamp: 0 }
        );
      const endSharePrice = end.snapshots
        .filter((item) => item.sharePrice)
        .reduce(
          (prev, item) => {
            return { sharePrice: prev.sharePrice + Number(item.sharePrice), timestamp: prev.timestamp + Number(item.timestamp) };
          },
          { sharePrice: 0, timestamp: 0 }
        );

      const real30DaysAgo = Number(endSharePrice.timestamp / end.snapshots.length) - Number(startSharePrice.timestamp / start.snapshots.length) || 1;
      const sharePrice30DaysAgo = startSharePrice.sharePrice / start.snapshots.length;
      const sharePriceNow = endSharePrice.sharePrice / end.snapshots.length;
      const SECONDS_IN_YEAR = 31536000;
      const apy = sharePrice30DaysAgo == 0 ? 0 : (SECONDS_IN_YEAR / real30DaysAgo) * Math.log(Number(sharePriceNow) / Number(sharePrice30DaysAgo));
      return BigInt((apy * Number(SCALING_FACTOR)).toFixed(0));
    } catch (error) {
      console.error(error);

      throw new Error("subgraph Error");
    }
  }
}
