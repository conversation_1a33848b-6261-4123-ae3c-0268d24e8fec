import { Hex } from "viem";
import { SCALING_FACTOR } from "../../constants";

/* A Stability Deposit and its accrued gains.
 *
 * @public
 */
export type TokenBalance = {
  contractAddress: Hex;
  balance: bigint;
  price: bigint;
  userBalance?: bigint;
  underlyingAssets?: { contractAddress: Hex; balance: bigint; price: bigint; userBalance?: bigint }[];
};
export class StabilityDeposit {
  /** Amount of users sNECT amount in the Stability Deposit. */
  readonly shares: bigint;
  /** Amount of total sNECT amount in the Stability Deposit. */
  readonly sharesTotalSupply: bigint;

  /** Amount of collateral received in exchange for the used-up NECT. */
  readonly assets: TokenBalance[];

  readonly entryFee: bigint;
  readonly exitFee: bigint;
  constructor(shares: bigint, sharesTotalSupply: bigint, assets: TokenBalance[], entryFee: bigint, exitFee: bigint) {
    this.shares = shares;
    this.sharesTotalSupply = sharesTotalSupply;
    this.entryFee = entryFee;
    this.exitFee = exitFee;
    this.assets = assets;
  }

  get isEmpty(): boolean {
    return this.assets.length === 0;
  }
  getUnderlyingAssets(assets?: TokenBalance[]): Omit<TokenBalance, "underlyingAssets">[] {
    const mergedAssets = new Map<Hex, { balance: bigint; price: bigint; userBalance?: bigint }>();
    (assets ?? this.assets)
      .flatMap((item) =>
        item.underlyingAssets
          ? item.userBalance !== undefined
            ? item.underlyingAssets.map((underlying) => ({
                ...underlying,
                userBalance: item.balance && item.userBalance ? (item.userBalance * underlying.balance) / item.balance : 0n,
              }))
            : item.underlyingAssets
          : [item]
      )
      .forEach(({ contractAddress, balance, price, userBalance }) => {
        if (mergedAssets.has(contractAddress)) {
          const existing = mergedAssets.get(contractAddress)!;
          mergedAssets.set(contractAddress, {
            balance: existing.balance + balance,
            userBalance: existing.userBalance ? existing.userBalance + (userBalance ?? 0n) : userBalance,
            price: existing.price,
          });
        } else {
          mergedAssets.set(contractAddress, { balance, price, userBalance });
        }
      });
    return Array.from(mergedAssets, ([contractAddress, { balance, price, userBalance }]) => {
      return {
        contractAddress,
        balance,
        price,
        userBalance,
      };
    });
  }

  getMyShare(value: bigint, shares?: bigint): bigint {
    if (this.sharesTotalSupply === 0n) return 0n;
    return ((shares ?? this.shares) * value) / this.sharesTotalSupply;
  }
  convertSharesToAssets = (share: bigint, shareToAssetRatio: bigint) => {
    return (share * shareToAssetRatio) / SCALING_FACTOR;
  };
  convertAssetsToShares = (assets: bigint, shareToAssetRatio: bigint) => {
    if (shareToAssetRatio === 0n) return 0n;
    return (assets * SCALING_FACTOR) / shareToAssetRatio;
  };
  convertDebtToShares = (debtToken: bigint, debtToSharesRatio: bigint) => {
    return (debtToken * debtToSharesRatio) / SCALING_FACTOR;
  };
  /**
   *
   * Compare to another instance of `StabilityDeposit`.
   */
  equals(that: StabilityDeposit): boolean {
    return (
      this.assets.length === that.assets.length &&
      this.assets.every((item, index) => item.balance === that.assets[index].balance && item.contractAddress === that.assets[index].contractAddress)
    );
  }
}
