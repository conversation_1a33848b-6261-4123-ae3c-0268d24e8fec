import { addGasForBaseRateUpdate, addGasForPotentialListTraversal } from "../../gasHelpers";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { DenAdjustmentParams, DenCreationParams } from "../../Den/types";
import { _normalizeDenAdjustment, _normalizeDenCreation, Den } from "../../Den/Den";
import { MultiCollateralHintHelpers } from "../MultiCollateralHintHelpers/Contract";
import { DEFAULT_SLIPPAGE_TOLERANCE, MAXIMUM_BORROWING_MINT_RATE, SCALING_FACTOR, SCALING_FACTOR_BP } from "../../constants";
import LeverageRouterAbi from "../../abi/LeverageRouterAbi";
import { Hex } from "viem";
import { DenManager } from "../DenManager/Contract";
import { CollateralVault } from "../CollateralVaults/Contract";
import { OogaBoogaServices } from "../../Services/OogaBooga/services";
import { BeraborrowCore } from "../BeraborrowCore/Contract";
import { assertAddress, requireAddress } from "../../utils";

export class LeverageRouter {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;
  readonly OogaBoogaApi: OogaBoogaServices;
  readonly BeraborrowCore: BeraborrowCore;
  private readonly protocol: Hex;
  private readonly multiCollateralHintHelpers: MultiCollateralHintHelpers;
  constructor(connection: BeraBorrowConnection, protocol: Hex) {
    this.connection = connection;
    this.protocol = protocol;
    this.contractAddress = this.connection.addresses.leverageRouter;
    this.OogaBoogaApi = new OogaBoogaServices(this.connection);
    this.BeraborrowCore = new BeraborrowCore(connection);
    this.multiCollateralHintHelpers = new MultiCollateralHintHelpers(this.connection, this.protocol);
    assertAddress(this.contractAddress);
    assertAddress(this.protocol);
  }

  /**
   *  * Open a new Den by depositing collateral and borrowing Debt.
   * @param dmAddr Den Manager Address
   * @param collVault Collateral Vault Address
   * @param address Borrowers address
   * @param params DenCreationParameters
   * @param currentBorrowingRate current borrowing Rate for NECT
   * @param slippage slippage on swaps and vault deposits
   * @param nativeToken is native token deposited
   */
  async automaticLoopingOpenDen(
    dmAddr: Hex,
    collVault: Hex,
    address: Hex,
    leverage: bigint,
    margin: bigint,
    params: DenCreationParams<bigint>,
    currentBorrowingRate: bigint,
    slippage?: bigint,
    // nativeToken = false,
    overrides?: TransactionOptions
  ) {
    slippage ??= DEFAULT_SLIPPAGE_TOLERANCE;
    leverage = (leverage * SCALING_FACTOR_BP) / SCALING_FACTOR;
    const normalizedParams = _normalizeDenCreation(params);
    const maxBorrowingRate = currentBorrowingRate + slippage < MAXIMUM_BORROWING_MINT_RATE ? currentBorrowingRate + slippage : MAXIMUM_BORROWING_MINT_RATE;
    const collateralVault = new CollateralVault(this.connection, collVault);
    const denManagerIdx = BigInt(this.connection.addresses.denManagers[dmAddr].index);
    const marginInShares = await collateralVault.previewDeposit(margin);
    const debtAmount = normalizedParams.borrowNECT; //await this._calculateDebtAmount(dmAddr, collVault, marginInShares, leverage, !!currentBorrowingRate);
    const collAssetsToDeposit = (marginInShares * leverage) / SCALING_FACTOR_BP;
    const newDen = Den.create(normalizedParams, this.connection.liquidationReserve, currentBorrowingRate);
    const [hints, dexCalldataResp] = await Promise.all([
      this.multiCollateralHintHelpers.findHintsForNominalCollateralRatio(dmAddr, newDen._nominalCollateralRatio, address),
      this.OogaBoogaApi.getSwap(
        this.connection.addresses.debtToken.contractAddress,
        this.connection.addresses.vaults[collVault].collateral,
        this.connection.addresses.leverageRouter,
        debtAmount.toString()
      ),
    ]);

    if (dexCalldataResp?.data === null) {
      throw new Error(dexCalldataResp.error);
    }
    const dexCalldata = dexCalldataResp.data.tx.data;
    const dexCollOutputMin = BigInt(dexCalldataResp.data.routerParams.swapTokenInfo.outputMin);
    if (overrides?.gas === undefined) {
      const decayedBorrowingRate = maxBorrowingRate;
      const decayedDen = Den.create(normalizedParams, this.connection.liquidationReserve, decayedBorrowingRate);
      const denRecreate = Den.recreate(decayedDen, this.connection.liquidationReserve, currentBorrowingRate);
      if (denRecreate === undefined) {
        throw new Error(`Den's unable to recreate `);
      }
      const gas = await this.connection.publicClient.estimateContractGas({
        address: this.contractAddress,
        abi: LeverageRouterAbi,
        functionName: "automaticLoopingOpenDen",
        args: [
          dmAddr,
          collVault,
          collAssetsToDeposit,
          {
            denParams: { maxFeePercentage: maxBorrowingRate, debtAmount, upperHint: hints[0], lowerHint: hints[1] },
            dexAggregator: { dexCalldata: dexCalldata, collOutputMin: dexCollOutputMin },
          },
          denManagerIdx,
        ],
        // value: nativeToken ? depositCollateral : undefined,
        type: "eip1559",
        ...overrides,
        account: address,
      });

      const gasLimit = addGasForBaseRateUpdate(addGasForPotentialListTraversal(gas));

      overrides = { ...overrides, gas: gasLimit };
    }
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: LeverageRouterAbi,
      functionName: "automaticLoopingOpenDen",
      args: [
        dmAddr,
        collVault,
        collAssetsToDeposit,
        {
          denParams: { maxFeePercentage: maxBorrowingRate, debtAmount, upperHint: hints[0], lowerHint: hints[1] },
          dexAggregator: { dexCalldata: dexCalldata, collOutputMin: dexCollOutputMin },
        },
        denManagerIdx,
      ],
      //   value: nativeToken ? depositCollateral : undefined,
      type: "eip1559",
      ...overrides,
      account: address,
    });
  }

  /**
   * Adjust an existing Den by depositing/withdrawing collateral and/or borrowing Debt.
   * @param dmAddr Den Manager Address
   * @param collVault Collateral Vault Address
   * @param address Borrowers address
   * @param  collateralToDeposit: the amount of collateral that will be deposited
   * @param params DenCreationParameters
   * @param currentBorrowingRate current borrowing Rate for NECT
   * @param slippage slippage on swaps and vault deposits
   * @param nativeToken is native token deposited
   */
  async automaticLoopingAddCollateral(
    dmAddr: Hex,
    collVault: Hex,
    address: Hex,
    leverage: bigint,
    margin: bigint,
    params: DenAdjustmentParams<bigint>,
    currentBorrowingRate: bigint,
    slippage?: bigint,
    // nativeToken = false,
    overrides?: TransactionOptions
  ) {
    slippage ??= DEFAULT_SLIPPAGE_TOLERANCE;
    leverage = (leverage * SCALING_FACTOR_BP) / SCALING_FACTOR;
    const normalizedParams = _normalizeDenAdjustment(params);
    const collateralVault = new CollateralVault(this.connection, collVault);
    const denManager = new DenManager(this.connection, dmAddr);
    const [den, marginInShares] = await Promise.all([denManager.getDen(address), collateralVault.previewDeposit(margin)]);
    const maxBorrowingRate = currentBorrowingRate + slippage < MAXIMUM_BORROWING_MINT_RATE ? currentBorrowingRate + slippage : MAXIMUM_BORROWING_MINT_RATE;
    const adjustedDen = den.adjust(normalizedParams, currentBorrowingRate);
    const denManagerIdx = BigInt(this.connection.addresses.denManagers[dmAddr].index);
    const debtAmount = normalizedParams.borrowNECT ?? 0n; //await this._calculateDebtAmount(dmAddr, collVault, marginInShares, leverage, !!currentBorrowingRate);
    const collAssetsToDeposit = (marginInShares * leverage) / SCALING_FACTOR_BP;
    const [hints, dexCalldataResp] = await Promise.all([
      this.multiCollateralHintHelpers.findHintsForNominalCollateralRatio(dmAddr, adjustedDen._nominalCollateralRatio, address),
      this.OogaBoogaApi.getSwap(
        this.connection.addresses.debtToken.contractAddress,
        this.connection.addresses.vaults[collVault].collateral,
        this.connection.addresses.leverageRouter,
        debtAmount.toString()
      ),
    ]);
    if (dexCalldataResp?.data === null) {
      throw new Error(dexCalldataResp.error);
    }

    const dexCalldata = dexCalldataResp.data.tx.data;

    const dexCollOutputMin = BigInt(dexCalldataResp.data.routerParams.swapTokenInfo.outputMin);
    if (overrides?.gas === undefined) {
      const decayedBorrowingRate = maxBorrowingRate;

      const decayedDen = den.adjust(normalizedParams, decayedBorrowingRate);

      const denAdjustment = den.adjustTo(decayedDen, this.connection.liquidationReserve, currentBorrowingRate);

      if (denAdjustment === undefined) {
        throw new Error(`Den's adjustment is Invalid `);
      }

      const gas = await this.connection.publicClient.estimateContractGas({
        address: this.contractAddress,
        abi: LeverageRouterAbi,
        functionName: "automaticLoopingAddCollateral",
        args: [
          dmAddr,
          collVault,
          collAssetsToDeposit,
          {
            denParams: { maxFeePercentage: maxBorrowingRate, debtAmount, upperHint: hints[0], lowerHint: hints[1] },
            dexAggregator: { dexCalldata, collOutputMin: dexCollOutputMin },
          },
          denManagerIdx,
        ],
        type: "eip1559",
        ...overrides,
        account: address,
        // value: nativeToken ? depositCollateral : undefined,
      });
      let gasLimit = addGasForPotentialListTraversal(gas);

      overrides = { ...overrides, gas: gasLimit };
    }
    return this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: LeverageRouterAbi,
      functionName: "automaticLoopingAddCollateral",
      args: [
        dmAddr,
        collVault,
        collAssetsToDeposit,
        {
          denParams: { maxFeePercentage: maxBorrowingRate, debtAmount, upperHint: hints[0], lowerHint: hints[1] },
          dexAggregator: { dexCalldata, collOutputMin: dexCollOutputMin },
        },
        denManagerIdx,
      ],
      type: "eip1559",
      ...overrides,
      account: address,
      //   value: nativeToken ? depositCollateral : undefined,
    });
  }
  flashLoanFee(): Promise<bigint> {
    return this.BeraborrowCore.getPeripheryFlashLoanFee(this.contractAddress);
  }
  private _calculateDebtAmount(
    dmAddr: Hex,
    collVault: Hex,
    margin: bigint, // Amount of margin in Collateral Vault asset
    leverage: bigint, // in basis points
    isRecoveryMode: boolean,
    address?: Hex,
    overrides?: TransactionOptions
  ): Promise<bigint> {
    address ??= requireAddress(this.connection);
    if (leverage <= SCALING_FACTOR) {
      throw new Error("Leverage must be greater than SCALING_FACTOR");
    }
    const ratio = (leverage * SCALING_FACTOR) / (leverage - SCALING_FACTOR);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: LeverageRouterAbi,
      functionName: "calculateDebtAmount",
      args: [address, margin, leverage, ratio, dmAddr, collVault, isRecoveryMode],
      ...overrides,
    });
  }
}
