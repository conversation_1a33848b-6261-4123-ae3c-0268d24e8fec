import { Hex } from "viem";
import { BeraBorrowConnection } from "../../types";
import { BaseVotingEscrow } from "./BaseVotingEscrow";
import { assertAddress } from "../../utils";

export class LpVotingEscrow extends BaseVotingEscrow {
  readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection) {
    super(connection);
    this.contractAddress = this.connection.addresses.lpVotingEscrow;
    assertAddress(this.contractAddress);
  }
}
