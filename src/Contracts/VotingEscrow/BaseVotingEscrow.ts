import { Hex } from "viem";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { requireAddress } from "../../utils";
import VotingEscrowPollenAbi from "../../abi/VotingEscrowPollenAbi";
import UserVeLockStakingRewardsAbi from "../../abi/UserVeLockStakingRewardsAbi";

export abstract class BaseVotingEscrow {
  protected readonly connection: BeraBorrowConnection;
  abstract readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection) {
    this.connection = connection;
  }

  /**
   * Creates a new lock in the Voting Escrow
   * @param address The address of the user creating the lock
   * @param amount The amount of token to lock
   * @param expiry The expiry date of the lock
   * @returns The new ve balance of the lock
   */
  async increaseLockPosition(amount: bigint, newExpiry: bigint, address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: VotingEscrowPollenAbi,
      functionName: "increaseLockPosition",
      args: [amount, newExpiry],
      account: address,
      ...overrides,
    });
  }

  /**
   * Withdraws the locked POLLEN from the Voting Escrow
   * @param address The address of the user withdrawing the lock
   * @returns The amount of locking token received
   */
  async withdraw(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: VotingEscrowPollenAbi,
      functionName: "withdraw",
      args: [],
      account: address,
      ...overrides,
    });
  }

  /**
   * Gets the updated total ve supply after updating the Linear Decay slope
   * @param address The address to check the balance of
   * @returns The total ve supply
   */
  async totalSupplyCurrent(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: VotingEscrowPollenAbi,
      functionName: "totalSupplyCurrent",
      args: [],
      account: address,
      ...overrides,
    });
  }

  /**
   * Gets the ve balance of the address
   * @param address
   * @returns User ve balance
   */
  async getUserVeBalance(address?: Hex, overrides?: TransactionOptions): Promise<bigint> {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: VotingEscrowPollenAbi,
      functionName: "balanceOf",
      args: [address],
      account: address,
      ...overrides,
    });
  }

  /**
   * Gets the total ve supply
   * @param address
   * @returns Total ve supply
   */
  async getTotalVeBalance(address?: Hex, overrides?: TransactionOptions): Promise<bigint> {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: VotingEscrowPollenAbi,
      functionName: "totalSupplyStored",
      args: [],
      account: address,
      ...overrides,
    });
  }

  /**
   * Returns the address of the UserVeLockStaking rewards accountant instance for user
   * @param address User address
   * @returns Address for UserVeLockStaking rewards accountant instance for user
   */
  async getUserVirtualAccountant(address?: Hex, overrides?: TransactionOptions): Promise<Hex> {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: VotingEscrowPollenAbi,
      functionName: "userToStakingRewards",
      args: [address],
      account: address,
      ...overrides,
    });
  }

  /**
   * Claims rewards from the user's VeLock staking rewards contract
   * @param address User address to claim rewards for
   * @returns Simulation result for claiming rewards
   */
  async claimForVeLock(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    const accountantAddress = await this.getUserVirtualAccountant(address, overrides);

    return await this.connection.publicClient.simulateContract({
      address: accountantAddress,
      abi: UserVeLockStakingRewardsAbi,
      functionName: "claimForVeLock",
      args: [],
      account: address,
      ...overrides,
    });
  }
}
