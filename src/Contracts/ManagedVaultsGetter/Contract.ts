import ManagedLeveragedVaultGettersAbi from "../../abi/ManagedLeveragedVaultGettersAbi";
import { assertAddress, requireAddress } from "../../utils";
import { BeraBorrowConnection, TransactionOptions } from "../../types";
import { Hex } from "viem";

export class ManagedVaultsGetter {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: Hex;

  constructor(connection: BeraBorrowConnection, contractAddress: Hex) {
    this.connection = connection;
    this.contractAddress = contractAddress;
    assertAddress(this.contractAddress);
  }

  /**
   * Get Preview of Shares amount as asset
   * @param amount Amount to deposit
   */
  async getDetails(address?: Hex, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: ManagedLeveragedVaultGettersAbi,
      functionName: "getParameters",
      args: [],
      account: address,
      ...overrides,
    });
  }
}
