import IERC20Abi from "../../abi/IERC20Abi";
import { assertAddress, requireAddress } from "../../utils";
import { BeraBorrowConnection, TransactionOptions } from "../../types";

export class Collateral {
  private readonly connection: BeraBorrowConnection;
  readonly contractAddress: `0x${string}`;
  readonly ticker: string;
  readonly decimals: number;

  constructor(connection: BeraBorrowConnection, contractAddress: `0x${string}`, ticker: string, decimals: number) {
    this.connection = connection;
    this.contractAddress = contractAddress;
    this.ticker = ticker;
    this.decimals = decimals;
    assertAddress(this.contractAddress);
  }
  /**
   * Get the amount of Collateral held by an address.
   *
   * @param address - Address whose balance should be retrieved.
   */
  async getBalance(address?: `0x${string}`, overrides?: TransactionOptions): Promise<bigint> {
    address ??= requireAddress(this.connection, overrides);
    return this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: IERC20Abi,
      functionName: "balanceOf",
      args: [address],
      ...overrides,
      account: address,
    });
  }
  /**
   * transfer Collateral tokens to an address.
   *
   * @param receiver - Address of receipient.
   * @param amount - Amount of POLLEN to send.
   *
   */
  async transfer(receiver: `0x${string}`, amount: bigint, address?: `0x${string}`, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: IERC20Abi,
      functionName: "transfer",
      args: [receiver, amount],
      account: address,
      type: "eip1559",
      ...overrides,
    });
  }
  /**
   * Gets the spending Allowance of the contract
   * @param address
   * @param spender Contract Address of Spender
   * @returns spending Allowance
   */
  async allowance(address: `0x${string}`, spender: `0x${string}`, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.readContract({
      address: this.contractAddress,
      abi: IERC20Abi,
      functionName: "allowance",
      args: [address, spender],
      account: address,
      ...overrides,
    });
  }
  /**
   * Approves the spending Allowance of the contract
   * @param amount Amount to spend
   * @param spender Contract Address of Spender
   */
  async approve(amount: bigint, spender: `0x${string}`, address?: `0x${string}`, overrides?: TransactionOptions) {
    address ??= requireAddress(this.connection, overrides);
    return await this.connection.publicClient.simulateContract({
      address: this.contractAddress,
      abi: IERC20Abi,
      functionName: "approve",
      args: [spender, amount],
      account: address,
      type: "eip1559",
      ...overrides,
    });
  }
}
