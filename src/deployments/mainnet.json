{"chainId": 80094, "deploymentTimestamp": 1739640927, "bootstrapPeriod": 1209600, "startBlock": 1134927, "averageBlockTime": 2, "liquidationReserve": 1, "minimumDebt": 69, "SUBGRAPH_API_URL": "https://api.goldsky.com/api/public/project_cm0v01jq86ry701rr6jta9tqm/subgraphs/bera-borrow-prod/1.0.12/gn", "SUBGRAPH_POLLEN_API_URL": "https://api.goldsky.com/api/public/project_cm91ck6gc3d4401t6fjcgg6sl/subgraphs/pollen/1.0.0/gn", "BERABORROW_API_URL": "https://api.beraborrow.com", "addresses": {"multicall": "0xcA11bde05977b3631167028862bE2a173976CA11", "defaultProtocol": "0x12347cAF4300B1c4a9bF0Ae7DE2531A2BCFB93E9", "stabilityPool": "******************************************", "priceFeed": "0xa686DC84330b1B3787816de2DaCa485D305c8589", "LSPGetters": "0xF8519658cfF16FA095A8bCEB3dCC576D94399e32", "LSPRouter": "0x3A7ED65b35fDfaaCC9F0E881846A9F4E57181446", "leverageRouter": "******************************************", "psmBond": "0xB2F796FA30A8512C1D27a1853a9a1a8056b5CC25", "protocols": {"0x12347cAF4300B1c4a9bF0Ae7DE2531A2BCFB93E9": {"beraborrowCore": "0x12347cAF4300B1c4a9bF0Ae7DE2531A2BCFB93E9", "borrowerOperations": "0xDB32cA8f3bB099A76D4Ec713a2c2AACB3d8e84B9", "denManagerGetters": "0xFA7908287c1f1B256831c812c7194cb95BB440e6", "multiCollateralHintHelpers": "0x4A91b96A615D133e4196655Bc1735430ec97A391", "collVaultRouter": "0x5f1619FfAEfdE17F7e54f850fe90AD5EE44dbf47"}, "0xb8d17199f58dF90Af367cF48A677A845237dc3DD": {"beraborrowCore": "0xb8d17199f58dF90Af367cF48A677A845237dc3DD", "borrowerOperations": "0x29589A06e7fb66204637f9F1B5F32046341ef5E9", "denManagerGetters": "0xa2ECbE7a6BBfB0F14ABbCFE3c19FE54dC7878588", "multiCollateralHintHelpers": "0xc5ea6d45866040Ac7bFcFE1B93bCb01EA90e064e", "collVaultRouter": "0x4e7e2DEAEDB362CA81a5aB94619dC18E8C342A7C"}}, "CollateralVaultRegistry": "0xcE997aC8FD015A2B3c3950Cb33E9e6Bb962E35e1", "ibgtVault": "0xe59ab0c3788217e48399dae3cd11929789e4d3b2", "lpPollenStaking": "0xb7e161D08B842ae46Cf9dbD405D8b4e7223d9Cf1", "sPollenStaking": "0x135951615b6C35aFa56Ecb092fc72B83EE1B8942", "lpVotingEscrow": "0x794550c1685fD8D84765Fc405a0f291F0c7267b5", "lpVeFeeDistributor": "0x70AE7cD08144E5b22874931799f1866a0e37dccc", "sPollenVotingEscrow": "0xCC3df0Bc8362AD0a7dbb5756e39E2d47d3142a9d", "sPollenVeFeeDistributor": "0xD5f2e0a1C5B78b07E881506dc30Bb98Ce51Baf83", "stagingPriceFeed": "0xf4Cf85489FdE339a914c451084526E1718E35F13", "pollenToken": {"contractAddress": "******************************************", "ticker": "POLLEN", "decimals": 18}, "pollenLpToken": {"contractAddress": "******************************************", "ticker": "KODI WBERA-POLLEN", "decimals": 18}, "sPollenToken": {"contractAddress": "0x0bE9F37F672e72C0326061781724f38b34AA121c", "ticker": "sPOLLEN", "decimals": 18}, "debtToken": {"contractAddress": "******************************************", "ticker": "NECT", "decimals": 18}, "wrappedDenManagers": {"******************************************": "0xf1356Cb726C2988C65c5313350C9115D9Af0f954"}, "denManagers": {"0xFec9F8cd6F7B7a6674CD846938Fe44b001f7a568": {"denManager": "0xFec9F8cd6F7B7a6674CD846938Fe44b001f7a568", "collateral": "******************************************", "sortedDens": "0xd650380ab909745C0e9f4591e9E9737f0382E56A", "permissioned": true, "vault": "******************************************", "index": 0}, "0xb6Ab580B36ed2DEF692dc608aFAc0A27C725CD0E": {"denManager": "0xb6Ab580B36ed2DEF692dc608aFAc0A27C725CD0E", "collateral": "******************************************", "sortedDens": "0x106D00356d6A4793EFc6c4Deb6df0d5d81f55DD5", "permissioned": true, "vault": "******************************************", "index": 1}, "0xcEDEFfec2860409E3ccB0D798C71dD1239259d8A": {"denManager": "0xcEDEFfec2860409E3ccB0D798C71dD1239259d8A", "collateral": "******************************************", "sortedDens": "0xcfB5bdbCA05573DBB9Ca2bE01fde6FC88b3C3d46", "permissioned": true, "vault": "******************************************", "index": 2}, "0xE66E9873E5851b25a3b539879265F554c50f2f8c": {"denManager": "0xE66E9873E5851b25a3b539879265F554c50f2f8c", "collateral": "******************************************", "sortedDens": "0xED103b400aA80B71c209769DeA0CBDC2aEc9c5Eb", "permissioned": true, "vault": "******************************************", "index": 3}, "0xA0f32354419c2e02cd5D535edd5Fd7F5e81Fbe96": {"denManager": "0xA0f32354419c2e02cd5D535edd5Fd7F5e81Fbe96", "collateral": "******************************************", "sortedDens": "0x631395a9c5F79eeC521a3EB6B6f968d6528f6d6f", "permissioned": true, "vault": "******************************************", "index": 4}, "0x2016885fC46E879Aa3D170C7472Ce623090D4c1a": {"denManager": "0x2016885fC46E879Aa3D170C7472Ce623090D4c1a", "collateral": "******************************************", "sortedDens": "0x6f3D61b70824DB322d91Dfaea819EbA11fc86610", "permissioned": true, "vault": "******************************************", "index": 5}, "0x9A3549ef882584a687C1FF1843e3B3C07a2A0cB2": {"denManager": "0x9A3549ef882584a687C1FF1843e3B3C07a2A0cB2", "collateral": "******************************************", "sortedDens": "0xBb0c90857d5d399d578A1243DA83397a1D161b16", "permissioned": true, "vault": "******************************************", "index": 6}, "******************************************": {"denManager": "******************************************", "collateral": "******************************************", "sortedDens": "0xF284290577C42d099ab418386AB0bDD7be4F8C82", "permissioned": true, "vault": "******************************************", "index": 7}, "0xC94CD2ADB51E5CbE2d9fa0d09481a24929d7B7fF": {"denManager": "0xC94CD2ADB51E5CbE2d9fa0d09481a24929d7B7fF", "collateral": "******************************************", "sortedDens": "0x9D74d927b3C8D434c847B62F9Af5a1c8a852e1bB", "permissioned": true, "vault": "******************************************", "index": 8}, "0xEe49c5FF02b70c4099EA63B1792d963ABD9947f0": {"denManager": "0xEe49c5FF02b70c4099EA63B1792d963ABD9947f0", "collateral": "******************************************", "sortedDens": "0x00c6dB55E11BBdD352A76a598bbb6f5723a4f786", "permissioned": true, "vault": "******************************************", "index": 9}, "0x1d2A3141BE9a5D4c785A56A01C53Fd02fA4eAa04": {"denManager": "0x1d2A3141BE9a5D4c785A56A01C53Fd02fA4eAa04", "collateral": "******************************************", "sortedDens": "0x7Bf774A86f345f5417151EaD9f56111D1bcF01B7", "permissioned": true, "vault": "******************************************", "index": 10}, "0x8Ed442f8A10d046b2Bd9A5866B2ecd7eE0e2aBEe": {"denManager": "0x8Ed442f8A10d046b2Bd9A5866B2ecd7eE0e2aBEe", "collateral": "******************************************", "sortedDens": "0x72DF3973635eec7274eBaE0013feEef0ae4bfBF4", "permissioned": true, "vault": "******************************************", "index": 11}, "0xf1356Cb726C2988C65c5313350C9115D9Af0f954": {"denManager": "0xf1356Cb726C2988C65c5313350C9115D9Af0f954", "collateral": "******************************************", "sortedDens": "0xB98bC62eE90749fDBbd9E0632e4F968db60a51db", "vault": "******************************************", "index": 12}, "******************************************": {"denManager": "0xf1356Cb726C2988C65c5313350C9115D9Af0f954", "collateral": "******************************************", "wrappedCollateral": "******************************************", "sortedDens": "0xB98bC62eE90749fDBbd9E0632e4F968db60a51db", "vault": "******************************************", "index": 12}, "0x053EEbc21D5129CDB1abf7EAf09D59b19e75B8ce": {"denManager": "0x053EEbc21D5129CDB1abf7EAf09D59b19e75B8ce", "collateral": "******************************************", "sortedDens": "0x700110A972D08894D9AA39a858fC6001C31ED64D", "vault": "******************************************", "index": 13}, "0xD80AB5da2507008c8ede90648407BE098F1F1521": {"denManager": "0xD80AB5da2507008c8ede90648407BE098F1F1521", "collateral": "******************************************", "sortedDens": "0xf27Fda8386C85738A7c71AAd0758053D97B74019", "vault": "******************************************", "index": 14}, "0x7E9F625aa50abb8E49f26a62D2E8001889b0E27E": {"denManager": "0x7E9F625aa50abb8E49f26a62D2E8001889b0E27E", "collateral": "******************************************", "sortedDens": "0x21837d611D8BF3ca4dbC6418703908261cea45FC", "vault": "******************************************", "index": 15}, "0xadBe8C446252b8B99EFaFC9e5740E00D0ea25954": {"denManager": "0xadBe8C446252b8B99EFaFC9e5740E00D0ea25954", "collateral": "******************************************", "sortedDens": "0xE6e12c3dC39Ca861Cf7EB804Af6B5a2fdE2E657f", "vault": "******************************************", "index": 16}, "0x4BCc222279a2a01242B7F21F1B96eb6220B73D93": {"denManager": "0x4BCc222279a2a01242B7F21F1B96eb6220B73D93", "collateral": "******************************************", "sortedDens": "0x7462979b9F8e62D8c6e0952eBB760f45deA85a48", "vault": "******************************************", "index": 17}, "0xbCdb96d989cFfa5d75dA93F0a4eAE8bf07F9dD6D": {"denManager": "0xbCdb96d989cFfa5d75dA93F0a4eAE8bf07F9dD6D", "collateral": "******************************************", "sortedDens": "0x091Cf06f35bFfBA9C53C59c42F8a7aaE038Ac55b", "vault": "******************************************", "index": 18}, "0xD9Ae6F135e03F20596B9204403B741423C9597c5": {"denManager": "0xD9Ae6F135e03F20596B9204403B741423C9597c5", "collateral": "******************************************", "sortedDens": "0x9e0A52267d1887f336DA422ebc22c9B680a74F94", "vault": "******************************************", "index": 19}, "0x4489f63DC4cEAFd7eAE59346A283aC7B6f047cED": {"denManager": "0x4489f63DC4cEAFd7eAE59346A283aC7B6f047cED", "collateral": "******************************************", "sortedDens": "0xE774AAf38165F27Af2AEC335333a31de0d6be0E7", "vault": "0x33aD23316A0C3dd999d4AeDe4fe99074DE42324b", "index": 20}, "0x28C8004688b8915CfdE736930BB36906c87adD4A": {"denManager": "0x28C8004688b8915CfdE736930BB36906c87adD4A", "collateral": "******************************************", "sortedDens": "0xC43950586105D3f9C4c111131133928Dda6C9439", "vault": "******************************************", "index": 21}, "0x1189F8585ca59FdDF0BD5E59d917531b91D39B8C": {"denManager": "0x1189F8585ca59FdDF0BD5E59d917531b91D39B8C", "collateral": "******************************************", "sortedDens": "0xb4513b5046a862c2bB1d0417e839Ab291F1E8be0", "vault": "******************************************", "index": 22}, "0xB9268b1DD4cC9F09981f095FE1Ef3B6e8Dd81ed5": {"denManager": "0xB9268b1DD4cC9F09981f095FE1Ef3B6e8Dd81ed5", "collateral": "******************************************", "sortedDens": "0x0cDbaBb0678FbaCA1F01143e44740C058cC0DBb5", "vault": "******************************************", "index": 23}, "0xf872934CBD465952cb0Bc7735aF5EFE98e2ee4F2": {"denManager": "0xf872934CBD465952cb0Bc7735aF5EFE98e2ee4F2", "collateral": "******************************************", "sortedDens": "0xE8265D6aC005b1151361Cc0b03a9b7d11C3fda0B", "vault": "******************************************", "index": 24}, "0x97BF0bf851e02F62664ded5cCBD582a16AAA36bC": {"denManager": "0x97BF0bf851e02F62664ded5cCBD582a16AAA36bC", "collateral": "******************************************", "sortedDens": "0x13401a9662EbedEb28c371Df059791438B9eDeD5", "vault": "******************************************", "index": 25}, "0x026c153b8e5b807805F2dfD8a370D9352C8cc20C": {"denManager": "0x026c153b8e5b807805F2dfD8a370D9352C8cc20C", "collateral": "******************************************", "sortedDens": "0x97540FD8f560FcdcFB42eb5e9E6aB65f023c3ecb", "vault": "******************************************", "index": 26}, "0xF9dB1b321CF012f9a1189Fdf5b9aE97864a96c8C": {"denManager": "0xF9dB1b321CF012f9a1189Fdf5b9aE97864a96c8C", "collateral": "******************************************", "sortedDens": "0xca93e740321298606D13972ab34fCD825f8D96cB", "vault": "******************************************", "index": 27}, "0x3C97B31601cA659499a8525850F646D0Fe54c7c8": {"denManager": "0x3C97B31601cA659499a8525850F646D0Fe54c7c8", "collateral": "******************************************", "sortedDens": "0x2fA963F4620D4d7dCD2413CFc5aB812410a31d5b", "vault": "******************************************", "index": 28}, "0xc50ECe3ea18a265Fe46Cad306C72fFc8CC5ed20b": {"denManager": "0xc50ECe3ea18a265Fe46Cad306C72fFc8CC5ed20b", "collateral": "******************************************", "sortedDens": "0x524a266472329B2b1CeFCA70Ba0e1a5b3AFFcbE1", "vault": "******************************************", "protocol": "0xb8d17199f58dF90Af367cF48A677A845237dc3DD", "index": 201}, "0x51dB782783019B04cf706B4ce9bEBD9700De297b": {"denManager": "0x51dB782783019B04cf706B4ce9bEBD9700De297b", "collateral": "******************************************", "sortedDens": "0x4397dD564aBEE886B72148E078aB5b7AbcdA2d26", "vault": "******************************************", "index": -1}, "******************************************": {"denManager": "******************************************", "collateral": "******************************************", "sortedDens": "0xD5b3b1dE265f2CFa33E871cC51F5641CaDdEa6aA", "vault": "******************************************", "index": -1}, "0x9657290e0f824638362c96Bfce0d62120E3fA395": {"denManager": "0x9657290e0f824638362c96Bfce0d62120E3fA395", "collateral": "******************************************", "sortedDens": "0xae43EaFB7D48DAdcf73F10efc544D625CF6E6E3b", "vault": "******************************************", "index": -100}, "0x19493316159Ebe9D8aBFa36443Cdb289594d69C2": {"denManager": "0x19493316159Ebe9D8aBFa36443Cdb289594d69C2", "collateral": "******************************************", "sortedDens": "0xD41117A8976F1923d80f2a7101c81F6093913D5b", "vault": "******************************************", "permissioned": true, "index": -100}, "0xA1C9fbDF853617Fa27E2c39EE830703A3Fa9D2A3": {"denManager": "0xA1C9fbDF853617Fa27E2c39EE830703A3Fa9D2A3", "collateral": "******************************************", "sortedDens": "0x38DCd58C61c23403d09F286e05f59b8429903111", "vault": "******************************************", "index": -100}, "0xCdccD6eE755eA20bDB0cACa6E9CA2bCdAC64722b": {"denManager": "0xCdccD6eE755eA20bDB0cACa6E9CA2bCdAC64722b", "collateral": "******************************************", "sortedDens": "0x34798d678B3Be41F794D874bc84950e4dabbb320", "vault": "******************************************", "index": -100}, "0xFA830d4e4AAe7b65Ce76Fd4186Eb962739DE3CF0": {"denManager": "0xFA830d4e4AAe7b65Ce76Fd4186Eb962739DE3CF0", "collateral": "******************************************", "sortedDens": "0xD47E9F419DE50e860317CF419E009206F57b76f7", "vault": "******************************************", "index": -100}, "0x0ce2CF1B106686CDA9D01c247ccc3Bae1c2E9C15": {"denManager": "0x0ce2CF1B106686CDA9D01c247ccc3Bae1c2E9C15", "collateral": "******************************************", "sortedDens": "0xd397283414aA09c653CcdB260C1AA62C65Cf5687", "vault": "******************************************", "index": -100}, "0x3C68cb0eC450f607c6d1E12ed0F9C1B6dfcc3822": {"denManager": "0x3C68cb0eC450f607c6d1E12ed0F9C1B6dfcc3822", "collateral": "******************************************", "sortedDens": "0x8d173B5375d0ebF6a14a51AAde53B14aD96D360E", "vault": "******************************************", "index": -100, "protocol": "0xb8d17199f58dF90Af367cF48A677A845237dc3DD"}, "0xDcc63C4dB3cF9C3C61ed38e7DAD658F8c7AB950d": {"denManager": "0xDcc63C4dB3cF9C3C61ed38e7DAD658F8c7AB950d", "collateral": "******************************************", "sortedDens": "0xc1880425eE49bDE1C80360B5f8246cBb2e85aAc6", "vault": "******************************************", "index": -100, "protocol": "0xb8d17199f58dF90Af367cF48A677A845237dc3DD"}, "0xaa58e1691c369f6F2632f7beF7B23551f4d59524": {"denManager": "0xaa58e1691c369f6F2632f7beF7B23551f4d59524", "collateral": "******************************************", "sortedDens": "0xb84B599752AE95d385eB491daaf0AB389258db6D", "vault": "******************************************", "index": -100, "protocol": "0xb8d17199f58dF90Af367cF48A677A845237dc3DD"}, "0x6ddf065CBD250c69917beA7E51B9b8D7fbE3Aeb4": {"denManager": "0x6ddf065CBD250c69917beA7E51B9b8D7fbE3Aeb4", "collateral": "******************************************", "sortedDens": "0x91ffd3B0D944F20b01061FAFd6af4E81E15842fD", "vault": "******************************************", "index": -100}, "0x9E84d944118f7F5FE5268132E7096983D9833ba6": {"denManager": "0x9E84d944118f7F5FE5268132E7096983D9833ba6", "collateral": "******************************************", "sortedDens": "0x2B32985b10d30Db4b7480eD13A62c1194e95703F", "vault": "0x2354801363308e95b45E417334667908da723524", "index": -100}, "0x389836Bb8fE644820C5e764CEe5F8Fc8d1a04E9f": {"denManager": "0x389836Bb8fE644820C5e764CEe5F8Fc8d1a04E9f", "collateral": "******************************************", "sortedDens": "0x7Ab63d21B4b84D661D520302fBf14d55B0A41Ce5", "vault": "0x42b82586c4A1e5E0BFe99356916d5C1FB82d0926", "protocol": "0xb8d17199f58dF90Af367cF48A677A845237dc3DD", "index": -100}}, "managedVaults": {"0x2B9371E53b5301B517c772E117616a5c165081F2": {"contractAddress": "0x2B9371E53b5301B517c772E117616a5c165081F2", "collateral": "******************************************", "denManager": "0xEe49c5FF02b70c4099EA63B1792d963ABD9947f0", "getterAddress": "0x58457c601D7ACc3a312AD6F4E0527d23BD50e2a8"}, "0x9b6cf6ab16c409b3a2c796211c274c8a8da28d1d": {"contractAddress": "0x9b6cf6ab16c409b3a2c796211c274c8a8da28d1d", "collateral": "******************************************", "denManager": "0x1d2A3141BE9a5D4c785A56A01C53Fd02fA4eAa04", "getterAddress": "0x3bb082ee32B7ff019b9767212C88Cb8dF1781CeA"}, "0x9cE81bC708d6F846E4fA64891982f069941DF0C7": {"contractAddress": "0x9cE81bC708d6F846E4fA64891982f069941DF0C7", "collateral": "******************************************", "denManager": "0x8Ed442f8A10d046b2Bd9A5866B2ecd7eE0e2aBEe", "getterAddress": "0xD9aD491aCCa136CBc338ac18F55721F9bac10354"}, "0x26666a82cfE70E1aD048939708cA3ACc4982cF9F": {"contractAddress": "0x26666a82cfE70E1aD048939708cA3ACc4982cF9F", "collateral": "******************************************", "denManager": "0xb6Ab580B36ed2DEF692dc608aFAc0A27C725CD0E", "getterAddress": "0xbEfB54fbBa63C8A5A6a96E1E817Ce61Aac1d8fC4"}, "0xC9Dd6792768d1a72Dc75891549B0301e18F702aa": {"contractAddress": "0xC9Dd6792768d1a72Dc75891549B0301e18F702aa", "collateral": "******************************************", "denManager": "0xcEDEFfec2860409E3ccB0D798C71dD1239259d8A", "getterAddress": "0x1b32e789b32291316004E3df70C3351b240dB6b2"}, "0xd62fB1785dc26514657a165BE71e4f8b14A74a44": {"contractAddress": "0xd62fB1785dc26514657a165BE71e4f8b14A74a44", "collateral": "******************************************", "denManager": "0xE66E9873E5851b25a3b539879265F554c50f2f8c", "getterAddress": "0xf39B05922c7240Cc14eC5882c2C21A34Ff86551c"}, "0x9335B678179D433588a16065C4016133E3c2f523": {"contractAddress": "0x9335B678179D433588a16065C4016133E3c2f523", "collateral": "******************************************", "denManager": "0xFec9F8cd6F7B7a6674CD846938Fe44b001f7a568", "getterAddress": "0xbC750d416A1391274d4F6A9650aFD2fd761ac260"}, "0x8dcb18B561CE7E7b309A2d172bdc2633266dfc85": {"contractAddress": "0x8dcb18B561CE7E7b309A2d172bdc2633266dfc85", "collateral": "******************************************", "denManager": "0xA0f32354419c2e02cd5D535edd5Fd7F5e81Fbe96", "getterAddress": "0x4487Ec9c11dc6B6a0C0D70B19334FA1651Bf778f"}, "0xeC5CB1b6849258eEab0613139DFf7698ae256997": {"contractAddress": "0xeC5CB1b6849258eEab0613139DFf7698ae256997", "collateral": "******************************************", "denManager": "0x2016885fC46E879Aa3D170C7472Ce623090D4c1a", "getterAddress": "0x765D38A431e7Df9188354812F8436f3A29C5bB40"}, "0x849232E2144BD5118B5e4A070FE15035cC07b388": {"contractAddress": "0x849232E2144BD5118B5e4A070FE15035cC07b388", "collateral": "******************************************", "denManager": "0x9A3549ef882584a687C1FF1843e3B3C07a2A0cB2", "getterAddress": "0xF2F907bCD9bB3007804E0dD96392f376546b9cD6"}, "0x86a9DcbBf816A99e0422143A8E4A326F6811Fb01": {"contractAddress": "0x86a9DcbBf816A99e0422143A8E4A326F6811Fb01", "collateral": "******************************************", "denManager": "0xC94CD2ADB51E5CbE2d9fa0d09481a24929d7B7fF", "getterAddress": "0xbCF6206F646920A3AdD515BD5906eD1A80625871"}, "0x8e669500abf7990215f522cee0f903aeff337097": {"contractAddress": "0x8e669500abf7990215f522cee0f903aeff337097", "collateral": "******************************************", "denManager": "0x026c153b8e5b807805F2dfD8a370D9352C8cc20C", "getterAddress": "0x06405dB1f27C21f088eC77D5F3fa75522c20302B"}, "0x0d1177dE4C98140C8de12BFdE63A851fC80e29d9": {"contractAddress": "0x0d1177dE4C98140C8de12BFdE63A851fC80e29d9", "collateral": "******************************************", "denManager": "0x1189F8585ca59FdDF0BD5E59d917531b91D39B8C", "getterAddress": "0x17832bC0077357B985A728C8272F4AcDEC1D7016"}, "0x3a427d3A7842E83E0514CF017ab4735a5e443C25": {"contractAddress": "0x3a427d3A7842E83E0514CF017ab4735a5e443C25", "collateral": "******************************************", "denManager": "0xB9268b1DD4cC9F09981f095FE1Ef3B6e8Dd81ed5", "getterAddress": "0x967216939E6018d4292247b408F16D5746826E92"}, "0x2509710322dc52CD4dC5e88fcE1E5B556a913977": {"contractAddress": "0x2509710322dc52CD4dC5e88fcE1E5B556a913977", "collateral": "******************************************", "denManager": "0xFA830d4e4AAe7b65Ce76Fd4186Eb962739DE3CF0", "getterAddress": "0x8a407C56E7bA5B6C929099013C071a7eBD44C654"}, "0x853784dc856f6e691b9d6dae8833a6dc22d18c74": {"contractAddress": "0x853784dc856f6e691b9d6dae8833a6dc22d18c74", "collateral": "******************************************", "denManager": "0x0ce2CF1B106686CDA9D01c247ccc3Bae1c2E9C15", "getterAddress": "0xdC6dB7f2d80cCcC8f7a575d849d8ab1799197551"}, "0x9d6a82e212c9319cd41c2e34fb282d03550791d7": {"contractAddress": "0x9d6a82e212c9319cd41c2e34fb282d03550791d7", "collateral": "******************************************", "denManager": "0xDcc63C4dB3cF9C3C61ed38e7DAD658F8c7AB950d", "getterAddress": "0x2A7782A965D5E9a3Fb07a4Eca28FC3cD3486A66B"}, "0xdb036335fd7479988afc73e3378c12fbaa69db3b": {"contractAddress": "0xdb036335fd7479988afc73e3378c12fbaa69db3b", "collateral": "******************************************", "denManager": "0x3C68cb0eC450f607c6d1E12ed0F9C1B6dfcc3822", "getterAddress": "0xc411c24D5238E9285c1e2D3f7f7b53F2c416828A"}, "0xB3E57D4787995347cFF1B28bCda1a0E9ba916482": {"contractAddress": "0xB3E57D4787995347cFF1B28bCda1a0E9ba916482", "collateral": "******************************************", "denManager": "0xaa58e1691c369f6F2632f7beF7B23551f4d59524", "getterAddress": "0xEF395786e48e1296500E6b74C8DC3E5F057123Ab"}, "0xE194397a1E86bafdbEe3e98728fD93E8c9080433": {"contractAddress": "0xE194397a1E86bafdbEe3e98728fD93E8c9080433", "collateral": "******************************************", "denManager": "0x6ddf065CBD250c69917beA7E51B9b8D7fbE3Aeb4", "getterAddress": "0x8DcC949338441d67B6e98EcB6Ec88a14cfB20F09"}, "0xAf4B3BB2302C69c96242588414e44EAA41D4a445": {"contractAddress": "0xAf4B3BB2302C69c96242588414e44EAA41D4a445", "collateral": "******************************************", "denManager": "0xCdccD6eE755eA20bDB0cACa6E9CA2bCdAC64722b", "getterAddress": "0x6e9C731C6144aC51DD583194Ab3Fb21eFA2DDf42"}, "0x7c62017958ab9878eb1782afe34d365fd5c229e8": {"contractAddress": "0x7c62017958ab9878eb1782afe34d365fd5c229e8", "collateral": "******************************************", "denManager": "0x3C97B31601cA659499a8525850F646D0Fe54c7c8", "getterAddress": "0x8FA466485D7FAA7798fcA6028E4d2121521Ae017"}, "0x0642e5Ea445b5e572E95c381ef67eF3160572f43": {"contractAddress": "0x0642e5Ea445b5e572E95c381ef67eF3160572f43", "collateral": "******************************************", "denManager": "0xF9dB1b321CF012f9a1189Fdf5b9aE97864a96c8C", "getterAddress": "0x94f2355552b7442A70D6CA16B82A0795Ae35e717"}, "0xEC5F6Cf02731B1a76CdF11E83bC8Ca9922ef9439": {"contractAddress": "0xEC5F6Cf02731B1a76CdF11E83bC8Ca9922ef9439", "collateral": "******************************************", "denManager": "0x053EEbc21D5129CDB1abf7EAf09D59b19e75B8ce", "getterAddress": "0x3239Cdda1Aa0004E5E9857781fdA30dE0b59BA67"}, "0x5929Fa7F900C2Ec72C45DF985508CE1ac3B54c71": {"contractAddress": "0x5929Fa7F900C2Ec72C45DF985508CE1ac3B54c71", "collateral": "******************************************", "denManager": "0x19493316159Ebe9D8aBFa36443Cdb289594d69C2", "getterAddress": "0x97C04C345B82761F84F15c47EEc81A34EB741C6D"}, "0xC14C24d1f89bf4dd5E488bA94De2fEd4DE6127b1": {"contractAddress": "0xC14C24d1f89bf4dd5E488bA94De2fEd4DE6127b1", "collateral": "******************************************", "denManager": "0x9657290e0f824638362c96Bfce0d62120E3fA395", "getterAddress": "0x61556B0112f0643dAc96168930B980818412a592"}, "0xbc82396f53Bd66617c905B9224647DBe6F29DC3b": {"contractAddress": "0xbc82396f53Bd66617c905B9224647DBe6F29DC3b", "collateral": "******************************************", "denManager": "0xc50ECe3ea18a265Fe46Cad306C72fFc8CC5ed20b", "getterAddress": "0x11Cf8eb76845F1Df26d2A84D1b9d14D1408E11ca"}, "0x9Cd9759688728f53dbdB4a95623F40F03803051c": {"contractAddress": "0x9Cd9759688728f53dbdB4a95623F40F03803051c", "collateral": "******************************************", "denManager": "0xbCdb96d989cFfa5d75dA93F0a4eAE8bf07F9dD6D", "getterAddress": "0x4Ab34c52008D23EA0faCDcb539c24c6249b0BfB3"}, "0x08cdab555E4519D02bc9431dFE01463613D3123B": {"contractAddress": "0x08cdab555E4519D02bc9431dFE01463613D3123B", "collateral": "******************************************", "denManager": "0x4489f63DC4cEAFd7eAE59346A283aC7B6f047cED", "getterAddress": "0x5172E17650A64Ea005cEE586D7765be27d3975a4"}, "0x72Bc8209eCFEc365Cb9CF2438914ca7aDfB78bE8": {"contractAddress": "0x72Bc8209eCFEc365Cb9CF2438914ca7aDfB78bE8", "collateral": "******************************************", "denManager": "0xf872934CBD465952cb0Bc7735aF5EFE98e2ee4F2", "getterAddress": "0x0712E857B880A6024778dE35Ca4dAFaedf7504A4"}, "0xFBEE201e32c776EEF30715bFE632B5544d2DbE93": {"contractAddress": "0xFBEE201e32c776EEF30715bFE632B5544d2DbE93", "collateral": "******************************************", "denManager": "0x97BF0bf851e02F62664ded5cCBD582a16AAA36bC", "getterAddress": "0x5f891Ff1aB3A8320a821f177d3A2B97E603Afda6"}, "0x583B0f2325a11F52b5c0C937ABB4F2407168ccBD": {"contractAddress": "0x583B0f2325a11F52b5c0C937ABB4F2407168ccBD", "collateral": "******************************************", "denManager": "0x51dB782783019B04cf706B4ce9bEBD9700De297b", "getterAddress": "0xa43890aC77885f1fF6fF77F9A03c3003076063d6"}, "0x68c761eeb006d91D0e6eFcB8Bc490a22d8D95010": {"contractAddress": "0x68c761eeb006d91D0e6eFcB8Bc490a22d8D95010", "collateral": "******************************************", "denManager": "******************************************", "getterAddress": "******************************************"}, "******************************************": {"contractAddress": "******************************************", "collateral": "******************************************", "denManager": "******************************************", "getterAddress": "******************************************"}}, "collateralTokens": {"******************************************": {"ticker": "SolvBTC", "decimals": 18, "category": ["btc"]}, "******************************************": {"ticker": "xSolvBTC", "decimals": 18, "category": ["btc"]}, "******************************************": {"ticker": "uniBTC", "decimals": 8, "category": ["btc"]}, "******************************************": {"ticker": "pumpBTC", "decimals": 8, "category": ["btc"]}, "******************************************": {"ticker": "beraETH", "decimals": 18, "category": ["eth"]}, "******************************************": {"ticker": "STONE", "decimals": 18, "category": ["eth"]}, "******************************************": {"ticker": "StoneBTC", "decimals": 18, "category": ["btc"]}, "******************************************": {"ticker": "WETH", "decimals": 18, "category": ["eth"]}, "******************************************": {"ticker": "ylstETH", "decimals": 18, "category": ["eth"]}, "******************************************": {"ticker": "ylBTCLST", "decimals": 8, "category": ["btc"]}, "******************************************": {"ticker": "ylpumpBTC", "decimals": 8, "category": ["btc"]}, "******************************************": {"ticker": "stBTC", "decimals": 18, "category": ["btc"]}, "******************************************": {"ticker": "rsETH", "decimals": 18, "category": ["eth"]}, "******************************************": {"ticker": "ylrsETH", "decimals": 18, "category": ["eth"]}, "******************************************": {"ticker": "USDe", "decimals": 18, "category": ["stable"]}, "******************************************": {"ticker": "USDC.e", "decimals": 6, "category": ["stable"]}, "******************************************": {"ticker": "HONEY", "decimals": 18, "category": ["stable"]}, "******************************************": {"ticker": "WBTC", "decimals": 8, "category": ["btc"]}, "******************************************": {"ticker": "BERA", "decimals": 18, "category": ["bera"]}, "******************************************": {"ticker": "wBERA", "decimals": 18, "category": ["bera"]}, "******************************************": {"ticker": "BYUSD", "decimals": 6, "category": ["stable"]}, "******************************************": {"ticker": "OHM", "decimals": 9}, "******************************************": {"ticker": "iBERA", "decimals": 18, "category": ["bera"]}, "******************************************": {"ticker": "NAV", "decimals": 18}, "******************************************": {"ticker": "bm", "decimals": 18}, "******************************************": {"ticker": "YEET", "decimals": 18}, "******************************************": {"ticker": "USDbr", "decimals": 18, "category": ["stable"]}, "******************************************": {"ticker": "RAMEN", "decimals": 18}, "******************************************": {"ticker": "LBGT", "decimals": 18, "category": ["bera"]}, "******************************************": {"ticker": "DINERO", "decimals": 18}, "******************************************": {"ticker": "RUSD", "decimals": 18, "category": ["stable"]}, "******************************************": {"ticker": "sNECT", "decimals": 18, "getUrl": "https://app.beraborrow.com/pool/deposit", "protocol": "Beraborrow", "category": ["stable"]}, "******************************************": {"ticker": "NECT", "decimals": 18, "getUrl": "https://app.beraborrow.com/borrow/deposit", "protocol": "Beraborrow", "category": ["stable"]}, "******************************************": {"ticker": "HOLD", "decimals": 18}, "******************************************": {"ticker": "wgBERA", "decimals": 18, "category": ["bera"]}, "******************************************": {"ticker": "sUSDe", "decimals": 18, "category": ["stable"]}, "******************************************": {"ticker": "<PERSON><PERSON>", "decimals": 18}, "******************************************": {"ticker": "BREAD", "decimals": 18}, "******************************************": {"ticker": "OOGA", "decimals": 18}, "******************************************": {"ticker": "HENLO", "decimals": 18}, "******************************************": {"ticker": "weETH", "decimals": 18, "category": ["eth"]}, "******************************************": {"ticker": "eBTC", "decimals": 8, "category": ["btc"]}, "******************************************": {"ticker": "LBTC", "decimals": 8, "category": ["btc"]}, "******************************************": {"ticker": "solvBTC.BERA", "decimals": 18, "getUrl": "https://app.solv.finance/bera?network=bera", "protocol": "Solv", "category": ["btc"]}, "******************************************": {"ticker": "brBTC", "decimals": 8, "category": ["btc"]}, "******************************************": {"ticker": "USDT0", "decimals": 18, "category": ["stable"]}, "******************************************": {"ticker": "USDT0-rUSD", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["stable"]}, "******************************************": {"ticker": "NECT-USDT0", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["stable"]}, "******************************************": {"ticker": "brBTC-uniBTC", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["btc"]}, "******************************************": {"ticker": "NECT-USDC.e-HONEY", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "token3": "******************************************", "provider": "<PERSON><PERSON><PERSON>"}, "getUrl": "https://app.burrbear.io/#/berachain/pool/0xd10e65a5f8ca6f835f2b1832e37cf150fb955f23000000000000000000000004/add-liquidity", "protocol": "<PERSON><PERSON><PERSON>", "category": ["stable"]}, "******************************************": {"ticker": "rsETH-beraETH", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?chain=berachain_mainnet", "protocol": "Kodiak", "category": ["eth"]}, "******************************************": {"ticker": "WBTC-pumpBTC", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?chain=berachain_mainnet", "protocol": "Kodiak", "category": ["btc"]}, "******************************************": {"ticker": "USDe-HONEY", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?chain=berachain_mainnet", "protocol": "Kodiak", "category": ["stable"]}, "******************************************": {"ticker": "sUSDe-HONEY", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?chain=berachain_mainnet", "protocol": "Kodiak", "category": ["stable"]}, "******************************************": {"ticker": "wETH-STONE", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["eth"]}, "******************************************": {"ticker": "beraETH-STONE", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["eth"]}, "******************************************": {"ticker": "WETH-wBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["eth", "bera"]}, "******************************************": {"ticker": "eBTC-wBTC", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?chain=berachain_mainnet", "protocol": "Kodiak", "category": ["btc"]}, "******************************************": {"ticker": "weETH-wETH", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["eth"]}, "******************************************": {"ticker": "HENLO-wBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Aquabera"}, "getUrl": "https://app.aquabera.com/vault/******************************************", "protocol": "Aquabera", "category": ["bera"]}, "******************************************": {"ticker": "OOGA-wBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "******************************************": {"ticker": "LBGT-BREAD", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/0x54c603173ca92a42b81f8838d705fd3e0f98d5d4?farm=0x359424085d84c490a7b276e4bc72cef90a04068b&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "******************************************": {"ticker": "Pollen-wBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "******************************************": {"ticker": "SolvBTC-xSOLVBTC", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["btc"]}, "******************************************": {"ticker": "SolvBTC-WBTC", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["btc"]}, "******************************************": {"ticker": "HOLD-WBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "******************************************": {"ticker": "NECT-wgBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "<PERSON><PERSON><PERSON>"}, "getUrl": "https://app.burrbear.io/#/berachain/pool/0xe416c064946112c1626d6700d1081a750b1b1dd7000200000000000000000008", "protocol": "<PERSON><PERSON><PERSON>", "category": ["bera", "stable"]}, "******************************************": {"ticker": "WBERA-LBGT", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Be<PERSON>"}, "getUrl": "https://hub.berachain.com/pools/0x705fc16ba5a1eb67051934f2fb17eacae660f6c70002000000000000000000d5/deposit/", "protocol": "Be<PERSON>", "category": ["bera"]}, "******************************************": {"ticker": "WBERA-DINERO", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Be<PERSON>"}, "getUrl": "https://hub.berachain.com/pools/0x2461e93d5963c2bb69de499676763e67a63c7ba50002000000000000000000c2/deposit/", "protocol": "Be<PERSON>", "category": ["bera"]}, "******************************************": {"ticker": "WETH-beraETH", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Be<PERSON>"}, "getUrl": "https://hub.berachain.com/pools/0x258d8933b625566fbe057874121783a2808adafa0000000000000000000000c1/deposit/", "protocol": "Be<PERSON>", "category": ["eth"]}, "******************************************": {"ticker": "rUSD-HONEY", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["stable"]}, "******************************************": {"ticker": "WBTC-WBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera", "btc"]}, "******************************************": {"ticker": "OHM-WBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "******************************************": {"ticker": "WBTC-uniBTC", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["btc"]}, "******************************************": {"ticker": "WBERA-RAMEN", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "******************************************": {"ticker": "USDbr-HONEY", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["stable"]}, "******************************************": {"ticker": "YEET-WBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/0xec8ba456b4e009408d0776cde8b91f8717d13fa1?farm=0x0710abffb1a54211a5e88d18bf9854cba86d0819&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "0x0FD67ac75F9DF51f5d81F582417B318F44b45f5c": {"ticker": "bm-WBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/0x0fd67ac75f9df51f5d81f582417b318f44b45f5c?farm=0x193ff57dc9efa1dec154946c10332ba31c8e72b2&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "0xEf8C3239E0D8Be9d58f145E18F5909Ddad681365": {"ticker": "NAV-WBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/0xef8c3239e0d8be9d58f145e18f5909ddad681365?farm=0x66eb42c499372e897929efbf6026821b0a148119&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "******************************************": {"ticker": "WBERA-iBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/0xe3eeb9e48934634d8b5b39a0d15dd89ee0f969c4?farm=0xa2c5adb20a446fa71a1762002e3c9b4dd37dbaf4&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "******************************************": {"ticker": "WBERA-iBGT", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera"]}, "******************************************": {"ticker": "WETH-beraETH", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?chain=berachain_mainnet", "protocol": "Kodiak", "category": ["eth"]}, "******************************************": {"ticker": "OHM-HONEY", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["stable"]}, "******************************************": {"ticker": "WBTC-HONEY", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "category": ["stable", "btc"]}, "******************************************": {"ticker": "WETH-HONEY", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "provider": "Kodiak", "category": ["eth", "bera"]}, "******************************************": {"ticker": "WETH-WBTC", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "provider": "Kodiak", "category": ["eth", "btc"]}, "******************************************": {"ticker": "HONEY-BERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Kodiak"}, "getUrl": "https://app.kodiak.finance/#/liquidity/pools/******************************************?farm=******************************************&chain=berachain_mainnet", "protocol": "Kodiak", "category": ["bera", "stable"]}, "******************************************": {"ticker": "HONEY-WBERA", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Be<PERSON>"}, "getUrl": "https://hub.berachain.com/pools/0x2c4a603a2aa5596287a06886862dc29d56dbc354000200000000000000000002/deposit/", "protocol": "Be<PERSON>", "category": ["bera", "stable"]}, "******************************************": {"ticker": "WBERA-WBTC", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Be<PERSON>"}, "getUrl": "https://hub.berachain.com/pools/0x38fdd999fe8783037db1bbfe465759e312f2d809000200000000000000000004/deposit/", "protocol": "Be<PERSON>", "category": ["bera", "btc"]}, "******************************************": {"ticker": "WBERA-WETH", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Be<PERSON>"}, "getUrl": "https://hub.berachain.com/pools/0xdd70a5ef7d8cfe5c5134b5f9874b09fb5ce812b4000200000000000000000003/deposit/", "protocol": "Be<PERSON>", "category": ["bera", "eth"]}, "******************************************": {"ticker": "BYUSD-HONEY", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Be<PERSON>"}, "getUrl": "https://hub.berachain.com/pools/0xde04c469ad658163e2a5e860a03a86b52f6fa8c8000000000000000000000000/deposit/", "protocol": "Be<PERSON>", "category": ["stable"]}, "******************************************": {"ticker": "HONEY-USDC.e", "decimals": 18, "lpToken": {"token1": "******************************************", "token2": "******************************************", "provider": "Be<PERSON>"}, "getUrl": "https://hub.berachain.com/pools/0xf961a8f6d8c69e7321e78d254ecafbcc3a637621000000000000000000000001/deposit/", "protocol": "Be<PERSON>", "category": ["stable"]}, "******************************************": {"ticker": "iBGT", "decimals": 18, "getUrl": "https://app.oogabooga.io/?fromToken=0X1CE0A25D13CE4D52071AE7E02CF1F6606F4C79D3&toToken=0XAC03CABA51E17C86C921E1F6CBFBDC91F8BB2E6B", "protocol": "Infrared", "category": ["bera"]}}, "vaults": {"0x42b82586c4A1e5E0BFe99356916d5C1FB82d0926": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.USDT0-rUSD", "decimals": 18, "apyType": "assets", "public": true}, "0x2354801363308e95b45E417334667908da723524": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.wgbera", "decimals": 18, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.NECT-USDT0", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.BRBTC-UNIBTC", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 4049555, "collateral": "******************************************", "ticker": "BB.NECT-USDC.e-HONEY", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.solvBTC.BERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "B<PERSON><PERSON>beraEth-STONE", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.STONE-WETH", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.sUSDe-HONEY", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.USDe-HONEY", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.pumpBTC-WBTC", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.rsETH-beraETH", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.eBTC", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.p.eBTC", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.weETH", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.LBTC", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 1, "collateral": "******************************************", "ticker": "BB.WETH-wBERA", "decimals": 18, "public": false, "apyType": "assets"}, "******************************************": {"startBlock": 4049555, "collateral": "******************************************", "ticker": "BB.weETH-wETH", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 4047570, "collateral": "******************************************", "ticker": "BB.eBTC-wBTC", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 3782799, "collateral": "******************************************", "ticker": "BB.HENLO-wBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 3782744, "collateral": "******************************************", "ticker": "BB.OOGA-wBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 3782601, "collateral": "******************************************", "ticker": "BB.LBGT-BREAD", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 3412580, "collateral": "******************************************", "ticker": "BB.POLLEN-WBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 1940609, "collateral": "******************************************", "ticker": "BB.sUSDe", "decimals": 18}, "******************************************": {"startBlock": 3525264, "collateral": "******************************************", "ticker": "BB.WBTC-uniBTC", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 3522038, "collateral": "******************************************", "ticker": "BB.SolvBTC-WBTC", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 3521574, "collateral": "******************************************", "ticker": "BB.SolvBTC-xSOLVBTC", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 3476942, "collateral": "******************************************", "ticker": "BB.HOLD-WBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 3476855, "collateral": "******************************************", "ticker": "BB.NECT-wgBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 3398935, "collateral": "******************************************", "ticker": "BB.SNECT", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 3131127, "collateral": "******************************************", "ticker": "BB.rUSD-HONEY", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 3122960, "collateral": "******************************************", "ticker": "BB.WETH-beraETH", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 3122731, "collateral": "******************************************", "ticker": "BB.WBERA-DINERO", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 3119897, "collateral": "******************************************", "ticker": "BB.WBERA-LBGT", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 3128890, "collateral": "******************************************", "ticker": "BB.WBTC-WBERA", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 3117398, "collateral": "******************************************", "ticker": "BB.HONEY-BERA", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 290633, "collateral": "******************************************", "ticker": "BB.iBERA", "decimals": 18}, "******************************************": {"startBlock": 2902272, "collateral": "******************************************", "ticker": "BB.OHM-WBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 2902272, "collateral": "******************************************", "ticker": "B<PERSON><PERSON>-RAMEN", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 2900402, "collateral": "******************************************", "ticker": "BB.<PERSON>br-HONEY", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "0xD08BE9db61798FbE8a5F10C01e31C39956Ad545B": {"startBlock": 2900092, "collateral": "******************************************", "ticker": "BB.YEET-WBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "0xc837E31a5440A294a8296237986aFeC64C43dA75": {"startBlock": 2899974, "collateral": "0x0FD67ac75F9DF51f5d81F582417B318F44b45f5c", "ticker": "BB.bm-WBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "0xD190E3F0E174b5869d10d3d02AF4e6d1FdEaf0B0": {"startBlock": 2868996, "collateral": "0xEf8C3239E0D8Be9d58f145E18F5909Ddad681365", "ticker": "BB.NAV-WBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "0x33aD23316A0C3dd999d4AeDe4fe99074DE42324b": {"startBlock": 2865886, "collateral": "******************************************", "ticker": "BB.WBERA-iBERA", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 2857122, "collateral": "******************************************", "ticker": "BB.wBERA-iBGT", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 2779999, "collateral": "******************************************", "ticker": "BB.WETH-beraETH", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 2770012, "collateral": "******************************************", "ticker": "BB.OHM-HONEY", "decimals": 18, "public": true, "apyType": "assets"}, "******************************************": {"startBlock": 2548580, "collateral": "******************************************", "ticker": "BB.OHM", "decimals": 18}, "******************************************": {"startBlock": 2516209, "collateral": "******************************************", "ticker": "BB.WETH", "decimals": 18}, "******************************************": {"startBlock": 2514947, "collateral": "******************************************", "ticker": "BB.WBTC", "decimals": 18}, "******************************************": {"startBlock": 1424917, "collateral": "******************************************", "ticker": "BB.wBERA", "decimals": 18}, "******************************************": {"startBlock": 1350913, "collateral": "******************************************", "ticker": "BB.kWETH-WBTC", "decimals": 18, "apyType": "assets"}, "******************************************": {"startBlock": 1350913, "collateral": "******************************************", "ticker": "BB.kWETH-HONEY", "decimals": 18, "apyType": "assets"}, "******************************************": {"startBlock": 1350911, "collateral": "******************************************", "ticker": "BB.kWBTC-HONEY", "decimals": 18, "apyType": "assets"}, "******************************************": {"startBlock": 1350909, "collateral": "******************************************", "ticker": "BB.RSETH", "decimals": 18}, "******************************************": {"startBlock": 1350908, "collateral": "******************************************", "ticker": "BB.YLSTETH", "decimals": 18}, "******************************************": {"startBlock": 1350908, "collateral": "******************************************", "ticker": "BB.WETH", "decimals": 18}, "******************************************": {"startBlock": 1350906, "collateral": "******************************************", "ticker": "BB.STONE", "decimals": 18}, "******************************************": {"startBlock": 1350904, "collateral": "******************************************", "ticker": "BB.BERAETH", "decimals": 18}, "******************************************": {"startBlock": 1350903, "collateral": "******************************************", "ticker": "BB.UNIBTC", "decimals": 18, "apyType": "assets"}, "******************************************": {"startBlock": 1350900, "collateral": "******************************************", "ticker": "BB.xSOLVBTC", "decimals": 18}, "******************************************": {"startBlock": 1350900, "collateral": "******************************************", "ticker": "BB.SOLVBTC", "decimals": 18}, "******************************************": {"startBlock": 1309270, "collateral": "******************************************", "ticker": "BB.PUMPBTC", "decimals": 18}, "******************************************": {"startBlock": 2771260, "collateral": "******************************************", "ticker": "BB.HONEY-WBERA", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 2771260, "collateral": "******************************************", "ticker": "BB.WBERA-WBTC", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 2771260, "collateral": "******************************************", "ticker": "BB.WBERA-WETH", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 2771260, "collateral": "******************************************", "ticker": "BB.BYUSD-HONEY", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 2771260, "collateral": "******************************************", "ticker": "BB.HONEY-USDC.e", "decimals": 18, "public": true, "apyType": "assets", "oracleless": true}, "******************************************": {"startBlock": 2771260, "collateral": "******************************************", "ticker": "BB.iBGT", "decimals": 18, "name": "", "description": "<PERSON><PERSON><PERSON><PERSON>", "public": true, "apyType": "ibgt"}}, "psmBondCollaterals": ["******************************************"]}}