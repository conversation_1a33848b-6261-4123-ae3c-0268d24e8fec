function log2BigInt(value: bigint): bigint {
  let count = BigInt(0);
  while (value > BigInt(1)) {
    value >>= BigInt(1);
    count++;
  }
  return count;
}
// Takes ~6-7K (use 10K to be safe) to update lastFeeOperationTime, but the cost of calculating the
// decayed baseRate increases logarithmically with time elapsed since the last update.
export const addGasForBaseRateUpdate = (gas: bigint, maxMinutesSinceLastUpdate = 10n) =>
  gas + (BigInt(10000) + BigInt(1414) * log2BigInt(maxMinutesSinceLastUpdate + BigInt(1)) + BigInt(1));

// First traversal in ascending direction takes ~50K, then ~13.5K per extra step.
// 80K should be enough for 3 steps, plus some extra to be safe.
export const addGasForPotentialListTraversal = (gas: bigint) => gas + BigInt(80000);
export const addGasForPOLLENIssuance = (gas: bigint) => gas + BigInt(50000);
