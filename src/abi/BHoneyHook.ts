export default [
  {
    type: "constructor",
    inputs: [
      { name: "_b<PERSON><PERSON>", type: "address", internalType: "address" },
      { name: "_honey", type: "address", internalType: "address" },
      { name: "_collVaultRouter", type: "address", internalType: "address" },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "preDepositHook",
    inputs: [
      { name: "owner", type: "address", internalType: "address" },
      { name: "data", type: "bytes", internalType: "bytes" },
    ],
    outputs: [],
    stateMutability: "payable",
  },
] as const;
