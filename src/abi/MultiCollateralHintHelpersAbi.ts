export default [
  {
    type: "constructor",
    inputs: [
      { name: "_borrowerOperationsAddress", type: "address", internalType: "address" },
      { name: "_gasCompensation", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "DEBT_GAS_COMPENSATION", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "DECIMAL_PRECISION", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "PERCENT_DIVISOR", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "borrowerOperations", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBorrowerOperations" }], stateMutability: "view" },
  {
    type: "function",
    name: "computeCR",
    inputs: [
      { name: "_coll", type: "uint256", internalType: "uint256" },
      { name: "_debt", type: "uint256", internalType: "uint256" },
      { name: "_price", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "computeNominalCR",
    inputs: [
      { name: "_coll", type: "uint256", internalType: "uint256" },
      { name: "_debt", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "getApproxHint",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "_CR", type: "uint256", internalType: "uint256" },
      { name: "_numTrials", type: "uint256", internalType: "uint256" },
      { name: "_inputRandomSeed", type: "uint256", internalType: "uint256" },
    ],
    outputs: [
      { name: "hintAddress", type: "address", internalType: "address" },
      { name: "diff", type: "uint256", internalType: "uint256" },
      { name: "latestRandomSeed", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getRedemptionHints",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "_debtAmount", type: "uint256", internalType: "uint256" },
      { name: "_price", type: "uint256", internalType: "uint256" },
      { name: "_maxIterations", type: "uint256", internalType: "uint256" },
    ],
    outputs: [
      { name: "firstRedemptionHint", type: "address", internalType: "address" },
      { name: "partialRedemptionHintNICR", type: "uint256", internalType: "uint256" },
      { name: "truncatedDebtAmount", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
] as const;
