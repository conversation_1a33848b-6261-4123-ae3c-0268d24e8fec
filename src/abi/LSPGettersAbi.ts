export default [
  { type: "constructor", inputs: [{ name: "_lsp", type: "address", internalType: "contract ILiquidStabilityPool" }], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "balance",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "beraborrowCore", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "collateralTokens", inputs: [], outputs: [{ name: "res", type: "address[]", internalType: "address[]" }], stateMutability: "view" },
  {
    type: "function",
    name: "containsExtraAsset",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "emissionScheduleEmissions",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint128", internalType: "uint128" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "emissionScheduleLockTimestamp",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint64", internalType: "uint64" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "emissionScheduleUnlockRatePerSecond",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint64", internalType: "uint64" }],
    stateMutability: "view",
  },
  { type: "function", name: "extraAssets", inputs: [], outputs: [{ name: "res", type: "address[]", internalType: "address[]" }], stateMutability: "view" },
  {
    type: "function",
    name: "extraAssetsIndex",
    inputs: [{ name: "asset", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "feeReceiver", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "firstSunsetIndexKey", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  {
    type: "function",
    name: "getBalanceOfWithFutureEmissions",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getFullProfitUnlockTimestamp",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getThreshold",
    inputs: [
      { name: "tokenIn", type: "address", internalType: "address" },
      { name: "tokenOut", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTokenVirtualBalance",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "indexByCollateral",
    inputs: [{ name: "collateral", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "isFactory",
    inputs: [{ name: "factory", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "isLiquidationManager",
    inputs: [{ name: "liquidationManager", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  { type: "function", name: "lsp", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract ILiquidStabilityPool" }], stateMutability: "view" },
  { type: "function", name: "nextSunsetIndexKey", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  {
    type: "function",
    name: "sunsetIndexExpiry",
    inputs: [{ name: "index", type: "uint16", internalType: "uint16" }],
    outputs: [{ name: "", type: "uint128", internalType: "uint128" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "sunsetIndexIdx",
    inputs: [{ name: "index", type: "uint16", internalType: "uint16" }],
    outputs: [{ name: "", type: "uint128", internalType: "uint128" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "threshold",
    inputs: [{ name: "key", type: "bytes32", internalType: "bytes32" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "unlockRatePerSecond",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "unlockedEmissions",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
] as const;
