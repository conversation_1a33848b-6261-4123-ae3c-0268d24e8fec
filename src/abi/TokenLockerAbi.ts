export default [
  {
    type: "constructor",
    inputs: [
      { name: "_beraborrowCore", type: "address", internalType: "address" },
      { name: "_token", type: "address", internalType: "contract IPollenToken" },
      { name: "_voter", type: "address", internalType: "contract IIncentiveVoting" },
      { name: "_manager", type: "address", internalType: "address" },
      { name: "_lockToTokenRatio", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "BERABORROW_CORE", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBeraborrowCore" }], stateMutability: "view" },
  { type: "function", name: "MAX_LOCK_WEEKS", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "allowPenaltyWithdrawAfter", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "beraborrowCore", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBeraborrowCore" }], stateMutability: "view" },
  { type: "function", name: "deploymentManager", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "extendLock",
    inputs: [
      { name: "_amount", type: "uint256", internalType: "uint256" },
      { name: "_weeks", type: "uint256", internalType: "uint256" },
      { name: "_newWeeks", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "extendMany",
    inputs: [
      {
        name: "newExtendLocks",
        type: "tuple[]",
        internalType: "struct TokenLocker.ExtendLockData[]",
        components: [
          { name: "amount", type: "uint256", internalType: "uint256" },
          { name: "currentWeeks", type: "uint256", internalType: "uint256" },
          { name: "newWeeks", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "freeze", inputs: [], outputs: [], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "getAccountActiveLocks",
    inputs: [
      { name: "account", type: "address", internalType: "address" },
      { name: "minWeeks", type: "uint256", internalType: "uint256" },
    ],
    outputs: [
      {
        name: "lockData",
        type: "tuple[]",
        internalType: "struct TokenLocker.LockData[]",
        components: [
          { name: "amount", type: "uint256", internalType: "uint256" },
          { name: "weeksToUnlock", type: "uint256", internalType: "uint256" },
        ],
      },
      { name: "frozenAmount", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getAccountBalances",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
    outputs: [
      { name: "locked", type: "uint256", internalType: "uint256" },
      { name: "unlocked", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getAccountWeight",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getAccountWeightAt",
    inputs: [
      { name: "account", type: "address", internalType: "address" },
      { name: "week", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getAccountWeightWrite",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "getTotalWeight", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getTotalWeightAt",
    inputs: [{ name: "week", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "getTotalWeightWrite", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "nonpayable" },
  { type: "function", name: "getWeek", inputs: [], outputs: [{ name: "week", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getWithdrawWithPenaltyAmounts",
    inputs: [
      { name: "account", type: "address", internalType: "address" },
      { name: "amountToWithdraw", type: "uint256", internalType: "uint256" },
    ],
    outputs: [
      { name: "amountWithdrawn", type: "uint256", internalType: "uint256" },
      { name: "penaltyAmountPaid", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  { type: "function", name: "guardian", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "incentiveVoter", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IIncentiveVoting" }], stateMutability: "view" },
  {
    type: "function",
    name: "lock",
    inputs: [
      { name: "_account", type: "address", internalType: "address" },
      { name: "_amount", type: "uint256", internalType: "uint256" },
      { name: "_weeks", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "lockMany",
    inputs: [
      { name: "_account", type: "address", internalType: "address" },
      {
        name: "newLocks",
        type: "tuple[]",
        internalType: "struct TokenLocker.LockData[]",
        components: [
          { name: "amount", type: "uint256", internalType: "uint256" },
          { name: "weeksToUnlock", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "lockToTokenRatio", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "lockToken", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IPollenToken" }], stateMutability: "view" },
  { type: "function", name: "owner", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "penaltyWithdrawalsEnabled", inputs: [], outputs: [{ name: "", type: "bool", internalType: "bool" }], stateMutability: "view" },
  {
    type: "function",
    name: "setAllowPenaltyWithdrawAfter",
    inputs: [{ name: "_timestamp", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setPenaltyWithdrawalsEnabled",
    inputs: [{ name: "_enabled", type: "bool", internalType: "bool" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "totalDecayRate", inputs: [], outputs: [{ name: "", type: "uint32", internalType: "uint32" }], stateMutability: "view" },
  { type: "function", name: "totalUpdatedWeek", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  { type: "function", name: "unfreeze", inputs: [{ name: "keepIncentivesVote", type: "bool", internalType: "bool" }], outputs: [], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "withdrawExpiredLocks",
    inputs: [{ name: "_weeks", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "withdrawWithPenalty",
    inputs: [{ name: "amountToWithdraw", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "event",
    name: "LockCreated",
    inputs: [
      { name: "account", type: "address", indexed: true, internalType: "address" },
      { name: "amount", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_weeks", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LockExtended",
    inputs: [
      { name: "account", type: "address", indexed: true, internalType: "address" },
      { name: "amount", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_weeks", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "newWeeks", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LocksCreated",
    inputs: [
      { name: "account", type: "address", indexed: true, internalType: "address" },
      {
        name: "newLocks",
        type: "tuple[]",
        indexed: false,
        internalType: "struct TokenLocker.LockData[]",
        components: [
          { name: "amount", type: "uint256", internalType: "uint256" },
          { name: "weeksToUnlock", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LocksExtended",
    inputs: [
      { name: "account", type: "address", indexed: true, internalType: "address" },
      {
        name: "locks",
        type: "tuple[]",
        indexed: false,
        internalType: "struct TokenLocker.ExtendLockData[]",
        components: [
          { name: "amount", type: "uint256", internalType: "uint256" },
          { name: "currentWeeks", type: "uint256", internalType: "uint256" },
          { name: "newWeeks", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LocksFrozen",
    inputs: [
      { name: "account", type: "address", indexed: true, internalType: "address" },
      { name: "amount", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LocksUnfrozen",
    inputs: [
      { name: "account", type: "address", indexed: true, internalType: "address" },
      { name: "amount", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LocksWithdrawn",
    inputs: [
      { name: "account", type: "address", indexed: true, internalType: "address" },
      { name: "withdrawn", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "penalty", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
] as const;
