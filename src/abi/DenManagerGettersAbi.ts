export default [
  { type: "constructor", inputs: [{ name: "_factory", type: "address", internalType: "contract IFactory" }], stateMutability: "nonpayable" },
  { type: "function", name: "factory", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IFactory" }], stateMutability: "view" },
  {
    type: "function",
    name: "getActiveDenManagersForAccount",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "address[]", internalType: "address[]" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getAllCollateralsAndDenManagers",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "tuple[]",
        internalType: "struct DenManagerGetters.Collateral[]",
        components: [
          { name: "collateral", type: "address", internalType: "address" },
          { name: "denManagers", type: "address[]", internalType: "address[]" },
        ],
      },
    ],
    stateMutability: "view",
  },
] as const;
