export default [
  {
    type: "constructor",
    inputs: [
      { name: "_beraborrowCore", type: "address", internalType: "address" },
      { name: "_debtToken", type: "address", internalType: "contract IDebtToken" },
      { name: "_liquidStabilityPool", type: "address", internalType: "contract ILiquidStabilityPool" },
      { name: "_borrowerOperations", type: "address", internalType: "contract IBorrowerOperations" },
      { name: "_sortedDens", type: "address", internalType: "address" },
      { name: "_denManager", type: "address", internalType: "address" },
      { name: "_liquidationManager", type: "address", internalType: "contract ILiquidationManager" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "BERABORROW_CORE", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBeraborrowCore" }], stateMutability: "view" },
  { type: "function", name: "borrowerOperations", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBorrowerOperations" }], stateMutability: "view" },
  { type: "function", name: "debtToken", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IDebtToken" }], stateMutability: "view" },
  { type: "function", name: "denManagerCount", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "denManagerImpl", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "denManagers",
    inputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "deployNewInstance",
    inputs: [
      { name: "collateral", type: "address", internalType: "address" },
      { name: "priceFeed", type: "address", internalType: "address" },
      { name: "customDenManagerImpl", type: "address", internalType: "address" },
      { name: "customSortedDensImpl", type: "address", internalType: "address" },
      {
        name: "params",
        type: "tuple",
        internalType: "struct IFactory.DeploymentParams",
        components: [
          { name: "minuteDecayFactor", type: "uint256", internalType: "uint256" },
          { name: "redemptionFeeFloor", type: "uint256", internalType: "uint256" },
          { name: "maxRedemptionFee", type: "uint256", internalType: "uint256" },
          { name: "borrowingFeeFloor", type: "uint256", internalType: "uint256" },
          { name: "maxBorrowingFee", type: "uint256", internalType: "uint256" },
          { name: "interestRateInBps", type: "uint256", internalType: "uint256" },
          { name: "maxDebt", type: "uint256", internalType: "uint256" },
          { name: "MCR", type: "uint256", internalType: "uint256" },
          { name: "collVaultRouter", type: "address", internalType: "address" },
        ],
      },
      { name: "unlockRatePerSecond", type: "uint64", internalType: "uint64" },
      { name: "forceThroughLspBalanceCheck", type: "bool", internalType: "bool" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "guardian", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "liquidStabilityPool", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract ILiquidStabilityPool" }], stateMutability: "view" },
  { type: "function", name: "liquidationManager", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract ILiquidationManager" }], stateMutability: "view" },
  { type: "function", name: "owner", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "setImplementations",
    inputs: [
      { name: "_denManagerImpl", type: "address", internalType: "address" },
      { name: "_sortedDensImpl", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "sortedDensImpl", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "event",
    name: "NewDeployment",
    inputs: [
      { name: "collateral", type: "address", indexed: false, internalType: "address" },
      { name: "priceFeed", type: "address", indexed: false, internalType: "address" },
      { name: "denManager", type: "address", indexed: false, internalType: "address" },
      { name: "sortedDens", type: "address", indexed: false, internalType: "address" },
    ],
    anonymous: false,
  },
] as const;
