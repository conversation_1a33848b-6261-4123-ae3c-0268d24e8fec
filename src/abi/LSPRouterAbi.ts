export default [
  {
    type: "constructor",
    inputs: [
      { name: "_lsp", type: "address", internalType: "address" },
      { name: "_lspGetters", type: "address", internalType: "address" },
      { name: "_collVault<PERSON>outer", type: "address", internalType: "address" },
      { name: "_priceFeed", type: "address", internalType: "address" },
      { name: "_metaBeraborrowCore", type: "address", internalType: "address" },
      { name: "_ob<PERSON>out<PERSON>", type: "address", internalType: "address" },
      { name: "_wB<PERSON>", type: "address", internalType: "address" },
      { name: "_IbgtV<PERSON>", type: "address", internalType: "address" },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "claimLockedTokens",
    inputs: [
      { name: "tokens", type: "address[]", internalType: "contract IERC20[]" },
      { name: "amounts", type: "uint256[]", internalType: "uint256[]" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "deposit",
    inputs: [
      {
        name: "params",
        type: "tuple",
        internalType: "struct ILSPRouter.DepositTokenParams",
        components: [
          { name: "inputToken", type: "address", internalType: "address" },
          { name: "inputAmount", type: "uint256", internalType: "uint256" },
          { name: "minSharesReceived", type: "uint256", internalType: "uint256" },
          { name: "receiver", type: "address", internalType: "address" },
          { name: "dexCalldata", type: "bytes", internalType: "bytes" },
        ],
      },
    ],
    outputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
    stateMutability: "payable",
  },
  { type: "function", name: "lspUnderlyingTokens", inputs: [], outputs: [{ name: "tokens", type: "address[]", internalType: "address[]" }], stateMutability: "view" },
  {
    type: "function",
    name: "previewRedeemPreferredUnderlying",
    inputs: [
      { name: "shares", type: "uint256", internalType: "uint256" },
      { name: "preferredUnderlyingTokens", type: "address[]", internalType: "address[]" },
      { name: "unwrap", type: "bool", internalType: "bool" },
    ],
    outputs: [
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "tokens", type: "address[]", internalType: "address[]" },
      { name: "amounts", type: "uint256[]", internalType: "uint256[]" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "redeem",
    inputs: [
      {
        name: "params",
        type: "tuple",
        internalType: "struct ILSPRouter.RedeemWithoutPrederredUnderlyingParams",
        components: [
          { name: "shares", type: "uint256", internalType: "uint256" },
          { name: "receiver", type: "address", internalType: "address" },
          { name: "minAssetsWithdrawn", type: "uint256", internalType: "uint256" },
          { name: "minUnderlyingWithdrawn", type: "uint256[]", internalType: "uint256[]" },
          { name: "receivedTokensLengthHint", type: "uint256", internalType: "uint256" },
          { name: "tokensToClaim", type: "address[]", internalType: "address[]" },
          { name: "unwrap", type: "bool", internalType: "bool" },
        ],
      },
    ],
    outputs: [
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "tokens", type: "address[]", internalType: "address[]" },
      { name: "amounts", type: "uint256[]", internalType: "uint256[]" },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "redeemPreferredUnderlying",
    inputs: [
      {
        name: "params",
        type: "tuple",
        internalType: "struct ILSPRouter.RedeemPreferredUnderlyingParams",
        components: [
          { name: "shares", type: "uint256", internalType: "uint256" },
          { name: "preferredUnderlyingTokens", type: "address[]", internalType: "address[]" },
          { name: "receiver", type: "address", internalType: "address" },
          { name: "minAssetsWithdrawn", type: "uint256", internalType: "uint256" },
          { name: "minUnderlyingWithdrawn", type: "uint256[]", internalType: "uint256[]" },
          { name: "unwrap", type: "bool", internalType: "bool" },
          { name: "redeemedTokensLength", type: "uint8", internalType: "uint8" },
        ],
      },
    ],
    outputs: [
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "tokens", type: "address[]", internalType: "address[]" },
      { name: "amounts", type: "uint256[]", internalType: "uint256[]" },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "redeemPreferredUnderlyingToOne",
    inputs: [
      {
        name: "params",
        type: "tuple",
        internalType: "struct ILSPRouter.RedeemPreferredUnderlyingToOneParams",
        components: [
          { name: "shares", type: "uint256", internalType: "uint256" },
          { name: "preferredUnderlyingTokens", type: "address[]", internalType: "address[]" },
          { name: "receiver", type: "address", internalType: "address" },
          { name: "targetToken", type: "address", internalType: "address" },
          { name: "minAssetsWithdrawn", type: "uint256", internalType: "uint256" },
          { name: "minUnderlyingWithdrawn", type: "uint256[]", internalType: "uint256[]" },
          { name: "pathDefinitions", type: "bytes[]", internalType: "bytes[]" },
          { name: "minOutputs", type: "uint256[]", internalType: "uint256[]" },
          { name: "quoteAmounts", type: "uint256[]", internalType: "uint256[]" },
          { name: "minTargetTokenAmount", type: "uint256", internalType: "uint256" },
          { name: "executor", type: "address", internalType: "address" },
          { name: "referralCode", type: "uint32", internalType: "uint32" },
          { name: "redeemedTokensLength", type: "uint8", internalType: "uint8" },
        ],
      },
    ],
    outputs: [
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "totalAmountOut", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "withdraw",
    inputs: [
      {
        name: "params",
        type: "tuple",
        internalType: "struct ILSPRouter.WithdrawFromlspParams",
        components: [
          { name: "assets", type: "uint256", internalType: "uint256" },
          { name: "receiver", type: "address", internalType: "address" },
          { name: "maxSharesWithdrawn", type: "uint256", internalType: "uint256" },
          { name: "unwrap", type: "bool", internalType: "bool" },
          { name: "minUnderlyingWithdrawn", type: "uint256[]", internalType: "uint256[]" },
          { name: "receivedTokensLengthHint", type: "uint256", internalType: "uint256" },
          { name: "tokensToClaim", type: "address[]", internalType: "address[]" },
        ],
      },
    ],
    outputs: [
      { name: "shares", type: "uint256", internalType: "uint256" },
      { name: "tokens", type: "address[]", internalType: "address[]" },
      { name: "amounts", type: "uint256[]", internalType: "uint256[]" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "error", name: "DuplicateToken", inputs: [] },
  { type: "error", name: "InvalidToken", inputs: [] },
] as const;
