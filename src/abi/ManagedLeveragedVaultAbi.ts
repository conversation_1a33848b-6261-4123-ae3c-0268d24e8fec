export default [
  { type: "constructor", inputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "UPGRADE_INTERFACE_VERSION", inputs: [], outputs: [{ name: "", type: "string", internalType: "string" }], stateMutability: "view" },
  {
    type: "function",
    name: "allowance",
    inputs: [
      { name: "owner", type: "address", internalType: "address" },
      { name: "spender", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "approve",
    inputs: [
      { name: "spender", type: "address", internalType: "address" },
      { name: "value", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "asset", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "balanceOf",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "cancelWithdrawalIntent",
    inputs: [
      { name: "epoch", type: "uint256", internalType: "uint256" },
      { name: "sharesToCancel", type: "uint256", internalType: "uint256" },
      { name: "receiver", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "computeLosses",
    inputs: [{ name: "totalShares", type: "uint256", internalType: "uint256" }],
    outputs: [
      { name: "epochValueLoss", type: "uint256", internalType: "uint256" },
      { name: "maxCollWithdrawnWithPremium", type: "uint256", internalType: "uint256" },
      { name: "nectCompensation", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "convertToAssets",
    inputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "convertToShares",
    inputs: [{ name: "assets", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "decimals", inputs: [], outputs: [{ name: "", type: "uint8", internalType: "uint8" }], stateMutability: "view" },
  {
    type: "function",
    name: "decreaseLeverage",
    inputs: [
      { name: "_exposureAmount", type: "uint256", internalType: "uint256" },
      { name: "_upperHint", type: "address", internalType: "address" },
      { name: "_lowerHint", type: "address", internalType: "address" },
      {
        name: "exposureToNectParams",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.ExternalRebalanceParams",
        components: [
          { name: "swapper", type: "address", internalType: "address" },
          { name: "payload", type: "bytes", internalType: "bytes" },
          { name: "minRebalanceOut", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    outputs: [{ name: "debt", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "deposit",
    inputs: [
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "receiver", type: "address", internalType: "address" },
      {
        name: "params",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.AddCollParams",
        components: [
          { name: "upperHint", type: "address", internalType: "address" },
          { name: "lowerHint", type: "address", internalType: "address" },
          { name: "minSharesOut", type: "uint256", internalType: "uint256" },
          { name: "minCollVaultShares", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "deposit",
    inputs: [
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "receiver", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "executeWithdrawalEpoch",
    inputs: [
      {
        name: "params",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.ExecuteWithdrawalParams",
        components: [
          { name: "epoch", type: "uint256", internalType: "uint256" },
          { name: "upperHint", type: "address", internalType: "address" },
          { name: "lowerHint", type: "address", internalType: "address" },
        ],
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "extSloads",
    inputs: [{ name: "slots", type: "bytes32[]", internalType: "bytes32[]" }],
    outputs: [{ name: "res", type: "bytes32[]", internalType: "bytes32[]" }],
    stateMutability: "view",
  },
  { type: "function", name: "getCollVaultBalance", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getCollVaultSharesToWithdraw",
    inputs: [
      { name: "_collVaultSharesRequested", type: "uint256", internalType: "uint256" },
      { name: "_debtToUnwind", type: "uint256", internalType: "uint256" },
      { name: "_exposureWithdrawn", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "getCollateralValue", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "getCurrentDenICR", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getCutOffStartTimestamp",
    inputs: [{ name: "_epoch", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  { type: "function", name: "getDebtBalance", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getDebtToUnwindAndCollRequested",
    inputs: [{ name: "_epoch", type: "uint256", internalType: "uint256" }],
    outputs: [
      { name: "debtToUnwind", type: "uint256", internalType: "uint256" },
      { name: "collVaultSharesRequested", type: "uint256", internalType: "uint256" },
      { name: "shareFee", type: "uint256", internalType: "uint256" },
      { name: "currICR", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  { type: "function", name: "getExposureBalance", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "getExposureValue", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "getPnL", inputs: [], outputs: [{ name: "", type: "int256", internalType: "int256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getPrice",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "scaledPriceInUsdWad", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "getTargetICR", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getWithdrawalRequestEpoch",
    inputs: [{ name: "timestamp", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "increaseLeverage",
    inputs: [
      { name: "_maxFeePercentage", type: "uint256", internalType: "uint256" },
      { name: "_debtAmount", type: "uint256", internalType: "uint256" },
      { name: "_upperHint", type: "address", internalType: "address" },
      { name: "_lowerHint", type: "address", internalType: "address" },
      {
        name: "params",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.ExternalRebalanceParams",
        components: [
          { name: "swapper", type: "address", internalType: "address" },
          { name: "payload", type: "bytes", internalType: "bytes" },
          { name: "minRebalanceOut", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    outputs: [{ name: "exposure", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "maxDeposit",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "maxMint",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "maxRedeem",
    inputs: [{ name: "owner", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "maxWithdraw",
    inputs: [{ name: "owner", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "mint",
    inputs: [
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "name", inputs: [], outputs: [{ name: "", type: "string", internalType: "string" }], stateMutability: "view" },
  {
    type: "function",
    name: "onFlashLoan",
    inputs: [
      { name: "initiator", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "data", type: "bytes", internalType: "bytes" },
    ],
    outputs: [{ name: "", type: "bytes32", internalType: "bytes32" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "openDen",
    inputs: [
      { name: "_maxFeePercentage", type: "uint256", internalType: "uint256" },
      { name: "_minCollVaultShares", type: "uint256", internalType: "uint256" },
      { name: "_upperHint", type: "address", internalType: "address" },
      { name: "_lowerHint", type: "address", internalType: "address" },
      {
        name: "exposureToAssetParams",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.ExternalRebalanceParams",
        components: [
          { name: "swapper", type: "address", internalType: "address" },
          { name: "payload", type: "bytes", internalType: "bytes" },
          { name: "minRebalanceOut", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "previewDeposit",
    inputs: [{ name: "assets", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "previewMint",
    inputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "previewRedeem",
    inputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "previewWithdraw",
    inputs: [{ name: "assets", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "proxiableUUID", inputs: [], outputs: [{ name: "", type: "bytes32", internalType: "bytes32" }], stateMutability: "view" },
  {
    type: "function",
    name: "realizeLoss",
    inputs: [
      {
        name: "params",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.RealizeLossParams",
        components: [
          { name: "upperHint", type: "address", internalType: "address" },
          { name: "lowerHint", type: "address", internalType: "address" },
          { name: "withdrawalEpoch", type: "uint256", internalType: "uint256" },
          {
            name: "collVaultToNectParams",
            type: "tuple",
            internalType: "struct IManagedLeveragedVault.ExternalRebalanceParams",
            components: [
              { name: "swapper", type: "address", internalType: "address" },
              { name: "payload", type: "bytes", internalType: "bytes" },
              { name: "minRebalanceOut", type: "uint256", internalType: "uint256" },
            ],
          },
        ],
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "realizeProfit",
    inputs: [
      { name: "profitsPercentageInBP", type: "uint16", internalType: "uint16" },
      {
        name: "exposureToCollParams",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.ExternalRebalanceParams",
        components: [
          { name: "swapper", type: "address", internalType: "address" },
          { name: "payload", type: "bytes", internalType: "bytes" },
          { name: "minRebalanceOut", type: "uint256", internalType: "uint256" },
        ],
      },
      { name: "_upperHint", type: "address", internalType: "address" },
      { name: "_lowerHint", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "redeem",
    inputs: [
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "redeemIntent",
    inputs: [
      { name: "shares", type: "uint256", internalType: "uint256" },
      { name: "receiver", type: "address", internalType: "address" },
      { name: "owner", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "assets", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setPairThreshold",
    inputs: [
      { name: "_tokenIn", type: "address", internalType: "address" },
      { name: "_tokenOut", type: "address", internalType: "address" },
      { name: "_thresholdInBP", type: "uint16", internalType: "uint16" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setParameters",
    inputs: [
      {
        name: "params",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.VaultParameters",
        components: [
          { name: "denICR", type: "uint64", internalType: "uint64" },
          { name: "maxDeviationICRinBP", type: "uint16", internalType: "uint16" },
          { name: "denManager", type: "address", internalType: "address" },
          { name: "keeper", type: "address", internalType: "address" },
          { name: "borrowerOperations", type: "address", internalType: "address" },
          { name: "entryFeeInBP", type: "uint16", internalType: "uint16" },
          { name: "exitFeeInBP", type: "uint16", internalType: "uint16" },
          { name: "maxCompensationInBP", type: "uint16", internalType: "uint16" },
          { name: "realizeLossThresholdInBP", type: "uint16", internalType: "uint16" },
          { name: "withdrawalMaxLossInBP", type: "uint16", internalType: "uint16" },
          { name: "epochOffset", type: "uint256", internalType: "uint256" },
          { name: "exposureToken", type: "address", internalType: "address" },
        ],
      },
      { name: "_previousTotalAssets", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setWhitelist",
    inputs: [
      { name: "_swapper", type: "address", internalType: "address" },
      { name: "_whitelisted", type: "bool", internalType: "bool" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "symbol", inputs: [], outputs: [{ name: "", type: "string", internalType: "string" }], stateMutability: "view" },
  { type: "function", name: "totalAssets", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "totalSupply", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "transfer",
    inputs: [
      { name: "to", type: "address", internalType: "address" },
      { name: "value", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "transferFrom",
    inputs: [
      { name: "from", type: "address", internalType: "address" },
      { name: "to", type: "address", internalType: "address" },
      { name: "value", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "upgradeToAndCall",
    inputs: [
      { name: "newImplementation", type: "address", internalType: "address" },
      { name: "data", type: "bytes", internalType: "bytes" },
    ],
    outputs: [],
    stateMutability: "payable",
  },
  {
    type: "function",
    name: "withdraw",
    inputs: [
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "withdrawFromEpoch",
    inputs: [
      { name: "epoch", type: "uint256", internalType: "uint256" },
      { name: "receiver", type: "address", internalType: "address" },
      {
        name: "unwrapParams",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.ExternalRebalanceParams",
        components: [
          { name: "swapper", type: "address", internalType: "address" },
          { name: "payload", type: "bytes", internalType: "bytes" },
          { name: "minRebalanceOut", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "event",
    name: "Approval",
    inputs: [
      { name: "owner", type: "address", indexed: true, internalType: "address" },
      { name: "spender", type: "address", indexed: true, internalType: "address" },
      { name: "value", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "BoycoAuthorized",
    inputs: [
      { name: "boyco", type: "address", indexed: false, internalType: "address" },
      { name: "authorized", type: "bool", indexed: false, internalType: "bool" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "Deposit",
    inputs: [
      { name: "sender", type: "address", indexed: true, internalType: "address" },
      { name: "owner", type: "address", indexed: true, internalType: "address" },
      { name: "assets", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "shares", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "EpochExecuted",
    inputs: [
      { name: "epoch", type: "uint256", indexed: true, internalType: "uint256" },
      { name: "totalShares", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "wrappedAssetsWithdrawnRequested", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  { type: "event", name: "Initialized", inputs: [{ name: "version", type: "uint64", indexed: false, internalType: "uint64" }], anonymous: false },
  {
    type: "event",
    name: "LossRealized",
    inputs: [
      { name: "epoch", type: "uint256", indexed: true, internalType: "uint256" },
      { name: "totalShares", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "collLoss", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "epochCollLoss", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "NewPairThreshold",
    inputs: [
      { name: "tokenIn", type: "address", indexed: false, internalType: "address" },
      { name: "tokenOut", type: "address", indexed: false, internalType: "address" },
      { name: "threshold", type: "uint16", indexed: false, internalType: "uint16" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ParametersUpdated",
    inputs: [
      {
        name: "params",
        type: "tuple",
        indexed: false,
        internalType: "struct IManagedLeveragedVault.VaultParameters",
        components: [
          { name: "denICR", type: "uint64", internalType: "uint64" },
          { name: "maxDeviationICRinBP", type: "uint16", internalType: "uint16" },
          { name: "denManager", type: "address", internalType: "address" },
          { name: "keeper", type: "address", internalType: "address" },
          { name: "borrowerOperations", type: "address", internalType: "address" },
          { name: "entryFeeInBP", type: "uint16", internalType: "uint16" },
          { name: "exitFeeInBP", type: "uint16", internalType: "uint16" },
          { name: "maxCompensationInBP", type: "uint16", internalType: "uint16" },
          { name: "realizeLossThresholdInBP", type: "uint16", internalType: "uint16" },
          { name: "withdrawalMaxLossInBP", type: "uint16", internalType: "uint16" },
          { name: "epochOffset", type: "uint256", internalType: "uint256" },
          { name: "exposureToken", type: "address", internalType: "address" },
        ],
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ProfitRealized",
    inputs: [
      { name: "profitInExposure", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "sentAmount", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "coll", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "SwapperWhitelisted",
    inputs: [
      { name: "swapper", type: "address", indexed: true, internalType: "address" },
      { name: "whitelisted", type: "bool", indexed: false, internalType: "bool" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "Transfer",
    inputs: [
      { name: "from", type: "address", indexed: true, internalType: "address" },
      { name: "to", type: "address", indexed: true, internalType: "address" },
      { name: "value", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  { type: "event", name: "Upgraded", inputs: [{ name: "implementation", type: "address", indexed: true, internalType: "address" }], anonymous: false },
  {
    type: "event",
    name: "Withdraw",
    inputs: [
      { name: "sender", type: "address", indexed: true, internalType: "address" },
      { name: "receiver", type: "address", indexed: true, internalType: "address" },
      { name: "owner", type: "address", indexed: true, internalType: "address" },
      { name: "assets", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "shares", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "WithdrawalIntentCanceled",
    inputs: [
      { name: "user", type: "address", indexed: true, internalType: "address" },
      { name: "receiver", type: "address", indexed: false, internalType: "address" },
      { name: "epoch", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "shares", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "WithdrawalProcessed",
    inputs: [
      { name: "user", type: "address", indexed: true, internalType: "address" },
      { name: "receiver", type: "address", indexed: true, internalType: "address" },
      { name: "epoch", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "shares", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "assets", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "WithdrawalRequested",
    inputs: [
      { name: "user", type: "address", indexed: true, internalType: "address" },
      { name: "shares", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "epoch", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  { type: "error", name: "AlreadyOpened", inputs: [] },
  { type: "error", name: "AlreadyReported", inputs: [] },
  { type: "error", name: "AmountZero", inputs: [] },
  { type: "error", name: "BadDebt", inputs: [{ name: "amountInUsd", type: "uint256", internalType: "uint256" }] },
  { type: "error", name: "BelowCR", inputs: [] },
  {
    type: "error",
    name: "BelowThreshold",
    inputs: [
      { name: "sentValue", type: "uint256", internalType: "uint256" },
      { name: "receivedValue", type: "uint256", internalType: "uint256" },
    ],
  },
  { type: "error", name: "CancelTooLate", inputs: [] },
  { type: "error", name: "ERC1967InvalidImplementation", inputs: [{ name: "implementation", type: "address", internalType: "address" }] },
  { type: "error", name: "ERC1967NonPayable", inputs: [] },
  {
    type: "error",
    name: "ERC20InsufficientAllowance",
    inputs: [
      { name: "spender", type: "address", internalType: "address" },
      { name: "allowance", type: "uint256", internalType: "uint256" },
      { name: "needed", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "ERC20InsufficientBalance",
    inputs: [
      { name: "sender", type: "address", internalType: "address" },
      { name: "balance", type: "uint256", internalType: "uint256" },
      { name: "needed", type: "uint256", internalType: "uint256" },
    ],
  },
  { type: "error", name: "ERC20InvalidApprover", inputs: [{ name: "approver", type: "address", internalType: "address" }] },
  { type: "error", name: "ERC20InvalidReceiver", inputs: [{ name: "receiver", type: "address", internalType: "address" }] },
  { type: "error", name: "ERC20InvalidSender", inputs: [{ name: "sender", type: "address", internalType: "address" }] },
  { type: "error", name: "ERC20InvalidSpender", inputs: [{ name: "spender", type: "address", internalType: "address" }] },
  {
    type: "error",
    name: "ERC4626ExceededMaxDeposit",
    inputs: [
      { name: "receiver", type: "address", internalType: "address" },
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "max", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "ERC4626ExceededMaxMint",
    inputs: [
      { name: "receiver", type: "address", internalType: "address" },
      { name: "shares", type: "uint256", internalType: "uint256" },
      { name: "max", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "ERC4626ExceededMaxRedeem",
    inputs: [
      { name: "owner", type: "address", internalType: "address" },
      { name: "shares", type: "uint256", internalType: "uint256" },
      { name: "max", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "ERC4626ExceededMaxWithdraw",
    inputs: [
      { name: "owner", type: "address", internalType: "address" },
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "max", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "EpochTooEarly",
    inputs: [
      { name: "epoch", type: "uint256", internalType: "uint256" },
      { name: "currTs", type: "uint256", internalType: "uint256" },
      { name: "cutOffTs", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "EpochTooLate",
    inputs: [
      { name: "epoch", type: "uint256", internalType: "uint256" },
      { name: "maxEpoch", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "ExceededMaxRedeem",
    inputs: [
      { name: "receiver", type: "address", internalType: "address" },
      { name: "expected", type: "uint256", internalType: "uint256" },
      { name: "actual", type: "uint256", internalType: "uint256" },
    ],
  },
  { type: "error", name: "InvalidExposureToken", inputs: [] },
  { type: "error", name: "InvalidInitialization", inputs: [] },
  { type: "error", name: "LossAlreadyRealized", inputs: [] },
  { type: "error", name: "LossNotRealized", inputs: [] },
  { type: "error", name: "NoLoss", inputs: [] },
  { type: "error", name: "NoProfit", inputs: [{ name: "profit", type: "int256", internalType: "int256" }] },
  { type: "error", name: "NotBoyco", inputs: [{ name: "sender", type: "address", internalType: "address" }] },
  { type: "error", name: "NotInitializing", inputs: [] },
  { type: "error", name: "NotNect", inputs: [{ name: "caller", type: "address", internalType: "address" }] },
  { type: "error", name: "NotOwner", inputs: [{ name: "sender", type: "address", internalType: "address" }] },
  { type: "error", name: "NotReported", inputs: [] },
  { type: "error", name: "NotSupportedMethod", inputs: [] },
  {
    type: "error",
    name: "PositionOutOfTargetCR",
    inputs: [
      { name: "currentICR", type: "uint256", internalType: "uint256" },
      { name: "targetICR", type: "uint256", internalType: "uint256" },
    ],
  },
  { type: "error", name: "SafeERC20FailedOperation", inputs: [{ name: "token", type: "address", internalType: "address" }] },
  {
    type: "error",
    name: "SameCurrency",
    inputs: [
      { name: "sent", type: "address", internalType: "address" },
      { name: "received", type: "address", internalType: "address" },
    ],
  },
  {
    type: "error",
    name: "SurpassedAvailableDebt",
    inputs: [
      { name: "requested", type: "uint256", internalType: "uint256" },
      { name: "available", type: "uint256", internalType: "uint256" },
    ],
  },
  { type: "error", name: "SurpassedPrecision", inputs: [{ name: "precision", type: "uint256", internalType: "uint256" }] },
  {
    type: "error",
    name: "SurpassedTotalShares",
    inputs: [
      { name: "requested", type: "uint256", internalType: "uint256" },
      { name: "totalShares", type: "uint256", internalType: "uint256" },
    ],
  },
  { type: "error", name: "SwapperNotWhitelisted", inputs: [{ name: "swapper", type: "address", internalType: "address" }] },
  { type: "error", name: "TotalAssetsDeviation", inputs: [] },
  { type: "error", name: "UUPSUnauthorizedCallContext", inputs: [] },
  { type: "error", name: "UUPSUnsupportedProxiableUUID", inputs: [{ name: "slot", type: "bytes32", internalType: "bytes32" }] },
  {
    type: "error",
    name: "VaultSlippage",
    inputs: [
      { name: "expected", type: "uint256", internalType: "uint256" },
      { name: "actual", type: "uint256", internalType: "uint256" },
    ],
  },
  { type: "error", name: "ZeroAddress", inputs: [] },
] as const;
