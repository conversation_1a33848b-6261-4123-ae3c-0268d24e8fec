export default [
  {
    inputs: [
      {
        internalType: "address",
        name: "_metaBeraborrowCore",
        type: "address",
      },
      {
        internalType: "address",
        name: "_nect",
        type: "address",
      },
      {
        internalType: "address[]",
        name: "_stables",
        type: "address[]",
      },
      {
        internalType: "address",
        name: "_feeHook",
        type: "address",
      },
      {
        internalType: "address",
        name: "_feeReceiver",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [],
    name: "Address<PERSON><PERSON>",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "AlreadyListed",
    type: "error",
  },
  {
    inputs: [],
    name: "Amount<PERSON><PERSON>",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "NotListedToken",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "caller",
        type: "address",
      },
    ],
    name: "<PERSON>Owner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "mintCap",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "minted",
        type: "uint256",
      },
    ],
    name: "PassedMintCap",
    type: "error",
  },
  {
    inputs: [],
    name: "Paused",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "feePercentage",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "maxFeePercentage",
        type: "uint256",
      },
    ],
    name: "SurpassedFeePercentage",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "caller",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "mintedNect",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "fee",
        type: "uint256",
      },
    ],
    name: "Deposit",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "address",
        name: "feeHook",
        type: "address",
      },
    ],
    name: "FeeHookSet",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "address",
        name: "feeReceiver",
        type: "address",
      },
    ],
    name: "FeeReceiverSet",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "uint256",
        name: "mintCap",
        type: "uint256",
      },
    ],
    name: "MintCap",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "uint256",
        name: "nectBurned",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "address",
        name: "stable",
        type: "address",
      },
    ],
    name: "NectBurned",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "uint256",
        name: "nectMinted",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "address",
        name: "stable",
        type: "address",
      },
    ],
    name: "NectMinted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "bool",
        name: "paused",
        type: "bool",
      },
    ],
    name: "PausedSet",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "caller",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "burnedNect",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "fee",
        type: "uint256",
      },
    ],
    name: "Withdraw",
    type: "event",
  },
  {
    inputs: [],
    name: "DEFAULT_FEE",
    outputs: [
      {
        internalType: "uint16",
        name: "",
        type: "uint16",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_stable",
        type: "address",
      },
    ],
    name: "blacklistStable",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "receiver",
        type: "address",
      },
      {
        internalType: "uint16",
        name: "maxFeePercentage",
        type: "uint16",
      },
    ],
    name: "deposit",
    outputs: [
      {
        internalType: "uint256",
        name: "mintedNect",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "feeHook",
    outputs: [
      {
        internalType: "contract IFeeHook",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "feeReceiver",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "metaBeraborrowCore",
    outputs: [
      {
        internalType: "contract IMetaBeraborrowCore",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "nectAmount",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "receiver",
        type: "address",
      },
      {
        internalType: "uint16",
        name: "maxFeePercentage",
        type: "uint16",
      },
    ],
    name: "mint",
    outputs: [
      {
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
    ],
    name: "mintCap",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "nect",
    outputs: [
      {
        internalType: "contract IDebtToken",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
    ],
    name: "nectMinted",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "paused",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
      {
        internalType: "uint16",
        name: "maxFeePercentage",
        type: "uint16",
      },
    ],
    name: "previewDeposit",
    outputs: [
      {
        internalType: "uint256",
        name: "mintedNect",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "nectFee",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "nectAmount",
        type: "uint256",
      },
      {
        internalType: "uint16",
        name: "maxFeePercentage",
        type: "uint16",
      },
    ],
    name: "previewMint",
    outputs: [
      {
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "nectFee",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "nectAmount",
        type: "uint256",
      },
      {
        internalType: "uint16",
        name: "maxFeePercentage",
        type: "uint16",
      },
    ],
    name: "previewRedeem",
    outputs: [
      {
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "stableFee",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
      {
        internalType: "uint16",
        name: "maxFeePercentage",
        type: "uint16",
      },
    ],
    name: "previewWithdraw",
    outputs: [
      {
        internalType: "uint256",
        name: "burnedNect",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "stableFee",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "nectAmount",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "receiver",
        type: "address",
      },
      {
        internalType: "uint16",
        name: "maxFeePercentage",
        type: "uint16",
      },
    ],
    name: "redeem",
    outputs: [
      {
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_feeHook",
        type: "address",
      },
    ],
    name: "setFeeHook",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_feeReceiver",
        type: "address",
      },
    ],
    name: "setFeeReceiver",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "_mintCap",
        type: "uint256",
      },
    ],
    name: "setMintCap",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bool",
        name: "_paused",
        type: "bool",
      },
    ],
    name: "setPaused",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    name: "stables",
    outputs: [
      {
        internalType: "uint64",
        name: "wadOffset",
        type: "uint64",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_stable",
        type: "address",
      },
    ],
    name: "whitelistStable",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "stable",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "stableAmount",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "receiver",
        type: "address",
      },
      {
        internalType: "uint16",
        name: "maxFeePercentage",
        type: "uint16",
      },
    ],
    name: "withdraw",
    outputs: [
      {
        internalType: "uint256",
        name: "burnedNect",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;
