export default [
  {
    type: "constructor",
    inputs: [
      {
        name: "_metaBeraborrowCore",
        type: "address",
        internalType: "address",
      },
      {
        name: "_ve<PERSON><PERSON><PERSON>",
        type: "address",
        internalType: "address",
      },
      {
        name: "_rewardToken",
        type: "address",
        internalType: "address",
      },
      {
        name: "_rewardDistributor",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "addSource",
    inputs: [
      {
        name: "source",
        type: "address",
        internalType: "address",
      },
      {
        name: "_startWeek",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "allSources",
    inputs: [
      {
        name: "",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [
      {
        name: "",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "claimReward",
    inputs: [],
    outputs: [
      {
        name: "amountRewardOut",
        type: "uint256[]",
        internalType: "uint256[]",
      },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "fees",
    inputs: [
      {
        name: "",
        type: "address",
        internalType: "address",
      },
      {
        name: "",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [
      {
        name: "",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "firstFundedWeek",
    inputs: [
      {
        name: "",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [
      {
        name: "",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "fund",
    inputs: [
      {
        name: "sources",
        type: "address[]",
        internalType: "address[]",
      },
      {
        name: "wTimes",
        type: "uint256[][]",
        internalType: "uint256[][]",
      },
      {
        name: "amounts",
        type: "uint256[][]",
        internalType: "uint256[][]",
      },
      {
        name: "totalAmountToFund",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "getAllActiveSources",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "address[]",
        internalType: "address[]",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "lastFundedWeek",
    inputs: [
      {
        name: "",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [
      {
        name: "",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "metaBeraborrowCore",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "address",
        internalType: "contract IMetaBeraborrowCore",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "rewardDistributor",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "token",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "userInfo",
    inputs: [
      {
        name: "",
        type: "address",
        internalType: "address",
      },
      {
        name: "",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [
      {
        name: "firstUnclaimedWeek",
        type: "uint128",
        internalType: "uint128",
      },
      {
        name: "iter",
        type: "uint128",
        internalType: "uint128",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "vePollen",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "event",
    name: "ClaimReward",
    inputs: [
      {
        name: "source",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "user",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "wTime",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "SourceAdded",
    inputs: [
      {
        name: "source",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "startWeek",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "UpdateFee",
    inputs: [
      {
        name: "source",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "wTime",
        type: "uint256",
        indexed: true,
        internalType: "uint256",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "error",
    name: "ArrayLengthMismatch",
    inputs: [],
  },
  {
    type: "error",
    name: "FDEpochLengthMismatch",
    inputs: [],
  },
  {
    type: "error",
    name: "FDInvalidSource",
    inputs: [
      {
        name: "source",
        type: "address",
        internalType: "address",
      },
    ],
  },
  {
    type: "error",
    name: "FDInvalidStartEpoch",
    inputs: [
      {
        name: "startEpoch",
        type: "uint256",
        internalType: "uint256",
      },
    ],
  },
  {
    type: "error",
    name: "FDInvalidWTimeFund",
    inputs: [
      {
        name: "lastFunded",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "wTime",
        type: "uint256",
        internalType: "uint256",
      },
    ],
  },
  {
    type: "error",
    name: "FDNotOwner",
    inputs: [
      {
        name: "caller",
        type: "address",
        internalType: "address",
      },
    ],
  },
  {
    type: "error",
    name: "FDNotOwnerOrDistributor",
    inputs: [
      {
        name: "caller",
        type: "address",
        internalType: "address",
      },
    ],
  },
  {
    type: "error",
    name: "FDSourceAlreadyExists",
    inputs: [
      {
        name: "source",
        type: "address",
        internalType: "address",
      },
    ],
  },
  {
    type: "error",
    name: "FDTotalAmountFundedNotMatch",
    inputs: [
      {
        name: "actualTotalAmount",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "expectedTotalAmount",
        type: "uint256",
        internalType: "uint256",
      },
    ],
  },
  {
    type: "error",
    name: "ZeroAddress",
    inputs: [],
  },
] as const;
