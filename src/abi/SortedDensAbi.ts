export default [
  {
    type: "function",
    name: "contains",
    inputs: [{ name: "_id", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "data",
    inputs: [],
    outputs: [
      { name: "head", type: "address", internalType: "address" },
      { name: "tail", type: "address", internalType: "address" },
      { name: "size", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  { type: "function", name: "den<PERSON>anager", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IDenManager" }], stateMutability: "view" },
  {
    type: "function",
    name: "findInsertPosition",
    inputs: [
      { name: "_NICR", type: "uint256", internalType: "uint256" },
      { name: "_prevId", type: "address", internalType: "address" },
      { name: "_nextId", type: "address", internalType: "address" },
    ],
    outputs: [
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
    ],
    stateMutability: "view",
  },
  { type: "function", name: "getFirst", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "getLast", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "getNext",
    inputs: [{ name: "_id", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPrev",
    inputs: [{ name: "_id", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  { type: "function", name: "getSize", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "insert",
    inputs: [
      { name: "_id", type: "address", internalType: "address" },
      { name: "_NICR", type: "uint256", internalType: "uint256" },
      { name: "_prevId", type: "address", internalType: "address" },
      { name: "_nextId", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "isEmpty", inputs: [], outputs: [{ name: "", type: "bool", internalType: "bool" }], stateMutability: "view" },
  {
    type: "function",
    name: "reInsert",
    inputs: [
      { name: "_id", type: "address", internalType: "address" },
      { name: "_newNICR", type: "uint256", internalType: "uint256" },
      { name: "_prevId", type: "address", internalType: "address" },
      { name: "_nextId", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "remove", inputs: [{ name: "_id", type: "address", internalType: "address" }], outputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "setAddresses", inputs: [{ name: "_denManagerAddress", type: "address", internalType: "address" }], outputs: [], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "validInsertPosition",
    inputs: [
      { name: "_NICR", type: "uint256", internalType: "uint256" },
      { name: "_prevId", type: "address", internalType: "address" },
      { name: "_nextId", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "event",
    name: "NodeAdded",
    inputs: [
      { name: "_id", type: "address", indexed: false, internalType: "address" },
      { name: "_NICR", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  { type: "event", name: "NodeRemoved", inputs: [{ name: "_id", type: "address", indexed: false, internalType: "address" }], anonymous: false },
] as const;
