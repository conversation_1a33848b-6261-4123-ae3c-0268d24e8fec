export default [
  {
    type: "constructor",
    inputs: [
      { name: "_beraborrowCore", type: "address", internalType: "address" },
      { name: "_gasPoolAddress", type: "address", internalType: "address" },
      { name: "_debtTokenAddress", type: "address", internalType: "address" },
      { name: "_borrowerOperations", type: "address", internalType: "address" },
      { name: "_liquidationManager", type: "address", internalType: "address" },
      { name: "_brimeDen", type: "address", internalType: "address" },
      { name: "_gasCompensation", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "BERABORROW_CORE", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBeraborrowCore" }], stateMutability: "view" },
  { type: "function", name: "DEBT_GAS_COMPENSATION", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "DECIMAL_PRECISION", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "Dens",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [
      { name: "debt", type: "uint256", internalType: "uint256" },
      { name: "coll", type: "uint256", internalType: "uint256" },
      { name: "stake", type: "uint256", internalType: "uint256" },
      { name: "status", type: "uint8", internalType: "enum DenManager.Status" },
      { name: "arrayIndex", type: "uint128", internalType: "uint128" },
      { name: "activeInterestIndex", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  { type: "function", name: "L_collateral", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "L_debt", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "MCR", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "PERCENT_DIVISOR", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "SUNSETTING_INTEREST_RATE", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "activeInterestIndex", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "addCollateralSurplus",
    inputs: [
      { name: "borrower", type: "address", internalType: "address" },
      { name: "collSurplus", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "applyPendingRewards",
    inputs: [{ name: "_borrower", type: "address", internalType: "address" }],
    outputs: [
      { name: "coll", type: "uint256", internalType: "uint256" },
      { name: "debt", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "baseRate", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "borrowerOperations", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "borrowingFeeFloor", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "brimeDen", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "claimCollateral",
    inputs: [
      { name: "borrower", type: "address", internalType: "address" },
      { name: "_receiver", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "closeDen",
    inputs: [
      { name: "_borrower", type: "address", internalType: "address" },
      { name: "_receiver", type: "address", internalType: "address" },
      { name: "collAmount", type: "uint256", internalType: "uint256" },
      { name: "debtAmount", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "closeDenByLiquidation", inputs: [{ name: "_borrower", type: "address", internalType: "address" }], outputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "collVaultRouter", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "collateralToken", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IERC20" }], stateMutability: "view" },
  { type: "function", name: "collectInterests", inputs: [], outputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "debtToken", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IDebtToken" }], stateMutability: "view" },
  {
    type: "function",
    name: "decayBaseRateAndGetBorrowingFee",
    inputs: [{ name: "_debt", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "decreaseDebtAndSendCollateral",
    inputs: [
      { name: "account", type: "address", internalType: "address" },
      { name: "debt", type: "uint256", internalType: "uint256" },
      { name: "coll", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "defaultedCollateral", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "defaultedDebt", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "fetchPrice", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "finalizeLiquidation",
    inputs: [
      { name: "_liquidator", type: "address", internalType: "address" },
      { name: "_debt", type: "uint256", internalType: "uint256" },
      { name: "_coll", type: "uint256", internalType: "uint256" },
      { name: "_collSurplus", type: "uint256", internalType: "uint256" },
      { name: "_debtGasComp", type: "uint256", internalType: "uint256" },
      { name: "_collGasComp", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "getBorrowingFee",
    inputs: [{ name: "_debt", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getBorrowingFeeWithDecay",
    inputs: [{ name: "_debt", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "getBorrowingRate", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "getBorrowingRateWithDecay", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getCurrentICR",
    inputs: [
      { name: "_borrower", type: "address", internalType: "address" },
      { name: "_price", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getDenCollAndDebt",
    inputs: [{ name: "_borrower", type: "address", internalType: "address" }],
    outputs: [
      { name: "coll", type: "uint256", internalType: "uint256" },
      { name: "debt", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getDenFromDenOwnersArray",
    inputs: [{ name: "_index", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  { type: "function", name: "getDenOwnersCount", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getDenStake",
    inputs: [{ name: "_borrower", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getDenStatus",
    inputs: [{ name: "_borrower", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getEntireDebtAndColl",
    inputs: [{ name: "_borrower", type: "address", internalType: "address" }],
    outputs: [
      { name: "debt", type: "uint256", internalType: "uint256" },
      { name: "coll", type: "uint256", internalType: "uint256" },
      { name: "pendingDebtReward", type: "uint256", internalType: "uint256" },
      { name: "pendingCollateralReward", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getEntireSystemBalances",
    inputs: [],
    outputs: [
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  { type: "function", name: "getEntireSystemColl", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "getEntireSystemDebt", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getNominalICR",
    inputs: [{ name: "_borrower", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPendingCollAndDebtRewards",
    inputs: [{ name: "_borrower", type: "address", internalType: "address" }],
    outputs: [
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getRedemptionFeeWithDecay",
    inputs: [{ name: "_collateralDrawn", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "getRedemptionRate", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "getRedemptionRateWithDecay", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "getTotalActiveCollateral", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "getTotalActiveDebt", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "getWeek", inputs: [], outputs: [{ name: "week", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "guardian", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "hasPendingRewards",
    inputs: [{ name: "_borrower", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  { type: "function", name: "interestPayable", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "interestRate", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "lastActiveIndexUpdate", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "lastCollateralError_Redistribution", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "lastDebtError_Redistribution", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "lastFeeOperationTime", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "liquidationManager", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "maxBorrowingFee", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "maxRedemptionFee", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "maxSystemDebt", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "minuteDecayFactor", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "movePendingDenRewardsToActiveBalances",
    inputs: [
      { name: "_debt", type: "uint256", internalType: "uint256" },
      { name: "_collateral", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "openDen",
    inputs: [
      { name: "_borrower", type: "address", internalType: "address" },
      { name: "_collateralAmount", type: "uint256", internalType: "uint256" },
      { name: "_compositeDebt", type: "uint256", internalType: "uint256" },
      { name: "NICR", type: "uint256", internalType: "uint256" },
      { name: "_upperHint", type: "address", internalType: "address" },
      { name: "_lowerHint", type: "address", internalType: "address" },
    ],
    outputs: [
      { name: "stake", type: "uint256", internalType: "uint256" },
      { name: "arrayIndex", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "owner", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "paused", inputs: [], outputs: [{ name: "", type: "bool", internalType: "bool" }], stateMutability: "view" },
  { type: "function", name: "priceFeed", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IPriceFeed" }], stateMutability: "view" },
  {
    type: "function",
    name: "redeemCollateral",
    inputs: [
      { name: "_debtAmount", type: "uint256", internalType: "uint256" },
      { name: "_firstRedemptionHint", type: "address", internalType: "address" },
      { name: "_upperPartialRedemptionHint", type: "address", internalType: "address" },
      { name: "_lowerPartialRedemptionHint", type: "address", internalType: "address" },
      { name: "_partialRedemptionHintNICR", type: "uint256", internalType: "uint256" },
      { name: "_maxIterations", type: "uint256", internalType: "uint256" },
      { name: "_maxFeePercentage", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "redemptionFeeFloor", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "rewardSnapshots",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [
      { name: "collateral", type: "uint256", internalType: "uint256" },
      { name: "debt", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "setAddresses",
    inputs: [
      { name: "_priceFeedAddress", type: "address", internalType: "address" },
      { name: "_sortedDensAddress", type: "address", internalType: "address" },
      { name: "_collateralToken", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "setCollVaultRouter", inputs: [{ name: "_collVaultRouter", type: "address", internalType: "address" }], outputs: [], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "setParameters",
    inputs: [
      {
        name: "params",
        type: "tuple",
        internalType: "struct IFactory.DeploymentParams",
        components: [
          { name: "minuteDecayFactor", type: "uint256", internalType: "uint256" },
          { name: "redemptionFeeFloor", type: "uint256", internalType: "uint256" },
          { name: "maxRedemptionFee", type: "uint256", internalType: "uint256" },
          { name: "borrowingFeeFloor", type: "uint256", internalType: "uint256" },
          { name: "maxBorrowingFee", type: "uint256", internalType: "uint256" },
          { name: "interestRateInBps", type: "uint256", internalType: "uint256" },
          { name: "maxDebt", type: "uint256", internalType: "uint256" },
          { name: "MCR", type: "uint256", internalType: "uint256" },
          { name: "collVaultRouter", type: "address", internalType: "address" },
        ],
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "setPaused", inputs: [{ name: "_paused", type: "bool", internalType: "bool" }], outputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "setPriceFeed", inputs: [{ name: "_priceFeedAddress", type: "address", internalType: "address" }], outputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "sortedDens", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract ISortedDens" }], stateMutability: "view" },
  { type: "function", name: "startSunset", inputs: [], outputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "sunsetting", inputs: [], outputs: [{ name: "", type: "bool", internalType: "bool" }], stateMutability: "view" },
  {
    type: "function",
    name: "surplusBalances",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "systemDeploymentTime", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "totalCollateralSnapshot", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "totalStakes", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "totalStakesSnapshot", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "updateBalances", inputs: [], outputs: [], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "updateDenFromAdjustment",
    inputs: [
      { name: "_isDebtIncrease", type: "bool", internalType: "bool" },
      { name: "_debtChange", type: "uint256", internalType: "uint256" },
      { name: "_netDebtChange", type: "uint256", internalType: "uint256" },
      { name: "_isCollIncrease", type: "bool", internalType: "bool" },
      { name: "_collChange", type: "uint256", internalType: "uint256" },
      { name: "_upperHint", type: "address", internalType: "address" },
      { name: "_lowerHint", type: "address", internalType: "address" },
      { name: "_borrower", type: "address", internalType: "address" },
      { name: "_receiver", type: "address", internalType: "address" },
    ],
    outputs: [
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "event", name: "BaseRateUpdated", inputs: [{ name: "_baseRate", type: "uint256", indexed: false, internalType: "uint256" }], anonymous: false },
  {
    type: "event",
    name: "CollateralSent",
    inputs: [
      { name: "_to", type: "address", indexed: false, internalType: "address" },
      { name: "_amount", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "DenIndexUpdated",
    inputs: [
      { name: "_borrower", type: "address", indexed: false, internalType: "address" },
      { name: "_newIndex", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "DenSnapshotsUpdated",
    inputs: [
      { name: "_L_collateral", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_L_debt", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "DenUpdated",
    inputs: [
      { name: "_borrower", type: "address", indexed: true, internalType: "address" },
      { name: "_debt", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_coll", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_stake", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_operation", type: "uint8", indexed: false, internalType: "enum DenManager.DenManagerOperation" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LTermsUpdated",
    inputs: [
      { name: "_L_collateral", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_L_debt", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  { type: "event", name: "LastFeeOpTimeUpdated", inputs: [{ name: "_lastFeeOpTime", type: "uint256", indexed: false, internalType: "uint256" }], anonymous: false },
  {
    type: "event",
    name: "NewParameters",
    inputs: [
      {
        name: "params",
        type: "tuple",
        indexed: false,
        internalType: "struct IFactory.DeploymentParams",
        components: [
          { name: "minuteDecayFactor", type: "uint256", internalType: "uint256" },
          { name: "redemptionFeeFloor", type: "uint256", internalType: "uint256" },
          { name: "maxRedemptionFee", type: "uint256", internalType: "uint256" },
          { name: "borrowingFeeFloor", type: "uint256", internalType: "uint256" },
          { name: "maxBorrowingFee", type: "uint256", internalType: "uint256" },
          { name: "interestRateInBps", type: "uint256", internalType: "uint256" },
          { name: "maxDebt", type: "uint256", internalType: "uint256" },
          { name: "MCR", type: "uint256", internalType: "uint256" },
          { name: "collVaultRouter", type: "address", internalType: "address" },
        ],
      },
    ],
    anonymous: false,
  },
  { type: "event", name: "PriceFeedUpdated", inputs: [{ name: "_priceFeed", type: "address", indexed: false, internalType: "address" }], anonymous: false },
  {
    type: "event",
    name: "Redemption",
    inputs: [
      { name: "_redeemer", type: "address", indexed: true, internalType: "address" },
      { name: "_attemptedDebtAmount", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_actualDebtAmount", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_collateralSent", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_collateralFee", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "SystemSnapshotsUpdated",
    inputs: [
      { name: "_totalStakesSnapshot", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_totalCollateralSnapshot", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  { type: "event", name: "TotalStakesUpdated", inputs: [{ name: "_newTotalStakes", type: "uint256", indexed: false, internalType: "uint256" }], anonymous: false },
] as const;
