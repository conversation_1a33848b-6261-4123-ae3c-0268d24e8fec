export default [
  { type: "constructor", inputs: [{ name: "_vault", type: "address", internalType: "contract IManagedLeveragedVault" }], stateMutability: "nonpayable" },
  { type: "function", name: "borrowerOps", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "collVault", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "collVaultRouter", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "denICR", inputs: [], outputs: [{ name: "", type: "uint64", internalType: "uint64" }], stateMutability: "view" },
  { type: "function", name: "den<PERSON>anager", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "entryFeebp", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  { type: "function", name: "epochOffset", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "exitFeebp", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  { type: "function", name: "exposureToken", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "getBoycoVaultStorage",
    inputs: [],
    outputs: [
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getEpochReport",
    inputs: [{ name: "epoch", type: "uint256", internalType: "uint256" }],
    outputs: [
      {
        name: "",
        type: "tuple",
        internalType: "struct ManagedLeveragedVaultGetters.EpochReportData",
        components: [
          { name: "totalShares", type: "uint256", internalType: "uint256" },
          { name: "wrappedAssets", type: "uint256", internalType: "uint256" },
          { name: "reported", type: "bool", internalType: "bool" },
          { name: "lossRealized", type: "bool", internalType: "bool" },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getParameters",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "tuple",
        internalType: "struct IManagedLeveragedVault.VaultParameters",
        components: [
          { name: "denICR", type: "uint64", internalType: "uint64" },
          { name: "maxDeviationICRinBP", type: "uint16", internalType: "uint16" },
          { name: "denManager", type: "address", internalType: "address" },
          { name: "keeper", type: "address", internalType: "address" },
          { name: "borrowerOperations", type: "address", internalType: "address" },
          { name: "entryFeeInBP", type: "uint16", internalType: "uint16" },
          { name: "exitFeeInBP", type: "uint16", internalType: "uint16" },
          { name: "maxCompensationInBP", type: "uint16", internalType: "uint16" },
          { name: "realizeLossThresholdInBP", type: "uint16", internalType: "uint16" },
          { name: "withdrawalMaxLossInBP", type: "uint16", internalType: "uint16" },
          { name: "epochOffset", type: "uint256", internalType: "uint256" },
          { name: "exposureToken", type: "address", internalType: "address" },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getRemainingStorage",
    inputs: [],
    outputs: [
      { name: "metaCore_", type: "address", internalType: "address" },
      { name: "lsp_", type: "address", internalType: "address" },
      { name: "collVault_", type: "address", internalType: "address" },
      { name: "nect_", type: "address", internalType: "address" },
      { name: "collVaultRouter_", type: "address", internalType: "address" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getReportBalanceOf",
    inputs: [
      { name: "epoch", type: "uint256", internalType: "uint256" },
      { name: "who", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "isAuthorized",
    inputs: [{ name: "who", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  { type: "function", name: "keeper", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "lsp", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "maxCompensationbp", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  { type: "function", name: "maxDeviationICRbp", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  { type: "function", name: "maxWithdrawalLossbp", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  { type: "function", name: "metaCore", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "nect", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "realizeLossThrbp", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  {
    type: "function",
    name: "reportAt",
    inputs: [{ name: "idx", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "bytes32", internalType: "bytes32" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "swapperWhitelist",
    inputs: [{ name: "swapper", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "threshold",
    inputs: [{ name: "key", type: "bytes32", internalType: "bytes32" }],
    outputs: [{ name: "", type: "uint16", internalType: "uint16" }],
    stateMutability: "view",
  },
  { type: "function", name: "vault", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IManagedLeveragedVault" }], stateMutability: "view" },
  { type: "function", name: "withdrawableWrappedAssets", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
] as const;
