export default [
  {
    type: "constructor",
    inputs: [
      { name: "_liquidStabilityPoolAddress", type: "address", internalType: "contract ILiquidStabilityPool" },
      { name: "_borrowerOperations", type: "address", internalType: "contract IBorrowerOperations" },
      { name: "_factory", type: "address", internalType: "address" },
      { name: "_gasCompensation", type: "uint256", internalType: "uint256" },
      { name: "_validatorPool", type: "address", internalType: "address" },
      { name: "_sNectGauge", type: "address", internalType: "address" },
      { name: "_liquidator<PERSON>ee", type: "uint256", internalType: "uint256" },
      { name: "_sNectGaugeFee", type: "uint256", internalType: "uint256" },
      { name: "_poolFee", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "DEBT_GAS_COMPENSATION", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "DECIMAL_PRECISION", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "DOMAIN_SEPARATOR", inputs: [], outputs: [{ name: "", type: "bytes32", internalType: "bytes32" }], stateMutability: "view" },
  { type: "function", name: "PERCENT_DIVISOR", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "PERMIT_TYPEHASH1", inputs: [], outputs: [{ name: "", type: "bytes32", internalType: "bytes32" }], stateMutability: "view" },
  { type: "function", name: "PERMIT_TYPEHASH2", inputs: [], outputs: [{ name: "", type: "bytes32", internalType: "bytes32" }], stateMutability: "view" },
  { type: "function", name: "PERMIT_TYPEHASH3", inputs: [], outputs: [{ name: "", type: "bytes32", internalType: "bytes32" }], stateMutability: "view" },
  {
    type: "function",
    name: "batchLiquidateDens",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "_denArray", type: "address[]", internalType: "address[]" },
      { name: "liquidator", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "batchLiquidateDensWithPermit",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "_denArray", type: "address[]", internalType: "address[]" },
      { name: "liquidator", type: "address", internalType: "address" },
      { name: "deadline", type: "uint256", internalType: "uint256" },
      { name: "v", type: "uint8", internalType: "uint8" },
      { name: "r", type: "bytes32", internalType: "bytes32" },
      { name: "s", type: "bytes32", internalType: "bytes32" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "borrowerOperations", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBorrowerOperations" }], stateMutability: "view" },
  {
    type: "function",
    name: "enableDenManager",
    inputs: [{ name: "_denManager", type: "address", internalType: "contract IDenManager" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "factory", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "liquidStabilityPool", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract ILiquidStabilityPool" }], stateMutability: "view" },
  {
    type: "function",
    name: "liquidate",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "borrower", type: "address", internalType: "address" },
      { name: "liquidator", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "liquidateDens",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "maxDensToLiquidate", type: "uint256", internalType: "uint256" },
      { name: "maxICR", type: "uint256", internalType: "uint256" },
      { name: "liquidator", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "liquidateDensWithPermit",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "maxDensToLiquidate", type: "uint256", internalType: "uint256" },
      { name: "maxICR", type: "uint256", internalType: "uint256" },
      { name: "liquidator", type: "address", internalType: "address" },
      { name: "deadline", type: "uint256", internalType: "uint256" },
      { name: "v", type: "uint8", internalType: "uint8" },
      { name: "r", type: "bytes32", internalType: "bytes32" },
      { name: "s", type: "bytes32", internalType: "bytes32" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "liquidateWithPermit",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "borrower", type: "address", internalType: "address" },
      { name: "liquidator", type: "address", internalType: "address" },
      { name: "deadline", type: "uint256", internalType: "uint256" },
      { name: "v", type: "uint8", internalType: "uint8" },
      { name: "r", type: "bytes32", internalType: "bytes32" },
      { name: "s", type: "bytes32", internalType: "bytes32" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "liquidationsFeeAndRecipients",
    inputs: [],
    outputs: [
      {
        name: "data",
        type: "tuple",
        internalType: "struct ILiquidationManager.LiquidationFeeData",
        components: [
          { name: "liquidatorFee", type: "uint256", internalType: "uint256" },
          { name: "sNectGaugeFee", type: "uint256", internalType: "uint256" },
          { name: "poolFee", type: "uint256", internalType: "uint256" },
          { name: "validatorPool", type: "address", internalType: "address" },
          { name: "sNectGauge", type: "address", internalType: "address" },
        ],
      },
    ],
    stateMutability: "view",
  },
  { type: "function", name: "liquidatorFee", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "liquidatorLiquidationFee", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "nonces",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "poolFee", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "poolLiquidationFee",
    inputs: [],
    outputs: [
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  { type: "function", name: "sNectGauge", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "sNectGaugeFee", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "sNectGaugeLiquidationFee",
    inputs: [],
    outputs: [
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "setFees",
    inputs: [
      { name: "_liquidatorFee", type: "uint256", internalType: "uint256" },
      { name: "_sNectGaugeFee", type: "uint256", internalType: "uint256" },
      { name: "_poolFee", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "setSNECTGauge", inputs: [{ name: "_sNectGauge", type: "address", internalType: "address" }], outputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "setValidatorPool", inputs: [{ name: "_validatorPool", type: "address", internalType: "address" }], outputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "validatorPool", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "event",
    name: "DenLiquidated",
    inputs: [
      { name: "_denManager", type: "address", indexed: true, internalType: "contract IDenManager" },
      { name: "_borrower", type: "address", indexed: true, internalType: "address" },
      { name: "_debt", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_coll", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_operation", type: "uint8", indexed: false, internalType: "enum LiquidationManager.DenManagerOperation" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "DenUpdated",
    inputs: [
      { name: "_denManager", type: "address", indexed: true, internalType: "contract IDenManager" },
      { name: "_borrower", type: "address", indexed: true, internalType: "address" },
      { name: "_debt", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_coll", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_stake", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_operation", type: "uint8", indexed: false, internalType: "enum LiquidationManager.DenManagerOperation" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FeesChanged",
    inputs: [
      { name: "_liquidatorFee", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_sNectGaugeFee", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_poolFee", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "Liquidation",
    inputs: [
      { name: "_denManager", type: "address", indexed: true, internalType: "contract IDenManager" },
      { name: "_liquidatedDebt", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_liquidatedColl", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_collGasCompensation", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "_debtGasCompensation", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  { type: "event", name: "SnectGaugeSet", inputs: [{ name: "_sNectGauge", type: "address", indexed: true, internalType: "address" }], anonymous: false },
  { type: "event", name: "ValidatorPoolSet", inputs: [{ name: "_validatorPool", type: "address", indexed: true, internalType: "address" }], anonymous: false },
] as const;
