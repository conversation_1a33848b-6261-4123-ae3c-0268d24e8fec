export default [
  { type: "function", name: "UPGRADE_INTERFACE_VERSION", inputs: [], outputs: [{ name: "", type: "string", internalType: "string" }], stateMutability: "view" },
  {
    type: "function",
    name: "_convertToValue",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "allowance",
    inputs: [
      { name: "owner", type: "address", internalType: "address" },
      { name: "spender", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "approve",
    inputs: [
      { name: "spender", type: "address", internalType: "address" },
      { name: "value", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "asset", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "assetDecimals", inputs: [], outputs: [{ name: "", type: "uint8", internalType: "uint8" }], stateMutability: "view" },
  {
    type: "function",
    name: "balanceOf",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "convertToAssets",
    inputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "convertToShares",
    inputs: [{ name: "assets", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "decimals", inputs: [], outputs: [{ name: "", type: "uint8", internalType: "uint8" }], stateMutability: "view" },
  {
    type: "function",
    name: "deposit",
    inputs: [
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "receiver", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "fetchPrice", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "getBalance",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getBalanceOfWithFutureEmissions",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getFullProfitUnlockTimestamp",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getLockedEmissions",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "getMetaBeraborrowCore", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IMetaBeraborrowCore" }], stateMutability: "view" },
  { type: "function", name: "getPerformanceFee", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  {
    type: "function",
    name: "getPrice",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "getPriceFeed", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IPriceFeed" }], stateMutability: "view" },
  { type: "function", name: "getWithdrawFee", inputs: [], outputs: [{ name: "", type: "uint16", internalType: "uint16" }], stateMutability: "view" },
  { type: "function", name: "iRedToken", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "ibgt", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "ibgtVault", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "infraredVault", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IInfraredVault" }], stateMutability: "view" },
  {
    type: "function",
    name: "initialize",
    inputs: [
      {
        name: "baseParams",
        type: "tuple",
        internalType: "struct IInfraredCollateralVault.InfraredInitParams",
        components: [
          {
            name: "_baseParams",
            type: "tuple",
            internalType: "struct IBaseCollateralVault.BaseInitParams",
            components: [
              { name: "_minWithdrawFee", type: "uint16", internalType: "uint16" },
              { name: "_maxWithdrawFee", type: "uint16", internalType: "uint16" },
              { name: "_withdrawFee", type: "uint16", internalType: "uint16" },
              { name: "_metaBeraborrowCore", type: "address", internalType: "contract IMetaBeraborrowCore" },
              { name: "_asset", type: "address", internalType: "contract IERC20" },
              { name: "_sharesName", type: "string", internalType: "string" },
              { name: "_sharesSymbol", type: "string", internalType: "string" },
            ],
          },
          { name: "_minPerformanceFee", type: "uint16", internalType: "uint16" },
          { name: "_maxPerformanceFee", type: "uint16", internalType: "uint16" },
          { name: "_performanceFee", type: "uint16", internalType: "uint16" },
          { name: "_iRedToken", type: "address", internalType: "address" },
          { name: "_infraredVault", type: "address", internalType: "contract IInfraredVault" },
          { name: "_ibgtVault", type: "address", internalType: "address" },
          { name: "_infraredWrapper", type: "address", internalType: "address" },
        ],
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "internalizeDonations",
    inputs: [
      { name: "tokens", type: "address[]", internalType: "address[]" },
      { name: "amounts", type: "uint128[]", internalType: "uint128[]" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "maxDeposit",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "maxMint",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "maxRedeem",
    inputs: [{ name: "owner", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "maxWithdraw",
    inputs: [{ name: "_owner", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "mint",
    inputs: [
      { name: "shares", type: "uint256", internalType: "uint256" },
      { name: "receiver", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "assets", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "name", inputs: [], outputs: [{ name: "", type: "string", internalType: "string" }], stateMutability: "view" },
  {
    type: "function",
    name: "previewDeposit",
    inputs: [{ name: "assets", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "previewMint",
    inputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "previewRedeem",
    inputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "previewWithdraw",
    inputs: [{ name: "assets", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  { type: "function", name: "proxiableUUID", inputs: [], outputs: [{ name: "", type: "bytes32", internalType: "bytes32" }], stateMutability: "view" },
  {
    type: "function",
    name: "rebalance",
    inputs: [
      {
        name: "p",
        type: "tuple",
        internalType: "struct IInfraredCollateralVault.RebalanceParams",
        components: [
          { name: "sentCurrency", type: "address", internalType: "address" },
          { name: "sentAmount", type: "uint256", internalType: "uint256" },
          { name: "swapper", type: "address", internalType: "address" },
          { name: "payload", type: "bytes", internalType: "bytes" },
        ],
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "receiveDonations",
    inputs: [
      { name: "tokens", type: "address[]", internalType: "address[]" },
      { name: "amounts", type: "uint256[]", internalType: "uint256[]" },
      { name: "receiver", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "redeem",
    inputs: [
      { name: "shares", type: "uint256", internalType: "uint256" },
      { name: "receiver", type: "address", internalType: "address" },
      { name: "_owner", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "assets", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "rewardedTokens", inputs: [], outputs: [{ name: "", type: "address[]", internalType: "address[]" }], stateMutability: "view" },
  { type: "function", name: "setIRED", inputs: [{ name: "_iRedToken", type: "address", internalType: "address" }], outputs: [], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "setPairThreshold",
    inputs: [
      { name: "tokenIn", type: "address", internalType: "address" },
      { name: "thresholdInBP", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "setPerformanceFee", inputs: [{ name: "_performanceFee", type: "uint16", internalType: "uint16" }], outputs: [], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "setUnlockRatePerSecond",
    inputs: [
      { name: "token", type: "address", internalType: "address" },
      { name: "_unlockRatePerSecond", type: "uint64", internalType: "uint64" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "setWithdrawFee", inputs: [{ name: "_withdrawFee", type: "uint16", internalType: "uint16" }], outputs: [], stateMutability: "nonpayable" },
  { type: "function", name: "symbol", inputs: [], outputs: [{ name: "", type: "string", internalType: "string" }], stateMutability: "view" },
  { type: "function", name: "totalAssets", inputs: [], outputs: [{ name: "amountInAsset", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "totalSupply", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "transfer",
    inputs: [
      { name: "to", type: "address", internalType: "address" },
      { name: "value", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "transferFrom",
    inputs: [
      { name: "from", type: "address", internalType: "address" },
      { name: "to", type: "address", internalType: "address" },
      { name: "value", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "unlockRatePerSecond",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "upgradeToAndCall",
    inputs: [
      { name: "newImplementation", type: "address", internalType: "address" },
      { name: "data", type: "bytes", internalType: "bytes" },
    ],
    outputs: [],
    stateMutability: "payable",
  },
  {
    type: "function",
    name: "withdraw",
    inputs: [
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "receiver", type: "address", internalType: "address" },
      { name: "_owner", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "shares", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "event",
    name: "Approval",
    inputs: [
      { name: "owner", type: "address", indexed: true, internalType: "address" },
      { name: "spender", type: "address", indexed: true, internalType: "address" },
      { name: "value", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "Deposit",
    inputs: [
      { name: "sender", type: "address", indexed: true, internalType: "address" },
      { name: "owner", type: "address", indexed: true, internalType: "address" },
      { name: "assets", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "shares", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "EmissionsAdded",
    inputs: [
      { name: "token", type: "address", indexed: true, internalType: "address" },
      { name: "amount", type: "uint128", indexed: false, internalType: "uint128" },
    ],
    anonymous: false,
  },
  { type: "event", name: "Initialized", inputs: [{ name: "version", type: "uint64", indexed: false, internalType: "uint64" }], anonymous: false },
  {
    type: "event",
    name: "NewUnlockRatePerSecond",
    inputs: [
      { name: "token", type: "address", indexed: true, internalType: "address" },
      { name: "unlockRatePerSecond", type: "uint64", indexed: false, internalType: "uint64" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "PerformanceFee",
    inputs: [
      { name: "token", type: "address", indexed: true, internalType: "address" },
      { name: "amount", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "Rebalance",
    inputs: [
      { name: "sentCurrency", type: "address", indexed: true, internalType: "address" },
      { name: "sent", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "received", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "sentValue", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "receivedValue", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "Transfer",
    inputs: [
      { name: "from", type: "address", indexed: true, internalType: "address" },
      { name: "to", type: "address", indexed: true, internalType: "address" },
      { name: "value", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  { type: "event", name: "Upgraded", inputs: [{ name: "implementation", type: "address", indexed: true, internalType: "address" }], anonymous: false },
  {
    type: "event",
    name: "Withdraw",
    inputs: [
      { name: "sender", type: "address", indexed: true, internalType: "address" },
      { name: "receiver", type: "address", indexed: true, internalType: "address" },
      { name: "owner", type: "address", indexed: true, internalType: "address" },
      { name: "assets", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "shares", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  { type: "error", name: "AmountCannotBeZero", inputs: [] },
  { type: "error", name: "ERC1967InvalidImplementation", inputs: [{ name: "implementation", type: "address", internalType: "address" }] },
  { type: "error", name: "ERC1967NonPayable", inputs: [] },
  {
    type: "error",
    name: "ERC20InsufficientAllowance",
    inputs: [
      { name: "spender", type: "address", internalType: "address" },
      { name: "allowance", type: "uint256", internalType: "uint256" },
      { name: "needed", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "ERC20InsufficientBalance",
    inputs: [
      { name: "sender", type: "address", internalType: "address" },
      { name: "balance", type: "uint256", internalType: "uint256" },
      { name: "needed", type: "uint256", internalType: "uint256" },
    ],
  },
  { type: "error", name: "ERC20InvalidApprover", inputs: [{ name: "approver", type: "address", internalType: "address" }] },
  { type: "error", name: "ERC20InvalidReceiver", inputs: [{ name: "receiver", type: "address", internalType: "address" }] },
  { type: "error", name: "ERC20InvalidSender", inputs: [{ name: "sender", type: "address", internalType: "address" }] },
  { type: "error", name: "ERC20InvalidSpender", inputs: [{ name: "spender", type: "address", internalType: "address" }] },
  {
    type: "error",
    name: "ERC4626ExceededMaxDeposit",
    inputs: [
      { name: "receiver", type: "address", internalType: "address" },
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "max", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "ERC4626ExceededMaxMint",
    inputs: [
      { name: "receiver", type: "address", internalType: "address" },
      { name: "shares", type: "uint256", internalType: "uint256" },
      { name: "max", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "ERC4626ExceededMaxRedeem",
    inputs: [
      { name: "owner", type: "address", internalType: "address" },
      { name: "shares", type: "uint256", internalType: "uint256" },
      { name: "max", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "ERC4626ExceededMaxWithdraw",
    inputs: [
      { name: "owner", type: "address", internalType: "address" },
      { name: "assets", type: "uint256", internalType: "uint256" },
      { name: "max", type: "uint256", internalType: "uint256" },
    ],
  },
  { type: "error", name: "EmissionRateExceedsMax", inputs: [] },
  { type: "error", name: "InvalidInitialization", inputs: [] },
  { type: "error", name: "NotInitializing", inputs: [] },
  { type: "error", name: "UUPSUnauthorizedCallContext", inputs: [] },
  { type: "error", name: "UUPSUnsupportedProxiableUUID", inputs: [{ name: "slot", type: "bytes32", internalType: "bytes32" }] },
  { type: "error", name: "WithdrawingLockedEmissions", inputs: [] },
] as const;
