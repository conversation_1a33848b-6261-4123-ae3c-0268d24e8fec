export default [
  { type: "constructor", inputs: [{ name: "_metaBeraborrowCore", type: "address", internalType: "address" }], stateMutability: "nonpayable" },
  { type: "function", name: "BERABORROW_CORE", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBeraborrowCore" }], stateMutability: "view" },
  { type: "function", name: "MAX_ORACLE_HEARTBEAT", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "MAX_PRICE_DEVIATION_FROM_PREVIOUS_ROUND", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "TARGET_DIGITS", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  {
    type: "function",
    name: "feedType",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [
      { name: "spotOracle", type: "address", internalType: "contract ISpotOracle" },
      { name: "isCollVault", type: "bool", internalType: "bool" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "fetchPrice",
    inputs: [{ name: "_token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getMultiplePrices",
    inputs: [{ name: "_tokens", type: "address[]", internalType: "address[]" }],
    outputs: [{ name: "prices", type: "uint256[]", internalType: "uint256[]" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getSpotOracle",
    inputs: [{ name: "_token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  { type: "function", name: "guardian", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "isCollVault",
    inputs: [{ name: "_token", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "oracleRecords",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [
      { name: "chainLinkOracle", type: "address", internalType: "contract IAggregatorV3Interface" },
      { name: "decimals", type: "uint8", internalType: "uint8" },
      { name: "heartbeat", type: "uint32", internalType: "uint32" },
      { name: "staleThreshold", type: "uint16", internalType: "uint16" },
      { name: "underlyingDerivative", type: "address", internalType: "address" },
    ],
    stateMutability: "view",
  },
  { type: "function", name: "owner", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "setOracle",
    inputs: [
      { name: "_token", type: "address", internalType: "address" },
      { name: "_chainlinkOracle", type: "address", internalType: "address" },
      { name: "_heartbeat", type: "uint32", internalType: "uint32" },
      { name: "_staleThreshold", type: "uint16", internalType: "uint16" },
      { name: "underlyingDerivative", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setSpotOracle",
    inputs: [
      { name: "_token", type: "address", internalType: "address" },
      { name: "_spotOracle", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "whitelistCollateralVault",
    inputs: [
      { name: "_token", type: "address", internalType: "address" },
      { name: "_enable", type: "bool", internalType: "bool" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "event",
    name: "NewCollVaultRegistered",
    inputs: [
      { name: "collVault", type: "address", indexed: false, internalType: "address" },
      { name: "enable", type: "bool", indexed: false, internalType: "bool" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "NewOracleRegistered",
    inputs: [
      { name: "token", type: "address", indexed: false, internalType: "address" },
      { name: "chainlinkAggregator", type: "address", indexed: false, internalType: "address" },
      { name: "underlyingDerivative", type: "address", indexed: false, internalType: "address" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "NewSpotOracleRegistered",
    inputs: [
      { name: "token", type: "address", indexed: false, internalType: "address" },
      { name: "spotOracle", type: "address", indexed: false, internalType: "address" },
    ],
    anonymous: false,
  },
  { type: "error", name: "PriceFeed__FeedFrozenError", inputs: [{ name: "token", type: "address", internalType: "address" }] },
  { type: "error", name: "PriceFeed__HeartbeatOutOfBoundsError", inputs: [] },
  { type: "error", name: "PriceFeed__InvalidFeedResponseError", inputs: [{ name: "token", type: "address", internalType: "address" }] },
  { type: "error", name: "PriceFeed__InvalidResponse", inputs: [{ name: "_token", type: "address", internalType: "address" }] },
  { type: "error", name: "PriceFeed__PotentialDos", inputs: [] },
] as const;
