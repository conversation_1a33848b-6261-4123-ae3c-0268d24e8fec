export default [
  { type: "constructor", inputs: [], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "getMultipleSortedDens",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "_startIdx", type: "int256", internalType: "int256" },
      { name: "_count", type: "uint256", internalType: "uint256" },
    ],
    outputs: [
      {
        name: "_dens",
        type: "tuple[]",
        internalType: "struct MultiDenGetter.CombinedDenData[]",
        components: [
          { name: "owner", type: "address", internalType: "address" },
          { name: "debt", type: "uint256", internalType: "uint256" },
          { name: "coll", type: "uint256", internalType: "uint256" },
          { name: "stake", type: "uint256", internalType: "uint256" },
          { name: "snapshotCollateral", type: "uint256", internalType: "uint256" },
          { name: "snapshotDebt", type: "uint256", internalType: "uint256" },
        ],
      },
    ],
    stateMutability: "view",
  },
] as const;
