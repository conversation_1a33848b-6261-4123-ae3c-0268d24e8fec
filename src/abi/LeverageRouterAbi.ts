export default [
  {
    type: "constructor",
    inputs: [
      { name: "_borrowerOperations", type: "address", internalType: "address" },
      { name: "_router", type: "address", internalType: "address" },
      { name: "_nect", type: "address", internalType: "address" },
      { name: "_priceFeed", type: "address", internalType: "address" },
      { name: "_factory", type: "address", internalType: "address" },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "automaticLoopingAddCollateral",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "collVault", type: "address", internalType: "address" },
      { name: "collAssetsToDeposit", type: "uint256", internalType: "uint256" },
      {
        name: "partialData",
        type: "tuple",
        internalType: "struct ILeverageRouter.DenLoopingParams",
        components: [
          {
            name: "denParams",
            type: "tuple",
            internalType: "struct ILeverageRouter.DenParams",
            components: [
              { name: "maxFeePercentage", type: "uint256", internalType: "uint256" },
              { name: "debtAmount", type: "uint256", internalType: "uint256" },
              { name: "upperHint", type: "address", internalType: "address" },
              { name: "lowerHint", type: "address", internalType: "address" },
            ],
          },
          {
            name: "dexAggregator",
            type: "tuple",
            internalType: "struct ILeverageRouter.DexAggregatorParams",
            components: [
              { name: "dexCalldata", type: "bytes", internalType: "bytes" },
              { name: "collOutputMin", type: "uint256", internalType: "uint256" },
            ],
          },
        ],
      },
      { name: "denManagerIdx", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "automaticLoopingOpenDen",
    inputs: [
      { name: "denManager", type: "address", internalType: "contract IDenManager" },
      { name: "collVault", type: "address", internalType: "address" },
      { name: "collAssetsToDeposit", type: "uint256", internalType: "uint256" },
      {
        name: "partialData",
        type: "tuple",
        internalType: "struct ILeverageRouter.DenLoopingParams",
        components: [
          {
            name: "denParams",
            type: "tuple",
            internalType: "struct ILeverageRouter.DenParams",
            components: [
              { name: "maxFeePercentage", type: "uint256", internalType: "uint256" },
              { name: "debtAmount", type: "uint256", internalType: "uint256" },
              { name: "upperHint", type: "address", internalType: "address" },
              { name: "lowerHint", type: "address", internalType: "address" },
            ],
          },
          {
            name: "dexAggregator",
            type: "tuple",
            internalType: "struct ILeverageRouter.DexAggregatorParams",
            components: [
              { name: "dexCalldata", type: "bytes", internalType: "bytes" },
              { name: "collOutputMin", type: "uint256", internalType: "uint256" },
            ],
          },
        ],
      },
      { name: "denManagerIdx", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "beraborrowCore", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBeraborrowCore" }], stateMutability: "view" },
  { type: "function", name: "borrowerOperations", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IBorrowerOperations" }], stateMutability: "view" },
  {
    type: "function",
    name: "calculateDebtAmount",
    inputs: [
      { name: "position", type: "address", internalType: "address" },
      { name: "margin", type: "uint256", internalType: "uint256" },
      { name: "leverage", type: "uint256", internalType: "uint256" },
      { name: "desiredCR", type: "uint256", internalType: "uint256" },
      { name: "denManager", type: "address", internalType: "address" },
      { name: "collVault", type: "address", internalType: "address" },
      { name: "isRecoveryMode", type: "bool", internalType: "bool" },
    ],
    outputs: [{ name: "debtAmount", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "calculateMaxLeverage",
    inputs: [
      { name: "currentColl", type: "uint256", internalType: "uint256" },
      { name: "currentDebt", type: "uint256", internalType: "uint256" },
      { name: "newMargin", type: "uint256", internalType: "uint256" },
      { name: "desiredCR", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "maxLeverage", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "claimLockedTokens",
    inputs: [
      { name: "tokens", type: "address[]", internalType: "contract IERC20[]" },
      { name: "amounts", type: "uint256[]", internalType: "uint256[]" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "factory", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IFactory" }], stateMutability: "view" },
  { type: "function", name: "nect", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IDebtToken" }], stateMutability: "view" },
  { type: "function", name: "obRouter", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "onFlashLoan",
    inputs: [
      { name: "", type: "address", internalType: "address" },
      { name: "collVault", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
      { name: "fee", type: "uint256", internalType: "uint256" },
      { name: "data", type: "bytes", internalType: "bytes" },
    ],
    outputs: [{ name: "", type: "bytes32", internalType: "bytes32" }],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "priceFeed", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IPriceFeed" }], stateMutability: "view" },
] as const;
