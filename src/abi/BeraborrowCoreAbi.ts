export default [
  {
    type: "constructor",
    inputs: [
      { name: "_metaBeraborrowCore", type: "address", internalType: "address" },
      { name: "_initialCCR", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "CCR", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "function", name: "dmBootstrapPeriod", inputs: [], outputs: [{ name: "", type: "uint64", internalType: "uint64" }], stateMutability: "view" },
  { type: "function", name: "feeReceiver", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "getLspEntryFee",
    inputs: [{ name: "rebalancer", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint16", internalType: "uint16" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getLspExitFee",
    inputs: [{ name: "rebalancer", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint16", internalType: "uint16" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPeripheryFlashLoanFee",
    inputs: [{ name: "peripheryContract", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint16", internalType: "uint16" }],
    stateMutability: "view",
  },
  { type: "function", name: "guardian", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "isPeriphery",
    inputs: [{ name: "peripheryContract", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  { type: "function", name: "lspBootstrapPeriod", inputs: [], outputs: [{ name: "", type: "uint64", internalType: "uint64" }], stateMutability: "view" },
  { type: "function", name: "manager", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "metaBeraborrowCore", inputs: [], outputs: [{ name: "", type: "address", internalType: "contract IMetaBeraborrowCore" }], stateMutability: "view" },
  { type: "function", name: "owner", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "paused", inputs: [], outputs: [{ name: "", type: "bool", internalType: "bool" }], stateMutability: "view" },
  { type: "function", name: "pendingOwner", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  { type: "function", name: "priceFeed", inputs: [], outputs: [{ name: "", type: "address", internalType: "address" }], stateMutability: "view" },
  {
    type: "function",
    name: "setDMBootstrapPeriod",
    inputs: [
      { name: "denManager", type: "address", internalType: "address" },
      { name: "_bootstrapPeriod", type: "uint64", internalType: "uint64" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "setNewCCR", inputs: [{ name: "newCCR", type: "uint256", internalType: "uint256" }], outputs: [], stateMutability: "nonpayable" },
  {
    type: "function",
    name: "setPeripheryEnabled",
    inputs: [
      { name: "_periphery", type: "address", internalType: "address" },
      { name: "_enabled", type: "bool", internalType: "bool" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  { type: "function", name: "startTime", inputs: [], outputs: [{ name: "", type: "uint256", internalType: "uint256" }], stateMutability: "view" },
  { type: "event", name: "CCRSet", inputs: [{ name: "initialCCR", type: "uint256", indexed: false, internalType: "uint256" }], anonymous: false },
  {
    type: "event",
    name: "DMBootstrapPeriodSet",
    inputs: [
      { name: "dm", type: "address", indexed: false, internalType: "address" },
      { name: "bootstrapPeriod", type: "uint64", indexed: false, internalType: "uint64" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "PeripheryEnabled",
    inputs: [
      { name: "periphery", type: "address", indexed: true, internalType: "address" },
      { name: "enabled", type: "bool", indexed: false, internalType: "bool" },
    ],
    anonymous: false,
  },
] as const;
