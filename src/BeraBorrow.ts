import { Hex, PublicClient } from "viem";
import { Collateral } from "./Contracts/Collateral/Contract";
import { DenManager } from "./Contracts/DenManager/Contract";
import { DenManagerGetters } from "./Contracts/DenManagerGetters/Contract";
import { MultiCollateralHintHelpers } from "./Contracts/MultiCollateralHintHelpers/Contract";
import { Debt } from "./Contracts/Debt/Contract";
import { BeraBorrowConnection, BeraBorrowContractAddresses, BeraBorrowDeploymentJSON, DenManagerObj, ManagedVaultObj, Protocol } from "./types";
import { connectionFrom } from "./BeraBorrowConnection";
import { Pollen } from "./Contracts/Pollen/Contract";
import { PollenLpToken } from "./Contracts/PollenLpToken/Contract";
import { LpPollenStaking } from "./Contracts/PollenStaking/LpPollenStaking";
import { SPollenStaking } from "./Contracts/PollenStaking/SPollenStaking";
import { SPollen } from "./Contracts/SPollen/Contract";
import { LpVotingEscrow } from "./Contracts/VotingEscrow/LpVotingEscrow";
import { SPollenVotingEscrow } from "./Contracts/VotingEscrow/SPollenVotingEscrow";
import { LpVeFeeDistributor } from "./Contracts/VeFeeDistributor/LpVeFeeDistributor";
import { SPollenVeFeeDistributor } from "./Contracts/VeFeeDistributor/SPollenVeFeeDistributor";
import { StabilityPool } from "./Contracts/StabilityPool/Contract";
import { SubgraphClient } from "./subgraph";
import { SubgraphPollenClient } from "./subgraph-pollen";
import { CollateralVault } from "./Contracts/CollateralVaults/Contract";
import { BorrowerOperationsHandler } from "./BorrowerOperations/BorrowerOperationsHandler";
import { VaultedDenManager } from "./Contracts/DenManager/VaultedDenManager";
import { LSPRouter } from "./Contracts/LSPRouter/Contract";
// import { LeverageRouter } from "./Contracts/LeverageRouter/Contract";
import { PriceFeed } from "./Contracts/PriceFeed/PriceFeed";
import { PsmBond } from "./Contracts/PsmBond/Contract";
import { ApiServices } from "./Services/API/services";
import { ManagedVault } from "./Contracts/ManagedVaults/Contract";

export class BeraBorrow {
  private readonly connection: BeraBorrowConnection;
  readonly constants: {
    deploymentTimestamp: number;
    liquidationReserve: bigint;
    minimumDebt: bigint;
    addresses: BeraBorrowContractAddresses;
  };
  readonly collateralTokens: { [key: Hex]: Collateral };
  readonly vaults: { [key: Hex]: CollateralVault };
  readonly denManagers: { [key: Hex]: DenManagerObj };
  readonly managedVaults: { [key: Hex]: ManagedVaultObj };

  readonly protocols: {
    [key: Hex]: Protocol;
  };

  readonly debtToken: Debt;
  readonly pollenToken: Pollen;
  readonly pollenLpToken: PollenLpToken;
  readonly sPollenToken: SPollen;
  readonly lpPollenStaking: LpPollenStaking;
  readonly sPollenStaking: SPollenStaking;
  readonly lpVotingEscrow: LpVotingEscrow;
  readonly lpVeFeeDistributor: LpVeFeeDistributor;
  readonly sPollenVotingEscrow: SPollenVotingEscrow;
  readonly sPollenVeFeeDistributor: SPollenVeFeeDistributor;
  readonly priceFeed: PriceFeed;
  readonly stagingPriceFeed: PriceFeed;
  readonly psmBond: PsmBond;
  readonly stabilityPool: StabilityPool;
  readonly lspRouter: LSPRouter;
  //   readonly leverageRouter: LeverageRouter;
  readonly subgraphClient: SubgraphClient;
  readonly subgraphPollenClient: SubgraphPollenClient;
  readonly api: ApiServices;
  readonly createToken: (contractAddress: `0x${string}`, ticker: string, decimals: number) => Collateral;
  /**
   *
   * @param chainId the chain ID to use
   * @param publicClient the users publicClient to simulate
   * @param userAddress the users address
   * @param config override chain configs, doing so disables simulations
   */
  constructor(
    publicClient: PublicClient,
    userAddress: Hex,
    readonly config: BeraBorrowDeploymentJSON
  ) {
    this.connection = connectionFrom(publicClient, userAddress, config);
    this.constants = {
      deploymentTimestamp: this.connection.deploymentTimestamp,
      liquidationReserve: this.connection.liquidationReserve,
      minimumDebt: this.connection.minimumDebt,
      addresses: this.connection.addresses,
    };
    this.createToken = (contractAddress: `0x${string}`, ticker: string, decimals: number) => new Collateral(this.connection, contractAddress, ticker, decimals);

    this.protocols = Object.keys(this.connection.addresses.protocols).reduce(
      (protocol, key) => {
        const typedKey = key as Hex;
        protocol[typedKey] = {
          denManagerGetters: new DenManagerGetters(this.connection, typedKey),
          multiCollateralHintHelpers: new MultiCollateralHintHelpers(this.connection, typedKey),
          borrowerOperationsHandler: new BorrowerOperationsHandler(this.connection, typedKey),
        };
        return protocol;
      },
      {} as {
        [key: Hex]: Protocol;
      }
    );

    this.vaults = Object.keys(this.connection.addresses.vaults).reduce(
      (vaults, key) => {
        const typedKey = key as Hex;
        vaults[typedKey] = new CollateralVault(this.connection, typedKey);
        return vaults;
      },
      {} as {
        [key: Hex]: CollateralVault;
      }
    );
    this.collateralTokens = Object.keys(this.connection.addresses.collateralTokens).reduce(
      (tokens, key) => {
        const typedKey = key as Hex;
        tokens[typedKey] = new Collateral(
          this.connection,
          typedKey,
          this.connection.addresses.collateralTokens[typedKey].ticker,
          this.connection.addresses.collateralTokens[typedKey].decimals
        );
        return tokens;
      },
      {} as {
        [key: Hex]: Collateral;
      }
    );

    this.denManagers = Object.keys(this.connection.addresses.denManagers).reduce(
      (denManager, key) => {
        const typedKey = key as Hex;
        const vaultAddress: Hex | undefined = this.connection.addresses.denManagers[typedKey].vault;
        const protocolAddress: Hex | undefined = this.connection.addresses.denManagers[typedKey].protocol;
        const wrappedCollateralAddr = this.connection.addresses.denManagers[typedKey].wrappedCollateral;

        const underlyingDenManagerAddr = this.connection.addresses.wrappedDenManagers[typedKey] ?? typedKey;
        denManager[typedKey] = {
          denManager:
            denManager[underlyingDenManagerAddr]?.denManager ??
            (vaultAddress
              ? new VaultedDenManager(this.connection, underlyingDenManagerAddr, vaultAddress, protocolAddress)
              : new DenManager(this.connection, underlyingDenManagerAddr, protocolAddress)),
          collateral: this.collateralTokens[this.connection.addresses.denManagers[typedKey].collateral],
          vault: vaultAddress ? this.vaults[vaultAddress] : undefined,
          wrappedCollateral: wrappedCollateralAddr ? this.collateralTokens[wrappedCollateralAddr] : undefined,
          permissioned: denManager[typedKey]?.permissioned,
        };

        return denManager;
      },
      {} as {
        [key: Hex]: DenManagerObj;
      }
    );
    this.managedVaults = Object.keys(this.connection.addresses.managedVaults).reduce(
      (managedVault, key) => {
        const typedKey = key as Hex;
        const denManagerAddress: Hex = this.connection.addresses.managedVaults[typedKey].denManager;
        const getterAddress: Hex = this.connection.addresses.managedVaults[typedKey].getterAddress;

        managedVault[typedKey] = {
          managedVault: new ManagedVault(this.connection, typedKey),
          denManager: this.denManagers[denManagerAddress].denManager,
          collateral: this.collateralTokens[this.connection.addresses.managedVaults[typedKey].collateral],
          vault: this.vaults[this.connection.addresses.denManagers[denManagerAddress].vault],
          getterAddress,
        };

        return managedVault;
      },
      {} as {
        [key: Hex]: ManagedVaultObj;
      }
    );

    this.debtToken = new Debt(this.connection);
    this.pollenToken = new Pollen(this.connection);
    this.pollenLpToken = new PollenLpToken(this.connection);
    this.sPollenToken = new SPollen(this.connection);
    this.lpPollenStaking = new LpPollenStaking(this.connection);
    this.sPollenStaking = new SPollenStaking(this.connection);
    this.lpVotingEscrow = new LpVotingEscrow(this.connection);
    this.lpVeFeeDistributor = new LpVeFeeDistributor(this.connection);
    this.sPollenVotingEscrow = new SPollenVotingEscrow(this.connection);
    this.sPollenVeFeeDistributor = new SPollenVeFeeDistributor(this.connection);
    this.priceFeed = new PriceFeed(this.connection);
    this.stagingPriceFeed = new PriceFeed(this.connection);
    this.psmBond = new PsmBond(this.connection);
    this.stabilityPool = new StabilityPool(this.connection);
    this.lspRouter = new LSPRouter(this.connection);
    // this.leverageRouter = new LeverageRouter(this.connection);
    this.subgraphClient = new SubgraphClient(this.connection);
    this.subgraphPollenClient = new SubgraphPollenClient(this.connection);
    this.api = new ApiServices(this.connection);
  }
}
