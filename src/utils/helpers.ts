import { formatUnits, Hex, maxUint256, parseUnits } from "viem";
import { SCALING_FACTOR_DECIMALS, Token } from "@Beraborrowofficial/sdk";
import { BERABORROW_ADDRESSES } from "./constants";
export const shortenAddress = (address: string) => address.substring(0, 6) + "..." + address.slice(-4);
export const setPrecision = (value: string, precision?: number, concat = false, maxPrecision = 3): string => {
  if (precision === undefined) return value.replace(/(-?\.\d*?[1-9])0+$|(-?\.0+)$/, "$1").replace(/(-?\.\d*?)0+$/, "$1");

  let [integerPart, fractionalPart] = value.split(".");

  if (fractionalPart) {
    if (integerPart === "0") {
      // Handle numbers smaller than 1
      const leadingZeros = fractionalPart.match(/^0+/);
      const leadingZeroCount = leadingZeros ? leadingZeros[0].length : 0;
      // Adjust precision based on leading zeros, respecting maxPrecision
      precision = Math.min(precision + leadingZeroCount, maxPrecision);
      // Slice to include the rounding digit
      fractionalPart = fractionalPart.slice(0, precision + 1);
    } else {
      // For numbers >= 1, slice fractional part normally
      fractionalPart = fractionalPart.slice(0, precision + 1);
    }
    const roundingDigit = parseInt(fractionalPart[precision] || "0", 10);
    fractionalPart = fractionalPart.slice(0, precision);

    if (!concat && roundingDigit >= 5) {
      const fractionalBigInt = BigInt(fractionalPart || "0") + BigInt(1);
      fractionalPart = fractionalBigInt.toString().padStart(precision, "0");
      if (fractionalPart.length > precision) {
        // Handle carry-over to the integer part
        fractionalPart = fractionalPart.slice(1);
        integerPart = (BigInt(integerPart.replace(/,/g, "")) + BigInt(1)).toString();
      }
    }
    value = fractionalPart ? `${integerPart}.${fractionalPart}` : integerPart;
  }

  return value.replace(/(\.\d*?[1-9])0+$|(\.0+)$/, "$1").replace(/\.0+$/, "");
};
function formatWithCommas(value: string, decimals: number): string {
  // return value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  return new Intl.NumberFormat(undefined, {
    minimumFractionDigits: value.split(".")?.[1]?.length ?? 0,
    maximumFractionDigits: decimals,
  }).format(Number(value));
}
export const formatTokenWithMillionsOrBillions = (value: bigint, decimals: number, precision?: number, ticker?: string, concat = false) => {
  if (value / 10n ** BigInt(decimals + 9)) {
    if (!precision) {
      precision = 2;
    }
    return formatWithCommas(formatDecimal(value / 10n ** 9n, decimals, precision, concat), decimals) + "b" + (ticker ? ` ${ticker}` : "");
  } else if (value / 10n ** BigInt(decimals + 6)) {
    if (!precision) {
      precision = 2;
    }
    return formatWithCommas(formatDecimal(value / 10n ** 6n, decimals, precision, concat), decimals) + "m" + (ticker ? ` ${ticker}` : "");
  } else {
    return formatWithCommas(formatDecimal(value, decimals, 0, concat), decimals) + (ticker ? ` ${ticker}` : "");
  }
};

export const formatToken = (value: bigint, decimals: number, precision?: number, ticker?: string, concat = false, maxPrecision = 8) =>
  formatWithCommas(formatDecimal(value, decimals, precision, concat, maxPrecision), decimals) + (ticker ? ` ${ticker}` : "");

export const formatBigIntPercentage = (value: bigint, precision = 2, noCommas = false, concat = false, decimals = SCALING_FACTOR_DECIMALS, maxPrecision = 8) => {
  const percentageDecimals = decimals - 2;
  return noCommas
    ? formatDecimal(value, percentageDecimals, precision, concat, maxPrecision)
    : formatWithCommas(formatDecimal(value, percentageDecimals, precision, concat, maxPrecision), percentageDecimals);
};
export const formatDecimal = (value: bigint, decimals: number, precision?: number, concat = true, maxPrecision = 8) =>
  setPrecision(formatUnits(value, decimals), precision, concat, maxPrecision);

export const formatNoDecimal = (value: string, decimals: number) => {
  return parseUnits(value, decimals);
};

export const regexDecimalToken = (value: string, decimals: number) => {
  value = value.replace(new RegExp(`^0(?!\\.|$)|[^0-9.]|(?<=\\.\\d{${decimals}})\\d*|(?<=\\..*)\\.`, "g"), "");
  if (value.length > 75) {
    const bigintValue = parseUnits(value, decimals);
    if (bigintValue > maxUint256) {
      return formatUnits(maxUint256, decimals);
    }
  }
  return value;
};

// Custom replacer to handle bigint values during JSON stringification
export const bigintJSONReplacer = (value: unknown) => {
  return typeof value === "bigint" ? "bigint:" + value.toString() : value;
};

// Custom reviver to handle bigint values during JSON parsing
export const bigintJSONReviver = (value: unknown) => {
  if (typeof value === "string" && value.startsWith("bigint:")) {
    return BigInt(value.slice(6));
  }
  return value;
};

export const formatCurrency = (value: number | bigint | string): string => {
  return new Intl.NumberFormat("en-US", { currency: "USD", style: "currency" }).format(Number(value));
};

export function truncateAddress(address: string | undefined, startLength: number = 6, endLength: number = 4) {
  // Check if the address is valid and of the expected length (42 characters)
  if (typeof address !== "string" || (address.length !== 42 && address.length !== 66)) {
    return address;
  }

  // Extract the starting and ending parts of the address
  const start = address.substring(0, startLength + 2); // +2 for the '0x' prefix
  const end = address.substring(address.length - endLength);

  // Return the truncated address
  return `${start}•••${end}`;
}

export const findToken = (
  contractAddress: Hex
):
  | {
      startBlock: number;
      collateral: Hex;
      ticker: string;
      decimals: number;
    }
  | Token
  | undefined => {
  return (
    BERABORROW_ADDRESSES.vaults[contractAddress] ??
    BERABORROW_ADDRESSES.collateralTokens[contractAddress] ??
    (contractAddress === BERABORROW_ADDRESSES.debtToken.contractAddress
      ? BERABORROW_ADDRESSES.debtToken
      : contractAddress === BERABORROW_ADDRESSES.pollenToken.contractAddress
        ? BERABORROW_ADDRESSES.pollenToken
        : undefined)
  );
};

export function preloadImage(src: string) {
  return new Promise<string>((resolve, reject) => {
    const img = new Image();
    img.onload = function () {
      resolve(src);
    };
    img.onerror = img.onabort = function () {
      reject(src);
    };
    img.src = src;
  });
}
export function extractTickerProvider(ticker: string | undefined): { provider?: string; ticker: string | undefined } {
  if (ticker) {
    const parts = ticker.split("-");

    if (parts.length >= 3) {
      const [provider, ...rest] = parts;
      return {
        provider,
        ticker: rest.join("-"),
      };
    }
  }
  return {
    ticker,
  };
}
