import { BIGINT_ZERO, BIGINT_SCALING_FACTOR } from "./bignumbers";
import { BigInt } from "@graphprotocol/graph-ts";

export function calculateCollateralRatio(collateral: BigInt, debt: BigInt, price: BigInt): BigInt {
  if (debt.equals(BIGINT_ZERO)) {
    return BIGINT_ZERO;
  }
  return collateral.times(price).div(debt);
}

export function calculateNominalCollateralRatio(collateral: BigInt, debt: BigInt): BigInt {
  if (debt.equals(BIGINT_ZERO)) {
    return BIGINT_ZERO;
  }
  return collateral.times(BIGINT_SCALING_FACTOR).div(debt);
}
