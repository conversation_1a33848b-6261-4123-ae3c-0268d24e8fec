import { Bytes, BigInt } from "@graphprotocol/graph-ts";

export let DECIMAL_PRECISION = 18;

// E.g. 1.5 is represented as 1.5 * 10^18, where 10^18 is called the scaling factor
export let BIGINT_SCALING_FACTOR = BigInt.fromI32(10).pow(18);
export let BIGINT_SCALING_FACTOR_BP = BigInt.fromI32(10).pow(8);

export let BIGINT_ZERO = BigInt.fromI32(0);
export let BIGINT_MAX_UINT256 = BigInt.fromUnsignedBytes(Bytes.fromHexString("0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF") as Bytes);
