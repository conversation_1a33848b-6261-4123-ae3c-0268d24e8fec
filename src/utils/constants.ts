import { BigInt } from "@graphprotocol/graph-ts";
export let ZERO_ADDRESS = "0x" + "0".repeat(40);
const mainnet = true;
export const POOL_ADDRESS = mainnet ? "0x597877Ccf65be938BD214C4c46907669e3E62128" : "0x3a7f6f2F27f7794a7820a32313F4a68e36580864";
export const LSP_GETTER_ADDRESS = mainnet ? "0xF8519658cfF16FA095A8bCEB3dCC576D94399e32" : "0x532F4a17E94D4e856A68F5FE33C348A262Fcd0cE";
export const LSP_GETTER_STARTBLOCK = mainnet ? BigInt.fromString("1134927") : BigInt.fromString("5874909");
export const DEBT_TOKEN_ADDRESS = mainnet ? "0x1cE0a25D13CE4d52071aE7e02Cf1F6606F4C79d3" : "0xf5AFCF50006944d17226978e594D4D25f4f92B40";
export const BORROWER_OPERATIONS_ADDRESS = mainnet ? "0xDB32cA8f3bB099A76D4Ec713a2c2AACB3d8e84B9" : "0x7d1f193d97d514a22325e1e57ae82a2715d5ed67";
export const COLL_VAULT_ROUTER_ADDRESS = mainnet ? "0x5f1619FfAEfdE17F7e54f850fe90AD5EE44dbf47" : "0xd257D6b56b2eE48A4B83e12F06b53195Dc4514D7";
export const IBGT_ADDRESS = mainnet ? "0x5f1619FfAEfdE17F7e54f850fe90AD5EE44dbf47" : "0xd257D6b56b2eE48A4B83e12F06b53195Dc4514D7";
