import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SCALING_FACTOR, BeraBorrowDeploymentJSON } from "@Beraborrowofficial/sdk";
import mainnetDeployment from "../utils/deployments/mainnet.json";
import { QueryClient } from "@tanstack/react-query";
import { createPublicClient, PublicClient, zeroAddress, http, fallback, Hex } from "viem";
import { Chain, anvil, berachain, berachainTestnetbArtio } from "viem/chains";
import posthog from "posthog-js";
import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import { createAppKit } from "@reown/appkit/react";
import { berachainTestnetbArtio as berachainTestnetbArtioNetwork, anvil as anvilNetwork, berachain as berachainNetwork, AppKitNetwork } from "@reown/appkit/networks";
import { safe } from "wagmi/connectors";
import axios from "axios";
export const BASE_URL = <string>import.meta.env.VITE_APP_URL;
export const PRODUCTION = <string>import.meta.env.VITE_PRODUCTION === "false" ? false : import.meta.env.PROD;
export const TESTNET = <string>import.meta.env.VITE_TESTNET === "true";
export const DEPLOYMENT_CHAIN_ID = parseInt(import.meta.env.VITE_CHAIN_ID);
export const WALLET_CONNECT_PROJECT_ID = <string>import.meta.env.VITE_WALLET_CONNECT_PROJECT_ID || "";
export const POSTHOG_API_KEY = <string>import.meta.env.VITE_POSTHOG_API_KEY || "";
export const ALCHEMY_URL = <string>import.meta.env.VITE_ALCHEMY_URL || "";
export const APP_VERSION = <string>import.meta.env.VITE_APP_VERSION;
export const GTM_ID = <string>import.meta.env.VITE_GTM_ID;
export const BOYCO = <string>import.meta.env.VITE_BOYCO === "true";
export const BERABORROW_API_URL = <string>import.meta.env.VITE_BERABORROW_API_URL;
console.info(APP_VERSION);
export const QUERY_CLIENT = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      refetchOnReconnect: true,
      refetchInterval: false,
      staleTime: 1000 * 30, // 30 seconds
      gcTime: 1000 * 60 * 30, // 30 minutes
    },
  },
});
const chains: { [key: number]: Chain } = {
  [anvil.id]: anvil,
  [berachainTestnetbArtio.id]: berachainTestnetbArtio,
  [berachain.id]: berachain,
};
const networks: { [key: number]: AppKitNetwork } = {
  [anvil.id]: anvilNetwork,
  [berachainTestnetbArtio.id]: berachainTestnetbArtioNetwork,
  [berachain.id]: berachainNetwork,
};

const chain: Chain = chains[DEPLOYMENT_CHAIN_ID];
const network = networks[DEPLOYMENT_CHAIN_ID];
const RPC = fallback([http(ALCHEMY_URL, { batch: true }), http(undefined, { batch: true })]);

export const WAGMI_ADAPTOR = new WagmiAdapter({
  connectors: [safe({ shimDisconnect: true })],
  transports: {
    [chain.id]: RPC,
  },
  chains: [chain],
  networks: [network],
  projectId: WALLET_CONNECT_PROJECT_ID,
});

export const SETUP_APPKIT = () => {
  createAppKit({
    adapters: [WAGMI_ADAPTOR],
    projectId: WALLET_CONNECT_PROJECT_ID,
    networks: [network],
    defaultNetwork: network,
    allowUnsupportedChain: false,
    debug: !PRODUCTION,
    features: {
      analytics: true,
      swaps: false,
    },
    metadata: {
      name: "BeraBorrow",
      url: BASE_URL,
      icons: [BASE_URL + "/beraborrow-logo.png"],
      description: "Beraborrow unlocks instant liquidity against Berachain native assets through our stablecoin called Nectar ($NECT).",
    },
    themeVariables: {
      "--w3m-font-family": "OpenRunde",
      "--w3m-accent": "var(--primary-main)",
      "--w3m-color-mix-strength": 40,
      "--w3m-color-mix": "var(--background-accent)",
      "--w3m-border-radius-master": "16px",
    },
    featuredWalletIds: [
      "c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96",
      "8a0ee50d1f22f6651afcae7eb4253e52a3310b90af5daef78a8c4929a9bb99d4",
      "5e4a8cc31d062b78a7ad9e017135574809b01c4dbbf30e4dbb467ddd43025618",
      "38f5d18bd8522c244bdd70cb4a68e0e718865155811c043f052fb9f1c51de662",
      "971e689d0a5be527bac79629b4ee9b925e82208e5168b733496a09c0faed0709",
      "225affb176778569276e484e1b92637ad061b01e13a048b35a9d280c3b58970f",
      "5864e2ced7c293ed18ac35e0db085c09ed567d67346ccb6f58a0327a75137489",
    ],
  });
};
export const ANON_CLIENT: PublicClient = createPublicClient({
  chain: chain,
  transport: RPC,
});

export const ANON_ADDRESS = PRODUCTION ? zeroAddress : ((localStorage.getItem("magic") ?? zeroAddress) as Hex);
//@ts-ignore
export const ANON_BERABORROW = new BeraBorrow(ANON_CLIENT, ANON_ADDRESS, mainnetDeployment as BeraBorrowDeploymentJSON);
export const BERABORROW_ADDRESSES = ANON_BERABORROW.constants.addresses;
// For nft check
// BFB NFT
export const BFB_NFT_ADDRESS = "0xAF2A7Da72eb8bFC1c6A0B6b0217037E1C7eA8548";
// Honey Comb NFT
export const HONEY_COMB_NFT_ADDRESS = "0xCB0477d1Af5b8b05795D89D59F4667b59eAE9244";

export const CLUB_LEVELS = {
  bronze: 1000n * SCALING_FACTOR,
  silver: 25000n * SCALING_FACTOR,
  gold: 100000n * SCALING_FACTOR,
};
if (!PRODUCTION) {
  document.title = import.meta.env.MODE;
}
posthog.init(POSTHOG_API_KEY, {
  api_host: "https://eu.i.posthog.com/",
  person_profiles: "identified_only",
  opt_out_capturing_by_default: localStorage.getItem("consentMode") === "true",
});
export const axiosAPI = axios.create({
  baseURL: `${BERABORROW_API_URL}/v1/`,
  headers: {
    accept: "application/json",
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
  },
});
