# sdk

SDK library for reading BeraBorrow protocol state and sending transactions.

## Anvil Test Usage

Simply start the anvil server with the following command:

```bash
$ anvil
```

In [blockend repo](https://github.com/Beraborrowofficial/blockend), populate the `.env` values shown in the `.env.example` file.
Important than the `OWNER` value is the public key of the private key you use at the deployment script.

Default values are setting `OWNER=0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266` and the following private key:

```bash
$ forge script script/mock/local/MockDeployment.s.sol:MockDeployment --broadcast --rpc-url http://127.0.0.1:8545 --private-key 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
```

Then run the following command on the root of this repo:

```bash
$ yarn test
```
