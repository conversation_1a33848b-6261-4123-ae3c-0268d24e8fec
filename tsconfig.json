{"compilerOptions": {"declaration": true, "declarationMap": true, "removeComments": true, "sourceMap": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2022", "lib": ["es2022", "DOM"], "module": "ESNext", "moduleResolution": "node", "strict": true, "strictNullChecks": true, "esModuleInterop": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "allowJs": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitAny": false, "outDir": "dist", "types": ["node"]}, "include": ["src"], "exclude": ["node_modules", "dist"]}