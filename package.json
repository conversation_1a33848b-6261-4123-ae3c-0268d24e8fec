{"name": "@beraborrowofficial/sdk", "version": "2.3.5", "description": "BeraBorrow SDK Viem-based implementation", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "protocol", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "homepage": "https://github.com/Beraborrowofficial/sdk#readme", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/Beraborrowofficial/sdk.git"}, "publishConfig": {"@Beraborrowofficial:registry": "https://npm.pkg.github.com"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.esm.js", "browser": "dist/index.umd.js", "typings": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "scripts": {"format": "prettier --write 'src/**/*.{ts,js,json,md}'", "lint": "eslint", "build": "yarn lint && tsc --project tsconfig.json && rimraf dist && rollup -c", "dev": "rollup -c -w", "test": "ts-mocha --paths --recursive -p tsconfig.testing.json 'src/**/*.spec.ts' --timeout 20000"}, "dependencies": {"@apollo/client": "^3.10.8", "axios": "1.8.1", "graphql": "^16.9.0", "viem": "2.23.3"}, "devDependencies": {"@jest/globals": "^29.7.0", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-typescript": "^12.1.2", "@types/node": "^20.14.2", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "eslint": "^9.4.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-tsdoc": "^0.3.0", "fs-extra": "^11.2.0", "prettier": "^3.3.3", "rimraf": "^6.0.1", "rollup": "^4.37.0", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-terser": "^7.0.2", "typescript": "^5.8.2"}, "packageManager": "yarn@2.4.3"}