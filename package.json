{"name": "beraborrow-subgraph", "license": "UNLICENSED", "scripts": {"format": "prettier --write 'src/**/*.{ts,js,json,md}'", "codegen": "graph codegen", "build": "yarn prepare:main && yarn codegen && graph build", "build:local": "yarn prepare:local && yarn codegen && graph build", "build:test": "yarn prepare:test && yarn codegen && graph build", "prepare:main": "mustache config/berachain-mainnet.json subgraph.template.yaml > subgraph.yaml", "prepare:test": "mustache config/berachain-testnet.json subgraph.template.yaml > subgraph.yaml", "prepare:dev": "mustache config/bsc-testnet.json subgraph.template.yaml > subgraph.yaml", "prepare:local": "mustache config/local-node.json subgraph.template.yaml > subgraph.yaml", "deploy": "<PERSON>sky subgraph deploy bera-borrow-prod/1.0.8  --path .", "deploy:test": "<PERSON>sky subgraph deploy bera-borrow-test/1.0.0 --path .", "deploy:dev": "<PERSON>sky subgraph deploy bera-borrow-dev/1.0.2 --path .", "create-local": "graph create --node http://localhost:8020/ beraborrow-subgraph", "remove-local": "graph remove --node http://localhost:8020/ beraborrow-subgraph", "deploy-local": "graph deploy --node http://localhost:8020/ --ipfs http://localhost:5001 beraborrow-subgraph", "deploy-thegraph": "graph deploy --studio beraborrow-subgraph-test", "test": "graph test"}, "dependencies": {"@graphprotocol/graph-cli": "^0.81.0", "@graphprotocol/graph-ts": "^0.35.1"}, "devDependencies": {"@types/node": "22.12.0", "matchstick-as": "^0.6.0", "mustache": "^4.2.0", "prettier": "^3.3.3"}, "packageManager": "yarn@4.5.0+sha512.837566d24eec14ec0f5f1411adb544e892b3454255e61fdef8fd05f3429480102806bac7446bc9daff3896b01ae4b62d00096c7e989f1596f2af10b927532f39"}