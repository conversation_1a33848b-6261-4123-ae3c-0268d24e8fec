Mock deployment addresses (keeper is up and harvesting rewards every 24h for both distributors):
MockMetaBeraborrowCore:0x807d43b7817fdad4c44e85433ac50edaf8ece8d0
Owner:0xA2b85A54E4C6CFD6a1669d5a24086468936687Bf
Treasury:0xA2b85A54E4C6CFD6a1669d5a24086468936687Bf
Keeper:0x75350C7Fd539147cE8675B3Fbb9A51Cf5A7FE0B4
MockPollen:0x1a263a34b0411eac12dccf8a341fe4afb78c012c
MockiBGT:0x3bc6ec2a7116c6400bc7240b8635ecfb5704c4e1
MockiRED:0x091f25d7b3b5d1915862af38449d583ee1704f67
MockoBERO:0xfe2e9e40faf33b3ae1f88be1fd19ef87ab1a2f85
SPollen:0x99d8937705d8b27cf9314b7a47b1cb2c3af739b1
MockPollenLP:0x4b7e961ee7808cd932fd8fca2934d859321f0e39
MockOBRouter:0x96db06e37c3c8209b6440bfc890c3cd3629fd34f
MockPriceFeed:0x57b61016342f7a477e9cde3343d79781c366954f
MockInfraredVault:0x6d0e31220195d506f30be81238f39b78ea7a345b
MockBeradromeGauge:0xab5a8aa3940a171c32822b624cfbb2aa1bd4023f
MockBeradromePlugin:0xe602ad0028c0e9cb5519daa70ded62b763d9f35b
PollenSwapper:0xe6c03b9236001a57789543b2b4e6dc0ebde1ae7c
INFRARED:
LpAdapter(impl):0x3cedae06fb3e5b9377183f35ab1c4f0fae6db075
LpAdapter(proxy):0xf0f6458eb7fae39565f87b7f5ca1db3b631e2124
InfraredRewardDistributor:0xc49350ee635748d89f0832e39854f5de5377cf5e
VotingEscrowPollen:0x56729e20cd8b33802bb46f1e17c677577e55c55c
PollenStaking:0x7bdef3efc74578df26455bdd7cfe523e7fbda787
VePollenFeeDistributor:0x9c114e37d96c03673d8e0af81d2dfc351a5e9472
BERADROME:
SPollenAdapter(impl):0x4a24da5fe6fc5d81290ca2b70466110238b00eff
SPollenAdapter(proxy):0x87abb4e8ea9c16c8fcc9f45dd6cae2baaa2b121d
BeradromeRewardDistributor:0x042dc9dbfabd911edfa1117350bf7a0643173309
VotingEscrowPollen:0xe5eee57097ea5809060859e9cb55e277637531d2
PollenStaking:0x4a830fe75b8ae1ce89e37bf32a06e6468e1e779a
VePollenFeeDistributor:0x52b3e477b013fc60ea58fd24aa78d6431c860b54
