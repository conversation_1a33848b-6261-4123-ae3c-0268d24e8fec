import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import jotaiDebugLabel from "jotai/babel/plugin-debug-label";
import jotaiReactRefresh from "jotai/babel/plugin-react-refresh";
import { VitePWA } from "vite-plugin-pwa";
import { sentryVitePlugin } from "@sentry/vite-plugin";
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  const PRODUCTION = env.VITE_PRODUCTION !== "false" && mode === "production";
  return {
    build: {
      sourcemap: PRODUCTION,
      rollupOptions: {
        output: {
          manualChunks: {
            mui: ["@mui/material", "@mui/icons-material"],
            wagmi: ["wagmi"],
            reown: ["@reown/appkit", "@reown/appkit-adapter-wagmi", "@reown/appkit-scaffold-ui"],
            sdk: ["@Beraborrowofficial/sdk"],
          },
        },
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
        },
      },
    },
    preserveSymlinks: true,
    resolve: {
      preserveSymlinks: true,
    },
    plugins: [
      react({ babel: { plugins: [jotaiDebugLabel, jotaiReactRefresh] } }),
      PRODUCTION &&
        sentryVitePlugin({
          authToken: process.env.SENTRY_AUTH_TOKEN,
          org: "beraborrow",
          project: "beraborrow",
        }),
      VitePWA({
        registerType: "prompt",
        manifest: {
          orientation: "any",
          display: "standalone",
          lang: "en-GB",
          name: "BeraBorrow",
          short_name: "BeraBorrow",
          description: "Beraborrow unlocks instant liquidity against Berachain native assets through our stablecoin called Nectar ($NECT).",
          start_url: "/",
          scope: "/",
          theme_color: "#ec6f15",
          background_color: "#826a5f",
          icons: [
            {
              src: "/android-icon-192x192.png",
              sizes: "192x192",
              type: "image/png",
            },
            {
              purpose: "maskable",
              sizes: "512x512",
              src: "icon512_maskable.png",
              type: "image/png",
            },
            {
              purpose: "any",
              sizes: "512x512",
              src: "icon512_rounded.png",
              type: "image/png",
            },
          ],
        },
        workbox: {
          clientsClaim: false,
          skipWaiting: false,
          cleanupOutdatedCaches: true, // Remove old caches
          runtimeCaching: [
            {
              urlPattern: /\.html$/,
              handler: "StaleWhileRevalidate",
              options: {
                cacheName: "html-cache",
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 1 * 24 * 60 * 60,
                },
              },
            },
            {
              urlPattern: /^(?!.*\/sw\.js$).*\.(?:js)$/,
              handler: "CacheFirst",
              options: {
                cacheName: "js-cache",
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 30 * 24 * 60 * 60,
                },
              },
            },
            {
              urlPattern: /\.(?:css)$/,
              handler: "CacheFirst",
              options: {
                cacheName: "css-cache",
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 30 * 24 * 60 * 60,
                },
              },
            },
            {
              urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
              handler: "CacheFirst",
              options: {
                cacheName: "image-cache",
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 7 * 24 * 60 * 60,
                },
              },
            },
            {
              urlPattern: /\.(?:woff|woff2)$/,
              handler: "CacheFirst",
              options: {
                cacheName: "font-cache",
                expiration: {
                  maxEntries: 20,
                  maxAgeSeconds: 30 * 24 * 60 * 60,
                },
              },
            },
          ],
          navigateFallback: null,
        },
        includeAssets: ["fonts/*.woff", "fonts/*.woff2", "icons/*.png", "icons/*.svg", "imgs/*.png", "imgs/*.svg", "background/*.webp", "background/landing/*.webp"],
      }),
    ],
  };
});
