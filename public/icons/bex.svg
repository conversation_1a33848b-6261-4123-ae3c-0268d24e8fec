<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40"><rect width="40" height="40" fill="url(#a)" rx="10"/><path fill="#F1EBE4" d="M28.202 23.396q1.56 2.082.654 4.528-.905 2.445-3.593 3.208a5 5 0 0 1-2.622.028q-1.337-.336-2.192-1.271-3.1 1.385-5.825-.136t-3.275-4.941a4.7 4.7 0 0 1-2.305-1.303q-.95-.972-1.37-2.452-.673-2.376 1.074-4.45 1.746-2.074 4.054-1.803l2.702.328a6.36 6.36 0 0 1 1.506-2.553 6.2 6.2 0 0 1 2.528-1.622l-.575-2.025A1.18 1.18 0 0 1 19.057 8a1.18 1.18 0 0 1 .743-.568 1.18 1.18 0 0 1 .931.093q.425.237.57.743l.662 2.337q1.563.02 2.757.67t2.321 2.098l2.26-.641a1.18 1.18 0 0 1 .93.093q.425.237.57.744a1.18 1.18 0 0 1-.094.93 1.18 1.18 0 0 1-.744.57l-2.025.574a6.5 6.5 0 0 1-.014 2.992 6 6 0 0 1-1.342 2.611zm-9.394 3.676q-.299-1.052-.405-2.095a11.3 11.3 0 0 1-.02-2.077 7.6 7.6 0 0 1-1.758 1.15 8.7 8.7 0 0 1-2.017.678q.431 1.52 1.556 2.147 1.126.628 2.644.197m-4.694-5.57q1.246-.354 2.112-.936t2.12-1.948l-5.227-.621q-1.262-.147-1.922.566-.66.714-.362 1.765.287 1.013 1.082 1.293.795.279 2.197-.119m10.265 6.514q.973-.275 1.384-1.13.41-.853-.111-1.504l-3.607-4.7q-.386 1.456-.441 2.819-.056 1.362.232 2.375.364 1.285 1.006 1.84.641.555 1.537.3m.117-9.376q.279-.5.33-1.209a4 4 0 0 0-.148-1.41q-.353-1.245-1.414-1.87-1.062-.625-2.269-.283a3.9 3.9 0 0 0-1.258.61q-.556.41-.863.96l3.436.54z" filter="url(#b)"/><defs><radialGradient id="a" cx="0" cy="0" r="1" gradientTransform="rotate(90 0 20)scale(20)" gradientUnits="userSpaceOnUse"><stop stop-color="#E9D9B2"/><stop offset="1" stop-color="#E6B434"/></radialGradient><filter id="b" width="27.367" height="27.944" x="5.487" y="6.377" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="bgImage"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1"/><feGaussianBlur stdDeviation="1"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.45 0"/><feBlend in2="bgImage" result="effect1"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy=".5"/><feGaussianBlur stdDeviation=".5"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix values="0 0 0 0 0.909804 0 0 0 0 0.490196 0 0 0 0 0.247059 0 0 0 0.75 0"/><feBlend in2="effect1" result="effect2"/><feBlend in="SourceGraphic" in2="effect2"/></filter></defs></svg>
