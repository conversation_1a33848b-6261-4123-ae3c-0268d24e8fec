type Global @entity {
  id: ID!

  transactionCount: Int!
  changeCount: Int!

  # Total supply of debt token
  debtTokenSupplies: BigInt!

  # Total Number of unique users since the protocol's inception
  cumulativeUniqueUsers: Int!

  #borrowerOperations Address
  borrowerOperations_address: String!

  # Relationship to DenManagers
  denManagers: [DenManager!]! @derivedFrom(field: "global")
  # Relationship to Vaults
  vaults: [Vault!]! @derivedFrom(field: "global")
}

type Token @entity {
  id: ID!
  price: TokenPrice
  oracle: String
  symbol: String!
  name: String!
  decimals: Int!

  priceHistory: [TokenPrice!] @derivedFrom(field: "token")
}
enum VaultSnapshotEvent {
  EVENT
  TIMED
  REBALANCE
}
type Vault @entity {
  id: ID!
  # Reference to Global entity
  global: Global!
  token: Token!
  asset: Token!
  withdrawFee: BigInt!
  vaultSnapshot: [VaultSnapshot!] @derivedFrom(field: "vault")
  vaultSlot: [VaultSlot!] @derivedFrom(field: "vault")
}
type VaultSnapshot @entity {
  id: ID!
  vault: Vault! @index
  price: BigInt!
  timestamp: BigInt!
  snapshotEvent: VaultSnapshotEvent!
  sharePrice: BigInt!
  totalAssets: BigInt!
  iBGT: BigInt!
  totalSupply: BigInt!
  underlyingAssets: [Holding!]!
}
type VaultPosition @entity {
  id: ID!
  vault: Vault! @index
  owner: User! @index
  shares: BigInt!
  assets: BigInt!
  deposited: BigInt!
  withdrawn: [Holding!]
  changes: [VaultPositionChange!] @derivedFrom(field: "vaultPosition")
}

type VaultPositionChange implements Change @entity {
  id: ID!
  sequenceNumber: Int!
  transaction: Transaction!
  owner: User! @index
  vault: Vault! @index
  vaultPosition: VaultPosition!
  vaultOperation: PoolOperation!
  shares: BigInt!
  assets: BigInt!
  shareChange: BigInt!
  assetsChange: BigInt!
  deposited: BigInt!
  withdrawn: [Holding!]
  underlyingAssets: [Holding!]!
}

type VaultSlot @entity {
  id: ID!
  vault: Vault! @index
  bucket: VaultSlotBucket!
  slotIndex: BigInt!
  startTime: BigInt!
  startBalance: BigInt!
  startTotalSupply: BigInt!
  lastTime: BigInt!
  lastBalance: BigInt!
  lastTotalSupply: BigInt!
  cumBalance: BigInt!
  cumTotalSupply: BigInt!
  cumPrice: BigInt!
  cumTime: BigInt!
  observations: Int!
}
enum VaultSlotBucket {
  DAILY
  HOURLY
  WEEKLY
}
type DenManager @entity {
  "'DenManager' + Asset's Ethereum address as a hex-string"
  id: ID!
  # Reference to Global entity
  global: Global!
  vault: Vault
  collateral: Token!
  totalCollateral: BigInt!
  totalDebt: BigInt!
  totalCollateralRatio: BigInt

  "Total amount of debt paid as borrowing fees"
  totalBorrowingFeesPaid: BigInt!
  "Total amount of collateral asset paid as redemption fees"
  totalRedemptionFeesPaid: BigInt!

  "Total redistributed per-stake collateral"
  rawTotalRedistributedCollateral: BigInt!

  "Total redistributed per-stake debt"
  rawTotalRedistributedDebt: BigInt!

  "Total Staked Amount"
  totalStakedAmount: BigInt!

  totalNumberOfDens: Int!

  liquidationCount: Int!
  redemptionCount: Int!

  numberOfOpenDens: Int!
  numberOfLiquidatedDens: Int!
  numberOfRedeemedDens: Int!
  numberOfDensClosedByOwner: Int!

  # To track borrowing fee rate changes
  borrowingFeeFloor: BigInt!
  maxBorrowingFee: BigInt!
  borrowingFeeRate: BigInt!

  dens: [Den!] @derivedFrom(field: "denManager")
  liquidations: [Liquidation!] @derivedFrom(field: "denManager")
  redemptions: [Redemption!] @derivedFrom(field: "denManager")

  borrowingRateSnapshots: [BorrowingRateSnapshot!] @derivedFrom(field: "denManager")

  interestRate: BigInt!

  # Snapshots
  snapshots: [DenManagerSnapshot!]! @derivedFrom(field: "denManager")
}

type DenManagerSnapshot @entity {
  id: ID!
  timestamp: BigInt!
  denManager: DenManager!

  totalCollateral: BigInt!
  totalDebt: BigInt!
  totalCollateralRatio: BigInt

  totalBorrowingFeesPaid: BigInt!
  totalRedemptionFeesPaid: BigInt!

  rawTotalRedistributedCollateral: BigInt!
  rawTotalRedistributedDebt: BigInt!
  totalStakedAmount: BigInt!

  totalNumberOfDens: Int!
  liquidationCount: Int!
  redemptionCount: Int!

  borrowingFeeFloor: BigInt!
  maxBorrowingFee: BigInt!
  borrowingFeeRate: BigInt!

  interestRate: BigInt!
}

type BorrowingRateSnapshot @entity {
  id: ID!
  blockNumber: BigInt!
  timestamp: BigInt!
  feeRate: BigInt!
  denManager: DenManager!
}

type User @entity {
  "User's Ethereum address as a hex-string"
  id: ID!

  # Data for liquidity stability pool share and share changes
  sharePosition: SharePosition

  totalDepositVolumeInDen: BigInt!

  dens: [Den!] @derivedFrom(field: "owner")
  liquidations: [Liquidation!] @derivedFrom(field: "liquidator")
  redemptions: [Redemption!] @derivedFrom(field: "redeemer")
}

enum DenStatus {
  open
  closedByOwner
  closedByLiquidation
  closedByRedemption
}

type Den @entity {
  "Owner's ID + '-' + an incremented integer"
  id: ID!
  denManager: DenManager!
  owner: User!
  status: DenStatus!

  collateral: BigInt!
  debt: BigInt!

  changes: [DenChange!] @derivedFrom(field: "den")

  rawCollateral: BigInt!
  rawDebt: BigInt!
  rawStake: BigInt!
  nominalCollateralRatio: BigInt!
  nominalCollateralRatioWithRedistribution: BigInt!

  "The value of total redistributed per-stake collateral the last time rewards were applied"
  rawSnapshotOfTotalRedistributedCollateral: BigInt!

  "The value of total redistributed per-stake debt the last time rewards were applied"
  rawSnapshotOfTotalRedistributedDebt: BigInt!

  "Ordering by this field will result in the same ordering as collateral ratio (except reversed)"
  collateralRatioSortKey: BigInt

  # accured collateral rewards
  accuredCollateralRewards: BigInt!
  # accured debt rewards
  accuredDebtRewards: BigInt!
}

type Holding @entity {
  # token address
  id: ID!
  asset: Token!
  price: TokenPrice
  balance: BigInt!
}

type Portfolio @entity {
  # Pool ID + '/' + timestamp
  id: ID!
  timestamp: BigInt
  holdings: [Holding!]!
  emissions: [Holding!]!
  pool: SharePool!
  sharePrice: BigInt!
  totalAssets: BigInt!
  totalShares: BigInt!
}

type SharePool @entity {
  "Asset id"
  id: ID!
  pool_address: String!
  lspGetter_address: String!
  lspGetter_startBlock: BigInt!
  debt_token: String!
  # Current state of the pool
  portfolio: Portfolio!
  # Add field for snapshot of portfolio
  snapshots: [Portfolio!] @derivedFrom(field: "pool")
}

enum PoolOperation {
  DEPOSIT
  WITHDRAW
  TRANSFER
}

type SharePositionChange implements Change @entity {
  id: ID!
  sequenceNumber: Int!
  transaction: Transaction!

  sharePosition: SharePosition!
  shareOperation: PoolOperation!

  shareAmountBefore: BigInt!
  shareAmountChange: BigInt!
  shareAmountAfter: BigInt!
}

type SharePosition @entity {
  "Owner's ID + '-' + 'pool.ID'"
  id: ID!
  owner: User!
  shareAmount: BigInt!
  changes: [SharePositionChange!] @derivedFrom(field: "sharePosition")
}

type Transaction @entity {
  "Transaction hash"
  id: ID!

  "Can be used to correctly sort transactions even if they were mined in the same block"
  sequenceNumber: Int!

  blockNumber: Int!

  "Timestamp of block that included this transaction (seconds since epoch)"
  timestamp: Int!

  changes: [Change!]! @derivedFrom(field: "transaction")
}

interface Change {
  "Same as sequence number, but as an ID (string)"
  id: ID!

  "Can be used to correctly sort changes even if they were made by the same transaction"
  sequenceNumber: Int!

  "Transaction that made this change"
  transaction: Transaction!
}

enum DenOperation {
  openDen
  closeDen
  adjustDen
  accrueRewards
  liquidateInNormalMode
  liquidateInRecoveryMode
  redeemCollateral
}

type DenChange implements Change @entity {
  id: ID!
  sequenceNumber: Int!
  transaction: Transaction!

  denManager: DenManager!

  den: Den!
  denOperation: DenOperation!

  collateralBefore: BigInt!
  collateralChange: BigInt!
  collateralAfter: BigInt!

  debtBefore: BigInt!
  debtChange: BigInt!
  debtAfter: BigInt!

  borrowingFee: BigInt

  collateralRatioBefore: BigInt
  collateralRatioAfter: BigInt

  liquidation: Liquidation
  redemption: Redemption
}

type Liquidation @entity {
  id: ID!
  transaction: Transaction!
  liquidator: User!
  denManager: DenManager!

  liquidatedDebt: BigInt!
  liquidatedCollateral: BigInt!
  collGasCompensation: BigInt!
  tokenGasCompensation: BigInt!

  denChanges: [DenChange!]! @derivedFrom(field: "liquidation")
}

type Redemption @entity {
  id: ID!
  transaction: Transaction!
  redeemer: User!
  denManager: DenManager!

  tokensAttemptedToRedeem: BigInt!
  tokensActuallyRedeemed: BigInt!
  collateralRedeemed: BigInt!
  partial: Boolean!

  fee: BigInt!

  denChanges: [DenChange!]! @derivedFrom(field: "redemption")
}

type TokenPrice @entity {
  id: ID!
  token: Token!
  price: BigInt!
  timestamp: BigInt!
}

type PriceFeed @entity {
  id: ID!
  address: String!
  tokenIds: [Bytes!]!
}

type FinancialDailySnapshot @entity {
  id: ID!

  dayStartTimestamp: BigInt!

  dailyBorrowUSD: BigInt!
  dailyDepositUSD: BigInt!

  totalValueLockedUSD: BigInt!
  debtTokenSupplies: BigInt!
  # Current state of the pool
  sharePoolPortfolio: Portfolio!
  timestamp: BigInt!
  blockNumber: BigInt!
  # Store IDs of the snapshots taken
  denManagerSnapshots: [DenManagerSnapshot!]!
}

type MarketDailySnapshot @entity {
  id: ID!

  dayStartTimestamp: BigInt!

  dailyBorrowUSD: BigInt!
  dailyDepositUSD: BigInt!

  debtTokenSupplies: BigInt!

  timestamp: BigInt!
  blockNumber: BigInt!
}

type MetricsDailySnapshot @entity {
  id: ID!

  dayStartTimestamp: BigInt!

  dailyTransactionCount: Int!
  cumulativeUniqueUsers: Int!

  timestamp: BigInt!
  blockNumber: BigInt!
}
