name: Deploy Beraborrow Interface Productions Mainnet

on:
  workflow_dispatch:
  release:
    types: published
jobs:
  deploy:
    name: Deploy Beraborrow Interface Productions Mainnet
    uses: Beraborrowofficial/interface/.github/workflows/build.yml@main
    with:
      build: yarn run build-mainnet
    secrets:
      aws_region: ${{ secrets.AWS_DEFAULT_REGION }}
      aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      cloudfront_distribution_id: ${{ secrets.INTERFACE_PROD_MAINNET_DISTRIBUTION_ID }}
      bucket: ${{ secrets.INTERFACE_PROD_MAINNET_BUCKET }}
      PRIVATE_REPO: ${{ secrets.PRIVATE_REPO }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
