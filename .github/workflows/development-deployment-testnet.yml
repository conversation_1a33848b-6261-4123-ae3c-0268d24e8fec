name: Deploy Beraborrow Interface Development Testnet

on:
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy Beraborrow Interface Development Testnet
    uses: Beraborrowofficial/interface/.github/workflows/build.yml@development
    with:
      build: yarn run build-dev-testnet
    secrets:
      aws_region: ${{ secrets.AWS_DEFAULT_REGION }}
      aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      cloudfront_distribution_id: ${{ secrets.INTERFACE_DEV_TESTNET_DISTRIBUTION_ID }}
      bucket: ${{ secrets.INTERFACE_DEV_TESTNET_BUCKET }}
      PRIVATE_REPO: ${{ secrets.PRIVATE_REPO }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
