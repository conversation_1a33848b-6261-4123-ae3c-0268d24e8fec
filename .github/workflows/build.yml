name: Build and Deploy Beraborrow Interface

on:
  workflow_call:
    inputs:
      build:
        required: true
        type: string
        description: The build command to run

    secrets:
      aws_region:
        required: true
      aws_access_key_id:
        required: true
      aws_secret_access_key:
        required: true
      cloudfront_distribution_id:
        required: true
      bucket:
        required: true
      PRIVATE_REPO:
        required: true
      SENTRY_AUTH_TOKEN:
        required: true

jobs:
  build_and_deploy:
    name: Build and Deploy Project
    runs-on: ubuntu-latest

    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.aws_access_key_id }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.aws_secret_access_key }}
      AWS_DEFAULT_REGION: ${{ secrets.aws_region }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

    steps:
      - name: Checkout the code
        uses: actions/checkout@v4

      - name: Cache Yarn dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.yarn/cache
            node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies and setup npmrc
        run: |
          yarn config set npmScopes.Beraborrowofficial.npmAuthToken "${{ secrets.PRIVATE_REPO }}"
          yarn config set npmScopes.Beraborrowofficial.npmRegistryServer "https://npm.pkg.github.com"
          yarn install
        env:
          PRIVATE_REPO: ${{ secrets.PRIVATE_REPO }}

      - name: Build the project
        run: ${{ inputs.build }}

      - name: Verify dist folder exists
        run: |
          if [ ! -d "./dist" ]; then
            echo "Build failed, 'dist' folder not found!"
            exit 1
          fi

      - name: Deploy to S3
        run: |
          echo "Deploying non-WebP files to S3..."
          aws s3 sync ./dist s3://${{ secrets.bucket }} --delete --exclude "*.webp" --cache-control "public, max-age=31536000, immutable"

          echo "Deploying WebP files with correct Content-Type..."
          aws s3 cp ./dist s3://${{ secrets.bucket }} --recursive --exclude "*" --include "*.webp" --content-type "image/webp" --cache-control "public, max-age=31536000, immutable"
      - name: Invalidate CloudFront Cache
        run: |
          echo "Invalidating CloudFront distribution cache..."
          aws cloudfront create-invalidation --distribution-id ${{ secrets.cloudfront_distribution_id }} --paths '/index.html' '/sw.js'
