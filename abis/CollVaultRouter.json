[{"type": "constructor", "inputs": [{"name": "_borrowerOperations", "type": "address", "internalType": "address"}, {"name": "_wBera", "type": "address", "internalType": "address"}, {"name": "_nectar", "type": "address", "internalType": "address"}, {"name": "_liquidStabilityPool", "type": "address", "internalType": "address"}, {"name": "_metaBeraborrowCore", "type": "address", "internalType": "address"}, {"name": "_obRouter", "type": "address", "internalType": "address"}, {"name": "_ibgt<PERSON><PERSON>", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "adjustDenVault", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct ICollVaultRouter.AdjustDenVaultParams", "components": [{"name": "den<PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IDenManager"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IInfraredCollateralVault"}, {"name": "_maxFeePercentage", "type": "uint256", "internalType": "uint256"}, {"name": "_collAssetToDeposit", "type": "uint256", "internalType": "uint256"}, {"name": "_collWithdrawal", "type": "uint256", "internalType": "uint256"}, {"name": "_debtChange", "type": "uint256", "internalType": "uint256"}, {"name": "_isDebtIncrease", "type": "bool", "internalType": "bool"}, {"name": "_upperHint", "type": "address", "internalType": "address"}, {"name": "_lowerHint", "type": "address", "internalType": "address"}, {"name": "unwrap", "type": "bool", "internalType": "bool"}, {"name": "_minSharesMinted", "type": "uint256", "internalType": "uint256"}, {"name": "_minAssetsWithdrawn", "type": "uint256", "internalType": "uint256"}, {"name": "_collIndex", "type": "uint256", "internalType": "uint256"}, {"name": "_preDeposit", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "claimCollateralRouter", "inputs": [{"name": "den<PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IDenManager"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IInfraredCollateralVault"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "minAssetsWithdrawn", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimLockedTokens", "inputs": [{"name": "tokens", "type": "address[]", "internalType": "contract IERC20[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "closeDenVault", "inputs": [{"name": "den<PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IDenManager"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IInfraredCollateralVault"}, {"name": "minAssetsWithdrawn", "type": "uint256", "internalType": "uint256"}, {"name": "collIndex", "type": "uint256", "internalType": "uint256"}, {"name": "unwrap", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "depositFromAny", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct ICollVaultRouter.DepositFromAnyParams", "components": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IInfraredCollateralVault"}, {"name": "inputToken", "type": "address", "internalType": "address"}, {"name": "inputAmount", "type": "uint256", "internalType": "uint256"}, {"name": "minSharesMinted", "type": "uint256", "internalType": "uint256"}, {"name": "outputMin", "type": "uint256", "internalType": "uint256"}, {"name": "outputReceiver", "type": "address", "internalType": "address"}, {"name": "dexCalldata", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "openDenVault", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct ICollVaultRouter.OpenDenVaultParams", "components": [{"name": "den<PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IDenManager"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IInfraredCollateralVault"}, {"name": "_maxFeePercentage", "type": "uint256", "internalType": "uint256"}, {"name": "_debtAmount", "type": "uint256", "internalType": "uint256"}, {"name": "_collAssetToDeposit", "type": "uint256", "internalType": "uint256"}, {"name": "_upperHint", "type": "address", "internalType": "address"}, {"name": "_lowerHint", "type": "address", "internalType": "address"}, {"name": "_minSharesMinted", "type": "uint256", "internalType": "uint256"}, {"name": "_collIndex", "type": "uint256", "internalType": "uint256"}, {"name": "_preDeposit", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "previewRedeemUnderlying", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IInfraredCollateralVault"}, {"name": "sharesToRedeem", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "tokens", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "redeemCollateralVault", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct ICollVaultRouter.RedeemCollateralVaultParams", "components": [{"name": "den<PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IDenManager"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IInfraredCollateralVault"}, {"name": "_debtAmount", "type": "uint256", "internalType": "uint256"}, {"name": "_firstRedemptionHint", "type": "address", "internalType": "address"}, {"name": "_upperPartialRedemptionHint", "type": "address", "internalType": "address"}, {"name": "_lowerPartialRedemptionHint", "type": "address", "internalType": "address"}, {"name": "_partialRedemptionHintNICR", "type": "uint256", "internalType": "uint256"}, {"name": "_maxIterations", "type": "uint256", "internalType": "uint256"}, {"name": "_maxFeePercentage", "type": "uint256", "internalType": "uint256"}, {"name": "_minSharesWithdrawn", "type": "uint256", "internalType": "uint256"}, {"name": "minAssetsWithdrawn", "type": "uint256", "internalType": "uint256"}, {"name": "collIndex", "type": "uint256", "internalType": "uint256"}, {"name": "unwrap", "type": "bool", "internalType": "bool"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeemToOne", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct ICollVaultRouter.RedeemToOneParams", "components": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "contract IInfraredCollateralVault"}, {"name": "targetToken", "type": "address", "internalType": "address"}, {"name": "minTargetTokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "outputQuotes", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "outputMins", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "pathDefinitions", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "executor", "type": "address", "internalType": "address"}, {"name": "referralCode", "type": "uint32", "internalType": "uint32"}]}], "outputs": [], "stateMutability": "nonpayable"}]