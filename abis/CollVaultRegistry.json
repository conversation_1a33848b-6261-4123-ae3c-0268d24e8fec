[{"inputs": [{"internalType": "address", "name": "_metaBeraborrowCore", "type": "address"}, {"internalType": "address", "name": "_initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "name": "NotCollateralVault", "type": "error"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}], "name": "Only<PERSON><PERSON>er", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "vault", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "protocolInstance", "type": "uint8"}], "name": "NewCollateralVault", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "", "type": "address"}], "name": "New<PERSON>wn<PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "", "type": "address"}], "name": "Removed<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "vault", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "protocolInstance", "type": "uint8"}], "name": "VaultModified", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "collateralVaults", "outputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "uint8", "name": "protocolInstance", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getCollateralVaults", "outputs": [{"components": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "uint8", "name": "protocolInstance", "type": "uint8"}], "internalType": "struct CollateralVaultRegistry.Vault[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isCollateralVault", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isOwner", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "metaBeraborrowCore", "outputs": [{"internalType": "contract IMetaBeraborrowCore", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "uint8", "name": "protocolInstance", "type": "uint8"}], "internalType": "struct CollateralVaultRegistry.Vault", "name": "_collVault", "type": "tuple"}], "name": "modifyCollateralVault", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_collVault", "type": "address"}], "name": "removeCollateral<PERSON>ault", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "uint8", "name": "protocolInstance", "type": "uint8"}], "internalType": "struct CollateralVaultRegistry.Vault[]", "name": "_collVaults", "type": "tuple[]"}], "name": "setCollateralVaults", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "bool", "name": "whitelisted", "type": "bool"}], "name": "whitelist<PERSON><PERSON>er", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]