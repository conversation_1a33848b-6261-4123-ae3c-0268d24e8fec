[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "SUNSET_DURATION", "inputs": [], "outputs": [{"name": "", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "addNewExtraAsset", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "_unlockRatePerSecond", "type": "uint64", "internalType": "uint64"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "asset", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToAssets", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToShares", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "enableCollateral", "inputs": [{"name": "_collateral", "type": "address", "internalType": "address"}, {"name": "_unlockRatePerSecond", "type": "uint64", "internalType": "uint64"}, {"name": "forceThroughBalanceCheck", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "extSloads", "inputs": [{"name": "slots", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [{"name": "res", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"type": "function", "name": "getCollateralTokens", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getLockedEmissions", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getPrice", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "scaledPriceInUsdWad", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalDebtTokenDeposits", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct ILiquidStabilityPool.InitParams", "components": [{"name": "_asset", "type": "address", "internalType": "contract IERC20"}, {"name": "_sharesName", "type": "string", "internalType": "string"}, {"name": "_sharesSymbol", "type": "string", "internalType": "string"}, {"name": "_metaBeraborrowCore", "type": "address", "internalType": "contract IMetaBeraborrowCore"}, {"name": "_liquidationManager", "type": "address", "internalType": "address"}, {"name": "_factory", "type": "address", "internalType": "address"}, {"name": "_feeReceiver", "type": "address", "internalType": "address"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "linearVestingExtraAssets", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "amount", "type": "int256", "internalType": "int256"}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "maxDeposit", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "maxMint", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON>", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "offset", "inputs": [{"name": "collateral", "type": "address", "internalType": "address"}, {"name": "_debtToOffset", "type": "uint256", "internalType": "uint256"}, {"name": "_collToAdd", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "previewDeposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewMint", "inputs": [{"name": "netShares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewRedeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewWithdraw", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "rebalance", "inputs": [{"name": "p", "type": "tuple", "internalType": "struct ILiquidStabilityPool.RebalanceParams", "components": [{"name": "sent<PERSON><PERSON><PERSON>cy", "type": "address", "internalType": "address"}, {"name": "sentAmount", "type": "uint256", "internalType": "uint256"}, {"name": "received<PERSON><PERSON>rency", "type": "address", "internalType": "address"}, {"name": "swapper", "type": "address", "internalType": "address"}, {"name": "payload", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "preferredUnderlyingTokens", "type": "address[]", "internalType": "address[]"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "_owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "_owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeExtraAsset", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setBoycoVaults", "inputs": [{"name": "_boycoVaults", "type": "address[]", "internalType": "address[]"}, {"name": "enable", "type": "bool[]", "internalType": "bool[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPairThreshold", "inputs": [{"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "thresholdInBP", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setUnlockRatePerSecond", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "_unlockRatePerSecond", "type": "uint64", "internalType": "uint64"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "startCollateralSunset", "inputs": [{"name": "collateral", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "amountInNect", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateProtocol", "inputs": [{"name": "_liquidationManager", "type": "address", "internalType": "address"}, {"name": "_factory", "type": "address", "internalType": "address"}, {"name": "_register", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "_owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "preferredUnderlyingTokens", "type": "address[]", "internalType": "address[]"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "_owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "AssetsWithdraw", "inputs": [{"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokens", "type": "address[]", "indexed": false, "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "CollateralOverwritten", "inputs": [{"name": "oldCollateral", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newCollateral", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "EmissionsAdded", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "EmissionsSub", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "ExtraAssetAdded", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ExtraAssetRemoved", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "NewUnlockRatePerSecond", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "unlockRatePerSecond", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Offset", "inputs": [{"name": "collateral", "type": "address", "indexed": false, "internalType": "address"}, {"name": "debtToOffset", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collToAdd", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collSurplusAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ProtocolBlacklisted", "inputs": [{"name": "factoryRemoved", "type": "address", "indexed": true, "internalType": "address"}, {"name": "LMremoved", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ProtocolRegistered", "inputs": [{"name": "factory", "type": "address", "indexed": true, "internalType": "address"}, {"name": "liquidationManager", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Rebalance", "inputs": [{"name": "sent<PERSON><PERSON><PERSON>cy", "type": "address", "indexed": true, "internalType": "address"}, {"name": "received<PERSON><PERSON>rency", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sentAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "receivedAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "sentValue", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "receivedValue", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Withdraw", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AddressZero", "inputs": []}, {"type": "error", "name": "AmountCannotBeZero", "inputs": []}, {"type": "error", "name": "BalanceRemaining", "inputs": []}, {"type": "error", "name": "BelowThreshold", "inputs": []}, {"type": "error", "name": "BootstrapPeriod", "inputs": []}, {"type": "error", "name": "CallerNotFactory", "inputs": []}, {"type": "error", "name": "CallerNotLM", "inputs": []}, {"type": "error", "name": "CollateralIsSunsetting", "inputs": []}, {"type": "error", "name": "CollateralMustBeSunset", "inputs": []}, {"type": "error", "name": "DuplicateToken", "inputs": []}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC4626ExceededMaxDeposit", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC4626ExceededMaxMint", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC4626ExceededMaxRedeem", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC4626ExceededMaxWithdraw", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "EmissionRateExceedsMax", "inputs": []}, {"type": "error", "name": "ExistingCollateral", "inputs": []}, {"type": "error", "name": "FactoryAlreadyRegistered", "inputs": []}, {"type": "error", "name": "FactoryNotRegistered", "inputs": []}, {"type": "error", "name": "InvalidArrayLength", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidThreshold", "inputs": []}, {"type": "error", "name": "InvalidToken", "inputs": []}, {"type": "error", "name": "LMAlreadyRegistered", "inputs": []}, {"type": "error", "name": "LMNotRegistered", "inputs": []}, {"type": "error", "name": "LastTokenMustBeNect", "inputs": []}, {"type": "error", "name": "NoPriceFeed", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "Only<PERSON><PERSON>er", "inputs": []}, {"type": "error", "name": "Paused", "inputs": []}, {"type": "error", "name": "SameTokens", "inputs": []}, {"type": "error", "name": "TokenCannotBeExtraAsset", "inputs": []}, {"type": "error", "name": "TokenCannotBeNect", "inputs": []}, {"type": "error", "name": "TokenIsVesting", "inputs": []}, {"type": "error", "name": "TokenMustBeExtraAsset", "inputs": []}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "WithdrawingLockedEmissions", "inputs": []}, {"type": "error", "name": "ZeroTotalSupply", "inputs": []}]