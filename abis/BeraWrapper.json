[{"type": "constructor", "inputs": [{"name": "_borrowerOperations", "type": "address", "internalType": "address"}, {"name": "_wBeraDenManager", "type": "address", "internalType": "address"}, {"name": "_wBera", "type": "address", "internalType": "address"}, {"name": "_nectar", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "addCollNative", "inputs": [{"name": "_upperHint", "type": "address", "internalType": "address"}, {"name": "_lowerHint", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "adjustDenNative", "inputs": [{"name": "_maxFeePercentage", "type": "uint256", "internalType": "uint256"}, {"name": "_collWithdrawal", "type": "uint256", "internalType": "uint256"}, {"name": "_debtChange", "type": "uint256", "internalType": "uint256"}, {"name": "_isDebtIncrease", "type": "bool", "internalType": "bool"}, {"name": "_upperHint", "type": "address", "internalType": "address"}, {"name": "_lowerHint", "type": "address", "internalType": "address"}, {"name": "unwrap", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "openDenNative", "inputs": [{"name": "_maxFeePercentage", "type": "uint256", "internalType": "uint256"}, {"name": "_debtAmount", "type": "uint256", "internalType": "uint256"}, {"name": "_upperHint", "type": "address", "internalType": "address"}, {"name": "_lowerHint", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}]